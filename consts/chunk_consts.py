"""
代码分块相关常量定义
"""
import os

# 预编译tree-sitter语言库路径
TREE_SITTER_LIB_PATH = os.environ.get('TREE_SITTER_LIB_PATH', 'build/my-languages.so')

# 配置文件后缀
CONFIG_SUFFIXES = (
    '.env', '.yml', '.yaml', '.json', '.ini', '.toml', 
    '.conf', '.cfg', '.properties', '.config', '.txt'
)

# 支持的代码文件后缀
SUPPORTED_CODE_SUFFIXES = ['.py', '.java', '.js', '.ts']

# Python相关常量
PYTHON_IMPORT_PATTERNS = [
    r'^(import |from .+ import )',
    r'^(import |package )',
    r'^(import |require$|export .+ from )',
    r'^(#include )'
]

# 特殊标记前缀
SPECIAL_MARKERS = ['FLASK_', 'DECORATOR_', 'DYNAMIC_CALL_', 'ASYNC_']

# 默认配置
DEFAULT_CACHE_TTL = 3600  # 缓存过期时间（秒）
DEFAULT_MAX_LINES = 400   # 每个片段最大行数
DEFAULT_MAX_DEP_DEPTH = 2 # 依赖递归深度
DEFAULT_CONTEXT_LINES = 20 # 上下文补全行数

# 排除的依赖库目录
EXCLUDE_DIRS = {
    'venv', 'env', '.venv', 'site-packages', 'node_modules', 
    '__pycache__', 'dist', 'build', '.git'
}