{"language": "python", "business": "security", "version": "1.0", "description": "Python安全审查专用规范", "custom_instructions": "\n## 安全审查要求\n1. 重点关注安全漏洞和风险点\n2. 对所有用户输入进行安全性检查\n3. 检查敏感信息泄露风险\n4. 验证权限控制和访问控制\n5. 检查加密和数据保护措施\n", "output_format": "JSON结构化输出，重点标注安全风险等级", "rules": [{"id": "SEC-P0-1", "level": "P0", "title": "SQL注入漏洞", "description": "存在SQL注入风险，使用字符串拼接构建SQL语句", "examples": ["cursor.execute(f\"SELECT * FROM users WHERE id = {user_id}\")", "query = \"SELECT * FROM \" + table_name", "直接拼接用户输入到SQL语句"], "keywords": ["SQL注入", "字符串拼接", "execute", "query"]}, {"id": "SEC-P0-2", "level": "P0", "title": "命令注入漏洞", "description": "存在命令注入风险，直接执行用户输入的命令", "examples": ["os.system(user_input)", "subprocess.call(shell_command)", "eval(user_data)"], "keywords": ["命令注入", "os.system", "subprocess", "eval", "exec"]}, {"id": "SEC-P0-3", "level": "P0", "title": "敏感信息泄露", "description": "硬编码密码、API密钥等敏感信息", "examples": ["password = \"123456\"", "api_key = \"sk-1234567890\"", "secret = \"my_secret_key\""], "keywords": ["硬编码", "密码", "API密钥", "secret", "token"]}, {"id": "SEC-P0-4", "level": "P0", "title": "不安全的反序列化", "description": "使用pickle等不安全的反序列化方法处理不可信数据", "examples": ["pickle.loads(user_data)", "pickle.load(untrusted_file)", "yaml.load(user_input)"], "keywords": ["pickle", "反序列化", "yaml.load", "不可信数据"]}, {"id": "SEC-P1-1", "level": "P1", "title": "弱密码策略", "description": "密码强度不足或缺少密码复杂度验证", "examples": ["简单密码验证", "缺少密码长度检查", "未验证密码复杂度"], "keywords": ["密码强度", "密码验证", "复杂度"]}, {"id": "SEC-P1-2", "level": "P1", "title": "不安全的随机数", "description": "使用不安全的随机数生成器生成安全相关的随机值", "examples": ["random.random()用于生成token", "time.time()作为随机种子", "简单的随机数生成"], "keywords": ["随机数", "random", "安全随机", "token生成"]}, {"id": "SEC-P1-3", "level": "P1", "title": "缺少输入验证", "description": "对用户输入缺少充分的验证和过滤", "examples": ["直接使用用户输入", "缺少输入长度检查", "未过滤特殊字符"], "keywords": ["输入验证", "用户输入", "过滤", "验证"]}, {"id": "SEC-P2-1", "level": "P2", "title": "信息泄露风险", "description": "可能通过错误信息或日志泄露敏感信息", "examples": ["详细的错误堆栈信息", "日志中包含敏感数据", "调试信息未清理"], "keywords": ["信息泄露", "错误信息", "日志", "调试"]}, {"id": "SEC-P2-2", "level": "P2", "title": "不安全的HTTP通信", "description": "使用HTTP而非HTTPS进行敏感数据传输", "examples": ["http://api.example.com", "不验证SSL证书", "明文传输敏感数据"], "keywords": ["HTTP", "HTTPS", "SSL", "明文传输"]}]}