{"language": "java", "business": "enterprise", "version": "1.0", "description": "Java企业级开发规范", "custom_instructions": "\n## Java企业级代码审查要求\n1. 严格遵循阿里巴巴Java开发手册\n2. 重点检查并发安全和性能问题\n3. 验证异常处理和资源管理\n4. 检查设计模式的正确使用\n5. 确保代码的可测试性和可维护性\n", "output_format": "JSON结构化输出，包含企业级规范检查结果", "rules": [{"id": "JAVA-P0-1", "level": "P0", "title": "空指针异常风险", "description": "存在潜在的空指针异常，未进行null检查", "examples": ["object.method()未检查object是否为null", "直接访问可能为null的对象属性", "集合操作未检查null"], "keywords": ["空指针", "null", "NullPointerException"]}, {"id": "JAVA-P0-2", "level": "P0", "title": "资源泄露", "description": "资源未正确关闭，可能导致内存泄露", "examples": ["InputStream未关闭", "数据库连接未释放", "未使用try-with-resources"], "keywords": ["资源泄露", "close", "try-with-resources", "InputStream", "Connection"]}, {"id": "JAVA-P0-3", "level": "P0", "title": "并发安全问题", "description": "多线程环境下存在线程安全问题", "examples": ["非线程安全的集合在多线程环境使用", "共享变量未同步", "竞态条件"], "keywords": ["并发", "线程安全", "synchronized", "volatile", "竞态条件"]}, {"id": "JAVA-P1-1", "level": "P1", "title": "命名规范违反", "description": "违反Java命名规范", "examples": ["类名不符合大驼峰命名", "方法名不符合小驼峰命名", "常量未使用大写下划线"], "keywords": ["命名规范", "驼峰命名", "常量命名"]}, {"id": "JAVA-P1-2", "level": "P1", "title": "异常处理不当", "description": "异常处理不规范或捕获过于宽泛", "examples": ["catch (Exception e)过于宽泛", "异常被吞噬", "未记录异常信息"], "keywords": ["异常处理", "Exception", "try-catch", "日志"]}, {"id": "JAVA-P1-3", "level": "P1", "title": "魔法值使用", "description": "代码中存在魔法数字或魔法字符串", "examples": ["硬编码的数字常量", "硬编码的字符串", "未定义的配置值"], "keywords": ["魔法值", "硬编码", "常量"]}, {"id": "JAVA-P2-1", "level": "P2", "title": "代码重复", "description": "存在重复代码，违反DRY原则", "examples": ["相同逻辑在多处重复", "可以抽取公共方法", "重复的业务逻辑"], "keywords": ["代码重复", "DRY", "重构"]}, {"id": "JAVA-P2-2", "level": "P2", "title": "方法过长", "description": "方法行数过多，建议拆分", "examples": ["方法超过50行", "逻辑复杂的长方法", "职责不单一的方法"], "keywords": ["方法长度", "重构", "单一职责"]}, {"id": "JAVA-P2-3", "level": "P2", "title": "注释不足", "description": "缺少必要的JavaDoc注释", "examples": ["公共方法缺少注释", "复杂逻辑缺少说明", "类缺少描述"], "keywords": ["注释", "JavaDoc", "文档"]}]}