"""
基础配置类

按照第一阶段架构重构要求实现统一的配置管理
"""

import json
import os
from pathlib import Path
from typing import Dict, Any, Optional, List
from pydantic import BaseModel, Field


class ZebraConfig(BaseModel):
    """Zebra数据库代理配置"""
    app_key: str = "com.sankuai.shangou.ai.cr"
    ref_keys: Dict[str, str] = Field(default_factory=lambda: {"default": "sgai_cr_test"})
    pool_size: int = 100
    pool_timeout: int = 30
    pool_recycle: int = 3600

class DatabaseConfig(BaseModel):
    """数据库配置"""
    type: str = "mysql"
    host: str = "localhost"
    port: int = 3306
    database: str = "shangou_ai_cr"
    username: str = "root"
    password: str = ""
    pool_size: int = 10

    # Zebra配置
    zebra: ZebraConfig = Field(default_factory=ZebraConfig)
    default_connection: str = "standard"  # standard 或 zebra


class LLMConfig(BaseModel):
    """LLM配置"""
    provider: str = "openai"
    model_name: str = "gpt-4o-2024-11-20"
    api_key: str = "1837019944677314648"
    base_url: str = "https://aigc.sankuai.com/v1/openai/native/"
    temperature: float = 0.1
    max_tokens: int = 4000
    timeout: int = 30

    # 默认模型配置
    default_models: Dict[str, str] = Field(default_factory=lambda: {
        "chat_model": "gpt-4o-2024-11-20",
        "embedding_model": "text-embedding-miffy-002"
    })


class CacheConfig(BaseModel):
    """缓存配置"""
    redis_host: str = "localhost"
    redis_port: int = 6379
    redis_db: int = 0
    redis_password: Optional[str] = None
    ttl: int = 3600
    max_memory_cache_size: int = 1000


class MonitoringConfig(BaseModel):
    """监控配置"""
    enable_metrics: bool = True
    metrics_port: int = 8080
    log_level: str = "INFO"
    log_file: Optional[str] = None


class MCPServerConfig(BaseModel):
    """MCP服务器配置"""
    name: str
    url: str
    timeout: int = 30
    retry_count: int = 3
    enabled: bool = True


class MCPClientConfig(BaseModel):
    """MCP客户端配置"""
    sse_url: str = "mcphub平台的接入点URL"
    timeout: int = 30
    max_connections: int = 10
    retry_interval: int = 5


class MCPConfig(BaseModel):
    """MCP协议配置"""
    enabled: bool = True
    client: MCPClientConfig = Field(default_factory=MCPClientConfig)
    servers: List[MCPServerConfig] = Field(default_factory=list)
    
    # Agent到MCP服务的映射
    agent_service_mapping: Dict[str, str] = Field(default_factory=lambda: {
        "chunking_agent": "code_analysis_service",
        "qa_agent": "knowledge_service", 
        "rule_matching_agent": "rule_check_service",
        "llm_review_agent": "code_review_service",
        "evaluation_agent": "evaluation_service",
        "feedback_agent": "feedback_service"
    })


class ElasticsearchConfig(BaseModel):
    """Elasticsearch配置"""
    hosts: str = "http://localhost:1200"
    username: str = "elastic"
    password: str = "infini_rag_flow"

    # 公司ES8集群配置
    use_company_infra: bool = True
    cluster_name: str = "shangou_sgrag_default"
    app_key: str = "com.sankuai.shangou.ai.sgrag"
    access_key: str = "2A139D9E4D7563C536A5435E6AA2103F"
    discovery_url: str = "http://openapi.eagle.test.sankuai.com/openapi"
    port: int = 8080
    use_ssl: bool = False
    verify_certs: bool = False

class SSOConfig(BaseModel):
    """SSO配置"""
    login_uri: str = "/login"
    auth_uri: str = "/oauth2.0/access-token"
    user_info_uri: str = "/api/session/userinfo"
    logout_uri: str = "/oauth2.0/logout"

    # 环境特定配置
    env: Dict[str, Dict[str, str]] = Field(default_factory=lambda: {
        "test": {
            "client_id": "d7885a354f",
            "secret": "a5b0392c3cdf40f28e3c9a1742fae749",
            "sso_host": "https://ssosv.it.test.sankuai.com/sson",
            "api_host": "https://ssosv.it.test.sankuai.com/open"
        },
        "st": {
            "client_id": "d7885a354f",
            "secret": "a5b0392c3cdf40f28e3c9a1742fae749",
            "sso_host": "https://ssosv.it.st.sankuai.com/sson",
            "api_host": "https://ssosv.it.st.sankuai.com/open"
        },
        "prod": {
            "client_id": "d7885a354f",
            "secret": "a5b0392c3cdf40f28e3c9a1742fae749",
            "sso_host": "https://ssosv.sankuai.com/sson",
            "api_host": "https://ssosv.sankuai.com/open"
        }
    })

class APIConfig(BaseModel):
    """API配置"""
    host: str = "0.0.0.0"
    port: int = 9000
    debug: bool = False
    cors_enabled: bool = True
    rate_limit_enabled: bool = True
    rate_limit_per_minute: int = 100
    api_key_required: bool = False
    api_keys: List[str] = Field(default_factory=list)

    # HTTP端口配置
    http_port: int = 9380


class BaseConfig(BaseModel):
    """基础配置类"""
    app_name: str = "Shangou AI CR"
    version: str = "1.0.0"
    debug: bool = False
    secret_key: str = Field(default_factory=lambda: os.urandom(32).hex())
    
    # 数据库配置
    database: DatabaseConfig = Field(default_factory=DatabaseConfig)
    
    # LLM配置
    llm: LLMConfig = Field(default_factory=LLMConfig)
    
    # 缓存配置
    cache: CacheConfig = Field(default_factory=CacheConfig)
    
    # 监控配置
    monitoring: MonitoringConfig = Field(default_factory=MonitoringConfig)
    
    # MCP协议配置
    mcp: MCPConfig = Field(default_factory=MCPConfig)
    
    # API配置
    api: APIConfig = Field(default_factory=APIConfig)

    # Elasticsearch配置
    elasticsearch: ElasticsearchConfig = Field(default_factory=ElasticsearchConfig)

    # SSO配置
    sso: SSOConfig = Field(default_factory=SSOConfig)
    
    @classmethod
    def load_from_file(cls, config_path: str) -> "BaseConfig":
        """从文件加载配置"""
        config_file = Path(config_path)
        if not config_file.exists():
            return cls()
        
        with open(config_file, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
        
        return cls(**config_data)
    
    @classmethod
    def load_from_env(cls) -> "BaseConfig":
        """从环境变量加载配置"""
        config_data = {}
        
        # 基础配置
        if os.getenv("APP_NAME"):
            config_data["app_name"] = os.getenv("APP_NAME")
        if os.getenv("DEBUG"):
            config_data["debug"] = os.getenv("DEBUG").lower() == "true"
        
        # 数据库配置
        db_config = {}
        if os.getenv("DB_HOST"):
            db_config["host"] = os.getenv("DB_HOST")
        if os.getenv("DB_PORT"):
            db_config["port"] = int(os.getenv("DB_PORT"))
        if os.getenv("DB_NAME"):
            db_config["database"] = os.getenv("DB_NAME")
        if os.getenv("DB_USER"):
            db_config["username"] = os.getenv("DB_USER")
        if os.getenv("DB_PASSWORD"):
            db_config["password"] = os.getenv("DB_PASSWORD")
        
        if db_config:
            config_data["database"] = db_config
        
        # LLM配置
        llm_config = {}
        if os.getenv("LLM_API_KEY"):
            llm_config["api_key"] = os.getenv("LLM_API_KEY")
        if os.getenv("LLM_MODEL"):
            llm_config["model_name"] = os.getenv("LLM_MODEL")
        if os.getenv("LLM_BASE_URL"):
            llm_config["base_url"] = os.getenv("LLM_BASE_URL")
        
        if llm_config:
            config_data["llm"] = llm_config
        
        # MCP配置
        mcp_config = {}
        if os.getenv("MCP_ENABLED"):
            mcp_config["enabled"] = os.getenv("MCP_ENABLED").lower() == "true"
        if os.getenv("MCP_SSE_URL"):
            mcp_config["client"] = {"sse_url": os.getenv("MCP_SSE_URL")}
        
        if mcp_config:
            config_data["mcp"] = mcp_config
        
        # API配置
        api_config = {}
        if os.getenv("API_HOST"):
            api_config["host"] = os.getenv("API_HOST")
        if os.getenv("API_PORT"):
            api_config["port"] = int(os.getenv("API_PORT"))
        if os.getenv("API_DEBUG"):
            api_config["debug"] = os.getenv("API_DEBUG").lower() == "true"
        if os.getenv("API_KEY_REQUIRED"):
            api_config["api_key_required"] = os.getenv("API_KEY_REQUIRED").lower() == "true"
        if os.getenv("API_KEYS"):
            api_config["api_keys"] = os.getenv("API_KEYS").split(",")
        
        if api_config:
            config_data["api"] = api_config

        # Elasticsearch配置
        es_config = {}
        if os.getenv("ES_HOSTS"):
            es_config["hosts"] = os.getenv("ES_HOSTS")
        if os.getenv("ES_USERNAME"):
            es_config["username"] = os.getenv("ES_USERNAME")
        if os.getenv("ES_PASSWORD"):
            es_config["password"] = os.getenv("ES_PASSWORD")
        if os.getenv("ES_USE_COMPANY_INFRA"):
            es_config["use_company_infra"] = os.getenv("ES_USE_COMPANY_INFRA").lower() == "true"

        if es_config:
            config_data["elasticsearch"] = es_config

        # 环境配置
        if os.getenv("INF_BOM_ENV"):
            config_data["environment"] = os.getenv("INF_BOM_ENV")

        return cls(**config_data)
    
    def save_to_file(self, config_path: str):
        """保存配置到文件"""
        config_file = Path(config_path)
        config_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(self.dict(), f, indent=2, ensure_ascii=False)
    
    def get_service_config(self, service_name: str) -> Dict[str, Any]:
        """获取特定服务的配置"""
        service_configs = {
            "database": self.database.dict(),
            "llm": self.llm.dict(),
            "cache": self.cache.dict(),
            "monitoring": self.monitoring.dict(),
            "mcp": self.mcp.dict(),
            "api": self.api.dict()
        }
        return service_configs.get(service_name, {})
