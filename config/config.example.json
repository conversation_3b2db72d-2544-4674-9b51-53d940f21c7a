{"app_name": "Shangou AI CR", "version": "1.0.0", "debug": false, "secret_key": "your-secret-key-here", "database": {"type": "mysql", "host": "localhost", "port": 3306, "database": "shangou_ai_cr", "username": "root", "password": "your-password", "pool_size": 10}, "llm": {"provider": "openai", "model_name": "gpt-3.5-turbo", "api_key": "your-openai-api-key", "base_url": null, "temperature": 0.1, "max_tokens": 4000, "timeout": 30}, "cache": {"redis_host": "localhost", "redis_port": 6379, "redis_db": 0, "redis_password": null, "ttl": 3600, "max_memory_cache_size": 1000}, "monitoring": {"enable_metrics": true, "metrics_port": 8080, "log_level": "INFO", "log_file": null}, "mcp": {"enabled": true, "client": {"sse_url": "mcphub平台的接入点URL", "timeout": 30, "max_connections": 10, "retry_interval": 5}, "servers": [], "agent_service_mapping": {"chunking_agent": "code_analysis_service", "qa_agent": "knowledge_service", "rule_matching_agent": "rule_check_service", "llm_review_agent": "code_review_service", "evaluation_agent": "evaluation_service", "feedback_agent": "feedback_service"}}, "api": {"host": "0.0.0.0", "port": 9000, "debug": false, "cors_enabled": true, "rate_limit_enabled": true, "rate_limit_per_minute": 100, "api_key_required": false, "api_keys": []}}