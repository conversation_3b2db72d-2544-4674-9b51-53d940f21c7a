"""
统一配置加载器
支持新旧配置格式的加载
"""

import os
import yaml
import json
from pathlib import Path
from typing import Dict, Any, Optional

from .base_config import BaseConfig


class ConfigLoader:
    """统一配置加载器"""
    
    def __init__(self, config_dir: Optional[str] = None):
        if config_dir:
            self.config_dir = Path(config_dir)
        else:
            # 默认使用当前文件所在目录
            self.config_dir = Path(__file__).parent
    
    def load_base_config(self, config_file: Optional[str] = None) -> BaseConfig:
        """加载基础配置"""
        if config_file:
            config_path = self.config_dir / config_file
            if config_path.exists():
                return BaseConfig.load_from_file(str(config_path))
        
        # 从环境变量加载
        return BaseConfig.load_from_env()
    
    def load_legacy_yaml(self, yaml_file: str) -> Dict[str, Any]:
        """加载旧的YAML配置文件"""
        yaml_path = self.config_dir / "legacy" / yaml_file
        
        if not yaml_path.exists():
            raise FileNotFoundError(f"配置文件不存在: {yaml_path}")
        
        with open(yaml_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    
    def load_cr_rules(self, rule_file: str) -> Dict[str, Any]:
        """加载CR规则配置"""
        rule_path = self.config_dir / "cr_rules" / rule_file
        
        if not rule_path.exists():
            raise FileNotFoundError(f"规则文件不存在: {rule_path}")
        
        with open(rule_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def get_database_config(self) -> Dict[str, Any]:
        """获取数据库配置（兼容新旧格式）"""
        try:
            # 优先使用新配置
            base_config = self.load_base_config()
            return base_config.database.dict()
        except Exception:
            # 降级到旧配置
            try:
                return self.load_legacy_yaml("database_conf.yaml")
            except Exception:
                # 使用默认配置
                return BaseConfig().database.dict()
    
    def get_service_config(self, service_name: str) -> Dict[str, Any]:
        """获取服务配置"""
        try:
            base_config = self.load_base_config()
            return base_config.get_service_config(service_name)
        except Exception:
            # 降级到旧配置
            try:
                service_config = self.load_legacy_yaml("service_conf.yaml")
                return service_config.get(service_name, {})
            except Exception:
                return {}


# 全局配置加载器实例
_config_loader = None

def get_config_loader() -> ConfigLoader:
    """获取全局配置加载器"""
    global _config_loader
    if _config_loader is None:
        _config_loader = ConfigLoader()
    return _config_loader


def load_legacy_config(config_file: str) -> Dict[str, Any]:
    """加载旧配置文件（便捷函数）"""
    loader = get_config_loader()
    return loader.load_legacy_yaml(config_file)


def load_app_config() -> BaseConfig:
    """加载应用配置（便捷函数）"""
    loader = get_config_loader()
    return loader.load_base_config()
