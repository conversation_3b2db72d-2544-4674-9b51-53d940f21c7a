# 配置目录结构说明

## 目录结构

```
config/
├── __init__.py                 # 配置模块初始化
├── base_config.py             # 新架构配置类（推荐使用）
├── config_loader.py           # 统一配置加载器
├── config.example.json        # 配置示例
├── cr_rules/                  # CR规则配置
│   ├── java_enterprise.json
│   ├── python_security.json
│   └── python_web_backend.json
├── legacy/                    # 旧YAML配置文件（兼容性保留）
│   ├── database_conf.yaml
│   ├── service_conf.yaml
│   ├── km_conf.yaml
│   └── org_conf.yaml
├── services/                  # 服务配置
│   └── envs/                  # 环境配置
│       ├── base.yaml
│       └── test.yaml
├── security/                  # 证书和密钥
│   ├── private.pem
│   └── public.pem
└── third_party/              # 第三方服务配置
    ├── zebra/                # Zebra代理配置
    └── squirrel/             # Squirrel配置
```

## 使用建议

### 新项目
推荐使用 `base_config.py` 中的配置类：

```python
from config.base_config import BaseConfig

# 从环境变量加载
config = BaseConfig.load_from_env()

# 从文件加载
config = BaseConfig.load_from_file("config/app_config.json")
```

### 旧项目兼容
如需使用旧的YAML配置，可以通过 `config_loader.py` 加载：

```python
from config.config_loader import load_legacy_config

config = load_legacy_config("legacy/database_conf.yaml")
```

## 迁移说明

1. **conf/** 目录已合并到 **config/** 目录
2. 旧的YAML配置文件保留在 **legacy/** 子目录中
3. 新架构推荐使用Python配置类
4. 第三方服务配置统一放在 **third_party/** 目录

## 注意事项

- 更新代码中的配置文件路径引用
- 逐步迁移到新的配置类
- 保持向后兼容性
