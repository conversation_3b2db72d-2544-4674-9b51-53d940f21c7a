#!/usr/bin/env bash
#!/usr/bin/env bash
URL=$1
MAX=10

# 校验URL格式
if [[ ! $URL =~ ^http:// ]]; then
    URL="http://$URL"
fi

echo "开始检查服务是否就绪: $URL"
for ((i=1; i<=$MAX; i++))
do
    # 首先尝试获取状态码
    http_code=$(curl -o /dev/null -s -w %{http_code} $URL --connect-timeout 5)
    
    if [ ${http_code} == 200 ]
    then
        echo "第 $i 次检查 URL $URL 返回 $http_code, 成功!"
        exit 0
    elif [ ${http_code} == 000 ]; then
        # 连接可能还未建立
        echo "第 $i/$MAX 次检查: 端口尚未开放或服务未启动，等待中..."
    else
        echo "第 $i/$MAX 次检查 URL $URL 返回 $http_code, 等待服务就绪..."
    fi
    sleep 3
done

echo "服务检查失败: 超过最大重试次数($MAX)"
exit 1