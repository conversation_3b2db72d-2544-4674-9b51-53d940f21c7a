#!/usr/bin/env bash
set -e

# 创建虚拟环境
#virtualenv venv

# 激活与build.sh相同的虚拟环境
source venv/bin/activate

# 确保Python能找到site-packages目录
#SITE_PACKAGES=$(python -c "import site; print(site.getsitepackages()[0])" 2>/dev/null || echo "venv/lib/python3.10/site-packages")
#export PYTHONPATH=$(pwd):$SITE_PACKAGES
echo $PYTHONPATH

# 设置tiktoken缓存目录为项目下的.tiktoken目录（使用绝对路径）
export TIKTOKEN_CACHE_DIR=$(pwd)/.tiktoken
# 转换为绝对路径
export TIKTOKEN_CACHE_DIR=$(realpath "$TIKTOKEN_CACHE_DIR")
export TIKTOKEN_SKIP_BPE_DOWNLOAD=1
export HF_ENDPOINT=https://hf-mirror.com
echo "tiktoken缓存目录(绝对路径): $TIKTOKEN_CACHE_DIR"

# 验证tiktoken文件是否存在
if [ -f "$TIKTOKEN_CACHE_DIR/cl100k_base.tiktoken" ]; then
    echo "✅ 已找到tiktoken编码文件: $TIKTOKEN_CACHE_DIR/cl100k_base.tiktoken"
    # 显示文件大小和权限
    ls -la "$TIKTOKEN_CACHE_DIR/cl100k_base.tiktoken"
    # 文件头信息
    echo "文件头信息 (前32字节):"
    hexdump -C "$TIKTOKEN_CACHE_DIR/cl100k_base.tiktoken" | head -n 2
    # 修改文件权限确保可读
    chmod 644 "$TIKTOKEN_CACHE_DIR/cl100k_base.tiktoken" 2>/dev/null || true
    echo "✅ 已设置tiktoken文件权限"
    # 再次显示权限
    ls -la "$TIKTOKEN_CACHE_DIR/cl100k_base.tiktoken"
else
    echo "⚠️ 警告: 未找到tiktoken编码文件，服务可能无法正常启动"
    echo "预期文件路径: $TIKTOKEN_CACHE_DIR/cl100k_base.tiktoken"
fi

# 下载deepdoc资源文件（仅当文件不存在时）
if [ ! -f "deepdoc_res.tar.gz" ]; then
    echo "deepdoc_res.tar.gz 不存在，正在下载..."
    curl -o deepdoc_res.tar.gz https://msstest.sankuai.com/sg-ug-api-test-sdk-agent/deepdoc_res.tar.gz
else
    echo "✅ deepdoc_res.tar.gz 已存在，跳过下载"
fi

# 检查deepdoc资源目录中是否已有必要文件
if [ ! -f "rag/res/deepdoc/det.onnx" ] || [ ! -f "rag/res/deepdoc/rec.onnx" ] || [ ! -f "rag/res/deepdoc/ocr.res" ]; then
    echo "deepdoc资源文件不完整，正在解压..."
    # 确保目标目录存在
    mkdir -p rag/res/deepdoc
    # 解压资源文件到目标目录
    tar -xf deepdoc_res.tar.gz -C rag/res/deepdoc
    echo "✅ deepdoc资源文件解压完成"
    # 显示解压后的文件
    ls -la rag/res/deepdoc/
else
    echo "✅ deepdoc资源文件已存在，跳过解压"
    # 显示现有文件
    ls -la rag/res/deepdoc/
fi

# 下载nltk数据（仅当文件不存在时）
if [ ! -f "nltk_data.tar.gz" ]; then
    echo "nltk_data.tar.gz 不存在，正在下载..."
    curl -o nltk_data.tar.gz https://msstest.sankuai.com/sg-ug-api-test-sdk-agent/nltk_data.tar.gz
else
    echo "✅ nltk_data.tar.gz 已存在，跳过下载"
fi

# 检查nltk_data目录中是否已有数据
if [ ! -d "venv/nltk_data/tokenizers" ] || [ ! -d "venv/nltk_data/corpora" ]; then
    echo "nltk数据不完整，正在解压..."
    # 解压nltk数据
    mkdir -p venv/nltk_data
    cd venv/nltk_data
    tar -xf ../../nltk_data.tar.gz
    cd ../../
    echo "✅ nltk数据解压完成"
else
    echo "✅ nltk数据目录已存在且包含必要文件，跳过解压"
fi

# 确保日志目录存在
LOG_DIR="/opt/logs"
if [ ! -d "$LOG_DIR" ]; then
    echo "日志目录不存在，尝试创建: $LOG_DIR"
    mkdir -p "$LOG_DIR" 2>/dev/null || { echo "无法创建日志目录，将使用当前目录"; LOG_DIR="./logs"; mkdir -p "$LOG_DIR"; }
fi

LOG_FILE="$LOG_DIR/com.sankuai.shangou.ai.sgrag.log"
echo "日志将输出到: $LOG_FILE"

# 只将输出写入到文件，不显示在屏幕上
bash docker/launch_backend_service.sh >> "$LOG_FILE" 2>&1