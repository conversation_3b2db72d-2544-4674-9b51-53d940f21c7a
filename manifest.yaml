build:
  os: centos7
  tools:
    python_with_virtualenv_and_buildout: 3.10
    # == pyicu 编译需要 ==
    icu: 
    pkgconfig:
    libicu-devel:
    gcc:
    gcc-c++:
    make:
    # == pyicu 编译需要 ==
    mkdir: /opt/meituan/com.sankuai.shangou.ai.cr
  run:
    workDir: ./
    cmd: 
      - sh build.sh
  target:
    distDir: ./
    files:
      - ./

autodeploy:
  hulkos: centos7
  tools:
    python_with_virtualenv_and_buildout: 3.10
    # 以下依赖因为会安装到/usr/lib64下面，需要再运行机器上安装
    mesa-libGL:
    libXext:
    libSM:
    libXrender:
    unixODBC: 
    unixODBC-devel:
  targetDir: /opt/meituan/com.sankuai.shangou.ai.cr
  run: sh install.sh
  check: sh check.sh `hostname`:9380
  checkRetry: 10
  checkInterval: 3s