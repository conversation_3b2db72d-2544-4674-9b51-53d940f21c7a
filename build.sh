#!/usr/bin/env bash
set -e
build_dir=$PWD
# 因为venv的依赖路径是写死的，此处需要到部署目录进行安装
target_dir=/opt/meituan/com.sankuai.shangou.ai.cr
cp pyproject.toml $target_dir
cp requirements.txt $target_dir
cp build.sh $target_dir
cd $target_dir
# 激活虚拟环境
virtualenv venv
source venv/bin/activate

# 确保关键依赖被安装
pip install --index-url http://pypi.sankuai.com/simple/ -r requirements.txt 
# 下载并安装mini-racer wheel包
#echo "从S3下载mini-racer wheel包..."
#MINI_RACER_URL="https://msstest.sankuai.com/sg-ug-api-test-sdk-agent/mini_racer-0.12.4-py3-none-manylinux_2_31_x86_64.whl"
#MINI_RACER_WHEEL="/tmp/mini_racer-0.12.4-py3-none-manylinux_2_31_x86_64.whl"
#
## 使用curl下载wheel包
#if curl -s -o "$MINI_RACER_WHEEL" "$MINI_RACER_URL"; then
#    echo "mini-racer wheel包下载成功，正在安装..."
#    if pip install --no-index "$MINI_RACER_WHEEL"; then
#        echo "✅ mini-racer wheel包安装成功！"
#    else
#        echo "⚠️ mini-racer wheel包安装失败，可能与系统不兼容"
#    fi
#else
#    echo "❌ mini-racer wheel包下载失败"
#fi

mv venv $build_dir