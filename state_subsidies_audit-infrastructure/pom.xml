<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.sankuai.statesubsidies.audit</groupId>
        <artifactId>state_subsidies_audit</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>state_subsidies_audit-infrastructure</artifactId>
    <version>${revision}</version>
    <packaging>jar</packaging>
    <name>state_subsidies_audit-infrastructure</name>

    <dependencies>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-zebra</artifactId>
        </dependency>
		<dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-squirrel</artifactId>
		</dependency>
        <dependency>
            <groupId>com.dianping.squirrel</groupId>
            <artifactId>squirrel-client</artifactId>
        </dependency>
		<dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-thrift</artifactId>
		</dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-web-base</artifactId>
        </dependency>
		<dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-web-toc</artifactId>
		</dependency>
        <!--核心依赖：数据源相关 -->
        <dependency>
            <groupId>com.dianping.zebra</groupId>
            <artifactId>zebra-api</artifactId>
        </dependency>
        <!-- cat 监控相关，必须引入，否则没有监控 -->
        <dependency>
            <groupId>com.dianping.zebra</groupId>
            <artifactId>zebra-ds-monitor-client</artifactId>
        </dependency>
        <!--可选依赖：mybatis相关，详见 zebra release note，建议大于等于 0.3.2-->
        <dependency>
            <groupId>com.dianping.zebra</groupId>
            <artifactId>zebra-dao</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.component</groupId>
            <artifactId>mdp-mybatis-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.shangou.sgmerchant</groupId>
            <artifactId>gov-subsidy-client</artifactId>
            <version>1.0.18-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.meituan.xframe</groupId>
            <artifactId>thrift-xframe-boot-starter</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.mybatis.generator</groupId>
                <artifactId>mybatis-generator-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>com.meituan.mdp.maven.plugins</groupId>
                <artifactId>mdp-mybatis-generator-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

</project>
