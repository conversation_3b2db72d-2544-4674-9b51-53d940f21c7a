package com.sankuai.statesubsidies.audit.state_subsidies_audit.infrastructure.proxy.config;


import com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy;
import com.sankuai.sgmerchant.govsubsidy.client.thrift.service.SgCouponOrderAuditQueryThriftService;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 地图API配置类
 */
@Configuration
public class CouponOrderAuditQueryServiceConfig {
    
    @Bean
    public SgCouponOrderAuditQueryThriftService sgCouponOrderAuditQueryService() throws Exception {
        ThriftClientProxy proxy = new ThriftClientProxy();
        proxy.setRemoteAppkey("com.sankuai.sgmerchant.govsubsidy");
        proxy.setFilterByServiceName(true);
        proxy.setRemoteUniProto(true);
        proxy.setTimeout(5000);
        proxy.setServiceInterface(SgCouponOrderAuditQueryThriftService.class);
        
        proxy.afterPropertiesSet();
        return (SgCouponOrderAuditQueryThriftService) proxy.getObject();
    }
    
} 