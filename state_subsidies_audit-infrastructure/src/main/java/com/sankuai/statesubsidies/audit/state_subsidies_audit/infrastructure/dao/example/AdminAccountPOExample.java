package com.sankuai.statesubsidies.audit.state_subsidies_audit.infrastructure.dao.example;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class AdminAccountPOExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    protected Integer offset;

    protected Integer rows;

    public AdminAccountPOExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
        rows = null;
        offset = null;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return this.offset;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    public Integer getRows() {
        return this.rows;
    }

    public AdminAccountPOExample limit(Integer rows) {
        this.rows = rows;
        return this;
    }

    public AdminAccountPOExample limit(Integer offset, Integer rows) {
        this.offset = offset;
        this.rows = rows;
        return this;
    }

    public AdminAccountPOExample page(Integer page, Integer pageSize) {
        this.offset = page * pageSize;
        this.rows = pageSize;
        return this;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andUsernameIsNull() {
            addCriterion("username is null");
            return (Criteria) this;
        }

        public Criteria andUsernameIsNotNull() {
            addCriterion("username is not null");
            return (Criteria) this;
        }

        public Criteria andUsernameEqualTo(String value) {
            addCriterion("username =", value, "username");
            return (Criteria) this;
        }

        public Criteria andUsernameNotEqualTo(String value) {
            addCriterion("username <>", value, "username");
            return (Criteria) this;
        }

        public Criteria andUsernameGreaterThan(String value) {
            addCriterion("username >", value, "username");
            return (Criteria) this;
        }

        public Criteria andUsernameGreaterThanOrEqualTo(String value) {
            addCriterion("username >=", value, "username");
            return (Criteria) this;
        }

        public Criteria andUsernameLessThan(String value) {
            addCriterion("username <", value, "username");
            return (Criteria) this;
        }

        public Criteria andUsernameLessThanOrEqualTo(String value) {
            addCriterion("username <=", value, "username");
            return (Criteria) this;
        }

        public Criteria andUsernameLike(String value) {
            addCriterion("username like", value, "username");
            return (Criteria) this;
        }

        public Criteria andUsernameNotLike(String value) {
            addCriterion("username not like", value, "username");
            return (Criteria) this;
        }

        public Criteria andUsernameIn(List<String> values) {
            addCriterion("username in", values, "username");
            return (Criteria) this;
        }

        public Criteria andUsernameNotIn(List<String> values) {
            addCriterion("username not in", values, "username");
            return (Criteria) this;
        }

        public Criteria andUsernameBetween(String value1, String value2) {
            addCriterion("username between", value1, value2, "username");
            return (Criteria) this;
        }

        public Criteria andUsernameNotBetween(String value1, String value2) {
            addCriterion("username not between", value1, value2, "username");
            return (Criteria) this;
        }

        public Criteria andPhoneNumberIsNull() {
            addCriterion("phone_number is null");
            return (Criteria) this;
        }

        public Criteria andPhoneNumberIsNotNull() {
            addCriterion("phone_number is not null");
            return (Criteria) this;
        }

        public Criteria andPhoneNumberEqualTo(String value) {
            addCriterion("phone_number =", value, "phoneNumber");
            return (Criteria) this;
        }

        public Criteria andPhoneNumberNotEqualTo(String value) {
            addCriterion("phone_number <>", value, "phoneNumber");
            return (Criteria) this;
        }

        public Criteria andPhoneNumberGreaterThan(String value) {
            addCriterion("phone_number >", value, "phoneNumber");
            return (Criteria) this;
        }

        public Criteria andPhoneNumberGreaterThanOrEqualTo(String value) {
            addCriterion("phone_number >=", value, "phoneNumber");
            return (Criteria) this;
        }

        public Criteria andPhoneNumberLessThan(String value) {
            addCriterion("phone_number <", value, "phoneNumber");
            return (Criteria) this;
        }

        public Criteria andPhoneNumberLessThanOrEqualTo(String value) {
            addCriterion("phone_number <=", value, "phoneNumber");
            return (Criteria) this;
        }

        public Criteria andPhoneNumberLike(String value) {
            addCriterion("phone_number like", value, "phoneNumber");
            return (Criteria) this;
        }

        public Criteria andPhoneNumberNotLike(String value) {
            addCriterion("phone_number not like", value, "phoneNumber");
            return (Criteria) this;
        }

        public Criteria andPhoneNumberIn(List<String> values) {
            addCriterion("phone_number in", values, "phoneNumber");
            return (Criteria) this;
        }

        public Criteria andPhoneNumberNotIn(List<String> values) {
            addCriterion("phone_number not in", values, "phoneNumber");
            return (Criteria) this;
        }

        public Criteria andPhoneNumberBetween(String value1, String value2) {
            addCriterion("phone_number between", value1, value2, "phoneNumber");
            return (Criteria) this;
        }

        public Criteria andPhoneNumberNotBetween(String value1, String value2) {
            addCriterion("phone_number not between", value1, value2, "phoneNumber");
            return (Criteria) this;
        }

        public Criteria andNeedAuthorizationCodeIsNull() {
            addCriterion("need_authorization_code is null");
            return (Criteria) this;
        }

        public Criteria andNeedAuthorizationCodeIsNotNull() {
            addCriterion("need_authorization_code is not null");
            return (Criteria) this;
        }

        public Criteria andNeedAuthorizationCodeEqualTo(Boolean value) {
            addCriterion("need_authorization_code =", value, "needAuthorizationCode");
            return (Criteria) this;
        }

        public Criteria andNeedAuthorizationCodeNotEqualTo(Boolean value) {
            addCriterion("need_authorization_code <>", value, "needAuthorizationCode");
            return (Criteria) this;
        }

        public Criteria andNeedAuthorizationCodeGreaterThan(Boolean value) {
            addCriterion("need_authorization_code >", value, "needAuthorizationCode");
            return (Criteria) this;
        }

        public Criteria andNeedAuthorizationCodeGreaterThanOrEqualTo(Boolean value) {
            addCriterion("need_authorization_code >=", value, "needAuthorizationCode");
            return (Criteria) this;
        }

        public Criteria andNeedAuthorizationCodeLessThan(Boolean value) {
            addCriterion("need_authorization_code <", value, "needAuthorizationCode");
            return (Criteria) this;
        }

        public Criteria andNeedAuthorizationCodeLessThanOrEqualTo(Boolean value) {
            addCriterion("need_authorization_code <=", value, "needAuthorizationCode");
            return (Criteria) this;
        }

        public Criteria andNeedAuthorizationCodeIn(List<Boolean> values) {
            addCriterion("need_authorization_code in", values, "needAuthorizationCode");
            return (Criteria) this;
        }

        public Criteria andNeedAuthorizationCodeNotIn(List<Boolean> values) {
            addCriterion("need_authorization_code not in", values, "needAuthorizationCode");
            return (Criteria) this;
        }

        public Criteria andNeedAuthorizationCodeBetween(Boolean value1, Boolean value2) {
            addCriterion("need_authorization_code between", value1, value2, "needAuthorizationCode");
            return (Criteria) this;
        }

        public Criteria andNeedAuthorizationCodeNotBetween(Boolean value1, Boolean value2) {
            addCriterion("need_authorization_code not between", value1, value2, "needAuthorizationCode");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Boolean value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Boolean value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Boolean value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Boolean value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Boolean value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Boolean value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Boolean> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Boolean> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Boolean value1, Boolean value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Boolean value1, Boolean value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andLoginCountIsNull() {
            addCriterion("login_count is null");
            return (Criteria) this;
        }

        public Criteria andLoginCountIsNotNull() {
            addCriterion("login_count is not null");
            return (Criteria) this;
        }

        public Criteria andLoginCountEqualTo(Integer value) {
            addCriterion("login_count =", value, "loginCount");
            return (Criteria) this;
        }

        public Criteria andLoginCountNotEqualTo(Integer value) {
            addCriterion("login_count <>", value, "loginCount");
            return (Criteria) this;
        }

        public Criteria andLoginCountGreaterThan(Integer value) {
            addCriterion("login_count >", value, "loginCount");
            return (Criteria) this;
        }

        public Criteria andLoginCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("login_count >=", value, "loginCount");
            return (Criteria) this;
        }

        public Criteria andLoginCountLessThan(Integer value) {
            addCriterion("login_count <", value, "loginCount");
            return (Criteria) this;
        }

        public Criteria andLoginCountLessThanOrEqualTo(Integer value) {
            addCriterion("login_count <=", value, "loginCount");
            return (Criteria) this;
        }

        public Criteria andLoginCountIn(List<Integer> values) {
            addCriterion("login_count in", values, "loginCount");
            return (Criteria) this;
        }

        public Criteria andLoginCountNotIn(List<Integer> values) {
            addCriterion("login_count not in", values, "loginCount");
            return (Criteria) this;
        }

        public Criteria andLoginCountBetween(Integer value1, Integer value2) {
            addCriterion("login_count between", value1, value2, "loginCount");
            return (Criteria) this;
        }

        public Criteria andLoginCountNotBetween(Integer value1, Integer value2) {
            addCriterion("login_count not between", value1, value2, "loginCount");
            return (Criteria) this;
        }

        public Criteria andLastLoginTimeIsNull() {
            addCriterion("last_login_time is null");
            return (Criteria) this;
        }

        public Criteria andLastLoginTimeIsNotNull() {
            addCriterion("last_login_time is not null");
            return (Criteria) this;
        }

        public Criteria andLastLoginTimeEqualTo(LocalDateTime value) {
            addCriterion("last_login_time =", value, "lastLoginTime");
            return (Criteria) this;
        }

        public Criteria andLastLoginTimeNotEqualTo(LocalDateTime value) {
            addCriterion("last_login_time <>", value, "lastLoginTime");
            return (Criteria) this;
        }

        public Criteria andLastLoginTimeGreaterThan(LocalDateTime value) {
            addCriterion("last_login_time >", value, "lastLoginTime");
            return (Criteria) this;
        }

        public Criteria andLastLoginTimeGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("last_login_time >=", value, "lastLoginTime");
            return (Criteria) this;
        }

        public Criteria andLastLoginTimeLessThan(LocalDateTime value) {
            addCriterion("last_login_time <", value, "lastLoginTime");
            return (Criteria) this;
        }

        public Criteria andLastLoginTimeLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("last_login_time <=", value, "lastLoginTime");
            return (Criteria) this;
        }

        public Criteria andLastLoginTimeIn(List<LocalDateTime> values) {
            addCriterion("last_login_time in", values, "lastLoginTime");
            return (Criteria) this;
        }

        public Criteria andLastLoginTimeNotIn(List<LocalDateTime> values) {
            addCriterion("last_login_time not in", values, "lastLoginTime");
            return (Criteria) this;
        }

        public Criteria andLastLoginTimeBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("last_login_time between", value1, value2, "lastLoginTime");
            return (Criteria) this;
        }

        public Criteria andLastLoginTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("last_login_time not between", value1, value2, "lastLoginTime");
            return (Criteria) this;
        }

        public Criteria andCreatorPhoneIsNull() {
            addCriterion("creator_phone is null");
            return (Criteria) this;
        }

        public Criteria andCreatorPhoneIsNotNull() {
            addCriterion("creator_phone is not null");
            return (Criteria) this;
        }

        public Criteria andCreatorPhoneEqualTo(String value) {
            addCriterion("creator_phone =", value, "creatorPhone");
            return (Criteria) this;
        }

        public Criteria andCreatorPhoneNotEqualTo(String value) {
            addCriterion("creator_phone <>", value, "creatorPhone");
            return (Criteria) this;
        }

        public Criteria andCreatorPhoneGreaterThan(String value) {
            addCriterion("creator_phone >", value, "creatorPhone");
            return (Criteria) this;
        }

        public Criteria andCreatorPhoneGreaterThanOrEqualTo(String value) {
            addCriterion("creator_phone >=", value, "creatorPhone");
            return (Criteria) this;
        }

        public Criteria andCreatorPhoneLessThan(String value) {
            addCriterion("creator_phone <", value, "creatorPhone");
            return (Criteria) this;
        }

        public Criteria andCreatorPhoneLessThanOrEqualTo(String value) {
            addCriterion("creator_phone <=", value, "creatorPhone");
            return (Criteria) this;
        }

        public Criteria andCreatorPhoneLike(String value) {
            addCriterion("creator_phone like", value, "creatorPhone");
            return (Criteria) this;
        }

        public Criteria andCreatorPhoneNotLike(String value) {
            addCriterion("creator_phone not like", value, "creatorPhone");
            return (Criteria) this;
        }

        public Criteria andCreatorPhoneIn(List<String> values) {
            addCriterion("creator_phone in", values, "creatorPhone");
            return (Criteria) this;
        }

        public Criteria andCreatorPhoneNotIn(List<String> values) {
            addCriterion("creator_phone not in", values, "creatorPhone");
            return (Criteria) this;
        }

        public Criteria andCreatorPhoneBetween(String value1, String value2) {
            addCriterion("creator_phone between", value1, value2, "creatorPhone");
            return (Criteria) this;
        }

        public Criteria andCreatorPhoneNotBetween(String value1, String value2) {
            addCriterion("creator_phone not between", value1, value2, "creatorPhone");
            return (Criteria) this;
        }

        public Criteria andCreatorMisIsNull() {
            addCriterion("creator_mis is null");
            return (Criteria) this;
        }

        public Criteria andCreatorMisIsNotNull() {
            addCriterion("creator_mis is not null");
            return (Criteria) this;
        }

        public Criteria andCreatorMisEqualTo(String value) {
            addCriterion("creator_mis =", value, "creatorMis");
            return (Criteria) this;
        }

        public Criteria andCreatorMisNotEqualTo(String value) {
            addCriterion("creator_mis <>", value, "creatorMis");
            return (Criteria) this;
        }

        public Criteria andCreatorMisGreaterThan(String value) {
            addCriterion("creator_mis >", value, "creatorMis");
            return (Criteria) this;
        }

        public Criteria andCreatorMisGreaterThanOrEqualTo(String value) {
            addCriterion("creator_mis >=", value, "creatorMis");
            return (Criteria) this;
        }

        public Criteria andCreatorMisLessThan(String value) {
            addCriterion("creator_mis <", value, "creatorMis");
            return (Criteria) this;
        }

        public Criteria andCreatorMisLessThanOrEqualTo(String value) {
            addCriterion("creator_mis <=", value, "creatorMis");
            return (Criteria) this;
        }

        public Criteria andCreatorMisLike(String value) {
            addCriterion("creator_mis like", value, "creatorMis");
            return (Criteria) this;
        }

        public Criteria andCreatorMisNotLike(String value) {
            addCriterion("creator_mis not like", value, "creatorMis");
            return (Criteria) this;
        }

        public Criteria andCreatorMisIn(List<String> values) {
            addCriterion("creator_mis in", values, "creatorMis");
            return (Criteria) this;
        }

        public Criteria andCreatorMisNotIn(List<String> values) {
            addCriterion("creator_mis not in", values, "creatorMis");
            return (Criteria) this;
        }

        public Criteria andCreatorMisBetween(String value1, String value2) {
            addCriterion("creator_mis between", value1, value2, "creatorMis");
            return (Criteria) this;
        }

        public Criteria andCreatorMisNotBetween(String value1, String value2) {
            addCriterion("creator_mis not between", value1, value2, "creatorMis");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}