package com.sankuai.statesubsidies.audit.state_subsidies_audit.infrastructure.dao.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisMixedBaseMapper;
import com.sankuai.statesubsidies.audit.state_subsidies_audit.infrastructure.dao.example.AdminAccountDistrictPOExample;
import com.sankuai.statesubsidies.audit.state_subsidies_audit.infrastructure.dao.po.AdminAccountDistrictPO;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface AdminAccountDistrictPOMapper extends MybatisMixedBaseMapper<AdminAccountDistrictPO, AdminAccountDistrictPOExample, Long> {
    int deleteByPrimaryKey(Long id);

    int insert(AdminAccountDistrictPO record);

    AdminAccountDistrictPO selectByPrimaryKey(Long id);

    int updateByPrimaryKey(AdminAccountDistrictPO record);

    int batchInsert(@Param("list") List<AdminAccountDistrictPO> list);
}