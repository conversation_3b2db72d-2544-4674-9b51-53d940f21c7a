package com.sankuai.statesubsidies.audit.state_subsidies_audit.infrastructure.dao.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisMixedBaseMapper;
import com.sankuai.statesubsidies.audit.state_subsidies_audit.infrastructure.dao.example.PermissionsPOExample;
import com.sankuai.statesubsidies.audit.state_subsidies_audit.infrastructure.dao.po.PermissionsPO;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface PermissionsPOMapper extends MybatisMixedBaseMapper<PermissionsPO, PermissionsPOExample, Long> {
    int deleteByPrimaryKey(Long id);

    int insert(PermissionsPO record);

    PermissionsPO selectByPrimaryKey(Long id);

    int updateByPrimaryKey(PermissionsPO record);

    int batchInsert(@Param("list") List<PermissionsPO> list);
}