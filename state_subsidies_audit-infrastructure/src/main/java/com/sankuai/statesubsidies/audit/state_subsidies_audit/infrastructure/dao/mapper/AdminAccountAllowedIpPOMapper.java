package com.sankuai.statesubsidies.audit.state_subsidies_audit.infrastructure.dao.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisMixedBaseMapper;
import com.sankuai.statesubsidies.audit.state_subsidies_audit.infrastructure.dao.example.AdminAccountAllowedIpPOExample;
import com.sankuai.statesubsidies.audit.state_subsidies_audit.infrastructure.dao.po.AdminAccountAllowedIpPO;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Repository;

@Repository
public interface AdminAccountAllowedIpPOMapper extends MybatisMixedBaseMapper<AdminAccountAllowedIpPO, AdminAccountAllowedIpPOExample, Long> {
    int deleteByPrimaryKey(Long id);

    int insert(AdminAccountAllowedIpPO record);

    AdminAccountAllowedIpPO selectByPrimaryKey(Long id);

    int updateByPrimaryKey(AdminAccountAllowedIpPO record);

    int batchInsert(@Param("list") List<AdminAccountAllowedIpPO> list);
}