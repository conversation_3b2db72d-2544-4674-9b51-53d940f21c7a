package com.sankuai.statesubsidies.audit.state_subsidies_audit.infrastructure.cache;

import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class AuditAccountTokenCache {

    @Autowired
    @Qualifier("redisClient")
    private RedisStoreClient redisStoreClient0;


    @Value("${squirrel.state_subsidies_audit.token.category}")
    private String category;

    public String getToken(String key) {
        StoreKey storeKey = new StoreKey(category, key);
        return redisStoreClient0.get(storeKey);
    }

    public void setToken(String key, String value) {
        StoreKey storeKey = new StoreKey(category, key);
        redisStoreClient0.set(storeKey, value);
    }
}
