package com.sankuai.statesubsidies.audit.state_subsidies_audit.infrastructure.proxy;

import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftClient;
import com.meituan.xframe.boot.thrift.autoconfigure.annotation.ThriftClientProxy;
import com.sankuai.sgmerchant.govsubsidy.client.thrift.command.*;
import com.sankuai.sgmerchant.govsubsidy.client.thrift.dto.*;
import com.sankuai.sgmerchant.govsubsidy.client.thrift.response.*;
import com.sankuai.sgmerchant.govsubsidy.client.thrift.service.SgCouponOrderAuditQueryThriftService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class CouponOrderAuditQueryServiceClient {

    @ThriftClientProxy(remoteAppKey = "com.sankuai.sgmerchant.govsubsidy", timeout = 5000)
    private SgCouponOrderAuditQueryThriftService couponOrderAuditQueryService;

    /**
     * 审核数据检索接口
     */

    public CommonPageResponse<CouponOrderAuditQueryPageDTO> pageCouponOrderAuditQueryList(
            CouponOrderAuditQuerySearchCmd command, PageInfoDTO pageDto) {
        return couponOrderAuditQueryService.pageCouponOrderAuditQueryList(command, pageDto);
    }

    /**
     * 审核数据详情
     */
    public CommonDataResponse<CouponOrderAuditQueryDetailDTO> queryCouponOrderAuditQueryDetail(
            CouponOrderAuditQueryQueryCmd command) {
        return couponOrderAuditQueryService.queryCouponOrderAuditQueryDetail(command);
    }

    /**
     * 审核数据下载任务分页查询
     */
    public CommonPageResponse<CouponOrderAuditQueryDownloadDTO> pageCouponOrderAuditQueryDownloadList(
            CouponOrderAuditQueryDownloadSearchCmd command, PageInfoDTO pageDto) {
        return couponOrderAuditQueryService.pageCouponOrderAuditQueryDownloadList(command, pageDto);
    }

    /**
     * 创建审核数据下载任务
     */
    public CommonResponse downloadCouponOrderAuditQuery(CouponOrderDownloadAuditQueryCmd command) {
        return couponOrderAuditQueryService.downloadCouponOrderAuditQuery(command);
    }

    /**
     * 失败重复下载
     */
    public CommonResponse downloadRetryCouponOrderAuditQuery(CouponOrderRetryDownloadAuditQueryCmd command) {
        return couponOrderAuditQueryService.downloadRetryCouponOrderAuditQuery(command);
    }
}