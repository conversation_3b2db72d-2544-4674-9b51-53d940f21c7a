package com.sankuai.statesubsidies.audit.state_subsidies_audit.infrastructure.dao.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisMixedBaseMapper;
import com.sankuai.statesubsidies.audit.state_subsidies_audit.infrastructure.dao.example.AdminAccountCityPOExample;
import com.sankuai.statesubsidies.audit.state_subsidies_audit.infrastructure.dao.po.AdminAccountCityPO;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface AdminAccountCityPOMapper extends MybatisMixedBaseMapper<AdminAccountCityPO, AdminAccountCityPOExample, Long> {
    int deleteByPrimaryKey(Long id);

    int insert(AdminAccountCityPO record);

    AdminAccountCityPO selectByPrimaryKey(Long id);

    int updateByPrimaryKey(AdminAccountCityPO record);

    int batchInsert(@Param("list") List<AdminAccountCityPO> list);
}