package com.sankuai.statesubsidies.audit.state_subsidies_audit.infrastructure.dao.po;

import lombok.*;

/**
 *
 *   表名: admin_account_allowed_ip
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AdminAccountAllowedIpPO {
    /**
     *   字段: id
     *   说明: 主键
     */
    private Long id;

    /**
     *   字段: account_id
     *   说明: 用户账号id
     */
    private Long accountId;

    /**
     *   字段: allowed_ip
     *   说明: 允许登录的IP
     */
    private String allowedIp;
}