package com.sankuai.statesubsidies.audit.state_subsidies_audit.infrastructure.dao.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisMixedBaseMapper;
import com.sankuai.statesubsidies.audit.state_subsidies_audit.infrastructure.dao.example.AdminAccountPermissionScopePOExample;
import com.sankuai.statesubsidies.audit.state_subsidies_audit.infrastructure.dao.po.AdminAccountPermissionScopePO;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface AdminAccountPermissionScopePOMapper extends MybatisMixedBaseMapper<AdminAccountPermissionScopePO, AdminAccountPermissionScopePOExample, Long> {
    int deleteByPrimaryKey(Long id);

    int insert(AdminAccountPermissionScopePO record);

    AdminAccountPermissionScopePO selectByPrimaryKey(Long id);

    int updateByPrimaryKey(AdminAccountPermissionScopePO record);

    int batchInsert(@Param("list") List<AdminAccountPermissionScopePO> list);
}