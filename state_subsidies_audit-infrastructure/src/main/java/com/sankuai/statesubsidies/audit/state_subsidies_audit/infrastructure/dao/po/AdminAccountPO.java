package com.sankuai.statesubsidies.audit.state_subsidies_audit.infrastructure.dao.po;

import java.time.LocalDateTime;
import lombok.*;

/**
 *
 *   表名: admin_account
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AdminAccountPO {
    /**
     *   字段: id
     *   说明: 用户账号唯一标识
     */
    private Long id;

    /**
     *   字段: username
     *   说明: 账号名称
     */
    private String username;

    /**
     *   字段: phone_number
     *   说明: 登录手机号
     */
    private String phoneNumber;

    /**
     *   字段: need_authorization_code
     *   说明: 是否授权码（0/1）
     */
    private Boolean needAuthorizationCode;

    /**
     *   字段: status
     *   说明: 是否启用（0/1）
     */
    private Boolean status;

    /**
     *   字段: login_count
     *   说明: 登录次数
     */
    private Integer loginCount;

    /**
     *   字段: last_login_time
     *   说明: 最近登录时间
     */
    private LocalDateTime lastLoginTime;

    /**
     *   字段: creator_phone
     *   说明: 授权人手机号
     */
    private String creatorPhone;

    /**
     *   字段: creator_mis
     *   说明: 授权人MIS号
     */
    private String creatorMis;
}