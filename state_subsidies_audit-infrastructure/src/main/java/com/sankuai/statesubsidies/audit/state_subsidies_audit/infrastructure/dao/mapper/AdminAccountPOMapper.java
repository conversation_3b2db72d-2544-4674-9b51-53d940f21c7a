package com.sankuai.statesubsidies.audit.state_subsidies_audit.infrastructure.dao.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisMixedBaseMapper;
import com.sankuai.statesubsidies.audit.state_subsidies_audit.infrastructure.dao.example.AdminAccountPOExample;
import com.sankuai.statesubsidies.audit.state_subsidies_audit.infrastructure.dao.po.AdminAccountPO;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Repository;


@Component
public interface AdminAccountPOMapper extends MybatisMixedBaseMapper<AdminAccountPO, AdminAccountPOExample, Long> {
    int deleteByPrimaryKey(Long id);

    int insert(AdminAccountPO record);

    AdminAccountPO selectByPrimaryKey(Long id);

    int updateByPrimaryKey(AdminAccountPO record);

    int batchInsert(@Param("list") List<AdminAccountPO> list);
}