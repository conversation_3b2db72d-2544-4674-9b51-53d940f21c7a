<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.statesubsidies.audit.state_subsidies_audit.infrastructure.dao.mapper.AdminAccountPOMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.statesubsidies.audit.state_subsidies_audit.infrastructure.dao.po.AdminAccountPO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="username" jdbcType="VARCHAR" property="username" />
    <result column="phone_number" jdbcType="VARCHAR" property="phoneNumber" />
    <result column="need_authorization_code" jdbcType="BIT" property="needAuthorizationCode" />
    <result column="status" jdbcType="BIT" property="status" />
    <result column="login_count" jdbcType="INTEGER" property="loginCount" />
    <result column="last_login_time" jdbcType="TIMESTAMP" property="lastLoginTime" />
    <result column="creator_phone" jdbcType="VARCHAR" property="creatorPhone" />
    <result column="creator_mis" jdbcType="VARCHAR" property="creatorMis" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, username, phone_number, need_authorization_code, status, login_count, last_login_time, 
    creator_phone, creator_mis
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.statesubsidies.audit.state_subsidies_audit.infrastructure.dao.example.AdminAccountPOExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from admin_account
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from admin_account
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from admin_account
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.statesubsidies.audit.state_subsidies_audit.infrastructure.dao.example.AdminAccountPOExample">
    delete from admin_account
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.statesubsidies.audit.state_subsidies_audit.infrastructure.dao.po.AdminAccountPO">
    insert into admin_account (id, username, phone_number, 
      need_authorization_code, status, login_count, 
      last_login_time, creator_phone, creator_mis
      )
    values (#{id,jdbcType=BIGINT}, #{username,jdbcType=VARCHAR}, #{phoneNumber,jdbcType=VARCHAR}, 
      #{needAuthorizationCode,jdbcType=BIT}, #{status,jdbcType=BIT}, #{loginCount,jdbcType=INTEGER}, 
      #{lastLoginTime,jdbcType=TIMESTAMP}, #{creatorPhone,jdbcType=VARCHAR}, #{creatorMis,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.statesubsidies.audit.state_subsidies_audit.infrastructure.dao.po.AdminAccountPO">
    insert into admin_account
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="username != null">
        username,
      </if>
      <if test="phoneNumber != null">
        phone_number,
      </if>
      <if test="needAuthorizationCode != null">
        need_authorization_code,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="loginCount != null">
        login_count,
      </if>
      <if test="lastLoginTime != null">
        last_login_time,
      </if>
      <if test="creatorPhone != null">
        creator_phone,
      </if>
      <if test="creatorMis != null">
        creator_mis,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="username != null">
        #{username,jdbcType=VARCHAR},
      </if>
      <if test="phoneNumber != null">
        #{phoneNumber,jdbcType=VARCHAR},
      </if>
      <if test="needAuthorizationCode != null">
        #{needAuthorizationCode,jdbcType=BIT},
      </if>
      <if test="status != null">
        #{status,jdbcType=BIT},
      </if>
      <if test="loginCount != null">
        #{loginCount,jdbcType=INTEGER},
      </if>
      <if test="lastLoginTime != null">
        #{lastLoginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creatorPhone != null">
        #{creatorPhone,jdbcType=VARCHAR},
      </if>
      <if test="creatorMis != null">
        #{creatorMis,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.statesubsidies.audit.state_subsidies_audit.infrastructure.dao.example.AdminAccountPOExample" resultType="java.lang.Long">
    select count(*) from admin_account
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update admin_account
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.username != null">
        username = #{record.username,jdbcType=VARCHAR},
      </if>
      <if test="record.phoneNumber != null">
        phone_number = #{record.phoneNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.needAuthorizationCode != null">
        need_authorization_code = #{record.needAuthorizationCode,jdbcType=BIT},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=BIT},
      </if>
      <if test="record.loginCount != null">
        login_count = #{record.loginCount,jdbcType=INTEGER},
      </if>
      <if test="record.lastLoginTime != null">
        last_login_time = #{record.lastLoginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.creatorPhone != null">
        creator_phone = #{record.creatorPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.creatorMis != null">
        creator_mis = #{record.creatorMis,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update admin_account
    set id = #{record.id,jdbcType=BIGINT},
      username = #{record.username,jdbcType=VARCHAR},
      phone_number = #{record.phoneNumber,jdbcType=VARCHAR},
      need_authorization_code = #{record.needAuthorizationCode,jdbcType=BIT},
      status = #{record.status,jdbcType=BIT},
      login_count = #{record.loginCount,jdbcType=INTEGER},
      last_login_time = #{record.lastLoginTime,jdbcType=TIMESTAMP},
      creator_phone = #{record.creatorPhone,jdbcType=VARCHAR},
      creator_mis = #{record.creatorMis,jdbcType=VARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.statesubsidies.audit.state_subsidies_audit.infrastructure.dao.po.AdminAccountPO">
    update admin_account
    <set>
      <if test="username != null">
        username = #{username,jdbcType=VARCHAR},
      </if>
      <if test="phoneNumber != null">
        phone_number = #{phoneNumber,jdbcType=VARCHAR},
      </if>
      <if test="needAuthorizationCode != null">
        need_authorization_code = #{needAuthorizationCode,jdbcType=BIT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=BIT},
      </if>
      <if test="loginCount != null">
        login_count = #{loginCount,jdbcType=INTEGER},
      </if>
      <if test="lastLoginTime != null">
        last_login_time = #{lastLoginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creatorPhone != null">
        creator_phone = #{creatorPhone,jdbcType=VARCHAR},
      </if>
      <if test="creatorMis != null">
        creator_mis = #{creatorMis,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.statesubsidies.audit.state_subsidies_audit.infrastructure.dao.po.AdminAccountPO">
    update admin_account
    set username = #{username,jdbcType=VARCHAR},
      phone_number = #{phoneNumber,jdbcType=VARCHAR},
      need_authorization_code = #{needAuthorizationCode,jdbcType=BIT},
      status = #{status,jdbcType=BIT},
      login_count = #{loginCount,jdbcType=INTEGER},
      last_login_time = #{lastLoginTime,jdbcType=TIMESTAMP},
      creator_phone = #{creatorPhone,jdbcType=VARCHAR},
      creator_mis = #{creatorMis,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into admin_account
    (id, username, phone_number, need_authorization_code, status, login_count, last_login_time, 
      creator_phone, creator_mis)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.username,jdbcType=VARCHAR}, #{item.phoneNumber,jdbcType=VARCHAR}, 
        #{item.needAuthorizationCode,jdbcType=BIT}, #{item.status,jdbcType=BIT}, #{item.loginCount,jdbcType=INTEGER}, 
        #{item.lastLoginTime,jdbcType=TIMESTAMP}, #{item.creatorPhone,jdbcType=VARCHAR}, 
        #{item.creatorMis,jdbcType=VARCHAR})
    </foreach>
  </insert>
</mapper>