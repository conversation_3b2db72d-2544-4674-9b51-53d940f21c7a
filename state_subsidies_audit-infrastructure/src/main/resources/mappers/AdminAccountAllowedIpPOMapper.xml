<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.statesubsidies.audit.state_subsidies_audit.infrastructure.dao.mapper.AdminAccountAllowedIpPOMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.statesubsidies.audit.state_subsidies_audit.infrastructure.dao.po.AdminAccountAllowedIpPO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="account_id" jdbcType="BIGINT" property="accountId" />
    <result column="allowed_ip" jdbcType="VARCHAR" property="allowedIp" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, account_id, allowed_ip
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.statesubsidies.audit.state_subsidies_audit.infrastructure.dao.example.AdminAccountAllowedIpPOExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'false' as QUERYID,
    <include refid="Base_Column_List" />
    from admin_account_allowed_ip
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from admin_account_allowed_ip
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from admin_account_allowed_ip
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.sankuai.statesubsidies.audit.state_subsidies_audit.infrastructure.dao.po.AdminAccountAllowedIpPO">
    insert into admin_account_allowed_ip (id, account_id, allowed_ip
      )
    values (#{id,jdbcType=BIGINT}, #{accountId,jdbcType=BIGINT}, #{allowedIp,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.statesubsidies.audit.state_subsidies_audit.infrastructure.dao.po.AdminAccountAllowedIpPO">
    insert into admin_account_allowed_ip
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="allowedIp != null">
        allowed_ip,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="accountId != null">
        #{accountId,jdbcType=BIGINT},
      </if>
      <if test="allowedIp != null">
        #{allowedIp,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByExampleSelective" parameterType="map">
    update admin_account_allowed_ip
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.accountId != null">
        account_id = #{record.accountId,jdbcType=BIGINT},
      </if>
      <if test="record.allowedIp != null">
        allowed_ip = #{record.allowedIp,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update admin_account_allowed_ip
    set id = #{record.id,jdbcType=BIGINT},
      account_id = #{record.accountId,jdbcType=BIGINT},
      allowed_ip = #{record.allowedIp,jdbcType=VARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.statesubsidies.audit.state_subsidies_audit.infrastructure.dao.po.AdminAccountAllowedIpPO">
    update admin_account_allowed_ip
    <set>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=BIGINT},
      </if>
      <if test="allowedIp != null">
        allowed_ip = #{allowedIp,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.statesubsidies.audit.state_subsidies_audit.infrastructure.dao.po.AdminAccountAllowedIpPO">
    update admin_account_allowed_ip
    set account_id = #{accountId,jdbcType=BIGINT},
      allowed_ip = #{allowedIp,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into admin_account_allowed_ip
    (id, account_id, allowed_ip)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.accountId,jdbcType=BIGINT}, #{item.allowedIp,jdbcType=VARCHAR}
        )
    </foreach>
  </insert>
</mapper>