<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.statesubsidies.audit.state_subsidies_audit.infrastructure.dao.mapper.AdminAccountPermissionScopePOMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.statesubsidies.audit.state_subsidies_audit.infrastructure.dao.po.AdminAccountPermissionScopePO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="account_id" jdbcType="BIGINT" property="accountId" />
    <result column="scope_id" jdbcType="INTEGER" property="scopeId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, account_id, scope_id
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.statesubsidies.audit.state_subsidies_audit.infrastructure.dao.example.AdminAccountPermissionScopePOExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from admin_account_permission_scope
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from admin_account_permission_scope
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from admin_account_permission_scope
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.statesubsidies.audit.state_subsidies_audit.infrastructure.dao.example.AdminAccountPermissionScopePOExample">
    delete from admin_account_permission_scope
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.statesubsidies.audit.state_subsidies_audit.infrastructure.dao.po.AdminAccountPermissionScopePO">
    insert into admin_account_permission_scope (id, account_id, scope_id
      )
    values (#{id,jdbcType=BIGINT}, #{accountId,jdbcType=BIGINT}, #{scopeId,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.statesubsidies.audit.state_subsidies_audit.infrastructure.dao.po.AdminAccountPermissionScopePO">
    insert into admin_account_permission_scope
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="scopeId != null">
        scope_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="accountId != null">
        #{accountId,jdbcType=BIGINT},
      </if>
      <if test="scopeId != null">
        #{scopeId,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.statesubsidies.audit.state_subsidies_audit.infrastructure.dao.example.AdminAccountPermissionScopePOExample" resultType="java.lang.Long">
    select count(*) from admin_account_permission_scope
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update admin_account_permission_scope
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.accountId != null">
        account_id = #{record.accountId,jdbcType=BIGINT},
      </if>
      <if test="record.scopeId != null">
        scope_id = #{record.scopeId,jdbcType=INTEGER},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update admin_account_permission_scope
    set id = #{record.id,jdbcType=BIGINT},
      account_id = #{record.accountId,jdbcType=BIGINT},
      scope_id = #{record.scopeId,jdbcType=INTEGER}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.statesubsidies.audit.state_subsidies_audit.infrastructure.dao.po.AdminAccountPermissionScopePO">
    update admin_account_permission_scope
    <set>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=BIGINT},
      </if>
      <if test="scopeId != null">
        scope_id = #{scopeId,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.statesubsidies.audit.state_subsidies_audit.infrastructure.dao.po.AdminAccountPermissionScopePO">
    update admin_account_permission_scope
    set account_id = #{accountId,jdbcType=BIGINT},
      scope_id = #{scopeId,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into admin_account_permission_scope
    (id, account_id, scope_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.accountId,jdbcType=BIGINT}, #{item.scopeId,jdbcType=INTEGER}
        )
    </foreach>
  </insert>
</mapper>