<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.statesubsidies.audit.state_subsidies_audit.infrastructure.dao.mapper.AdminAccountCityPOMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.statesubsidies.audit.state_subsidies_audit.infrastructure.dao.po.AdminAccountCityPO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="account_id" jdbcType="BIGINT" property="accountId" />
    <result column="city_id" jdbcType="VARCHAR" property="cityId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, account_id, city_id
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.statesubsidies.audit.state_subsidies_audit.infrastructure.dao.example.AdminAccountCityPOExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from _admin_account_city_1uqck3wz_202504182137
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from _admin_account_city_1uqck3wz_202504182137
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from _admin_account_city_1uqck3wz_202504182137
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.sankuai.statesubsidies.audit.state_subsidies_audit.infrastructure.dao.po.AdminAccountCityPO">
    insert into _admin_account_city_1uqck3wz_202504182137 (id, account_id, city_id
      )
    values (#{id,jdbcType=BIGINT}, #{accountId,jdbcType=BIGINT}, #{cityId,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.statesubsidies.audit.state_subsidies_audit.infrastructure.dao.po.AdminAccountCityPO">
    insert into _admin_account_city_1uqck3wz_202504182137
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="cityId != null">
        city_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="accountId != null">
        #{accountId,jdbcType=BIGINT},
      </if>
      <if test="cityId != null">
        #{cityId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.statesubsidies.audit.state_subsidies_audit.infrastructure.dao.po.AdminAccountCityPO">
    update _admin_account_city_1uqck3wz_202504182137
    <set>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=BIGINT},
      </if>
      <if test="cityId != null">
        city_id = #{cityId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.statesubsidies.audit.state_subsidies_audit.infrastructure.dao.po.AdminAccountCityPO">
    update _admin_account_city_1uqck3wz_202504182137
    set account_id = #{accountId,jdbcType=BIGINT},
      city_id = #{cityId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into _admin_account_city_1uqck3wz_202504182137
    (id, account_id, city_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.accountId,jdbcType=BIGINT}, #{item.cityId,jdbcType=VARCHAR}
        )
    </foreach>
  </insert>
</mapper>