<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:tx="http://www.springframework.org/schema/tx"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans.xsd
        http://www.springframework.org/schema/tx
        http://www.springframework.org/schema/tx/spring-tx.xsd">
    <bean id="redisClient" class="com.dianping.squirrel.client.impl.redis.spring.RedisClientBeanFactory">
        <!-- 集群名称,必填 -->
        <property name="clusterName" value="redis-sg-state-subsidies-beijing_qa"/>
        <!--读写的超时时间,缓存业务建议改成100，存储业务建议改成1000，默认值为1000。选填-->
        <property name="readTimeout" value="1000"/>
        <!--客户端连接池新建链接的超时时间，默认值为2000，不建议修改过小。选填-->
        <property name="connTimeout" value="2000"/>
        <!--路由策略,默认值是master-only表示只从主节点读取。slave-only表示只读从节点,master-slave表示主从都可以读。选填-->
        <property name="routerType" value="master-slave"/>
        <!--是否机房就近访问，true：会优先路由到与客户端同机房的节点上，false：随机打散访问所有的节点，无特殊情况建议配置为false-->
        <property name="idcSensitive" value="false"/>
        <!--连接redis节点的连接池配置-->
        <property name="poolMaxIdle" value="16"/>
        <property name="poolMaxTotal" value="32"/>
        <property name="poolWaitMillis" value="500"/>
        <property name="poolMinIdle" value="1"/>
        <!--异步操作线程池配置，选填-->
        <property name="asyncCoreSize" value="16"/>
        <property name="asyncMaxSize" value="64"/>
        <property name="asyncQueueSize" value="1000"/>
    </bean>
</beans>