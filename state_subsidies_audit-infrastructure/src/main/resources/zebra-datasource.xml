<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:tx="http://www.springframework.org/schema/tx"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans.xsd
        http://www.springframework.org/schema/tx
        http://www.springframework.org/schema/tx/spring-tx.xsd">

    <!--  shard 相关配置  -->
    <bean id="shardDataSource" class="com.dianping.zebra.shard.jdbc.ShardDataSource" init-method="init"
          destroy-method="close">
        <property name="useLiteSet" value="true"/>
        <!-- 必配。指定唯一确定数据库的key-->
        <property name="ruleName" value="statesubsidies_audit_test"/>
        <!-- 选配。指定底层使用的连接池类型，支持"c3p0","tomcat-jdbc","druid","hikaricp","dbcp2"和"dbcp"，推荐使用"tomcat-jdbc"，默认值为"c3p0" -->
        <property name="poolType" value="${zebra.poolType}"/>
        <!-- 选配。指定连接池的最小连接数，默认值是5。 -->
        <property name="minPoolSize" value="${zebra.minPoolSize}"/>
        <!-- 选配。指定连接池的最大连接数，默认值是20。 -->
        <property name="maxPoolSize" value="${zebra.maxPoolSize}"/>
        <!-- 选配。指定连接池的初始化连接数，默认值是5。 -->
        <property name="initialPoolSize" value="${zebra.initialPoolSize}"/>
        <!-- 选配。指定连接池的获取连接的超时时间，默认值是1000。 -->
        <property name="checkoutTimeout" value="${zebra.checkoutTimeout}"/>
        <!-- jdbcdriver到高版本之后，需要强制指定useSSL -->
        <property name="extraJdbcUrlParams" value="useSSL=false"/>
        <!--以下配置全部可以选配-->
        <property name="maxIdleTime" value="${zebra.maxIdleTime}"/>
        <property name="idleConnectionTestPeriod" value="${zebra.idleConnectionTestPeriod}"/>
        <property name="acquireRetryAttempts" value="${zebra.acquireRetryAttempts}"/>
        <property name="acquireRetryDelay" value="${zebra.acquireRetryDelay}"/>
        <property name="maxStatements" value="${zebra.maxStatements}"/>
        <property name="maxStatementsPerConnection" value="${zebra.maxStatementsPerConnection}"/>
        <property name="numHelperThreads" value="${zebra.numHelperThreads}"/>
        <property name="maxAdministrativeTaskTime" value="${zebra.maxAdministrativeTaskTime}"/>
        <property name="preferredTestQuery" value="${zebra.preferredTestQuery}"/>
        <property name="defaultDatasource" value="${zebra.shardDefaultJdbcRef}"/>
    </bean>

    <bean id="shardSqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
        <!--dataource-->
        <property name="dataSource" ref="shardDataSource"/>
        <property name="configLocation" value="classpath:mybatis-config.xml"/>
        <!--Mapper files-->
        <property name="mapperLocations" value="classpath*:mapper/**/*.xml"/>
        <!--这里改成实际entity目录,如果有多个，可以用,;\t\n进行分割-->
        <property name="typeAliasesPackage"
                  value="com.sankuai.statesubsidies.audit.state_subsidies_audit.infrastructure.dao.relation.model"/>
        <property name="plugins">
            <array>
                <bean class="com.github.pagehelper.PageInterceptor">
                    <property name="properties">
                        <!--使用下面的方式配置参数，一行配置一个 -->
                        <value>
                            helperDialect=mysql
                            reasonable=false
                            supportMethodsArguments=true
                            params=count=countSql
                        </value>
                    </property>
                </bean>
            </array>
        </property>
    </bean>

    <bean id="shardMybatisTransactionManager"
          class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
        <property name="dataSource" ref="shardDataSource"/>
    </bean>

    <tx:annotation-driven transaction-manager="shardMybatisTransactionManager" proxy-target-class="true"/>

    <bean id="transactionTemplate"
          class="org.springframework.transaction.support.TransactionTemplate">
        <property name="transactionManager" ref="shardMybatisTransactionManager"/>
    </bean>

    <!--如果本来使用的就是mybatis，主要就是替换掉这个配置-->
    <bean id="shardMapperScannerConfigurer" class="org.mybatis.spring.mapper.MapperScannerConfigurer">
        <!--这里改成实际dao目录,如果有多个，可以用,;\t\n进行分割-->
        <property name="sqlSessionFactoryBeanName" value="shardSqlSessionFactory"/>
        <property name="basePackage" value="com.sankuai.statesubsidies.audit.state_subsidies_audit.infrastructure.dao.relation.mapper"/>
    </bean>

    <bean id="franchiseDataSource" class="com.dianping.zebra.group.jdbc.GroupDataSource" init-method="init" destroy-method="close">
        <property name="useLiteSet" value="true" />
        <!-- 必配。指定唯一确定数据库的key-->
        <property name="jdbcRef" value="${zebra.franchiseJdbcRef}" />
        <!-- 选配。指定底层使用的连接池类型，支持"c3p0","tomcat-jdbc","druid","hikaricp","dbcp2"和"dbcp"，推荐使用"tomcat-jdbc"，默认值为"c3p0" -->
        <property name="poolType" value="${zebra.poolType}" />
        <!-- 选配。指定连接池的最小连接数，默认值是5。 -->
        <property name="minPoolSize" value="${zebra.minPoolSize}" />
        <!-- 选配。指定连接池的最大连接数，默认值是20。 -->
        <property name="maxPoolSize" value="${zebra.maxPoolSize}" />
        <!-- 选配。指定连接池的初始化连接数，默认值是5。 -->
        <property name="initialPoolSize" value="${zebra.initialPoolSize}" />
        <!-- 选配。指定连接池的获取连接的超时时间，默认值是1000。 -->
        <property name="checkoutTimeout" value="${zebra.checkoutTimeout}" />
        <!-- jdbcdriver到高版本之后，需要强制指定useSSL -->
        <property name="extraJdbcUrlParams" value="useSSL=false" />
        <!--以下配置全部可以选配-->
        <property name="maxIdleTime" value="${zebra.maxIdleTime}" />
        <property name="idleConnectionTestPeriod" value="${zebra.idleConnectionTestPeriod}" />
        <property name="acquireRetryAttempts" value="${zebra.acquireRetryAttempts}" />
        <property name="acquireRetryDelay" value="${zebra.acquireRetryDelay}" />
        <property name="maxStatements" value="${zebra.maxStatements}" />
        <property name="maxStatementsPerConnection" value="${zebra.maxStatementsPerConnection}" />
        <property name="numHelperThreads" value="${zebra.numHelperThreads}" />
        <property name="maxAdministrativeTaskTime" value="${zebra.maxAdministrativeTaskTime}" />
        <property name="preferredTestQuery" value="${zebra.preferredTestQuery}" />
    </bean>

    <bean id="franchiseSqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
        <!--dataource-->
        <property name="dataSource" ref="franchiseDataSource"/>
        <property name="configLocation" value="classpath:mybatis-config.xml"/>
        <!--Mapper files-->
        <property name="mapperLocations" value="classpath*:mapper/**/*.xml" />
        <!--这里改成实际entity目录,如果有多个，可以用,;\t\n进行分割-->
        <property name="typeAliasesPackage" value="com.sankuai.statesubsidies.audit.state_subsidies_audit.infrastructure.dao.audit.model"/>
        <property name="plugins">
            <array>
                <bean class="com.github.pagehelper.PageInterceptor">
                    <property name="properties">
                        <!--使用下面的方式配置参数，一行配置一个 -->
                        <value>
                            helperDialect=mysql
                            reasonable=false
                            supportMethodsArguments=true
                            params=count=countSql
                        </value>
                    </property>
                </bean>
            </array>
        </property>
    </bean>


    <bean id="franchiseTransactionManager"
          class="org.springframework.jdbc.datasource.DataSourceTransactionManager" primary="false">
        <property name="dataSource" ref="franchiseDataSource" />
    </bean>

    <tx:annotation-driven transaction-manager="franchiseTransactionManager" proxy-target-class="true"/>

    <bean id="franchiseMapperScannerConfigurer" class="org.mybatis.spring.mapper.MapperScannerConfigurer">
        <property name="sqlSessionFactoryBeanName" value="franchiseSqlSessionFactory"/>
        <property name="basePackage" value="com.sankuai.statesubsidies.audit.state_subsidies_audit.infrastructure.dao.audit.mapper" />
    </bean>

    <bean id="franchiseTransactionTemplate"
          class="org.springframework.transaction.support.TransactionTemplate">
        <property name="transactionManager" ref="franchiseTransactionManager"/>
    </bean>

</beans>