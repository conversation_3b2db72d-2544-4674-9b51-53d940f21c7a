//package com.sankuai.statesubsidies.audit.state_subsidies_audit;
//
//import com.dianping.squirrel.client.StoreKey;
//import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
//import com.dianping.squirrel.client.impl.redis.spring.RedisClientBeanFactory;
//import org.junit.Assert;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Qualifier;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.context.ApplicationContext;
//import org.springframework.test.annotation.DirtiesContext;
//import org.springframework.test.context.junit4.SpringRunner;
//
///**
// * <AUTHOR>
// * @date 2018/7/19
// **/
//@SpringBootTest
//@RunWith(SpringRunner.class)
//@DirtiesContext(classMode = DirtiesContext.ClassMode.BEFORE_CLASS)
//public class SquirrelSingleClientTest {
//
//    /**
//     * 使用前必读：
//     * 1. 本入口用来展示「单 Squirrel 集群 Client」使用
//     * 2. 本入口需要使用 test profile 配置文件，在启动前执行
//     *    `mvn -pl mdp-boot-starter-samples/mdp-boot-starter-squirrel-sample -am clean package -DskipTests -Ptest`
//     *    ，即单独构建此模块，并使用 test profile 配置文件。
//     * 3. 执行这个测试用例
//     */
//
//    private static final Logger logger = LoggerFactory.getLogger(SquirrelMultiClientTest.class.getName());
//
//    @Autowired
//    private ApplicationContext applicationContext;
//
//    @Autowired(required = false)
//    @Qualifier("redisClient")
//    private RedisStoreClient redisStoreClient;
//
//    /**
//     * 测试1：
//     * Bean 是否已经在 Spring 容器中
//     */
//    @Test
//    public void testBeanInjectSuc() throws Exception {
//
//        Assert.assertNotNull(redisStoreClient);
//
//    }
//
//    @Test
//    public void testBeanIsValid() throws Exception {
//
//        String key = "SquirrelSingleClientTest_key";
//        String val = "SquirrelSingleClientTest_val";
//
//        StoreKey storeKey = new StoreKey("cache_poi_real", key);
//
//        Boolean addResult = redisStoreClient.exists(storeKey);
//        logger.info("Redis sadd result:{}", addResult);
//
//    }
//
//
//    @Test
//    public void testBeanBuildCorrect() throws Exception {
//
//        RedisClientBeanFactory redisClientBeanFactory =
//                applicationContext.getBean(RedisClientBeanFactory.class);
//
//        Assert.assertNotNull(redisClientBeanFactory);
//
//        /*
//        mdp.squirrel[0].clientName=redisClient
//        mdp.Squirrel[0].clusterName=redis-hotel-switchapi_dev
//        mdp.Squirrel[0].readTimeout=100
//        mdp.Squirrel[0].routerType=master-slave
//        mdp.Squirrel[0].connTimeout=100
//         */
//
//        Assert.assertEquals("redis-hotel-switchapi_dev", redisClientBeanFactory.getClusterName());
//        Assert.assertEquals(100, redisClientBeanFactory.getReadTimeout());
//        Assert.assertEquals("master-slave", redisClientBeanFactory.getRouterType());
//        Assert.assertEquals(100, redisClientBeanFactory.getConnTimeout());
//
//        Assert.assertEquals(11, redisClientBeanFactory.getPoolMaxTotal());
//    }
//}