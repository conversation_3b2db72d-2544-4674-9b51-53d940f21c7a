# 应用基础配置
APP_NAME=Shangou AI CR
DEBUG=false

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_NAME=shangou_ai_cr
DB_USER=root
DB_PASSWORD=your-password

# LLM配置
LLM_API_KEY=your-openai-api-key
LLM_MODEL=gpt-3.5-turbo
LLM_BASE_URL=

# MCP协议配置
MCP_ENABLED=true
MCP_SSE_URL=mcphub平台的接入点URL

# API配置
API_HOST=0.0.0.0
API_PORT=9000
API_DEBUG=false
API_KEY_REQUIRED=false
API_KEYS=

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# 监控配置
LOG_LEVEL=INFO
METRICS_PORT=8080
