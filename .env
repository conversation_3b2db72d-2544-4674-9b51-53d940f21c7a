# 项目基础配置
INF_BOM_ENV=test
CLIENT_APP_KEY=com.sankuai.yunzhuan.devhelper

# 大象配置
DX_APP_KEY=com.sankuai.dxenterprise.open.gateway
DX_APP_ID_DEV=g222455160247104
DX_APP_ID_PROD=141229001323X21Q
DX_APP_KEY_NAME=dxopen_sk

# DevMind
DM_BASE_URL=https://devmind.web.test.sankuai.com/shangou_ai_rag
DM_DEFAULT_CHAT_ID=ee6766ee2f1511f082d8bac3ebfe36b5
DM_API_KEY=ragflow-FlNGMzYzAyMmYyNzExZjBiZjIwYmFjM2
DM_CR_RULE_KB_ID=dfd670ee36c611f0a20e0a580aab36c4


# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_NAME=shangou_ai_cr
DB_USER=root
DB_PASSWORD=your-password

# LLM配置
LLM_API_KEY=1902196215861792854
LLM_MODEL=anthropic.claude-3.5-sonnet
LLM_BASE_URL=https://aigc.sankuai.com/v1/openai/native

# MCP协议配置
MCP_ENABLED=true
MCP_SSE_URL=mcphub平台的接入点URL

# API配置
API_HOST=0.0.0.0
API_PORT=9000
API_DEBUG=false
API_KEY_REQUIRED=false
API_KEYS=

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# 监控配置
LOG_LEVEL=INFO