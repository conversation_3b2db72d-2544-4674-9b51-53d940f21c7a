<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.meituan.mdp</groupId>
        <artifactId>mdp-basic-parent</artifactId>
        <version>2.9.0.2</version>
        <relativePath/>
    </parent>

    <groupId>com.sankuai.statesubsidies.audit</groupId>
    <artifactId>state_subsidies_audit</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>
    <name>state_subsidies_audit</name>

    <modules>
        <module>state_subsidies_audit-api</module>
        <module>state_subsidies_audit-starter</module>
        <module>state_subsidies_audit-application</module>
        <module>state_subsidies_audit-domain</module>
        <module>state_subsidies_audit-infrastructure</module>
    </modules>

    <properties>
        <revision>1.0.0-SNAPSHOT</revision>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.sankuai.statesubsidies.audit</groupId>
                <artifactId>state_subsidies_audit-api</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.statesubsidies.audit</groupId>
                <artifactId>state_subsidies_audit-starter</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.statesubsidies.audit</groupId>
                <artifactId>state_subsidies_audit-domain</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.statesubsidies.audit</groupId>
                <artifactId>state_subsidies_audit-application</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.statesubsidies.audit</groupId>
                <artifactId>state_subsidies_audit-infrastructure</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.meituan.mdp.component</groupId>
                <artifactId>swagger-analysis-core</artifactId>
                <version>3.0.1-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.meituan.mdp.maven.plugins</groupId>
                <artifactId>swagger-analysis-maven-plugin</artifactId>
                <version>3.0.1-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.meituan.xframe</groupId>
                <artifactId>thrift-xframe-boot-starter</artifactId>
                <version>2.6.8</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                        <configuration>
                            <flattenMode>defaults</flattenMode>
                            <pomElements>
                                <parent>expand</parent>
                                <profiles>expand</profiles>
                                <dependencies>keep</dependencies>
                                <build>keep</build>
                            </pomElements>
                            <updatePomFile>true</updatePomFile>
                        </configuration>
                    </execution>
                    <execution>
                        <id>flatten-clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>
