package com.sankuai.statesubsidies.audit.state_subsidies_audit.starter.controller;

import com.sankuai.sgmerchant.govsubsidy.client.thrift.command.CouponOrderAuditQueryQueryCmd;
import com.sankuai.sgmerchant.govsubsidy.client.thrift.dto.CouponOrderAuditQueryDetailDTO;
import com.sankuai.sgmerchant.govsubsidy.client.thrift.response.CommonDataResponse;
import com.sankuai.statesubsidies.audit.state_subsidies_audit.infrastructure.cache.AuditAccountTokenCache;
import com.sankuai.statesubsidies.audit.state_subsidies_audit.infrastructure.dao.mapper.AdminAccountPOMapper;
import com.sankuai.statesubsidies.audit.state_subsidies_audit.infrastructure.dao.po.AdminAccountPO;
import com.sankuai.statesubsidies.audit.state_subsidies_audit.infrastructure.proxy.CouponOrderAuditQueryServiceClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/test")
public class ThriftServiceClientTest {

    @Autowired
    private CouponOrderAuditQueryServiceClient stateSubsidiesAuditThriftService;

//    @Autowired
//    private AdminAccountPOMapper adminAccountPOMapper;

    @Autowired
    private AuditAccountTokenCache auditAccountTokenCache;


    @RequestMapping("/audit_info")
    @ResponseBody
    public String testQueryCouponOrderAuditInfo() {
        CouponOrderAuditQueryQueryCmd request = new CouponOrderAuditQueryQueryCmd();
        request.setId(1L);
        CommonDataResponse<CouponOrderAuditQueryDetailDTO> response = stateSubsidiesAuditThriftService.queryCouponOrderAuditQueryDetail(request);
        System.out.println(response);
        return "success";
    }

/*    @RequestMapping("/test_mysql")
    @ResponseBody
    public String testMysqlConnection() {
        AdminAccountPO adminAccountPO = new AdminAccountPO();
        adminAccountPO.setUsername("test");

        int resultCode = adminAccountPOMapper.insert(adminAccountPO);
        return "success: " + resultCode;
    }*/

    @RequestMapping("/test_cache")
    @ResponseBody
    public String testCache() {
        auditAccountTokenCache.setToken("test", "test");
        return auditAccountTokenCache.getToken("test");
    }
}
