package com.sankuai.statesubsidies.audit.state_subsidies_audit;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import com.meituan.mdp.boot.starter.MdpContextUtils;

//@MapperScan(basePackages = "com.sankuai.statesubsidies.audit.state_subsidies_audit.infrastructure.dao.mapper")
@SpringBootApplication(exclude={DataSourceAutoConfiguration.class})
public class ApplicationLoader {

    public static void main(String[] args) {
        SpringApplication application = new SpringApplication(ApplicationLoader.class);
        application.setAdditionalProfiles(MdpContextUtils.getHostEnvStr());
        application.run(args);
    }
}


