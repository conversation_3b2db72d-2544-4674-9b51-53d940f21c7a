namespace java com.sankuai.xm.openplatform.api.entity

include "CommonEntity.thrift"

enum GroupTypeEnum {
        COMMON = 1,
        MULTI_ENTERPRISE = 2,
        ORG = 3,
        ALL = 4;
}

enum GroupMemberRole {
        MODERATOR = 1,
        ADMINISTRATOR = 2,
        PARTICIPANT = 3;
}

struct GroupInfo {
        1: i64 id;
        2: string name;
        3: string avatarUrl;
        4: GroupTypeEnum groupType;
        5: optional i64 cid;
}

struct GroupInfoResp {
        1: CommonEntity.RespStatus status;
        2: GroupInfo groupInfo;
}

struct GroupMemberInfo {
        1: list<CommonEntity.UserIdentify> moderators;
        2: list<CommonEntity.UserIdentify> administrators;
        3: list<CommonEntity.UserIdentify> participants;
}

struct GroupMemberInfoResp {
        1: CommonEntity.RespStatus status;
        2: GroupMemberInfo groupMemberInfo;
}

struct BatchInGroupResp {
        1: CommonEntity.RespStatus status;
        2: map<CommonEntity.UserIdentify, bool> inGroupMap;
}

struct MemberRoleResp {
        1: CommonEntity.RespStatus status;
        2: GroupMemberRole memberRole;
}

struct GroupDetailInfo{
        1: optional i32 membersSize;
        2: optional i32 membersCapacity;
}

struct GroupDetailResp {
        1: CommonEntity.RespStatus status;
        2: map<i64,GroupDetailInfo> groupInfo;
}

struct GroupMessagePermissionReq {
        1: string appkey;
        2: set<i64> groupIds;
        3: i64 applyUid;
        4: string reason;
}

struct GroupMessagePermissionResp {
        1: CommonEntity.RespStatus status;
        2: set<i64> failGroupIds;
}

struct GroupReadItem{
        1: i64 chatId;
        2: i64 readTime;
}

struct UpdateGroupReadTimeReq{
        1: i64 uid;
        2: list<GroupReadItem> readItems;
}

struct UpdateGroupReadTimeResp{
        1: CommonEntity.RespStatus status;
}

struct QueryGroupReadTimeReq{
        1: i64 uid;
        2: list<GroupReadItem> readItems;
}

struct QueryGroupReadTimeResp{
        1: CommonEntity.RespStatus status;
        2: list<GroupReadItem> readItems;
}

struct CreateGroupReq{
        1: optional string name;
        2: optional string info;
        3: optional string avatarPosition;
        4: optional set<i64> admins;
        5: optional set<i64> users;
        6: optional i64 owner;
        7: optional set<i64> botAdmins;
}

struct CreateGroupResp{
        1: CommonEntity.RespStatus status;
        2: i64 gid;
}

struct AddGroupMembersReq{
        1: required i64 gid;
        3: optional set<i64> users;
        4: optional set<i64> bots;
}

struct AddGroupMembersResp{
        1: CommonEntity.RespStatus status;
}

struct DelGroupMembersReq{
        1: required i64 gid;
        2: optional set<i64> users;
        3: optional set<i64> bots;
}

struct DelGroupMembersResp{
        1: CommonEntity.RespStatus status;
}

enum OwnerTypeEnum{
        BOT = 1;
        USER = 2;
}

struct GroupOwner{
        1: required i64 gid;
        2: required i64 id;
        3: required OwnerTypeEnum type;
}

struct GroupBotReq{
        1: required i64 gid;
}

struct GroupMembersReq{
        1: required i64 gid;
        2: required i32 pageNumber;
        3: required i32 pageSize;
}

struct GroupMemberDetail{
        1: string mis;
        2: i64 uid;
        3: string name;
        4: GroupMemberRole role;
}

struct GroupMembersResp{
        1: CommonEntity.RespStatus status;
        2: list<GroupMemberDetail> members;
        3: bool hasMore;
}

struct OwnerInfo{
        1: i64 id;
        2: string name;
        3: string avatarUrl;
        4: OwnerTypeEnum role;
}

enum HsitoryMsgEnum{
        CAN_NOT_SEE = 1;
        RECENT_DAY = 2;
        RECENT_WEEK = 3;
        CAN_SEE_ALL = 4;
}

struct GroupConfig{
        1: bool shareCard;
        2: bool atAll;
        3: bool addGroupVertify;
        4: bool toSpeak;
        5: bool pullPeople;
        6: HsitoryMsgEnum historyMsg;
}

struct GroupInformation {
        1: string name;
        2: string avatarUrl;
        3: string info;
        4: i64 category;
        5: OwnerInfo ownerInfo;
        6: GroupConfig config;
}

struct GroupInformationReq {
        1: required i64 gid;
}

struct GroupInformationResp {
        1: CommonEntity.RespStatus status;
        2: GroupInformation groupInfo;
}

struct GroupMemberReq {
        1: required i64 gid;
        2: required i64 memberUid;
}

struct GroupNoticeInfo{
        1: string noticeContent;
        2: i64 updateTime;
}

struct GetGroupNoticeReq{
        1: required i64 gid;
}

struct GetGroupNoticeResp{
        1: CommonEntity.RespStatus status;
        2: GroupNoticeInfo groupNoticeInfo;
}

struct UpdateGroupNoticeReq{
        1: required i64 gid;
        2: required string noticeContent;
}

struct AddGroupBotReq{
        1: required i64 gid;
}
enum GroupCapEnum{
        LEVEL_ONE = 1000;
        LEVEL_TWO = 2000;
        CAPPED = 3000;
}

struct GroupMemberCapReq{
        1: required i64 gid;
        2: required GroupCapEnum limitData;
}

enum GroupRoleEnum {
        ADMIN = 1,
        NORMAL = 2;
}

struct GroupRoleReq{
        1: required i64 gid;
        2: required GroupRoleEnum role
        3: optional set<i64> users;
        4: optional set<i64> bots;
}

struct GroupRoleResp{
        1: CommonEntity.RespStatus status;
}

struct GroupRoleName{
        1: i64 id;
        2: string roleName;
}

struct GroupRoleNameReq{
        1: required i64 gid;
        3: optional list<GroupRoleName> userRoles;
        4: optional list<GroupRoleName> botRoles;
}

struct GroupRoleNameResp{
        1: CommonEntity.RespStatus status;
}

