namespace java com.sankuai.xm.openplatform.api.service.open
include "MessageEntity.thrift"
include "CommonEntity.thrift"

service XmOpenMessageServiceI {
        MessageEntity.MessageTransmissionResp pushMessage( 1: string accessToken, 2: MessageEntity.MessageTransmissionReq req);

        CommonEntity.BooleanResp sendTextNoticeGeneral ( 1: string accessToken, 2: MessageEntity.SendTextNoticeReq req);

        MessageEntity.ChatListResp getChatList(1: string accessToken, 2: MessageEntity.ChatListReq chatListReq);

        MessageEntity.SendChatMsgByRobotRes sendChatMsgByRobot(1: string accessToken, 2: MessageEntity.SendChatMsgByRobotReq req);

        MessageEntity.SendGroupMsgByRobotRes sendGroupMsgByRobot(1: string accessToken, 2: MessageEntity.SendGroupMsgByRobotReq req);

        MessageEntity.SendGroupMsgByUidRes sendGroupMsgByUid(1: string userToken, 2: MessageEntity.SendGroupMsgByUidReq req);

        CommonEntity.EmptyResp drawChatMsgByRobot(1: string accessToken, 2: MessageEntity.DrawChatMsgReq req);

        CommonEntity.EmptyResp drawGroupMsgByRobot(1: string accessToken, 2: MessageEntity.DrawGroupMsgReq req);

        MessageEntity.UpdateChatMsgResp updateChatDynamicMsg(1: string accessToken, 2: MessageEntity.UpdateChatMsgReq req);

        MessageEntity.UpdateGroupMsgResp updateGroupDynamicMsg(1: string accessToken, 2: MessageEntity.UpdateGroupMsgReq req);

        MessageEntity.HistoryPubMsgResp getHistoryPubMsgs(1: string accessToken, 2: MessageEntity.HistoryPubMsgReq req);

        MessageEntity.HistoryGroupMsgResp getHistoryGroupMsgs(1: string accessToken, 2: MessageEntity.HistoryGroupMsgReq req);

        MessageEntity.PriorityChannelResp sendMsgToPriorityChannel(1: string accessToken, 2: MessageEntity.PriorityChannelReq req);

        MessageEntity.PrePriorityChannelResp preSendMsgToPriorityChannel(1: string accessToken, 2: MessageEntity.PrePriorityChannelReq req);

        MessageEntity.PriorityChannelWithPreIdResp sendMsgToPriorityChannelWithPreId(1: string accessToken, 2: MessageEntity.PriorityChannelWithPreIdReq req);

        MessageEntity.DrawPriorityMsgResp drawMsgFromPriorityChannel(1: string accessToken, 2: MessageEntity.DrawPriorityMsgReq req);

        MessageEntity.PubMsgResp getPubMsgByMsgId(1: string accessToken, 2: MessageEntity.PubMsgReq req);

        MessageEntity.GroupMsgResp getGroupMsgByMsgId(1: string accessToken, 2: MessageEntity.GroupMsgReq req);

        MessageEntity.MergeMsgResp getMergeMsg(1: string accessToken, 2: MessageEntity.MergeMsgReq req);

        MessageEntity.SendMergeMsgResp sendMergeMsg(1: string accessToken, 2: MessageEntity.SendMergeMsgReq req);

}