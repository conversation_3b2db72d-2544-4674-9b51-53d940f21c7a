namespace java com.sankuai.xm.openplatform.common.entity

struct RespStatus {
        1: i32 code;
        2: string msg;
}

struct BooleanResp {
        1: RespStatus status;
        2: bool value;
}

struct StringResp {
        1: RespStatus status;
        2: string value;
}

struct EmptyResp {
        1: RespStatus status;
}

struct MapResp {
        1: RespStatus status;
        2: map<i64,bool> value;
}

struct UserIdentify {

        /**
        * 用户在应用内唯一标识
        **/
        1: string openId;
        /**
        * 只有美团员工才具备
        **/
        2: string mis;
        /**
        * 大象uid
        **/
        3: optional i64 xmUid;

}
