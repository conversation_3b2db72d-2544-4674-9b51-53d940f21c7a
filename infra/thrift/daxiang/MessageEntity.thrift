namespace java com.sankuai.xm.openplatform.api.entity

include "CommonEntity.thrift"
include "GroupEntity.thrift"

enum MessageType {
        IM_MSG_TYPE_TEXT = 1;
        IM_MSG_TYPE_AUDIO = 2;
        IM_MSG_TYPE_VIDEO = 3;
        IM_MSG_TYPE_IMAGE = 4;
        IM_MSG_TYPE_CALENDAR = 5;
        IM_MSG_TYPE_LINK = 6;
        IM_MSG_TYPE_MULTI_LINK = 7;
        IM_MSG_TYPE_FILE = 8;
        IM_MSG_TYPE_GPS = 9;
        IM_MSG_TYPE_VCARD = 10;
        IM_MSG_TYPE_EMOTION = 11;
        IM_MSG_TYPE_EVENT = 12;
        IM_MSG_TYPE_CUSTOM = 13;
        IM_MSG_TYPE_NOTICE = 14;
        IM_MSG_TYPE_CALL = 15;
        IM_MSG_TYPE_REDPACKET = 16;
        IM_MSG_TYPE_GENERAL = 17;
        IM_MSG_TYPE_GVCARD = 18;
        IM_MSG_TYPE_NEWEMOTION = 19;
        IM_MSG_TYPE_QUOTE = 20;
}

enum MessageTransmissionTypeEnum {
        DYNAMIC_MESSAGE_UPDATE = 1;
}

struct MessageInfo {
        1: string message;
        2: MessageType messageType;
        3: CommonEntity.UserIdentify senderIdentify;
}

enum ChatType {
        CHAT,
        GROUP_CHAT,
        PUB_CHAT
}

struct ChatListFilter {
        1: list<ChatType> chatTypes;
        2: list<GroupEntity.GroupTypeEnum> groupTypes;
}

struct ChatListReq {
        1: CommonEntity.UserIdentify userIdentify;
        2: i64 startTime;
        3: i64 endTime;
        4: i32 limit;
        5: ChatListFilter chatListFilter;
}

struct ChatListItem {
        1: i64 chatId;
        2: ChatType chatType;
        3: string chatAvatarUrl;
        4: string chatName;
}

struct ChatListResp {
        1: CommonEntity.RespStatus status;
        2: list<ChatListItem> chatList;
}

struct TransMissionMessage {
        1: string msgId;
        2: string msgDetail;
}

struct MessageTransmissionReq {
        1: required list<CommonEntity.UserIdentify> transmissionUids;
        2: required TransMissionMessage message;
        3: required MessageTransmissionTypeEnum transmissionTypeEnum;
}

struct MessageTransmissionResItem {
        1: list<CommonEntity.UserIdentify> transmissionFailedUids;
}

struct MessageTransmissionResp {
        1: CommonEntity.RespStatus status;
        2: MessageTransmissionResItem transmissionItemList;
}

struct PubInfo{
        1: string appkey;
        2: string token;
        3: i64 pubUid;
}

struct SendTextNoticeReq {
        1: required i64 fromuid;
        2: required ChatType chatType;
        3: required i64 guid;
        4: optional list<i64> uids;
        5: required string text;
        6: optional string opt;
        7: optional PubInfo pubInfo;
        8: optional i16 channel;
}

struct TemplateContent{
        1: i64 templateId;
        2: list<i64> uids;
        3: map<string,string> values;
}

struct DynamicContentInfo{
        1: i64 templateId;
        2: map<string,string> values;
}

struct InitTemplateContent {
        1: i64 templateId;
        2: map<string,string> values;
}

struct DynamicInfo {
        1: required string bizRequestId;
        2: required list<TemplateContent> templateContents;
        3: optional InitTemplateContent initContent;
        4: optional string serialNum;
}

struct SendMsgInfo {
        1: optional string type;
        2: optional string body;
        3: optional string extension;
        4: required bool isDynamicMsg;
        5: optional DynamicInfo dynamicInfo;
}

struct SendMsgDetail {
        1: map<string,string> messageMap;
        2: map<string,string> failMap;
}

struct SendChatMsgByRobotReq {
        1: required list<i64> receiverIds;
        3: required SendMsgInfo sendMsgInfo;
}

struct SendChatMsgByRobotRes {
        1: CommonEntity.RespStatus status;
        2: SendMsgDetail detail;
}

struct SendGroupMsgByRobotReq {
        1: required i64 gid;
        3: required SendMsgInfo sendMsgInfo;
}

struct SendGroupMsgByRobotRes {
        1: CommonEntity.RespStatus status;
        2: SendMsgDetail detail;
}

struct SendGroupMsgByUidReq {
        1: required i64 senderId;
        2: required i64 recevierId;
        3: required SendMsgInfo sendMsgInfo;
}

struct SendGroupMsgByUidRes {
        1: CommonEntity.RespStatus status;
        2: SendMsgDetail detail;
}

struct DrawChatMsgReq {
        1: required i64 msgId;
}

struct DrawGroupMsgReq {
        1: required i64 msgId;
        2: required i64 gid;
}

struct UpdateChatMsgReq {
        1: required i64 msgId;
        2: required TemplateContent content;
}

struct UpdateGroupMsgReq {
        1: required i64 msgId;
        2: required TemplateContent content;
        3: required i64 gid;
}

struct UpdateMsgDetail {
        1: map<i64,i64> messageMap;
        2: map<i64,string> failMap;
}

struct UpdateChatMsgResp {
        1: CommonEntity.RespStatus status;
        2: UpdateMsgDetail detail;
}

struct UpdateGroupMsgResp {
        1: CommonEntity.RespStatus status;
        2: UpdateMsgDetail detail;
}

struct PubMsg {
        1: i64 msgId;
        2: i64 fromId;
        3: i64 toId;
        4: i32 direction;
        5: bool isCancel;
        6: i64 cts;
        7: i32 type;
        8: string message;
        9: string msgExt;
}

struct GroupMsg {
        1: i64 msgId;
        2: i64 fromUid;
        3: i64 fromPubId;
        4: i64 gid;
        5: bool isCancel;
        6: i64 cts;
        7: i32 type;
        8: string message;
        9: string msgExt;
}

struct HistoryPubMsgReq {
        1: required i64 uid;
        2: required i64 startTime;
        3: required i64 endTime;
        4: optional i64 pageToken;
        5: optional i32 pageSize;
}

struct HistoryPubMsgResp {
        1: CommonEntity.RespStatus status;
        2: bool hasMore;
        3: i64 pageToken;
        4: list<PubMsg> pubMsgList;
}

struct HistoryGroupMsgReq {
        1: required i64 gid;
        2: required i64 startTime;
        3: required i64 endTime;
        4: optional i64 pageToken;
        5: optional i32 pageSize;
}

struct HistoryGroupMsgResp {
        1: CommonEntity.RespStatus status;
        2: bool hasMore;
        3: i64 pageToken;
        4: list<GroupMsg> groupMsgList;
}

struct PriorityMsgReceiver {
        1: i64 uid;
        2: i64 msgId;
}

enum PriorityChatTypeEnum {
        CHAT,
        GROUP_CHAT,
        PUB_CHAT,
        NONE
}

enum ChannelTypeEnum {
        IMPORTANT,
        URGENT
}

struct PriorityChannel {
        1: required ChannelTypeEnum channelType;
        2: required i32 templateId;
        3: optional string data;
}

struct PriorityChannelReq {
        1: required string pushId;
        2: required PriorityChatTypeEnum chatType;
        3: optional i64 chatId;
        4: required list<PriorityMsgReceiver> receiverList;
        5: required PriorityChannel channel;
}

struct PriorityChannelResp {
        1: CommonEntity.RespStatus status;
        2: map<i64,i32> failedReceiveList;
}

struct PrePriorityChannelReq {
        1: required string pushId;
        2: required PriorityChannel channel;
}

struct PrePriorityChannelResp {
        1: CommonEntity.RespStatus status;
        2: i64 preId;
}

struct PriorityChannelWithPreIdReq {
        1: required i64 preId;
        2: required PriorityChatTypeEnum chatType;
        3: optional i64 chatId;
        4: required list<PriorityMsgReceiver> receiverList;
}

struct PriorityChannelWithPreIdResp {
        1: CommonEntity.RespStatus status;
        2: map<i64,i32> failedReceiveList;
}

struct PubMsgReq {
        1: required i64 msgId;
}
struct PubMsgResp {
        1: CommonEntity.RespStatus status;
        2: PubMsg msg;
}
struct GroupMsgReq {
        1: required i64 gid;
        2: required i64 msgId;
}
struct GroupMsgResp {
        1: CommonEntity.RespStatus status;
        2: GroupMsg msg;
}
struct ChatMsg {
        1: i64 msgId;
        2: i64 fromId;
        3: i64 toId;
        4: i32 direction;
        5: bool isCancel;
        6: i64 cts;
        7: i32 type;
        8: string message;
        9: string msgExt;
}
struct MergeMsgCondition {
        1: required i64 msgId;
        2: required ChatType chatType;
}
struct MergeMsgReq {
        1: required list<MergeMsgCondition> mergeCondition;
}
struct MergeMsgResp {
        1: CommonEntity.RespStatus status;
        2: ChatType originType;
        3: list<ChatMsg> chatMsgList;
        4: list<PubMsg> pubMsgList;
        5: list<GroupMsg> groupMsgList;
}

struct DrawPriorityMsgReq {
        1: required string pushId;
        2: required list<i64> receiverList;
}

struct DrawPriorityMsgResp {
        1: CommonEntity.RespStatus status;
}

struct SendMergeMsgReq {
        1: ChatType fromChatType;
        2: i64 fromChatId;
        3: list<i64> fromMsgIds;
        4: ChatType toChatType;
        5: i64 toChatId;
}

struct SendMergeMsgData {
        1: i64 successMsgId;
}

struct SendMergeMsgResp {
        1: CommonEntity.RespStatus status;
        2: SendMergeMsgData data;
}

