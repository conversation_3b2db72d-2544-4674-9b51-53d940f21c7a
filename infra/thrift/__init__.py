#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@author: auto-generated by idl2hints from python mtthrift(https://km.sankuai.com/page/135649157)
"""
from pathlib import Path
from types import ModuleType
from typing import Dict

import thriftpy2

_interfaces_path = Path("/infra/thrift")
_interfaces: Dict[str, ModuleType] = {}


def __getattr__(name):
    try:
        return _interfaces[name]
    except KeyError:
        interface = thriftpy2.load(str(_interfaces_path.joinpath(f"{name}.thrift")), module_name=f"{name}_thrift")
        _interfaces[name] = interface
        return interface
