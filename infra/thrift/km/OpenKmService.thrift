namespace java com.sankuai.xm.openplatform.api.service.open

include "CommonEntity.thrift"

enum AccessTypeEnum {
        ORG = 0,
        MEMBER = 1,
        EMAIL_GROUP = 3,
        DX_GROUP = 4
}

enum AccessScopeEnum {
        READ = 0,
        EDIT = 1,
        ADD = 2,
        DELETE = 3,
        MANAGE = 4,
        COMMENT = 5
}

enum AddAccessStatusEnum {
        ADD_SUCCESS = 0,
        ADD_FAIL = 1,
        HAS_AUTH = 2
}

enum CitadelDocType{
        NORMAL = 1,
        COLLABORATION = 2
}

struct AccessDataItem {
        1: AccessTypeEnum accessType;
        2: string authedName;
        3: list<string> position;
        4: list<string> contractType;
        5: AccessScopeEnum accessScope;
}

struct HasKmFileAccessPermissionRequest {
        1: string kmFileId;
        2: i32 eid;
        3: AccessDataItem accesssData;
}
struct HasKmFileAccessPermissionResult {
        1: CommonEntity.RespStatus status;
        2: bool hasPermission;
}

struct AddKmFileAccessScopeRequest{
        1: CommonEntity.UserIdentify user;
        2: i32 eid;
        3: string kmFileId;
        4: AccessDataItem accessData;
        5: i64 empId;
}

struct AddKmFileAccessScopeResult{
        1: CommonEntity.RespStatus status;
        2: AddAccessStatusEnum addAccessStatus;
}

struct GetKmFileStructureDataRequest{
        1: string kmFileId;
}

struct KmFileStructureData {
        1: i64 updateTime;
}

struct GetKmFileStructureDataResult{
        1: CommonEntity.RespStatus status;
        2: KmFileStructureData kmFileStructureData;
}

struct CollaborationContentReq{
        1: i64 operatorEmpId;
        2: string templateId;
        3: string spaceId;
        4: string parentId;
        5: string title;
        6: string copyFromContentId;
        7: string content;
}

struct CollaborationContentResp{
        1: CommonEntity.RespStatus status;
        2: string info;
}

struct UpdateCollaborationContentReq{
        1: required i64 operatorEmpId;
        2: required string contentId;
        3: required string content;
}

struct UpdateCollaborationContentResp{
        1: CommonEntity.RespStatus status;
}

struct GetContentMetaReq{
        1: required i64 operatorEmpId;
        2: required i64 contentId;
}

struct GetCollaborationContentReq{
        1: i64 operatorEmpId;
        2: required i64 contentId;
        3: string appId;
}

struct GetContentMetaWithTicketReq{
        1: required i64 contentId;
}

struct ContentMetaInfo{
        1: i64 contentId;
        2: string contentTitle;
        3: i64 spaceId;
        4: i64 createTime;
        5: i64 lastModifyTime;
        6: i64 owner;
        7: i32 secretLevel;
        8: i64 creatorEmpId;
        9: i64 lastModifierEmpId;
        10: i64 parentId;
        11: i32 version;
        12: CitadelDocType citadelDocType;
}

struct GetContentMetaResp{
        1: CommonEntity.RespStatus status;
        2: ContentMetaInfo contentMetaInfo;
}

struct GetDocContentInfo{
        1: string content;
}

struct GetCollaborationContentResp{
        1: CommonEntity.RespStatus status;
        2: GetDocContentInfo getDocContentInfo;
}

struct GetContentProfileReq{
        1: required i64 operatorEmpId;
        2: required i64 contentId;
        3: required set<string> features;
}

struct Feature{
        1: bool success;
        2: string info;
}

struct ContentProfileInfo{
        1: map<string,Feature> features;
}

struct GetContentProfileResp{
        1: CommonEntity.RespStatus status;
        2: ContentProfileInfo contentProfileInfo;
}

struct OrganizationDetail{
        1: optional i64 orgId;
        2: optional list<i64> roles;
        3: optional list<i64> contractTypes;
}

struct UpdatePermissionReq{
        1: required i64 operatorEmpId;
        2: required i64 contentId;
        3: required i64 permissionId;
        4: optional i32 permissionRole;
        5: optional OrganizationDetail organizationDetail;
}

struct UpdatePermissionResp{
        1: CommonEntity.RespStatus status;
}

struct DeletePermissionReq{
        1: required i64 operatorEmpId;
        2: required i64 contentId;
        3: required i64 permissionId;
}

struct DeletePermissionResp{
        1: CommonEntity.RespStatus status;
}

struct GetPermissionsReq{
        1: optional i64 operatorEmpId;
        2: required i64 contentId;
        3: required i32 permissionAgentType;
        4: optional i32 pageNo;
        5: optional i32 pageSize;
}

struct OrganizationInfo{
        1: i64 orgId;
        2: list<i64> roles;
        3: list<i64> contractTypes;
}

struct PermissionAgentInfo{
        1: i32 type;
        2: OrganizationInfo organizationInfo;
        3: i32 employeeId;
        4: string emailGroup;
        5: i32 xmGroup;
}

struct PermissionInfo{
        1: i64 permissionId;
        2: i32 permissionRole;
        3: PermissionAgentInfo permissionAgentInfo;
        4: bool inherited;
}

struct GetPermissionsResp{
        1: CommonEntity.RespStatus status;
        2: list<PermissionInfo>  permissionResults;
}

service XmOpenKmServiceI {

        HasKmFileAccessPermissionResult hasKmFileAccessPermission (-1:string accessToken, -2:HasKmFileAccessPermissionRequest request);

        AddKmFileAccessScopeResult addKmFileAccessScope (-1:string accessToken, -2:AddKmFileAccessScopeRequest request);

        GetKmFileStructureDataResult getKmFileStructureData (-1:string accessToken, -2:GetKmFileStructureDataRequest request);

        CollaborationContentResp addCollaborationContent (-1:string accessToken, -2:CollaborationContentReq request);

        UpdateCollaborationContentResp updateCollaborationContent(-1:string accessToken, -2:UpdateCollaborationContentReq req);

        GetContentMetaResp getContentMeta(-1:string accessToken, -2:GetContentMetaReq req);

        GetContentMetaResp getContentMetaWithTicket(-1:string accessToken, -2:string secureTicket, -3:GetContentMetaWithTicketReq req);

        GetCollaborationContentResp getCollaborationContentWithTicket(-1:string accessToken, -2:string secureTicket, -3:GetCollaborationContentReq req);

        GetContentProfileResp getContentProfile(-1:string accessToken, -2:GetContentProfileReq req);

        UpdatePermissionResp updatePermission(-1:string accessToken, -2:UpdatePermissionReq req);

        DeletePermissionResp deletePermission(-1:string accessToken, -2:DeletePermissionReq req);

        GetPermissionsResp getPermissions(-1:string accessToken, -2:GetPermissionsReq req);
}

