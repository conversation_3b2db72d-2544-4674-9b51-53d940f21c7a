#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Zebra代理适配器
用于将现有的数据库连接替换为Zebra代理连接
"""

import logging
import pymysql
from zebraproxyclient.api.sqlalchemy import create_engine as zebra_create_engine
from zebraproxyclient.config import ZebraConfig
from conf.zebra.config import ZEBRA_APP_KEY, ZEBRA_REF_KEYS, POOL_SIZE, POOL_TIMEOUT, POOL_RECYCLE

# 配置日志
logger = logging.getLogger(__name__)

# 使用PyMySQL作为MySQL驱动
pymysql.install_as_MySQLdb()


def get_zebra_engine(ref_key=None, **kwargs):
    """
    创建Zebra引擎
    
    Args:
        ref_key: 数据库引用Key，如果为None则使用默认引用Key
        **kwargs: 额外的引擎参数
    
    Returns:
        SQLAlchemy引擎实例
    """
    if ref_key is None:
        ref_key = ZEBRA_REF_KEYS["default"]
    
    logger.info(f"创建Zebra引擎，应用: {ZEBRA_APP_KEY}, 引用: {ref_key}")
    
    # 创建Zebra配置
    zebra_config = ZebraConfig(
        appname=ZEBRA_APP_KEY,
        ref_key=ref_key
    )
    
    # 设置默认连接池参数
    engine_kwargs = {
        'pool_size': POOL_SIZE,
        'pool_timeout': POOL_TIMEOUT,
        'pool_recycle': POOL_RECYCLE
    }
    
    # 更新用户传入的参数
    engine_kwargs.update(kwargs)
    
    # 使用zebra创建引擎
    return zebra_create_engine(zebra_config, **engine_kwargs)


def hack_flask_sqlalchemy(db, ref_key=None):
    """
    替换Flask-SQLAlchemy的引擎创建逻辑，使用Zebra创建引擎
    
    Args:
        db: Flask-SQLAlchemy实例
        ref_key: 数据库引用Key，如果为None则使用默认引用Key
    """
    if ref_key is None:
        ref_key = ZEBRA_REF_KEYS["default"]
    
    logger.info(f"正在进行Flask-SQLAlchemy引擎替换，应用: {ZEBRA_APP_KEY}, 引用: {ref_key}")
    
    # 创建Zebra配置
    zebra_config = ZebraConfig(
        appname=ZEBRA_APP_KEY,
        ref_key=ref_key
    )
    
    def _make_engine(bind_key, options, app):
        # 移除不需要的参数
        options.pop("url", None)
        options.setdefault("connect_args", {}).pop("check_same_thread", None)
        # 使用zebra创建引擎
        return zebra_create_engine(zebra_config, **options)

    def _create_engine(sa_url, engine_opts):
        # 移除不需要的参数
        engine_opts.setdefault("connect_args", {}).pop("check_same_thread", None)
        # 使用zebra创建引擎
        return zebra_create_engine(zebra_config, **engine_opts)
    
    # 替换Flask-SQLAlchemy的引擎创建方法
    if hasattr(db, "_make_engine"):
        logger.info("替换 _make_engine 方法")
        setattr(db, "_make_engine", _make_engine)
    if hasattr(db, "create_engine"):
        logger.info("替换 create_engine 方法")
        setattr(db, "create_engine", _create_engine) 