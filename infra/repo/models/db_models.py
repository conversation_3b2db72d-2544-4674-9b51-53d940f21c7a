#
#  Copyright 2024 The InfiniFlow Authors. All Rights Reserved.
#
#  Licensed under the Apache License, Version 2.0 (the "License");
#  you may not use this file except in compliance with the License.
#  You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
#
import logging
import inspect
import os
import sys
import typing
import operator
from enum import Enum
from functools import wraps
from flask_login import UserMixin
from playhouse.migrate import MySQLMigrator, PostgresqlMigrator
from peewee import (
    BigIntegerField, CharField,
    CompositeKey, IntegerField, TextField, FloatField, DateTimeField,
    Field, Model, Metadata
)
from playhouse.pool import PooledMySQLDatabase, PooledPostgresqlDatabase

from core import settings
import utils
from infra.repo import SerializedType


def singleton(cls, *args, **kw):
    instances = {}

    def _singleton():
        key = str(cls) + str(os.getpid())
        if key not in instances:
            instances[key] = cls(*args, **kw)
        return instances[key]

    return _singleton


CONTINUOUS_FIELD_TYPE = {IntegerField, FloatField, DateTimeField}
AUTO_DATE_TIMESTAMP_FIELD_PREFIX = {
    "create",
    "start",
    "end",
    "update",
    "read_access",
    "write_access"}


class TextFieldType(Enum):
    MYSQL = 'LONGTEXT'
    POSTGRES = 'TEXT'


class LongTextField(TextField):
    field_type = TextFieldType[settings.DATABASE_TYPE.upper()].value


class JSONField(LongTextField):
    default_value = {}

    def __init__(self, object_hook=None, object_pairs_hook=None, **kwargs):
        self._object_hook = object_hook
        self._object_pairs_hook = object_pairs_hook
        super().__init__(**kwargs)

    def db_value(self, value):
        if value is None:
            value = self.default_value
        return utils.json_dumps(value)

    def python_value(self, value):
        if not value:
            return self.default_value
        return utils.json_loads(
            value, object_hook=self._object_hook, object_pairs_hook=self._object_pairs_hook)


class ListField(JSONField):
    default_value = []


class SerializedField(LongTextField):
    def __init__(self, serialized_type=SerializedType.PICKLE,
                 object_hook=None, object_pairs_hook=None, **kwargs):
        self._serialized_type = serialized_type
        self._object_hook = object_hook
        self._object_pairs_hook = object_pairs_hook
        super().__init__(**kwargs)

    def db_value(self, value):
        if self._serialized_type == SerializedType.PICKLE:
            return utils.serialize_b64(value, to_str=True)
        elif self._serialized_type == SerializedType.JSON:
            if value is None:
                return None
            return utils.json_dumps(value, with_type=True)
        else:
            raise ValueError(
                f"the serialized type {self._serialized_type} is not supported")

    def python_value(self, value):
        if self._serialized_type == SerializedType.PICKLE:
            return utils.deserialize_b64(value)
        elif self._serialized_type == SerializedType.JSON:
            if value is None:
                return {}
            return utils.json_loads(
                value, object_hook=self._object_hook, object_pairs_hook=self._object_pairs_hook)
        else:
            raise ValueError(
                f"the serialized type {self._serialized_type} is not supported")


def is_continuous_field(cls: typing.Type) -> bool:
    if cls in CONTINUOUS_FIELD_TYPE:
        return True
    for p in cls.__bases__:
        if p in CONTINUOUS_FIELD_TYPE:
            return True
        elif p is not Field and p is not object:
            if is_continuous_field(p):
                return True
    else:
        return False


def auto_date_timestamp_field():
    return {f"{f}_time" for f in AUTO_DATE_TIMESTAMP_FIELD_PREFIX}


def auto_date_timestamp_db_field():
    return {f"f_{f}_time" for f in AUTO_DATE_TIMESTAMP_FIELD_PREFIX}


def remove_field_name_prefix(field_name):
    return field_name[2:] if field_name.startswith('f_') else field_name


class BaseModel(Model):
    _meta = None
    create_time = BigIntegerField(null=True, index=True)
    create_date = DateTimeField(null=True, index=True)
    update_time = BigIntegerField(null=True, index=True)
    update_date = DateTimeField(null=True, index=True)

    def __init__(self, *args, **kwargs):
        super().__init__(args, kwargs)
        self._meta = None

    def to_json(self):
        # This function is obsolete
        return self.to_dict()

    def to_dict(self):
        return self.__dict__['__data__']

    def to_human_model_dict(self, only_primary_with: list = None):
        model_dict = self.__dict__['__data__']

        if not only_primary_with:
            return {remove_field_name_prefix(
                k): v for k, v in model_dict.items()}

        human_model_dict = {}
        for k in self._meta.primary_key.field_names:
            human_model_dict[remove_field_name_prefix(k)] = model_dict[k]
        for k in only_primary_with:
            human_model_dict[k] = model_dict[f'f_{k}']
        return human_model_dict

    @property
    def meta(self) -> Metadata:
        return self._meta

    @classmethod
    def get_primary_keys_name(cls):
        return cls._meta.primary_key.field_names if isinstance(cls._meta.primary_key, CompositeKey) else [
            cls._meta.primary_key.name]

    @classmethod
    def getter_by(cls, attr):
        return operator.attrgetter(attr)(cls)

    @classmethod
    def query(cls, reverse=None, order_by=None, **kwargs):
        filters = []
        for f_n, f_v in kwargs.items():
            attr_name = '%s' % f_n
            if not hasattr(cls, attr_name) or f_v is None:
                continue
            if type(f_v) in {list, set}:
                f_v = list(f_v)
                if is_continuous_field(type(getattr(cls, attr_name))):
                    if len(f_v) == 2:
                        for i, v in enumerate(f_v):
                            if isinstance(
                                    v, str) and f_n in auto_date_timestamp_field():
                                # time type: %Y-%m-%d %H:%M:%S
                                f_v[i] = utils.date_string_to_timestamp(v)
                        lt_value = f_v[0]
                        gt_value = f_v[1]
                        if lt_value is not None and gt_value is not None:
                            filters.append(
                                cls.getter_by(attr_name).between(
                                    lt_value, gt_value))
                        elif lt_value is not None:
                            filters.append(
                                operator.attrgetter(attr_name)(cls) >= lt_value)
                        elif gt_value is not None:
                            filters.append(
                                operator.attrgetter(attr_name)(cls) <= gt_value)
                else:
                    filters.append(operator.attrgetter(attr_name)(cls) << f_v)
            else:
                filters.append(operator.attrgetter(attr_name)(cls) == f_v)
        if filters:
            query_records = cls.select().where(*filters)
            if reverse is not None:
                if not order_by or not hasattr(cls, f"{order_by}"):
                    order_by = "create_time"
                if reverse is True:
                    query_records = query_records.order_by(
                        cls.getter_by(f"{order_by}").desc())
                elif reverse is False:
                    query_records = query_records.order_by(
                        cls.getter_by(f"{order_by}").asc())
            return [query_record for query_record in query_records]
        else:
            return []

    @classmethod
    def insert(cls, __data=None, **insert):
        if isinstance(__data, dict) and __data:
            __data[cls._meta.combined["create_time"]
            ] = utils.current_timestamp()
        if insert:
            insert["create_time"] = utils.current_timestamp()

        return super().insert(__data, **insert)

    # update and insert will call this method
    @classmethod
    def _normalize_data(cls, data, kwargs):
        normalized = super()._normalize_data(data, kwargs)
        if not normalized:
            return {}

        normalized[cls._meta.combined["update_time"]
        ] = utils.current_timestamp()

        for f_n in AUTO_DATE_TIMESTAMP_FIELD_PREFIX:
            if {f"{f_n}_time", f"{f_n}_date"}.issubset(cls._meta.combined.keys()) and \
                    cls._meta.combined[f"{f_n}_time"] in normalized and \
                    normalized[cls._meta.combined[f"{f_n}_time"]] is not None:
                normalized[cls._meta.combined[f"{f_n}_date"]] = utils.timestamp_to_date(
                    normalized[cls._meta.combined[f"{f_n}_time"]])

        return normalized


class JsonSerializedField(SerializedField):
    def __init__(self, object_hook=utils.from_dict_hook,
                 object_pairs_hook=None, **kwargs):
        super(JsonSerializedField, self).__init__(serialized_type=SerializedType.JSON, object_hook=object_hook,
                                                  object_pairs_hook=object_pairs_hook, **kwargs)


class PooledDatabase(Enum):
    MYSQL = PooledMySQLDatabase
    POSTGRES = PooledPostgresqlDatabase


class DatabaseMigrator(Enum):
    MYSQL = MySQLMigrator
    POSTGRES = PostgresqlMigrator


@singleton
class BaseDataBase:
    def __init__(self):
        # 从环境变量获取数据库连接方式，默认为标准连接(0)，Zebra连接为1
        use_zebra = os.environ.get('USE_ZEBRA', '1') == '1'

        # 获取数据库类型（mysql或postgres）
        db_type = settings.DATABASE_TYPE.upper()

        if use_zebra:
            # 使用Zebra代理连接到公司MySQL
            try:
                from infra.database.adpter.zebra_peewee_adapter import ZebraPooledMySQLDatabase
                from utils.config.config_uitls import get_zebra_config

                # 使用新的配置加载机制获取Zebra配置
                zebra_config = get_zebra_config()

                # 获取必要的参数
                ref_key = zebra_config.get("ref_key")

                # 从标准数据库配置中获取其他连接参数
                database_config = settings.DATABASE.copy()
                db_name = database_config.pop("name")

                self.database_connection = ZebraPooledMySQLDatabase(
                    db_name,
                    ref_key=ref_key,
                    **database_config
                )
                logging.info(f'已成功初始化Zebra MySQL数据库连接，引用key: {ref_key}')
            except Exception as e:
                logging.exception(f"初始化Zebra连接失败: {str(e)}")
                logging.warning("回退到标准数据库连接方式")
                # 回退到标准连接方式
                database_config = settings.DATABASE.copy()
                db_name = database_config.pop("name")
                self.database_connection = PooledDatabase[db_type].value(db_name, **database_config)
                logging.info('已回退到标准数据库连接')
        else:
            # 使用标准连接方式
            database_config = settings.DATABASE.copy()
            db_name = database_config.pop("name")
            self.database_connection = PooledDatabase[db_type].value(db_name, **database_config)
            logging.info(f'已成功初始化{db_type}标准数据库连接')



class MysqlDatabaseLock:
    def __init__(self, lock_name, timeout=10, db=None):
        self.lock_name = lock_name
        self.timeout = int(timeout)
        self.db = db if db else DB
        logging.info(f"[数据库锁] 初始化MySQL锁: name={lock_name}, timeout={timeout}")

    def lock(self):
        # SQL parameters only support %s format placeholders
        logging.info(f"[数据库锁] 开始获取MySQL锁: name={self.lock_name}, timeout={self.timeout}")
        try:
            cursor = self.db.execute_sql(
                "SELECT GET_LOCK(%s, %s)", (self.lock_name, self.timeout))
            ret = cursor.fetchone()
            if ret[0] == 0:
                logging.error(f"[数据库锁] 获取MySQL锁超时: name={self.lock_name}, timeout={self.timeout}")
                raise Exception(f'acquire mysql lock {self.lock_name} timeout')
            elif ret[0] == 1:
                logging.info(f"[数据库锁] 成功获取MySQL锁: name={self.lock_name}")
                return True
            else:
                logging.error(f"[数据库锁] 获取MySQL锁失败: name={self.lock_name}, result={ret[0]}")
                raise Exception(f'failed to acquire lock {self.lock_name}')
        except Exception as e:
            logging.exception(f"[数据库锁] 获取MySQL锁异常: name={self.lock_name}, error={str(e)}")
            raise

    def unlock(self):
        logging.info(f"[数据库锁] 开始释放MySQL锁: name={self.lock_name}")
        try:
            cursor = self.db.execute_sql(
                "SELECT RELEASE_LOCK(%s)", (self.lock_name,))
            ret = cursor.fetchone()
            if ret[0] == 0:
                logging.error(f"[数据库锁] 释放MySQL锁失败(非当前线程持有): name={self.lock_name}")
                raise Exception(
                    f'mysql lock {self.lock_name} was not established by this thread')
            elif ret[0] == 1:
                logging.info(f"[数据库锁] 成功释放MySQL锁: name={self.lock_name}")
                return True
            else:
                logging.error(f"[数据库锁] 释放MySQL锁失败(锁不存在): name={self.lock_name}")
                raise Exception(f'mysql lock {self.lock_name} does not exist')
        except Exception as e:
            logging.exception(f"[数据库锁] 释放MySQL锁异常: name={self.lock_name}, error={str(e)}")
            raise

    def __enter__(self):
        if isinstance(self.db, PooledMySQLDatabase):
            try:
                self.lock()
            except Exception as e:
                logging.error(f"[数据库锁] 进入上下文获取锁失败: name={self.lock_name}, error={str(e)}")
                raise
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        if isinstance(self.db, PooledMySQLDatabase):
            try:
                self.unlock()
            except Exception as e:
                logging.error(f"[数据库锁] 退出上下文释放锁失败: name={self.lock_name}, error={str(e)}")
                # 不抛出异常，避免掩盖原始异常
                logging.exception("原始异常信息:")

    def __call__(self, func):
        @wraps(func)
        def magic(*args, **kwargs):
            with self:
                return func(*args, **kwargs)

        return magic


class DatabaseLock(Enum):
    MYSQL = MysqlDatabaseLock


DB = BaseDataBase().database_connection
DB.lock = DatabaseLock[settings.DATABASE_TYPE.upper()].value


def close_connection():
    try:
        if DB:
            DB.close_stale(age=30)
    except Exception as e:
        logging.exception(e)


class DataBaseModel(BaseModel):
    class Meta:
        database = DB


@DB.connection_context()
def init_database_tables(alter_fields=[]):
    members = inspect.getmembers(sys.modules[__name__], inspect.isclass)
    table_objs = []
    create_failed_list = []
    for name, obj in members:
        if obj != DataBaseModel and issubclass(obj, DataBaseModel):
            table_objs.append(obj)
            logging.debug(f"start create table {obj.__name__}")
            try:
                obj.create_table()
                logging.debug(f"create table success: {obj.__name__}")
            except Exception as e:
                logging.exception(e)
                create_failed_list.append(obj.__name__)
    if create_failed_list:
        logging.error(f"create tables failed: {create_failed_list}")
        raise Exception(f"create tables failed: {create_failed_list}")


def fill_db_model_object(model_object, human_model_dict):
    for k, v in human_model_dict.items():
        attr_name = '%s' % k
        if hasattr(model_object.__class__, attr_name):
            setattr(model_object, attr_name, v)
    return model_object


# 示例：只保留User表
class User(DataBaseModel, UserMixin):
    id = CharField(max_length=32, primary_key=True)
    name = CharField(max_length=100, null=False)

    def __str__(self):
        return self.name

    def get_id(self):
        return self.id

    class Meta:
        db_table = "user"


class Employee(BaseModel):
    id = IntegerField(primary_key=True, null=False)
    comment = CharField(null=True)

    class Meta:
        table_name = 'employee'
        # 不自动生成createdAt/updatedAt字段
        # peewee默认不会自动生成，已满足需求
