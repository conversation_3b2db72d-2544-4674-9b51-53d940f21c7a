"""
代码审查日志增强工具
用于详细打印审查结果和依赖分析信息
"""

import json
import logging
from typing import Dict, List, Any, Optional


class CRLogFormatter:
    """代码审查日志格式化器"""
    
    @staticmethod
    def format_cr_result(cr_result: Dict[str, Any], logger: logging.Logger = None) -> str:
        """
        格式化代码审查结果
        
        Args:
            cr_result: 代码审查结果
            logger: 日志记录器
            
        Returns:
            格式化后的字符串
        """
        if not logger:
            logger = logging.getLogger(__name__)
        
        try:
            # 基本信息
            check_branch = cr_result.get('checkBranch', 'unknown')
            sum_result = cr_result.get('sumCheckResult', 'unknown')
            total_problems = cr_result.get('totalProblem', '0')
            result_desc = cr_result.get('resultDesc', '')
            
            # 构建输出
            output_lines = [
                "=" * 80,
                "📋 代码审查结果详情",
                "=" * 80,
                f"🔍 检查分支: {check_branch}",
                f"📊 总体结果: {sum_result}",
                f"🚨 问题总数: {total_problems}",
                f"📝 结果描述: {result_desc}",
                ""
            ]
            
            # 问题列表详情
            problem_list = cr_result.get('problemList', [])
            if problem_list:
                output_lines.append("🔍 问题分类详情:")
                output_lines.append("-" * 60)
                
                for i, problem in enumerate(problem_list, 1):
                    scene = problem.get('scene', 'unknown')
                    num = problem.get('num', '0')
                    check_result = problem.get('checkResult', 'unknown')
                    details = problem.get('detail', [])
                    
                    output_lines.extend([
                        f"  {i}. 场景: {scene}",
                        f"     问题数量: {num}",
                        f"     检查结果: {check_result}",
                    ])
                    
                    if details:
                        output_lines.append(f"     具体问题:")
                        for j, detail in enumerate(details[:5], 1):  # 最多显示5个问题
                            problem_desc = detail.get('problemDesc', '')
                            code_position = detail.get('codePosition', '')
                            output_lines.append(f"       {j}. {problem_desc}")
                            if code_position:
                                output_lines.append(f"          位置: {code_position}")
                        
                        if len(details) > 5:
                            output_lines.append(f"       ... 还有 {len(details) - 5} 个问题")
                    
                    output_lines.append("")
            
            output_lines.append("=" * 80)
            
            formatted_output = "\n".join(output_lines)
            
            # 输出到日志
            logger.info("代码审查结果:")
            for line in output_lines:
                if line.strip():
                    logger.info(line)
            
            return formatted_output
            
        except Exception as e:
            error_msg = f"格式化代码审查结果失败: {str(e)}"
            logger.error(error_msg)
            return error_msg

    @staticmethod
    def format_dependency_analysis(segments: List[Dict[str, Any]], logger: logging.Logger = None) -> str:
        """
        格式化依赖分析结果
        
        Args:
            segments: 代码片段列表（包含依赖信息）
            logger: 日志记录器
            
        Returns:
            格式化后的字符串
        """
        if not logger:
            logger = logging.getLogger(__name__)
        
        try:
            output_lines = [
                "=" * 80,
                "🔗 上下游依赖分析详情",
                "=" * 80,
                f"📊 总片段数: {len(segments)}",
                ""
            ]
            
            total_upstream = 0
            total_downstream = 0
            
            for i, segment in enumerate(segments, 1):
                file_name = segment.get('file', 'unknown')
                start_line = segment.get('start_line', 0)
                end_line = segment.get('end_line', 0)
                segment_type = segment.get('type', 'unknown')
                
                upstream = segment.get('upstream', [])
                downstream = segment.get('downstream', [])
                upstream_code = segment.get('upstream_code', {})
                downstream_code = segment.get('downstream_code', {})
                
                total_upstream += len(upstream)
                total_downstream += len(downstream)
                
                output_lines.extend([
                    f"📄 片段 {i}: {file_name}:{start_line}-{end_line}",
                    f"   类型: {segment_type}",
                    f"   ⬆️  上游依赖: {len(upstream)} 个",
                    f"   ⬇️  下游依赖: {len(downstream)} 个",
                ])
                
                # 显示上游依赖
                if upstream:
                    output_lines.append("   📤 上游依赖列表:")
                    for j, dep in enumerate(upstream[:10], 1):  # 最多显示10个
                        has_code = dep in upstream_code
                        code_info = " (有代码)" if has_code else " (无代码)"
                        output_lines.append(f"      {j}. {dep}{code_info}")
                    
                    if len(upstream) > 10:
                        output_lines.append(f"      ... 还有 {len(upstream) - 10} 个上游依赖")
                
                # 显示下游依赖
                if downstream:
                    output_lines.append("   📥 下游依赖列表:")
                    for j, dep in enumerate(downstream[:10], 1):  # 最多显示10个
                        has_code = dep in downstream_code
                        code_info = " (有代码)" if has_code else " (无代码)"
                        output_lines.append(f"      {j}. {dep}{code_info}")
                    
                    if len(downstream) > 10:
                        output_lines.append(f"      ... 还有 {len(downstream) - 10} 个下游依赖")
                
                output_lines.append("")
            
            # 统计信息
            output_lines.extend([
                "📊 依赖统计:",
                f"   总上游依赖: {total_upstream} 个",
                f"   总下游依赖: {total_downstream} 个",
                f"   平均每片段上游依赖: {total_upstream / len(segments):.1f} 个" if segments else "   平均每片段上游依赖: 0 个",
                f"   平均每片段下游依赖: {total_downstream / len(segments):.1f} 个" if segments else "   平均每片段下游依赖: 0 个",
                "=" * 80
            ])
            
            formatted_output = "\n".join(output_lines)
            
            # 输出到日志
            logger.info("依赖分析结果:")
            for line in output_lines:
                if line.strip():
                    logger.info(line)
            
            return formatted_output
            
        except Exception as e:
            error_msg = f"格式化依赖分析结果失败: {str(e)}"
            logger.error(error_msg)
            return error_msg

    @staticmethod
    def format_code_segments(segments: List[Dict[str, Any]], logger: logging.Logger = None) -> str:
        """
        格式化代码片段信息
        
        Args:
            segments: 代码片段列表
            logger: 日志记录器
            
        Returns:
            格式化后的字符串
        """
        if not logger:
            logger = logging.getLogger(__name__)
        
        try:
            output_lines = [
                "=" * 80,
                "📝 代码片段分析详情",
                "=" * 80,
                f"📊 总片段数: {len(segments)}",
                ""
            ]
            
            for i, segment in enumerate(segments, 1):
                # 获取文件信息，优先使用file字段，然后是file_path
                file_name = segment.get('file', segment.get('file_path', 'unknown'))
                start_line = segment.get('start_line', 0)
                end_line = segment.get('end_line', 0)
                segment_type = segment.get('type', 'unknown')
                content = segment.get('content', '')

                # 构建代码位置信息
                code_position = segment.get('codePosition')
                if not code_position:
                    code_position = f"{file_name}:{start_line}-{end_line}"

                # 计算代码行数
                code_lines = len(content.split('\n')) if content else 0

                # 获取文件类型描述
                type_description = CRLogFormatter._get_type_description(segment_type)

                output_lines.extend([
                    f"📄 片段 {i}: {code_position}",
                    f"   文件: {file_name}",
                    f"   行范围: {start_line}-{end_line}",
                    f"   类型: {segment_type} ({type_description})",
                    f"   代码行数: {code_lines}",
                ])
                
                # 显示代码片段（前几行）
                if content:
                    content_lines = content.split('\n')
                    preview_lines = content_lines[:3]  # 显示前3行
                    output_lines.append("   代码预览:")
                    for line in preview_lines:
                        if line.strip():
                            output_lines.append(f"      {line[:80]}{'...' if len(line) > 80 else ''}")
                    
                    if len(content_lines) > 3:
                        output_lines.append(f"      ... 还有 {len(content_lines) - 3} 行代码")
                
                output_lines.append("")
            
            output_lines.append("=" * 80)
            
            formatted_output = "\n".join(output_lines)
            
            # 输出到日志
            logger.info("代码片段分析结果:")
            for line in output_lines:
                if line.strip():
                    logger.info(line)
            
            return formatted_output
            
        except Exception as e:
            error_msg = f"格式化代码片段信息失败: {str(e)}"
            logger.error(error_msg)
            return error_msg

    @staticmethod
    def log_cr_summary(cr_result: Dict[str, Any], segments: List[Dict[str, Any]], logger: logging.Logger = None):
        """
        输出完整的CR总结日志
        
        Args:
            cr_result: 代码审查结果
            segments: 代码片段列表
            logger: 日志记录器
        """
        if not logger:
            logger = logging.getLogger(__name__)
        
        logger.info("🚀 开始输出代码审查详细日志")
        
        # 输出代码片段信息
        CRLogFormatter.format_code_segments(segments, logger)
        
        # 输出依赖分析信息
        CRLogFormatter.format_dependency_analysis(segments, logger)
        
        # 输出审查结果
        CRLogFormatter.format_cr_result(cr_result, logger)
        
        logger.info("✅ 代码审查详细日志输出完成")

    @staticmethod
    def _get_type_description(file_type: str) -> str:
        """
        获取文件类型的描述

        Args:
            file_type: 文件类型

        Returns:
            str: 文件类型描述
        """
        type_descriptions = {
            'python': 'Python源码',
            'javascript': 'JavaScript源码',
            'typescript': 'TypeScript源码',
            'java': 'Java源码',
            'cpp': 'C++源码',
            'c': 'C源码',
            'go': 'Go源码',
            'rust': 'Rust源码',
            'php': 'PHP源码',
            'ruby': 'Ruby源码',
            'swift': 'Swift源码',
            'kotlin': 'Kotlin源码',
            'scala': 'Scala源码',
            'shell': 'Shell脚本',
            'sql': 'SQL脚本',
            'html': 'HTML文档',
            'css': 'CSS样式',
            'xml': 'XML文档',
            'json': 'JSON配置',
            'yaml': 'YAML配置',
            'toml': 'TOML配置',
            'config': '配置文件',
            'markdown': 'Markdown文档',
            'text': '文本文件',
            'docker': 'Docker配置',
            'makefile': 'Makefile',
            'unknown': '未知类型'
        }

        return type_descriptions.get(file_type, '未知类型')
