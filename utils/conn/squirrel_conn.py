#
#  Copyright 2025 The InfiniFlow Authors. All Rights Reserved.
#
#  Licensed under the Apache License, Version 2.0 (the "License");
#  you may not use this file except in compliance with the License.
#  You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
#
import os
import sys
import logging

# 添加项目根目录到导入路径，解决直接运行时的导入问题
if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

    # 获取当前文件所在目录的上两级，即项目根目录
    project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), "../.."))
    sys.path.insert(0, project_root)

import json
import time
import uuid  # 导入公司的Squirrel SDK
from squirrel.squirrelClient import SquirrelClient

from utils import singleton


class RedisMsg:
    """消息对象封装类，用于处理队列消息的读取和确认"""

    def __init__(self, consumer, queue_name, group_name, msg_id, message):
        """
        初始化消息对象
        
        Args:
            consumer: MTSquirrelDB实例
            queue_name: 队列名称
            group_name: 消费组名称
            msg_id: 消息ID
            message: 消息内容
        """
        self.__consumer = consumer
        self.__queue_name = queue_name
        self.__group_name = group_name
        self.__msg_id = msg_id

        # 处理消息格式
        try:
            if isinstance(message, dict):
                self.__message = message.get("message")
            else:
                self.__message = message

            # 尝试解析JSON字符串
            if isinstance(self.__message, str):
                try:
                    self.__message = json.loads(self.__message)
                except json.JSONDecodeError:
                    pass
        except Exception as e:
            logging.warning(f"[Squirrel队列] 消息格式处理异常: {str(e)}")
            self.__message = message

    def ack(self) -> bool:
        """
        确认消息已被处理
        
        Returns:
            bool: 确认是否成功
        """
        try:
            # 使用hash tag确保所有相关key在同一个slot
            base_key = self.__consumer.build_category_key(f"{{queue}}:{self.__queue_name}")
            pending_key = f"{base_key}:pending"
            group_pending_key = f"{base_key}:pending:{self.__group_name}"
            consumer_key = f"{base_key}:consumer:{self.__group_name}:{self.__consumer.__class__.__name__}"
            logging.info(
                f"[Squirrel队列] 确认消息: queue={self.__queue_name}, msg_id={self.__msg_id}, pending_key={pending_key}, group_pending_key={group_pending_key}, consumer_key={consumer_key}")
            # 执行确认脚本
            result = self.__consumer.REDIS.eval(
                self.__consumer.ack_script,
                3,  # KEYS数量
                pending_key,
                group_pending_key,
                consumer_key,
                self.__msg_id
            )

            if result == 1:
                logging.info(f"[Squirrel队列] 消息确认成功: queue={self.__queue_name}, msg_id={self.__msg_id}")
                return True
            else:
                logging.warning(
                    f"[Squirrel队列] 消息确认失败，消息可能已被确认: queue={self.__queue_name}, msg_id={self.__msg_id}")
                return False

        except Exception as e:
            logging.warning(
                f"[Squirrel队列] 确认消息时发生异常: queue={self.__queue_name}, msg_id={self.__msg_id}, error={str(e)}")
            return False

    def get_message(self):
        """
        获取消息内容
        
        Returns:
            消息内容，可能是字符串、字典或其他类型
        """
        return self.__message

    def get_msg_id(self):
        """
        获取消息ID
        
        Returns:
            str: 消息ID
        """
        return self.__msg_id


# 新增独立的build_rag_category_key函数
def build_rag_category_key(key):
    """
    构建符合公司内部规范的Redis键值
    
    格式: {categoryName}.c{key}_{version}
    
    参数:
        key: 键名
            
    返回:
        str: 构造的键值
        
    示例:
        build_rag_category_key("{queue}:group:processing") 返回 "sg_ai.c{queue}:group:processing_0"
    """
    try:
        _category = "sg_ai"
        _version = 0

        # 简单地包装key
        if key is None:
            key = ""

        result = f"{_category}.c{key}_{_version}"
        logging.debug(f"[键构建] 键: 输入={key}, 结果={result}")
        return result
    except Exception as e:
        logging.error(f"构建category key失败: {str(e)}")
        raise RuntimeError(f"构建category key失败: {str(e)}")


@singleton
class MTSquirrelDB:
    """通过公司Squirrel SDK访问Redis服务的连接类"""

    def __init__(self):
        self.REDIS = None

        # 使用新的配置加载机制获取Squirrel配置
        try:
            from utils.config.config_uitls import get_squirrel_config

            # 获取配置 - 只保留必要参数
            config = get_squirrel_config()

            # 设置属性
            self.cluster_name = config.get("cluster_name")
            self.proxy_config_path = config.get("proxy_config_path")
            self.appkey = config.get("appkey")

            # 记录配置信息
            logging.info(f"Squirrel配置: cluster={self.cluster_name}, appkey={self.appkey}")

            # 初始化Lua脚本
            self._init_lua_scripts()
        except (ImportError, AttributeError) as e:
            logging.warning(f"加载Squirrel配置失败，使用默认值: {str(e)}")
            self.cluster_name = "redis-sg-fission-activity_qa"
            self.proxy_config_path = os.path.join(os.path.dirname(__file__), "squirrel-proxy.conf")
            self.appkey = "com.sankuai.sg.ai"

        self.__open__()

    def _init_lua_scripts(self):
        """初始化Lua脚本"""
        # 生产者脚本：将消息添加到队列并维护消费组信息
        self.producer_script = """
        local queue_key = KEYS[1]
        local pending_key = KEYS[2]
        local group_key = KEYS[3]
        local msg = ARGV[1]
        local msg_id = ARGV[2]
        local max_len = tonumber(ARGV[3])
        
        -- 添加消息到队列，使用哈希表同时存储ID和内容
        redis.call('HSET', queue_key .. ':messages', msg_id, msg)
        redis.call('RPUSH', queue_key, msg_id)  -- 只存储ID
        
        -- 维护队列长度
        if max_len > 0 then
            -- 如果超出长度，移除最旧的消息
            local to_remove = redis.call('LRANGE', queue_key, 0, -max_len-1)
            if #to_remove > 0 then
                redis.call('LTRIM', queue_key, -max_len, -1)
                -- 清理对应的消息内容
                for i, old_id in ipairs(to_remove) do
                    redis.call('HDEL', queue_key .. ':messages', old_id)
                end
            end
        end
        
        -- 记录消息ID到pending列表
        redis.call('HSET', pending_key, msg_id, msg)
        
        -- 更新消费组信息
        local groups = redis.call('SMEMBERS', group_key)
        for i, group in ipairs(groups) do
            local group_pending = queue_key .. ':pending:' .. group
            redis.call('SADD', group_pending, msg_id)
        end
        
        return msg_id
        """

        # 消费者脚本：从队列中获取消息
        self.consumer_script = """
        local queue_key = KEYS[1]
        local pending_key = KEYS[2]
        local group_pending_key = KEYS[3]
        local consumer_key = KEYS[4]
        local msg_id = ARGV[1]
        
        -- 如果指定了消息ID，尝试从pending中获取
        if msg_id ~= '>' then
            local msg = redis.call('HGET', pending_key, msg_id)
            if msg then
                -- 标记消息被当前消费者处理
                redis.call('HSET', consumer_key, msg_id, msg)
                return {msg_id, msg}
            end
            return nil
        end
        
        -- 从队列头部获取消息ID
        local msg_id = redis.call('LPOP', queue_key)
        if not msg_id then
            return nil
        end
        
        -- 获取消息内容
        local msg = redis.call('HGET', queue_key .. ':messages', msg_id)
        if not msg then
            -- ID存在但内容不存在，记录错误并跳过
            redis.call('SADD', queue_key .. ':errors', 'Missing content for ID: ' .. msg_id)
            return nil
        end
        
        -- 记录消息到pending
        redis.call('HSET', pending_key, msg_id, msg)
        redis.call('SADD', group_pending_key, msg_id)
        
        -- 标记消息被当前消费者处理
        redis.call('HSET', consumer_key, msg_id, msg)
        
        return {msg_id, msg}
        """

        # 确认消息脚本
        self.ack_script = """
        local pending_key = KEYS[1]
        local group_pending_key = KEYS[2]
        local consumer_key = KEYS[3]
        local msg_id = ARGV[1]
        
        -- 检查消息是否存在
        if redis.call('HEXISTS', pending_key, msg_id) == 0 then
            return 0
        end
        
        -- 删除消息的处理记录
        redis.call('HDEL', pending_key, msg_id)
        redis.call('SREM', group_pending_key, msg_id)
        redis.call('HDEL', consumer_key, msg_id)
        
        return 1
        """

    def build_category_key(self, key):
        """
        构建符合公司内部规范的Redis键值，调用独立的build_rag_category_key函数
        """
        return build_rag_category_key(key)

    def __open__(self):
        try:
            # 检查Squirrel配置文件是否存在
            if not os.path.exists(self.proxy_config_path):
                logging.error(f"Squirrel配置文件不存在: {self.proxy_config_path}")
                return None

            # 使用Squirrel SDK连接Redis
            self.REDIS = SquirrelClient(
                proxy_config_path=self.proxy_config_path,
                cluster_name=self.cluster_name,
                print_debug_log=True,
                restart_t_healthy=True,
                appkey=self.appkey
            )
            logging.info(f"成功连接Squirrel Redis集群: {self.cluster_name}")
        except Exception as e:
            logging.error(f"连接Squirrel Redis集群失败: {str(e)}")
        return self.REDIS

    def health(self):
        self.REDIS.ping()
        test_key = f"test_health_{uuid.uuid4()}"
        test_value = "test_value"
        # 使用SDK的get_category_key方法构建键名
        category_key = self.build_category_key(test_key)
        self.REDIS.set(category_key, test_value, 3)

        if self.REDIS.get(category_key) == test_value:
            return True
        return False

    def is_alive(self):
        return self.REDIS is not None

    def exist(self, k):
        if not self.REDIS:
            return
        try:
            category_key = self.build_category_key(k)
            return self.REDIS.exists(category_key)
        except Exception as e:
            logging.warning(f"MTSquirrelDB.exist {k} 发生异常: {str(e)}")
            self.__open__()

    def get(self, k):
        if not self.REDIS:
            return
        try:
            category_key = self.build_category_key(k)
            return self.REDIS.get(category_key)
        except Exception as e:
            logging.warning(f"MTSquirrelDB.get {k} 发生异常: {str(e)}")
            self.__open__()

    def set_obj(self, k, obj, exp=3600):
        try:
            category_key = self.build_category_key(k)
            self.REDIS.set(category_key, json.dumps(obj, ensure_ascii=False), exp)
            return True
        except Exception as e:
            logging.warning(f"MTSquirrelDB.set_obj {k} 发生异常: {str(e)}")
            self.__open__()
        return False

    def set(self, k, v, exp=3600):
        try:
            category_key = self.build_category_key(k)
            self.REDIS.set(category_key, v, exp)
            return True
        except Exception as e:
            logging.warning(f"MTSquirrelDB.set {k} 发生异常: {str(e)}")
            self.__open__()
        return False

    def sadd(self, key: str, member: str):
        try:
            category_key = self.build_category_key(key)
            self.REDIS.sadd(category_key, member)
            return True
        except Exception as e:
            logging.warning(f"MTSquirrelDB.sadd {key} 发生异常: {str(e)}")
            self.__open__()
        return False

    def srem(self, key: str, member: str):
        try:
            category_key = self.build_category_key(key)
            self.REDIS.srem(category_key, member)
            return True
        except Exception as e:
            logging.warning(f"MTSquirrelDB.srem {key} 发生异常: {str(e)}")
            self.__open__()
        return False

    def smembers(self, key: str):
        try:
            category_key = self.build_category_key(key)
            res = self.REDIS.smembers(category_key)
            return res
        except Exception as e:
            logging.warning(f"MTSquirrelDB.smembers {key} 发生异常: {str(e)}")
            self.__open__()
        return None

    def zadd(self, key: str, member: str, score: float):
        try:
            category_key = self.build_category_key(key)
            self.REDIS.zadd(category_key, member, score)
            return True
        except Exception as e:
            logging.warning(f"MTSquirrelDB.zadd {key} 发生异常: {str(e)}")
            self.__open__()
        return False

    def zcount(self, key: str, min: float, max: float):
        try:
            category_key = self.build_category_key(key)
            res = self.REDIS.zcount(category_key, min, max)
            return res
        except Exception as e:
            logging.warning(f"MTSquirrelDB.zcount {key} 发生异常: {str(e)}")
            self.__open__()
        return 0

    def zpopmin(self, key: str, count: int):
        """
        获取并删除有序集合中最小得分的元素
        
        由于Squirrel SDK不直接支持zpopmin命令，使用简单的方式替代：
        1. 获取所有成员及其分数
        2. 按分数排序
        3. 删除最小分数的成员并返回结果
        """
        try:
            category_key = self.build_category_key(key)

            # 获取所有成员及分数对
            all_members = self.REDIS.zrange(category_key, 0, -1, withscores=True)
            if not all_members or len(all_members) == 0:
                return []

            # 取前count个成员
            result = all_members[:count]

            # 删除这些成员
            for member, _score in result:
                self.REDIS.zrem(category_key, member)

            return result
        except Exception as e:
            logging.warning(f"MTSquirrelDB.zpopmin {key} 发生异常: {str(e)}")
            self.__open__()
        return []

    def zrangebyscore(self, key: str, min: float, max: float):
        try:
            category_key = self.build_category_key(key)
            res = self.REDIS.zrangebyscore(category_key, min, max)
            return res
        except Exception as e:
            logging.warning(f"MTSquirrelDB.zrangebyscore {key} 发生异常: {str(e)}")
            self.__open__()
        return None

    def pipeline(self):
        return self.REDIS.pipeline()

    def transaction(self, key, value, exp=3600):
        try:
            category_key = self.build_category_key(key)
            # pipeline = self.REDIS.pipeline(transaction=True)
            return self.REDIS.set(category_key, value, exp, nx=True)
            # pipeline.execute()
            # return True
        except Exception as e:
            logging.warning(f"MTSquirrelDB.transaction {key} 发生异常: {str(e)}")
            self.__open__()
        return False

    def queue_product(self, queue, message, exp=3600) -> bool:
        """发送消息到队列"""
        try:
            # 使用hash tag确保所有相关key在同一个slot
            base_key = self.build_category_key(f"{{queue}}:{queue}")
            queue_key = base_key
            pending_key = f"{base_key}:pending"
            group_key = f"{base_key}:groups"

            # 序列化消息
            msg_str = json.dumps({"message": json.dumps(message)})
            msg_id = str(uuid.uuid4())

            # 执行生产者脚本
            result = self.REDIS.eval(
                self.producer_script,
                3,  # KEYS数量
                queue_key,
                pending_key,
                group_key,
                msg_str,
                msg_id,
                10000  # 最大队列长度
            )

            if result:
                logging.info(f"[Squirrel队列] 成功发送消息: queue={queue}, msg_id={msg_id}")
                return True

        except Exception as e:
            logging.exception(f"[Squirrel队列] 发送消息失败: queue={queue}, error={str(e)}")

        return False

    def queue_consumer(self, queue_name, group_name, consumer_name, msg_id=">") -> RedisMsg:
        """从队列消费消息"""
        try:
            # 使用hash tag确保所有相关key在同一个slot
            base_key = self.build_category_key(f"{{queue}}:{queue_name}")
            queue_key = base_key
            messages_key = f"{base_key}:messages"  # 新增消息内容哈希表键
            pending_key = f"{base_key}:pending"
            group_key = f"{base_key}:groups"
            group_pending_key = f"{base_key}:pending:{group_name}"
            consumer_key = f"{base_key}:consumer:{group_name}:{consumer_name}"

            # 确保消费组存在
            self.REDIS.sadd(group_key, group_name)
            # logging.info(f"[Squirrel队列] 消费组: queue={queue_name}, queue_key={queue_key}, group={group_name}, consumer={consumer_name}, consumer_key={consumer_key}, pending_key={pending_key}, group_pending_key={group_pending_key}")
            # 执行消费者脚本
            result = self.REDIS.eval(
                self.consumer_script,
                4,  # KEYS数量
                queue_key,
                pending_key,
                group_pending_key,
                consumer_key,
                str(msg_id)
            )

            if result and len(result) == 2:
                msg_id, message = result
                return RedisMsg(self, queue_name, group_name, msg_id, json.loads(message))

        except Exception as e:
            if "key" in str(e):
                logging.debug(f"[Squirrel队列] 队列不存在: queue={queue_name}")
            else:
                logging.exception(f"[Squirrel队列] 消费消息失败: queue={queue_name}, error={str(e)}")

        return None

    def queue_info(self, queue, group_name) -> dict | None:
        """获取队列信息"""
        try:
            # 使用hash tag确保所有相关key在同一个slot
            base_key = self.build_category_key(f"{{queue}}:{queue}")
            queue_key = base_key
            group_pending_key = f"{base_key}:pending:{group_name}"

            # 获取队列长度和pending消息数量
            queue_len = self.REDIS.llen(queue_key)
            pending_len = self.REDIS.scard(group_pending_key)

            return {
                "name": group_name,
                "consumers": 0,  # 暂不实现具体消费者统计
                "pending": pending_len,
                "last-delivered-id": "0-0",  # 兼容性字段
                "entries-read": queue_len,
                "lag": queue_len - pending_len
            }

        except Exception as e:
            logging.warning(f"[Squirrel队列] 获取队列信息失败: queue={queue}, error={str(e)}")
            return None

    def get_unacked_iterator(self, queue_name, group_name, consumer_name):
        """获取未确认消息的迭代器"""
        try:
            # 使用hash tag确保所有相关key在同一个slot
            base_key = self.build_category_key(f"{{queue}}:{queue_name}")
            group_pending_key = f"{base_key}:pending:{group_name}"

            # 获取所有未确认的消息ID
            pending_msgs = self.REDIS.smembers(group_pending_key)

            for msg_id in pending_msgs:
                # 尝试重新消费每个未确认的消息
                payload = self.queue_consumer(queue_name, group_name, consumer_name, msg_id)
                if payload:
                    yield payload

        except Exception as e:
            if "key" in str(e):
                return
            logging.exception(f"[Squirrel队列] 获取未确认消息失败: queue={queue_name}, error={str(e)}")


class MTSquirrelDistributedLock:
    """Squirrel分布式锁实现，用于在分布式环境中实现互斥访问"""

    def __init__(self, lock_key, timeout=10):
        self.original_lock_key = lock_key
        self.lock_key = build_rag_category_key(lock_key)
        self.lock_value = str(uuid.uuid4())
        self.timeout = timeout

    @staticmethod
    def clean_lock(lock_key):
        """清除指定的锁"""
        category_lock_key = build_rag_category_key(lock_key)
        MT_SQUIRREL_CONN.REDIS.delete(category_lock_key)

    def acquire_lock(self):
        """获取锁，如果获取成功返回True，否则在超时后返回False"""
        end_time = time.time() + self.timeout
        while time.time() < end_time:
            if MT_SQUIRREL_CONN.REDIS.setnx(self.lock_key, self.lock_value):
                return True
            time.sleep(1)
        return False

    def release_lock(self):
        """释放锁，只有当前实例创建的锁才会被释放"""
        if MT_SQUIRREL_CONN.REDIS.get(self.lock_key) == self.lock_value:
            MT_SQUIRREL_CONN.REDIS.delete(self.lock_key)

    def __enter__(self):
        """支持with语句，进入时获取锁"""
        self.acquire_lock()

    def __exit__(self, exception_type, exception_value, exception_traceback):
        """支持with语句，退出时释放锁"""
        self.release_lock()


# 创建全局Squirrel连接实例
MT_SQUIRREL_CONN = MTSquirrelDB()


def test():
    try:
        redis_conn = MT_SQUIRREL_CONN
        # 检查连接是否成功
        if redis_conn.is_alive():
            logging.info("成功连接到Squirrel Redis服务")

            # 健康检查
            try:
                if redis_conn.health():
                    logging.info("Squirrel Redis服务健康状态正常")
                else:
                    logging.error("Squirrel Redis服务健康检查失败")
                    return
            except Exception as e:
                logging.exception(f"健康检查异常: {str(e)}")
                return

            # 基本操作示例
            test_key = "test_sg_ai_key"
            test_value = "这是一个测试值"

            # 设置值 - 不需要在这里调用build_category_key，方法内部会处理
            if redis_conn.set(test_key, test_value, 3600):
                logging.info(f"成功设置键值: {test_key} = {test_value}")
            else:
                logging.error(f"设置键值失败: {test_key}")

            # 获取值
            retrieved_value = redis_conn.get(test_key)
            if retrieved_value:
                logging.info(f"成功获取键值: {test_key} = {retrieved_value}")
            else:
                logging.error(f"获取键值失败: {test_key}")

            # 测试exists命令
            if redis_conn.exist(test_key):
                logging.info(f"键存在检查成功: {test_key}")
            else:
                logging.error(f"键存在检查失败: {test_key}")

            # 测试集合操作
            set_key = "test_sg_ai_set"
            set_member = "测试成员1"

            # 添加集合成员
            if redis_conn.sadd(set_key, set_member):
                logging.info(f"成功添加集合成员: {set_key} <- {set_member}")
            else:
                logging.error(f"添加集合成员失败: {set_key}")

            # 获取集合成员
            members = redis_conn.smembers(set_key)
            if members:
                logging.info(f"成功获取集合成员: {set_key} = {members}")
            else:
                logging.warning(f"获取集合成员为空或失败: {set_key}")

            # 删除集合成员
            if redis_conn.srem(set_key, set_member):
                logging.info(f"成功删除集合成员: {set_key} -> {set_member}")
            else:
                logging.error(f"删除集合成员失败: {set_key}")

            # 测试有序集合操作
            zset_key = "test_sg_ai_zset"
            zset_member1 = "测试成员1"
            zset_member2 = "测试成员2"

            # 添加有序集合成员
            if redis_conn.zadd(zset_key, zset_member1, 1.0):
                logging.info(f"成功添加有序集合成员: {zset_key} <- {zset_member1}:1.0")
            else:
                logging.error(f"添加有序集合成员失败: {zset_key}")

            if redis_conn.zadd(zset_key, zset_member2, 2.0):
                logging.info(f"成功添加有序集合成员: {zset_key} <- {zset_member2}:2.0")

            # 测试zcount
            count = redis_conn.zcount(zset_key, 0, 3)
            logging.info(f"有序集合计数: {zset_key} [0-3] = {count}")

            # 测试zrangebyscore
            range_result = redis_conn.zrangebyscore(zset_key, 0, 3)
            if range_result:
                logging.info(f"有序集合范围查询: {zset_key} [0-3] = {range_result}")
            else:
                logging.warning(f"有序集合范围查询为空或失败: {zset_key}")

            # 测试zpopmin替代实现
            zset_key = "test_sg_ai_zset_pop"
            # 清理可能存在的旧数据
            category_key = redis_conn.build_category_key(zset_key)
            redis_conn.REDIS.delete(category_key)

            # 添加测试数据
            redis_conn.zadd(zset_key, "测试成员A", 1.0)
            redis_conn.zadd(zset_key, "测试成员B", 2.0)
            redis_conn.zadd(zset_key, "测试成员C", 3.0)

            logging.info(f"有序集合添加成员完成: {zset_key}")

            # 测试获取并删除最小分数的成员
            pop_result = redis_conn.zpopmin(zset_key, 2)
            if pop_result and len(pop_result) > 0:
                logging.info(f"有序集合弹出最小值成功: {zset_key} = {pop_result}")

                # 验证剩余成员
                remaining = redis_conn.REDIS.zrange(redis_conn.build_category_key(zset_key), 0, -1)
                logging.info(f"有序集合剩余成员: {zset_key} = {remaining}")

                # 验证剩余成员应该只有"测试成员C"
                if len(remaining) == 1 and remaining[0] == "测试成员C":
                    logging.info(f"有序集合弹出最小值验证成功")
                else:
                    logging.error(f"有序集合弹出最小值验证失败，剩余成员不符合预期")
            else:
                logging.error(f"有序集合弹出最小值失败: {zset_key}")

            # 测试transaction
            transaction_key = "test_sg_ai_transaction"
            # 确保键不存在
            redis_conn.REDIS.delete(redis_conn.build_category_key(transaction_key))

            transaction_value = "事务测试值"
            if redis_conn.transaction(transaction_key, transaction_value, 3600):
                logging.info(f"事务操作成功: {transaction_key} = {transaction_value}")

                # 再次尝试设置应该失败
                if not redis_conn.transaction(transaction_key, "新值", 3600):
                    logging.info(f"事务操作验证成功: 已存在的键无法再次设置")
                else:
                    logging.error(f"事务操作验证失败: 已存在的键可以再次设置")
            else:
                logging.error(f"事务操作失败: {transaction_key}")

            # 测试使用自定义模板的category key
            try:
                user_id = "12345"
                user_data = {"id": user_id, "name": "测试用户", "score": 100}

                # 设置对象值
                if redis_conn.set_obj(f"user:{user_id}", user_data, 3600):
                    logging.info(f"成功设置用户对象: user:{user_id}")

                    # 获取对象值
                    user_value_str = redis_conn.get(f"user:{user_id}")
                    if user_value_str:
                        user_value = json.loads(user_value_str)
                        logging.info(f"成功获取用户对象: user:{user_id} = {user_value}")
                    else:
                        logging.error(f"获取用户对象失败: user:{user_id}")
                else:
                    logging.error(f"设置用户对象失败: user:{user_id}")
            except Exception as e:
                logging.exception(f"用户对象操作失败: {str(e)}")

            # 测试分布式锁
            try:
                lock_key = "test_sg_ai_lock"
                lock = MTSquirrelDistributedLock(lock_key, timeout=5)

                # 获取锁
                if lock.acquire_lock():
                    logging.info(f"成功获取分布式锁: {lock_key}")

                    # 模拟处理逻辑
                    time.sleep(1)

                    # 释放锁
                    lock.release_lock()
                    logging.info(f"成功释放分布式锁: {lock_key}")
                else:
                    logging.error(f"获取分布式锁失败: {lock_key}")

                # 测试with语句
                with MTSquirrelDistributedLock(lock_key, timeout=5):
                    logging.info(f"使用with语句获取分布式锁: {lock_key}")
                    # with块结束后自动释放锁
                logging.info(f"使用with语句自动释放分布式锁: {lock_key}")

                # 测试清除锁
                MTSquirrelDistributedLock.clean_lock(lock_key)
                logging.info(f"手动清除分布式锁: {lock_key}")

            except Exception as e:
                logging.exception(f"测试分布式锁失败: {str(e)}")

            # 测试队列功能
            try:
                queue_name = "test_sg_ai_queue"
                group_name = "test_sg_ai_group"
                consumer_name = "test_consumer"
                message = {"id": str(uuid.uuid4()), "content": "测试队列消息", "timestamp": time.time()}

                # 发送消息到队列
                if redis_conn.queue_product(queue_name, message):
                    logging.info(f"成功发送消息到队列: {queue_name}")

                    # 消费消息
                    msg = redis_conn.queue_consumer(queue_name, group_name, consumer_name)
                    if msg:
                        received_msg = msg.get_message()
                        logging.info(f"成功从队列消费消息: {queue_name}, 消息内容: {received_msg}")

                        # 确认消息
                        if msg.ack():
                            logging.info(f"成功确认队列消息: {queue_name}")
                        else:
                            logging.error(f"确认队列消息失败: {queue_name}")
                    else:
                        logging.warning(f"未能从队列消费到消息: {queue_name}")

                    # 测试获取未确认消息迭代器
                    # 再发送一条消息，不确认，用于测试
                    if redis_conn.queue_product(queue_name, {"id": str(uuid.uuid4()), "content": "未确认测试消息",
                                                             "timestamp": time.time()}):
                        # 先消费但不确认
                        unacked_msg = redis_conn.queue_consumer(queue_name, group_name, consumer_name)
                        if unacked_msg:
                            logging.info(f"成功消费但不确认的消息: {queue_name}")

                            # 测试get_unacked_iterator
                            unacked_count = 0
                            for msg in redis_conn.get_unacked_iterator(queue_name, group_name, consumer_name):
                                unacked_count += 1
                                logging.info(f"获取到未确认消息: {msg.get_message()}")
                                msg.ack()  # 确认消息避免残留

                            logging.info(f"成功获取未确认消息数量: {unacked_count}")

                    # 获取队列信息
                    queue_info = redis_conn.queue_info(queue_name, group_name)
                    if queue_info:
                        logging.info(f"成功获取队列信息: {queue_name}, 信息: {queue_info}")
                    else:
                        logging.warning(f"获取队列信息失败或为空: {queue_name}")
                else:
                    logging.error(f"发送消息到队列失败: {queue_name}")
            except Exception as e:
                logging.exception(f"测试队列功能失败: {str(e)}")

            logging.info("Squirrel Redis操作测试完成")
        else:
            logging.error("连接Squirrel Redis服务失败")
    except Exception as e:
        logging.exception(f"Squirrel Redis连接或操作过程中发生异常: {str(e)}")


if __name__ == "__main__":
    test()
