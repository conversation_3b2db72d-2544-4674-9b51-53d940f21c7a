import mtthrift
# 导入生成的 Thrift 客户端

from infra.thrift import XmOpenKmServiceI, CollaborationContentReq


class KmService:
    def __init__(self):
        # 使用强类型注释生成client用于后续接口调用，注意其实际为stubs.SailorShopIface.SailorShopIface
        # thrift_obj入参必须为服务类，而非数据类
        self.shop_thrift = mtthrift.MTThrift('com.sankuai.dxenterprise.open.gateway',
                                             thrift_obj=XmOpenKmServiceI, # v5开始支持传入对象，v5.4.4也支持v5以下版本的传入thrift IDL文件的绝对路径或pathlib.Path（老工程用户）
                                             env='test',
                                             server_refresh_interval=1800,
                                             # 设置服务器列表刷新时间为半小时，一般用于服务不稳定等情况
                                             client_appkey='com.sankuai.yunzhuan.devhelper'
                                             )

        self.shop_rpc: XmOpenKmServiceI = self.shop_thrift.client


    def create_doc(self, params):
        """创建文档"""
        try:
            access_token = "your_access_token"  # 从配置或认证服务获取

            # 准备请求参数
            req = CollaborationContentReq(
                operatorEmpId=params.get('operatorEmpId'),
                templateId=params.get('templateId'),
                spaceId=params.get('spaceId'),
                parentId=params.get('parentId'),
                title=params.get('title'),
                copyFromContentId=params.get('copyFromContentId'),
                content=params.get('content')
            )

            # 获取客户端
            client, transport = self._get_client(access_token)

            try:
                # 调用 Thrift 服务
                response = client.addCollaborationContent(access_token, req)

                # 处理响应
                if response.status.code == 0:
                    return {
                        'success': True,
                        'data': response.info,
                        'message': 'Document created successfully'
                    }
                else:
                    return {
                        'success': False,
                        'message': response.status.message
                    }
            finally:
                # 关闭连接
                transport.close()

        except Exception as e:
            return {
                'success': False,
                'message': f'Error creating document: {str(e)}'
            }