"""
代码分块相关工具函数
"""
import re
import os
from typing import List, Dict, Any, Optional
from consts.chunk_consts import CONFIG_SUFFIXES, PYTHON_IMPORT_PATTERNS, EXCLUDE_DIRS


def is_import_only_block(content: str) -> bool:
    """
    判断内容是否为纯导入依赖语句（兼容多语言）
    """
    lines = [line.strip() for line in content.splitlines() if line.strip()]
    if not lines:
        return False
    
    # Python
    py_import = re.compile(r'^(import |from .+ import )')
    # Java
    java_import = re.compile(r'^(import |package )')
    # JS/TS
    js_import = re.compile(r'^(import |require$|export .+ from )')
    # C/C++
    c_import = re.compile(r'^(#include )')
    
    for line in lines:
        if not (py_import.match(line) or java_import.match(line) or 
                js_import.match(line) or c_import.match(line)):
            return False
    return True


def detect_source_roots(all_files: List[str]) -> List[str]:
    """
    自动检测项目源码主目录，排除常见依赖库目录
    """
    roots = set()
    for f in all_files:
        parts = f.split(os.sep)
        for i, part in enumerate(parts):
            if part in EXCLUDE_DIRS:
                break
        else:
            # 取第一级目录（如src/、app/、project/等）
            if len(parts) > 1:
                roots.add(parts[0])
    # 若没检测到，默认全部
    return list(roots) if roots else ['.']


def split_diff_by_file(diff_content: str) -> List[tuple]:
    """
    将diff内容按文件拆分，返回每个文件的diff块
    """
    file_blocks = []
    file_regex = r'^diff --git a\/([^\s]+) b\/([^\s]+)$'
    current_file = None
    current_lines = []
    
    for line in diff_content.splitlines():
        match = re.match(file_regex, line)
        if match:
            if current_file and current_lines:
                file_blocks.append((current_file, current_lines))
            current_file = match.group(2)
            current_lines = []
        elif current_file is not None:
            current_lines.append(line)
    
    if current_file and current_lines:
        file_blocks.append((current_file, current_lines))
    return file_blocks


def extract_new_code_from_diff(diff_lines: List[str]) -> str:
    """
    从diff块中提取新增后的代码（只保留+开头的代码行，去掉+号，排除diff元信息行）
    """
    code_lines = []
    for line in diff_lines:
        # 跳过diff元信息行
        if (
            line.startswith('+++') or line.startswith('---') or
            line.startswith('index') or line.startswith('new file mode') or
            line.startswith('deleted file mode') or line.startswith('similarity index') or
            line.startswith('rename from') or line.startswith('rename to') or
            line.startswith('@@')
        ):
            continue
        if line.startswith('+'):
            code_lines.append(line[1:])
        elif not line.startswith('-'):
            code_lines.append(line)
    return '\n'.join(code_lines)


def split_diff_by_continuous_lines(diff_lines: List[str]) -> List[Dict[str, Any]]:
    """
    将diff块按相连的diff行切分为多个片段，返回每个片段的起止行号和内容
    """
    segments = []
    current = []
    current_start = None
    current_end = None
    line_num = 0
    
    for line in diff_lines:
        line_num += 1
        # 跳过diff元信息行
        if (
            line.startswith('+++') or line.startswith('---') or
            line.startswith('index') or line.startswith('diff --git') or
            line.startswith('@@') or line.startswith('new file mode') or
            line.startswith('deleted file mode') or line.startswith('similarity index') or
            line.startswith('rename from') or line.startswith('rename to')
        ):
            continue
        
        # 只保留原始代码内容
        code_line = line
        if line.startswith('+') or line.startswith('-') or line.startswith(' '):
            code_line = line[1:]
        
        if code_line.strip():
            if current_end is not None and line_num == current_end + 1:
                current.append(code_line)
                current_end = line_num
            else:
                if current:
                    segments.append({
                        'start_line': current_start, 
                        'end_line': current_end, 
                        'lines': current
                    })
                current = [code_line]
                current_start = line_num
                current_end = line_num
        else:
            if current:
                segments.append({
                    'start_line': current_start, 
                    'end_line': current_end, 
                    'lines': current
                })
                current = []
                current_start = None
                current_end = None
    
    if current:
        segments.append({
            'start_line': current_start, 
            'end_line': current_end, 
            'lines': current
        })
    
    return segments


def find_best_chunk_for_comment(chunks: List[Dict], file_path: str, 
                               comment_start: int, comment_end: int) -> Optional[Dict]:
    """
    在chunks中查找与评论锚点重叠度最大的chunk
    """
    best_chunk = None
    max_overlap = 0
    
    for chunk in chunks:
        if chunk['file'] != file_path:
            continue
        
        chunk_start, chunk_end = chunk['start_line'], chunk['end_line']
        overlap = min(chunk_end, comment_end) - max(chunk_start, comment_start) + 1
        
        if overlap > 0 and overlap > max_overlap:
            max_overlap = overlap
            best_chunk = chunk
    
    return best_chunk


def safe_get_from_func_index(func_index: Dict, key: str, field: str) -> Any:
    """
    安全地从func_index中获取指定字段的值，支持多种数据结构
    """
    if not func_index or not key:
        return None

    # 归一化key，去除首尾空格
    key_norm = key.strip()
    
    try:
        entry = func_index.get(key_norm)
        if entry is None:
            # 兼容key类型不一致的情况，做宽松匹配
            for k in func_index.keys():
                if k.strip() == key_norm:
                    entry = func_index[k]
                    break
        
        if entry is None:
            # 再宽松一层：全部小写对比
            key_lower = key_norm.lower()
            for k in func_index.keys():
                if k.strip().lower() == key_lower:
                    entry = func_index[k]
                    break
        
        if entry is None:
            return None
            
        if isinstance(entry, list):
            return [e.get(field) for e in entry if isinstance(e, dict) and field in e]
        elif isinstance(entry, dict):
            return entry.get(field)
        else:
            return None
            
    except Exception as e:
        print(f"[safe_get_from_func_index] 获取字段异常: {str(e)}")
        return None


def find_unique_key_for_dep(func_index: Dict, dep_name: str) -> List[str]:
    """
    查找依赖的唯一键，支持多种匹配策略
    """
    if not func_index or not dep_name:
        return []

    keys = []

    # 策略1: 精确匹配全局名
    if f"global:{dep_name}" in func_index:
        keys.append(f"global:{dep_name}")

    # 策略2: 精确匹配唯一名（文件路径:方法名）
    keys.extend([k for k in func_index if k.endswith(f":{dep_name}") and k not in keys])

    # 策略3: 匹配简化名（最后一部分）
    keys.extend([k for k in func_index if k.split(":")[-1] == dep_name and k not in keys])

    # 策略4: Java特殊处理 - 对象.方法名 -> 类名.方法名
    if "." in dep_name and not keys:
        obj_name, method_name = dep_name.rsplit(".", 1)

        # 4.1: 尝试匹配 ClassName.methodName 格式
        for k in func_index:
            if ":" in k:
                func_part = k.split(":", 1)[1]
                if "." in func_part:
                    class_name, func_method = func_part.rsplit(".", 1)
                    if func_method == method_name and k not in keys:
                        keys.append(k)

        # 4.2: 尝试匹配方法名（忽略对象名）
        if not keys:
            keys.extend([k for k in func_index if k.split(":")[-1] == method_name and k not in keys])

    # 策略5: 模糊匹配（包含关系）
    if not keys:
        dep_lower = dep_name.lower()
        for k in func_index:
            if ":" in k:
                func_part = k.split(":", 1)[1].lower()
                if dep_lower in func_part or func_part in dep_lower:
                    if k not in keys:
                        keys.append(k)

    # 策略6: 最后的模糊匹配（部分字符串匹配）
    if not keys and len(dep_name) > 3:
        for k in func_index:
            if ":" in k:
                func_part = k.split(":", 1)[1]
                # 计算相似度
                common_chars = len(set(dep_name.lower()) & set(func_part.lower()))
                similarity = common_chars / max(len(dep_name), len(func_part))
                if similarity > 0.6 and k not in keys:
                    keys.append(k)

    return keys