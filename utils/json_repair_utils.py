#!/usr/bin/env python3
"""
JSON修复工具
专门处理CR结果中的JSON解析错误，特别是targetCode字段中的特殊字符问题
"""

import json
import re
import logging
from typing import Dict, Any, Optional


class JSONRepairUtils:
    """JSON修复工具类"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def repair_json_string(self, json_str: str) -> str:
        """
        修复JSON字符串中的常见问题
        
        Args:
            json_str: 原始JSON字符串
            
        Returns:
            修复后的JSON字符串
        """
        try:
            # 首先尝试直接解析
            json.loads(json_str)
            return json_str
        except json.JSONDecodeError as e:
            self.logger.info(f"JSON解析失败，开始修复: {str(e)}")
            return self._fix_json_string(json_str)
    
    def _fix_json_string(self, json_str: str) -> str:
        """修复JSON字符串的核心逻辑"""
        fixed_str = json_str
        
        # 1. 修复targetCode字段中的特殊字符
        fixed_str = self._fix_target_code_field(fixed_str)
        
        # 2. 修复其他字符串字段中的特殊字符
        fixed_str = self._fix_string_fields(fixed_str)
        
        # 3. 修复不完整的JSON结构
        fixed_str = self._fix_incomplete_json(fixed_str)
        
        # 4. 修复Python格式到JSON格式
        fixed_str = self._fix_python_to_json(fixed_str)
        
        return fixed_str
    
    def _fix_target_code_field(self, json_str: str) -> str:
        """专门修复targetCode字段中的特殊字符"""
        def fix_target_code_value(match):
            field_name = match.group(1)
            original_value = match.group(2)
            
            # 转义特殊字符
            fixed_value = self._escape_string_content(original_value)
            
            return f'"{field_name}": "{fixed_value}"'
        
        # 匹配targetCode字段（包括可能的引号问题）
        pattern = r'"(targetCode)"\s*:\s*"([^"]*(?:"[^"]*)*[^"]*)"'
        fixed_str = re.sub(pattern, fix_target_code_value, json_str, flags=re.DOTALL)
        
        # 处理没有引号包围的targetCode值
        pattern2 = r'"(targetCode)"\s*:\s*([^,}\]]+)'
        def fix_unquoted_target_code(match):
            field_name = match.group(1)
            value = match.group(2).strip()
            
            # 移除可能的尾部引号
            if value.endswith('"'):
                value = value[:-1]
            if value.startswith('"'):
                value = value[1:]
            
            # 转义并重新包装
            fixed_value = self._escape_string_content(value)
            return f'"{field_name}": "{fixed_value}"'
        
        fixed_str = re.sub(pattern2, fix_unquoted_target_code, fixed_str)
        
        return fixed_str
    
    def _fix_string_fields(self, json_str: str) -> str:
        """修复其他字符串字段中的特殊字符"""
        # 常见的字符串字段
        string_fields = ['problem', 'suggestion', 'codePosition', 'level']
        
        for field in string_fields:
            # 修复字段值中的未转义引号
            pattern = f'"{field}"\\s*:\\s*"([^"]*(?:"[^"]*)*[^"]*)"'
            
            def fix_field_value(match):
                value = match.group(1)
                fixed_value = self._escape_string_content(value)
                return f'"{field}": "{fixed_value}"'
            
            json_str = re.sub(pattern, fix_field_value, json_str, flags=re.DOTALL)
        
        return json_str
    
    def _escape_string_content(self, content: str) -> str:
        """转义字符串内容中的特殊字符"""
        if not content:
            return ""
        
        # 转义反斜杠（必须首先处理）
        content = content.replace('\\', '\\\\')
        
        # 转义双引号
        content = content.replace('"', '\\"')
        
        # 转义换行符
        content = content.replace('\n', '\\n')
        content = content.replace('\r', '\\r')
        
        # 转义制表符
        content = content.replace('\t', '\\t')
        
        # 转义其他控制字符
        content = content.replace('\b', '\\b')
        content = content.replace('\f', '\\f')
        
        return content
    
    def _fix_incomplete_json(self, json_str: str) -> str:
        """修复不完整的JSON结构"""
        fixed_str = json_str.strip()
        
        # 修复缺失的结束括号
        open_braces = fixed_str.count('{')
        close_braces = fixed_str.count('}')
        if open_braces > close_braces:
            missing_braces = open_braces - close_braces
            fixed_str += '}' * missing_braces
            self.logger.info(f"添加了 {missing_braces} 个缺失的结束括号")
        
        # 修复缺失的结束方括号
        open_brackets = fixed_str.count('[')
        close_brackets = fixed_str.count(']')
        if open_brackets > close_brackets:
            missing_brackets = open_brackets - close_brackets
            fixed_str += ']' * missing_brackets
            self.logger.info(f"添加了 {missing_brackets} 个缺失的结束方括号")
        
        return fixed_str
    
    def _fix_python_to_json(self, json_str: str) -> str:
        """将Python格式转换为JSON格式"""
        fixed_str = json_str
        
        # 转换Python布尔值
        fixed_str = re.sub(r'\bTrue\b', 'true', fixed_str)
        fixed_str = re.sub(r'\bFalse\b', 'false', fixed_str)
        fixed_str = re.sub(r'\bNone\b', 'null', fixed_str)
        
        return fixed_str
    
    def extract_and_repair_json(self, text: str) -> Optional[Dict[str, Any]]:
        """
        从文本中提取并修复JSON
        
        Args:
            text: 包含JSON的文本
            
        Returns:
            解析后的JSON对象，如果失败返回None
        """
        # 尝试多种方式提取JSON
        json_candidates = self._extract_json_candidates(text)
        
        for candidate in json_candidates:
            try:
                # 尝试修复并解析
                repaired = self.repair_json_string(candidate)
                result = json.loads(repaired)
                self.logger.info("JSON修复成功")
                return result
            except Exception as e:
                self.logger.debug(f"JSON候选修复失败: {str(e)}")
                continue
        
        self.logger.error("所有JSON修复尝试都失败了")
        return None
    
    def _extract_json_candidates(self, text: str) -> list:
        """提取可能的JSON候选字符串"""
        candidates = []
        
        # 1. 尝试提取完整的JSON对象
        json_patterns = [
            r'```json\s*(\{.*?\})\s*```',  # ```json {...} ```
            r'```\s*(\{.*?\})\s*```',      # ``` {...} ```
            r'(\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\})',  # 完整的JSON对象
        ]
        
        for pattern in json_patterns:
            matches = re.findall(pattern, text, re.DOTALL)
            candidates.extend(matches)
        
        # 2. 查找最大的JSON对象
        start_idx = text.find('{')
        if start_idx != -1:
            brace_count = 0
            for i, char in enumerate(text[start_idx:], start_idx):
                if char == '{':
                    brace_count += 1
                elif char == '}':
                    brace_count -= 1
                    if brace_count == 0:
                        candidate = text[start_idx:i+1]
                        candidates.append(candidate)
                        break
        
        # 3. 如果没有找到完整的JSON，尝试提取problems数组
        if not candidates:
            problems_match = re.search(r'"problems"\s*:\s*\[(.*?)\]', text, re.DOTALL)
            if problems_match:
                problems_json = '{"problems": [' + problems_match.group(1) + ']}'
                candidates.append(problems_json)
        
        return candidates


def repair_cr_json_output(json_str: str) -> Dict[str, Any]:
    """
    修复CR输出中的JSON格式问题
    
    Args:
        json_str: 原始JSON字符串
        
    Returns:
        修复后的JSON对象
    """
    repairer = JSONRepairUtils()
    
    try:
        # 首先尝试直接解析
        return json.loads(json_str)
    except json.JSONDecodeError:
        # 尝试修复
        result = repairer.extract_and_repair_json(json_str)
        if result:
            return result
        else:
            # 如果修复失败，返回默认结构
            return {
                "problems": [],
                "error": "JSON解析失败",
                "original_content": json_str[:200] + "..." if len(json_str) > 200 else json_str
            }


if __name__ == "__main__":
    # 测试用例
    test_json = '''
    {
      "problems": [
        {
          "targetCode": "print(ROOT_DIR + "/infra/thrift/daxiang/AuthEntity.thr...
          "problem": "路径拼接问题",
          "suggestion": "使用os.path.join()进行路径拼接"
        }
      ]
    }
    '''
    
    result = repair_cr_json_output(test_json)
    print(json.dumps(result, ensure_ascii=False, indent=2))
