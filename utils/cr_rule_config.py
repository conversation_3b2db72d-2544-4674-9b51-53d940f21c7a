"""
CR规则配置系统 - 支持不同语言和业务的代码审查规范
"""

import json
import os
from dataclasses import dataclass
from enum import Enum
from typing import Dict, List, Optional


class ProblemLevel(Enum):
    """问题级别"""
    P0 = "P0"  # 严重问题，必须修复
    P1 = "P1"  # 重要问题，建议修复
    P2 = "P2"  # 一般问题，建议优化


@dataclass
class CRRule:
    """单个CR规则"""
    id: str
    level: ProblemLevel
    title: str
    description: str
    examples: List[str] = None
    keywords: List[str] = None
    def __post_init__(self):
        if self.examples is None:
            self.examples = []
        if self.keywords is None:
            self.keywords = []


@dataclass
class CRRuleSet:
    """CR规则集"""
    language: str
    business: str
    version: str
    description: str
    rules: List[CRRule]
    custom_instructions: str = ""
    output_format: str = ""
    def get_rules_by_level(self, level: ProblemLevel) -> List[CRRule]:
        """获取指定级别的规则"""
        return [rule for rule in self.rules if rule.level == level]
    def get_rule_by_id(self, rule_id: str) -> Optional[CRRule]:
        """根据ID获取规则"""
        for rule in self.rules:
            if rule.id == rule_id:
                return rule
        return None


class CRRuleManager:
    """CR规则管理器"""
    def __init__(self, config_dir: str = "config/cr_rules"):
        self.config_dir = config_dir
        self.rule_sets: Dict[str, CRRuleSet] = {}
        self._load_default_rules()
    def _load_default_rules(self):
        """加载默认规则"""
        # Python默认规则
        python_rules = self._create_python_rules()
        self.rule_sets["python_default"] = python_rules
        # Java默认规则
        java_rules = self._create_java_rules()
        self.rule_sets["java_default"] = java_rules
        # JavaScript默认规则
        js_rules = self._create_javascript_rules()
        self.rule_sets["javascript_default"] = js_rules
    def _create_python_rules(self) -> CRRuleSet:
        """创建Python默认规则"""
        rules = [
            # P0规则 - 严重问题，必须修复
            CRRule(
                id="P0-1",
                level=ProblemLevel.P0,
                title="语法错误和运行时错误",
                description="存在语法错误、未定义变量、未导入模块、缩进错误等导致代码无法运行的问题",
                examples=[
                    "NameError: name 'undefined_var' is not defined",
                    "ImportError: No module named 'missing_module'",
                    "SyntaxError: invalid syntax",
                    "IndentationError: expected an indented block",
                    "TypeError: unsupported operand type(s)",
                    "AttributeError: 'NoneType' object has no attribute"
                ],
                keywords=["语法错误", "未定义", "导入", "缩进", "NameError", "ImportError", "SyntaxError", "IndentationError"]
            ),
            CRRule(
                id="P0-2",
                level=ProblemLevel.P0,
                title="安全漏洞",
                description="存在明显的安全漏洞，包括SQL注入、命令注入、明文密码、危险函数使用、路径遍历等",
                examples=[
                    "cursor.execute(f'SELECT * FROM users WHERE id = {user_id}')",
                    "os.system(user_input)",
                    "subprocess.call(shell_command, shell=True)",
                    "password = '123456'",
                    "eval(user_input)",
                    "exec(code_string)",
                    "pickle.loads(untrusted_data)",
                    "open(f'/path/{user_input}', 'r')",
                    "yaml.load(data)  # 应使用yaml.safe_load"
                ],
                keywords=["SQL注入", "命令注入", "密码", "eval", "exec", "pickle", "yaml.load", "os.system", "subprocess", "shell=True"]
            ),
            CRRule(
                id="P0-3",
                level=ProblemLevel.P0,
                title="业务逻辑漏洞",
                description="关键业务流程存在逻辑漏洞、数据丢失风险、状态不一致或竞态条件",
                examples=[
                    "删除数据前未备份",
                    "金额计算精度丢失",
                    "并发访问共享资源未加锁",
                    "事务回滚逻辑缺失",
                    "状态机状态转换错误",
                    "权限检查绕过"
                ],
                keywords=["业务逻辑", "数据丢失", "状态", "竞态", "事务", "权限", "并发"]
            ),
            CRRule(
                id="P0-4",
                level=ProblemLevel.P0,
                title="资源泄漏",
                description="资源未正确关闭或释放，包括文件、数据库连接、网络连接、内存等",
                examples=[
                    "file = open('data.txt'); data = file.read()  # 未关闭文件",
                    "conn = sqlite3.connect('db.sqlite'); cursor = conn.cursor()  # 未关闭连接",
                    "response = requests.get(url)  # 未关闭连接",
                    "大对象未及时删除导致内存泄漏"
                ],
                keywords=["资源泄漏", "文件", "连接", "关闭", "with", "context manager", "内存泄漏"]
            ),
            CRRule(
                id="P0-5",
                level=ProblemLevel.P0,
                title="线程安全问题",
                description="多线程环境下存在线程安全问题，如共享变量未加锁、死锁风险等",
                examples=[
                    "全局变量在多线程中修改未加锁",
                    "多个锁的获取顺序不一致导致死锁",
                    "线程间共享可变对象未同步"
                ],
                keywords=["线程安全", "锁", "死锁", "共享变量", "多线程", "同步"]
            ),

            # P1规则 - 重要问题，建议修复
            CRRule(
                id="P1-1",
                level=ProblemLevel.P1,
                title="PEP8规范违反",
                description="违反PEP8代码风格规范，影响代码可读性和维护性",
                examples=[
                    "函数名使用驼峰命名：getUserName() 应为 get_user_name()",
                    "类名使用下划线：user_model 应为 UserModel",
                    "常量未使用大写：max_size 应为 MAX_SIZE",
                    "行长度超过88字符（推荐）或79字符（严格）",
                    "import语句顺序错误或格式不规范",
                    "空行使用不当：类定义前后应有两个空行"
                ],
                keywords=["PEP8", "命名", "行长度", "空格", "import", "驼峰", "下划线", "常量"]
            ),
            CRRule(
                id="P1-2",
                level=ProblemLevel.P1,
                title="魔法数字和字符串",
                description="硬编码的数字和字符串未定义为常量，降低代码可维护性",
                examples=[
                    "if status == 200:  # 应定义 HTTP_OK = 200",
                    "time.sleep(3600)  # 应定义 ONE_HOUR = 3600",
                    "if user_type == 'admin':  # 应定义 USER_TYPE_ADMIN = 'admin'",
                    "MAX_RETRY = 3  # 直接使用数字3"
                ],
                keywords=["魔法数字", "硬编码", "常量", "字面量"]
            ),
            CRRule(
                id="P1-3",
                level=ProblemLevel.P1,
                title="异常处理不当",
                description="异常处理不规范，包括捕获过于宽泛、异常信息不明确、资源清理不当等",
                examples=[
                    "except:  # 裸露的except，应指定具体异常类型",
                    "except Exception:  # 过于宽泛，应捕获具体异常",
                    "except ValueError: pass  # 静默忽略异常",
                    "raise Exception('error')  # 应使用具体的异常类型",
                    "异常处理中未记录日志",
                    "finally块中未清理资源"
                ],
                keywords=["异常", "except", "try", "Exception", "raise", "finally", "日志"]
            ),
            CRRule(
                id="P1-4",
                level=ProblemLevel.P1,
                title="文档和注释缺失",
                description="函数、类、模块缺少必要的docstring注释或注释质量不高",
                examples=[
                    "公共函数缺少docstring",
                    "类缺少说明文档",
                    "复杂算法缺少注释",
                    "参数和返回值说明不清晰",
                    "模块级别缺少说明",
                    "TODO注释未及时处理"
                ],
                keywords=["docstring", "注释", "文档", "TODO", "参数说明", "返回值"]
            ),
            CRRule(
                id="P1-5",
                level=ProblemLevel.P1,
                title="类型提示缺失",
                description="缺少类型提示(Type Hints)，影响代码可读性和IDE支持",
                examples=[
                    "def process_data(data):  # 应添加类型提示",
                    "def get_user(user_id: int):  # 缺少返回类型",
                    "class User:  # 属性缺少类型注解",
                    "复杂数据结构未使用TypedDict或dataclass"
                ],
                keywords=["类型提示", "Type Hints", "typing", "返回类型", "参数类型"]
            ),
            CRRule(
                id="P1-6",
                level=ProblemLevel.P1,
                title="性能问题",
                description="存在明显的性能问题，如低效算法、重复计算、内存浪费等",
                examples=[
                    "在循环中重复创建相同对象",
                    "使用低效的字符串拼接方式",
                    "未使用生成器处理大数据集",
                    "重复的数据库查询",
                    "未使用缓存机制",
                    "O(n²)算法可优化为O(n log n)"
                ],
                keywords=["性能", "算法复杂度", "内存", "缓存", "生成器", "字符串拼接"]
            ),

            # P2规则 - 一般问题，建议优化
            CRRule(
                id="P2-1",
                level=ProblemLevel.P2,
                title="命名不清晰",
                description="变量、函数、类命名不清晰或不符合语义，影响代码可读性",
                examples=[
                    "变量名过于简短：d, tmp, data",
                    "函数名不表达功能：process(), handle()",
                    "布尔变量未使用is_/has_前缀",
                    "缩写不明确：usr, mgr, cfg",
                    "中英文混用命名"
                ],
                keywords=["命名", "语义", "清晰", "缩写", "布尔变量"]
            ),
            CRRule(
                id="P2-2",
                level=ProblemLevel.P2,
                title="代码结构问题",
                description="代码结构不清晰，函数过长、嵌套层级过深、职责不单一",
                examples=[
                    "函数超过50行（建议）或100行（严格）",
                    "嵌套层级超过4层",
                    "一个函数处理多个不相关的任务",
                    "类的方法过多（超过20个）",
                    "模块过大（超过1000行）",
                    "循环复杂度过高"
                ],
                keywords=["结构", "函数长度", "嵌套", "职责", "复杂度", "模块大小"]
            ),
            CRRule(
                id="P2-3",
                level=ProblemLevel.P2,
                title="调试代码未清理",
                description="临时调试代码未清理，包括print语句、临时变量、测试代码等",
                examples=[
                    "print(f'Debug: {variable}')",
                    "# TODO: remove this debug code",
                    "import pdb; pdb.set_trace()",
                    "临时的测试函数未删除",
                    "注释掉的旧代码未清理"
                ],
                keywords=["print", "debug", "调试", "pdb", "TODO", "注释代码"]
            ),
            CRRule(
                id="P2-4",
                level=ProblemLevel.P2,
                title="代码重复",
                description="存在重复代码，违反DRY原则，增加维护成本",
                examples=[
                    "相同的代码块在多处出现",
                    "相似的函数逻辑未抽象",
                    "重复的配置或常量定义",
                    "相同的数据处理逻辑"
                ],
                keywords=["重复代码", "DRY", "抽象", "重构"]
            ),
            CRRule(
                id="P2-5",
                level=ProblemLevel.P2,
                title="不符合Python习惯",
                description="代码不符合Python的惯用法(Pythonic)，可读性和效率不佳",
                examples=[
                    "使用range(len(list))而不是enumerate",
                    "手动管理索引而不是使用迭代器",
                    "不使用列表推导式或生成器表达式",
                    "不使用with语句管理资源",
                    "使用可变对象作为默认参数",
                    "不使用字典的get()方法"
                ],
                keywords=["Pythonic", "enumerate", "列表推导", "with", "生成器", "习惯用法"]
            ),
            CRRule(
                id="P2-6",
                level=ProblemLevel.P2,
                title="测试相关问题",
                description="测试代码质量问题或缺少必要的测试",
                examples=[
                    "关键功能缺少单元测试",
                    "测试用例覆盖率过低",
                    "测试用例命名不清晰",
                    "测试数据硬编码",
                    "集成测试缺失",
                    "边界条件测试不足"
                ],
                keywords=["测试", "单元测试", "覆盖率", "边界条件", "集成测试"]
            )
        ]
        return CRRuleSet(
            language="python",
            business="default",
            version="2.0",
            description="Python代码审查优化规范 - 基于PEP规范和最佳实践",
            rules=rules,
            custom_instructions="""
## Python代码审查要求

### 审查原则
1. **严格性**: 严格按照规则逐项检查，P0问题必须修复，P1问题强烈建议修复，P2问题建议优化
2. **精确性**: 仅对diff范围内的代码进行审查，问题定位需精确到具体代码行
3. **实用性**: 关注实际影响，避免过度纠结于风格细节
4. **教育性**: 每个问题需给出详细说明和修改建议，帮助开发者提升

### 检查重点
- **P0级别**: 功能性错误、安全漏洞、资源泄漏、线程安全
- **P1级别**: 代码规范、异常处理、文档注释、类型提示、性能问题
- **P2级别**: 代码风格、结构优化、Python习惯用法、测试质量

### 输出要求
1. 问题描述要具体明确，引用原始代码片段
2. 风险说明要客观准确，说明潜在影响
3. 修改建议要可操作，提供具体的改进方案
4. 如无问题，明确说明"代码质量良好，未发现问题"

### 特殊考虑
- 对于遗留代码，重点关注新增和修改部分
- 考虑项目上下文，某些规则可能需要灵活处理
- 鼓励使用现代Python特性和最佳实践
- 平衡代码质量和开发效率
            """,
            output_format="""
JSON结构化输出格式：
{
  "summary": {
    "total_issues": 数字,
    "p0_count": 数字,
    "p1_count": 数字, 
    "p2_count": 数字,
    "overall_quality": "优秀/良好/一般/较差"
  },
  "issues": [
    {
      "level": "P0/P1/P2",
      "rule_id": "规则ID",
      "title": "问题标题",
      "description": "详细问题描述",
      "risk": "风险说明",
      "suggestion": "修改建议",
      "code_position": "文件名:行号",
      "target_code": "问题代码片段",
      "suggested_code": "建议修改后的代码"
    }
  ]
}
            """
        )
    def _create_java_rules(self) -> CRRuleSet:
        """创建Java默认规则"""
        rules = [
            CRRule(
                id="J-P0-1",
                level=ProblemLevel.P0,
                title="编译错误",
                description="存在编译错误、类型不匹配、方法未定义等问题",
                keywords=["编译错误", "类型", "方法"]
            ),
            CRRule(
                id="J-P0-2",
                level=ProblemLevel.P0,
                title="安全漏洞",
                description="SQL注入、XSS、反序列化漏洞等安全问题",
                keywords=["SQL注入", "XSS", "反序列化", "安全"]
            ),
            CRRule(
                id="J-P1-1",
                level=ProblemLevel.P1,
                title="代码规范",
                description="违反Java代码规范（命名、格式、注释等）",
                keywords=["命名", "格式", "注释"]
            ),
            CRRule(
                id="J-P1-2",
                level=ProblemLevel.P1,
                title="异常处理",
                description="异常处理不当或资源未正确关闭",
                keywords=["异常", "资源", "try-catch"]
            )
        ]
        return CRRuleSet(
            language="java",
            business="default",
            version="1.0",
            description="Java代码审查默认规范",
            rules=rules
        )
    def _create_javascript_rules(self) -> CRRuleSet:
        """创建JavaScript默认规则"""
        rules = [
            CRRule(
                id="JS-P0-1",
                level=ProblemLevel.P0,
                title="语法错误",
                description="JavaScript语法错误或运行时错误",
                keywords=["语法错误", "运行时"]
            ),
            CRRule(
                id="JS-P0-2",
                level=ProblemLevel.P0,
                title="安全漏洞",
                description="XSS、CSRF、代码注入等安全问题",
                keywords=["XSS", "CSRF", "注入"]
            ),
            CRRule(
                id="JS-P1-1",
                level=ProblemLevel.P1,
                title="ESLint规范",
                description="违反ESLint代码规范",
                keywords=["ESLint", "规范"]
            )
        ]
        return CRRuleSet(
            language="javascript",
            business="default",
            version="1.0",
            description="JavaScript代码审查默认规范",
            rules=rules
        )
    def get_rule_set(self, language: str, business: str = "default") -> Optional[CRRuleSet]:
        """获取规则集"""
        key = f"{language}_{business}"
        return self.rule_sets.get(key)
    def register_rule_set(self, rule_set: CRRuleSet):
        """注册新的规则集"""
        key = f"{rule_set.language}_{rule_set.business}"
        self.rule_sets[key] = rule_set
    def load_rule_set_from_file(self, file_path: str) -> CRRuleSet:
        """从文件加载规则集"""
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        rules = []
        for rule_data in data.get('rules', []):
            rule = CRRule(
                id=rule_data['id'],
                level=ProblemLevel(rule_data['level']),
                title=rule_data['title'],
                description=rule_data['description'],
                examples=rule_data.get('examples', []),
                keywords=rule_data.get('keywords', [])
            )
            rules.append(rule)
        rule_set = CRRuleSet(
            language=data['language'],
            business=data['business'],
            version=data['version'],
            description=data['description'],
            rules=rules,
            custom_instructions=data.get('custom_instructions', ''),
            output_format=data.get('output_format', '')
        )
        return rule_set
    def save_rule_set_to_file(self, rule_set: CRRuleSet, file_path: str):
        """保存规则集到文件"""
        data = {
            'language': rule_set.language,
            'business': rule_set.business,
            'version': rule_set.version,
            'description': rule_set.description,
            'custom_instructions': rule_set.custom_instructions,
            'output_format': rule_set.output_format,
            'rules': []
        }
        for rule in rule_set.rules:
            rule_data = {
                'id': rule.id,
                'level': rule.level.value,
                'title': rule.title,
                'description': rule.description,
                'examples': rule.examples,
                'keywords': rule.keywords
            }
            data['rules'].append(rule_data)
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
    def list_available_rule_sets(self) -> List[str]:
        """列出可用的规则集"""
        return list(self.rule_sets.keys())

    def create_custom_rule_set(self, language: str, business: str,
                             base_rule_set: str = None) -> CRRuleSet:
        """创建自定义规则集"""
        if base_rule_set and base_rule_set in self.rule_sets:
            # 基于现有规则集创建
            base = self.rule_sets[base_rule_set]
            rules = [
                CRRule(
                    id=rule.id,
                    level=rule.level,
                    title=rule.title,
                    description=rule.description,
                    examples=rule.examples.copy(),
                    keywords=rule.keywords.copy()
                ) for rule in base.rules
            ]
            rule_set = CRRuleSet(
                language=language,
                business=business,
                version="1.0",
                description=f"基于{base.description}的自定义规则",
                rules=rules,
                custom_instructions=base.custom_instructions,
                output_format=base.output_format
            )
        else:
            # 创建空的规则集
            rule_set = CRRuleSet(
                language=language,
                business=business,
                version="1.0",
                description=f"{language}_{business}自定义规则",
                rules=[],
                custom_instructions="",
                output_format=""
            )
        return rule_set


# 全局规则管理器实例
cr_rule_manager = CRRuleManager()
