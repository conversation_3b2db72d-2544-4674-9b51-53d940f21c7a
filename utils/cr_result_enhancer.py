"""
CR结果增强器 - 生成符合前端展示需求的详细审查结果
"""

import json
import logging
import re
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict, field
from enum import Enum


class ProblemSeverity(Enum):
    """问题严重程度"""
    CRITICAL = "严重问题"  # P0
    WARNING = "警告问题"   # P1  
    MODERATE = "中等问题"  # P2
    MINOR = "轻微问题"     # P3+


@dataclass
class ProblemStatistics:
    """问题统计信息"""
    critical_count: int = 0    # 严重问题数量
    warning_count: int = 0     # 警告问题数量  
    moderate_count: int = 0    # 中等问题数量
    minor_count: int = 0       # 轻微问题数量
    
    @property
    def total_count(self) -> int:
        """总问题数量"""
        return self.critical_count + self.warning_count + self.moderate_count + self.minor_count


@dataclass
class TaskExecutionDetail:
    """任务执行详情"""
    task_name: str                    # 任务名称
    task_type: str                    # 任务类型 (commonCheck/customCheck/dependencyCheck等)
    execution_status: str             # 执行状态 (success/failed/partial)
    execution_time_ms: int            # 执行时间(毫秒)
    rules_applied: List[str]          # 应用的规则列表
    files_checked: List[str]          # 检查的文件列表
    coverage_percentage: float        # 覆盖率百分比
    problems_found: int               # 发现的问题数量
    task_summary: str                 # 任务总结
    detailed_metrics: Dict[str, Any]  # 详细指标
    recommendations: List[str]        # 任务级别的建议


@dataclass
class EnhancedProblemDetail:
    """增强的问题详情"""
    id: str                           # 问题ID
    title: str                        # 问题标题
    severity: ProblemSeverity         # 严重程度
    level: str                        # 原始级别 (P0/P1/P2)
    line_number: int                  # 行号
    description: str                  # 问题描述
    target_code: str                  # 问题代码
    suggestion: str                   # 修改建议
    code_position: str                # 代码位置
    code_position_array: List[int]    # 位置数组 [startLine, startColumn, endLine, endColumn]
    category: str                     # 问题分类 (commonCheck/customCheck)

    # 新增字段：保留完整的原始问题信息
    original_detail: Dict[str, Any]   # 原始问题详情
    file_path: Optional[str] = None   # 文件路径
    rule_name: Optional[str] = None   # 规则名称
    rule_description: Optional[str] = None  # 规则描述
    impact_level: Optional[str] = None      # 影响级别
    fix_complexity: Optional[str] = None    # 修复复杂度

    # 增强字段：更详细的问题信息
    detection_method: Optional[str] = None  # 检测方法 (static_analysis/llm_review/rule_based)
    confidence_score: Optional[float] = None # 置信度评分 (0-1)
    related_problems: List[str] = field(default_factory=list)  # 相关问题ID列表
    fix_examples: List[str] = field(default_factory=list)      # 修复示例
    reference_links: List[str] = field(default_factory=list)   # 参考链接


@dataclass
class EnhancedCRResult:
    """增强的CR结果"""
    # 基础信息
    check_branch: str                 # 检查分支
    review_time: str                  # 审查时间
    reviewer: str                     # 审查者

    # 总体评估
    overall_score: int                # 总体评分 (0-100)
    overall_result: str               # 总体结果 (通过/不通过)
    result_description: str           # 结果描述

    # 问题统计
    statistics: ProblemStatistics     # 问题统计

    # 详细问题列表
    problems: List[EnhancedProblemDetail]  # 问题详情列表

    # 新增字段：任务级别的详细信息
    task_execution_details: List[TaskExecutionDetail]  # 任务执行详情列表

    # 新增字段：增强的总结信息
    summary_details: Dict[str, Any]   # 详细总结信息
    review_metrics: Dict[str, Any]    # 审查指标
    recommendations: List[str]        # 改进建议

    # 新增字段：审查过程信息
    review_process_info: Dict[str, Any]  # 审查过程信息
    quality_gates: Dict[str, Any]        # 质量门禁信息

    # 原始数据
    original_result: Dict[str, Any]   # 原始CR结果


class CRResultEnhancer:
    """CR结果增强器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 问题级别映射
        self.severity_mapping = {
            'p0': ProblemSeverity.CRITICAL,
            'p1': ProblemSeverity.WARNING,
            'p2': ProblemSeverity.MODERATE,
            'p3': ProblemSeverity.MINOR,
            'p4': ProblemSeverity.MINOR,
            'p5': ProblemSeverity.MINOR,
            'p6': ProblemSeverity.MINOR,
        }
        
        # 评分权重 - 统一标准：P0×25 + P1×15 + P2×10 + P3+×5
        self.score_weights = {
            ProblemSeverity.CRITICAL: -25,  # P0严重问题扣25分
            ProblemSeverity.WARNING: -15,   # P1警告问题扣15分
            ProblemSeverity.MODERATE: -10,  # P2中等问题扣10分
            ProblemSeverity.MINOR: -5,      # P3+轻微问题扣5分
        }
    
    def enhance_cr_result(self, original_result: Dict[str, Any], 
                         reviewer: str = "AI代码审查", 
                         branch_info: Optional[Dict[str, str]] = None) -> EnhancedCRResult:
        """
        增强CR结果
        
        Args:
            original_result: 原始CR结果
            reviewer: 审查者名称
            branch_info: 分支信息
            
        Returns:
            EnhancedCRResult: 增强后的CR结果
        """
        try:
            # 保存原始结果供后续使用
            self._current_original_result = original_result

            # 提取基础信息
            check_branch = original_result.get('checkBranch', '未知分支')

            # 如果提供了分支信息，优先使用
            if branch_info and branch_info.get('fromBranch'):
                project = branch_info.get('project', '')
                repo = branch_info.get('repo', '')
                from_branch = branch_info.get('fromBranch', '')
                to_branch = branch_info.get('toBranch', '')

                if project and repo:
                    check_branch = f"{project}/{repo}:{from_branch}"
                else:
                    check_branch = from_branch

            # 如果checkBranch看起来像文件路径（包含冒号和数字），则认为是错误的
            elif ':' in check_branch and any(char.isdigit() for char in check_branch.split(':')[-1]):
                self.logger.warning(f"检测到错误的checkBranch格式: {check_branch}，重置为未知分支")
                check_branch = '未知分支'

            review_time = datetime.now().strftime("%H:%M:%S")

            # 解析问题列表
            problems = self._parse_problems(original_result.get('problemList', []))

            # 生成任务执行详情
            task_execution_details = self._generate_task_execution_details(original_result.get('problemList', []), problems)

            # 计算统计信息
            statistics = self._calculate_statistics(problems)

            # 计算总体评分
            overall_score = self._calculate_score(statistics)

            # 确定总体结果 - 基于问题数量和严重程度
            overall_result = self._determine_overall_result(statistics, original_result)

            # 生成结果描述
            result_description = self._generate_description(statistics)

            # 生成详细总结信息
            summary_details = self._generate_summary_details(statistics, problems, original_result)

            # 生成审查指标
            review_metrics = self._generate_review_metrics(statistics, problems)

            # 生成改进建议
            recommendations = self._generate_recommendations(statistics, problems)

            # 生成审查过程信息
            review_process_info = self._generate_review_process_info(original_result, task_execution_details)

            # 生成质量门禁信息
            quality_gates = self._generate_quality_gates(statistics, overall_score)

            enhanced_result = EnhancedCRResult(
                check_branch=check_branch,
                review_time=review_time,
                reviewer=reviewer,
                overall_score=overall_score,
                overall_result=overall_result,
                result_description=result_description,
                statistics=statistics,
                problems=problems,
                task_execution_details=task_execution_details,
                summary_details=summary_details,
                review_metrics=review_metrics,
                recommendations=recommendations,
                review_process_info=review_process_info,
                quality_gates=quality_gates,
                original_result=original_result
            )
            self.logger.debug(f"增强结果生成完成: {enhanced_result.check_branch}")

            # 验证数据一致性
            self._validate_result_consistency(enhanced_result)

            self.logger.info(f"CR结果增强完成: 评分{overall_score}, 问题{statistics.total_count}个, 结果{overall_result}")
            return enhanced_result
            
        except Exception as e:
            self.logger.error(f"CR结果增强失败: {str(e)}")
            # 返回基础结果
            return self._create_fallback_result(original_result, reviewer)
    
    def _parse_problems(self, problem_list: List[Dict[str, Any]]) -> List[EnhancedProblemDetail]:
        """解析问题列表，确保每个缺陷的详细信息都完整存入"""
        problems = []
        problem_id = 1

        self.logger.info(f"开始解析问题列表，共有 {len(problem_list)} 个类别")

        for category_index, category in enumerate(problem_list):
            scene = category.get('scene', 'commonCheck')
            details = category.get('detail', [])
            num = category.get('num', 0)

            self.logger.info(f"处理类别 {category_index + 1}: {scene}，包含 {len(details)} 个详细问题，总数: {num}")

            # 如果detail为空但有问题数量，从原始结果描述中提取问题
            if len(details) == 0 and num > 0:
                self.logger.info(f"📝 场景 {scene} 的detail为空，尝试从原始结果描述中提取问题")
                synthetic_problems = self._extract_problems_from_description(scene, num, problem_id)
                problems.extend(synthetic_problems)
                problem_id += len(synthetic_problems)
                continue

            for detail_index, detail in enumerate(details):
                try:
                    # 记录原始detail的完整信息
                    self.logger.debug(f"处理问题 {problem_id}: {detail}")

                    # 确保所有原始字段都被保留
                    original_detail_copy = dict(detail)  # 创建深拷贝确保完整性

                    # 提取问题信息
                    level = detail.get('level', 'p2').lower()
                    severity = self.severity_mapping.get(level, ProblemSeverity.MODERATE)

                    # 解析代码位置
                    code_position = detail.get('codePosition', '')
                    line_number = self._extract_line_number(code_position)
                    code_position_array = detail.get('codePositionArray', [line_number, 0, line_number, 0])

                    # 生成问题标题
                    title = self._generate_problem_title(detail.get('problem', ''), severity)

                    # 提取文件路径（如果存在）
                    file_path = self._extract_file_path(code_position)

                    # 分析修复复杂度
                    fix_complexity = self._analyze_fix_complexity(detail, severity)

                    # 确定影响级别
                    impact_level = self._determine_impact_level(severity, detail)

                    # 提取所有可能的额外字段
                    extra_fields = self._extract_extra_fields(detail)

                    # 生成增强的问题信息
                    detection_method = self._determine_detection_method(detail, scene)
                    confidence_score = self._calculate_confidence_score(detail, severity)
                    fix_examples = self._generate_fix_examples(detail)
                    reference_links = self._generate_reference_links(detail, scene)

                    problem = EnhancedProblemDetail(
                        id=str(problem_id),
                        title=title,
                        severity=severity,
                        level=level.upper(),
                        line_number=line_number,
                        description=detail.get('problem', ''),
                        target_code=detail.get('targetCode', ''),
                        suggestion=detail.get('suggestion', ''),
                        code_position=code_position,
                        code_position_array=code_position_array,
                        category=scene,
                        original_detail=original_detail_copy,  # 保留完整原始信息
                        file_path=file_path,
                        rule_name=detail.get('ruleName', f"{scene}_{level}"),
                        rule_description=detail.get('ruleDescription', ''),
                        impact_level=impact_level,
                        fix_complexity=fix_complexity,
                        detection_method=detection_method,
                        confidence_score=confidence_score,
                        related_problems=[],  # 将在后续步骤中填充
                        fix_examples=fix_examples,
                        reference_links=reference_links
                    )

                    problems.append(problem)
                    self.logger.debug(f"✅ 成功解析问题 {problem_id}: {title}")
                    problem_id += 1

                except Exception as e:
                    self.logger.error(f"❌ 解析问题详情失败 (类别: {scene}, 索引: {detail_index}): {str(e)}")
                    self.logger.error(f"原始detail内容: {detail}")

                    # 即使解析失败，也要尝试保留原始信息
                    try:
                        fallback_problem = self._create_fallback_problem(detail, scene, problem_id)
                        if fallback_problem:
                            problems.append(fallback_problem)
                            problem_id += 1
                            self.logger.info(f"⚠️ 使用降级方式保存问题 {problem_id - 1}")
                    except Exception as fallback_error:
                        self.logger.error(f"❌ 降级保存也失败: {str(fallback_error)}")
                    continue

        self.logger.info(f"✅ 问题解析完成，共解析 {len(problems)} 个问题")
        return problems

    def _extract_problems_from_description(self, scene: str, num: int, start_problem_id: int) -> List[EnhancedProblemDetail]:
        """从原始结果描述中提取问题"""
        problems = []

        try:
            # 从原始结果中获取描述信息
            original_result = getattr(self, '_current_original_result', {})
            result_desc = original_result.get('resultDesc', '')

            # 获取对应场景的描述
            level_desc = ""

            # 检查resultDesc的类型
            if isinstance(result_desc, dict):
                # 支持多种场景格式
                if scene in ["P0", "P1", "P2"]:
                    # 传统的P0/P1/P2格式
                    level_desc = result_desc.get(scene, '')
                else:
                    # 新的中文场景格式，尝试多种匹配方式
                    level_desc = self._find_description_for_scene(result_desc, scene)
            elif isinstance(result_desc, str):
                # 如果resultDesc是字符串，直接使用
                level_desc = result_desc
                self.logger.info(f"resultDesc是字符串格式: {result_desc[:100]}...")
            else:
                self.logger.warning(f"resultDesc格式不支持: {type(result_desc)}")
                level_desc = ""

            if not level_desc:
                self.logger.warning(f"未找到场景 {scene} 的描述信息，尝试生成通用问题")
                # 如果找不到描述，生成通用问题
                generic_problems = self._generate_generic_problems(scene, num, start_problem_id)
                self.logger.info(f"为场景 {scene} 生成了 {len(generic_problems)} 个通用问题")
                return generic_problems

            # 将描述分割成多个问题
            problem_descriptions = self._split_description_into_problems(level_desc, num)

            # 如果分割后的问题数量不足，生成额外的问题
            if len(problem_descriptions) < num:
                self.logger.info(f"分割后问题数量({len(problem_descriptions)})少于期望数量({num})，生成额外问题")
                additional_problems = self._generate_additional_problems(scene, level_desc, num - len(problem_descriptions))
                problem_descriptions.extend(additional_problems)

            for i, desc in enumerate(problem_descriptions[:num]):  # 确保不超过期望数量
                problem_id = start_problem_id + i

                # 确定问题级别
                level = self._determine_level_from_scene(scene)
                severity = self.severity_mapping.get(level.lower(), ProblemSeverity.MODERATE)

                # 生成合成问题
                synthetic_problem = EnhancedProblemDetail(
                    id=str(problem_id),
                    title=f"{scene}级别问题 {i+1}",
                    severity=severity,
                    level=level.upper(),
                    line_number=1,
                    description=desc.strip(),
                    target_code="",
                    suggestion=self._generate_generic_suggestion(scene, desc),
                    code_position="",
                    code_position_array=[1, 0, 1, 0],
                    category=scene,
                    original_detail={
                        'problem': desc.strip(),
                        'level': level,
                        'scene': scene,
                        'synthetic': True
                    },
                    file_path=None,
                    rule_name=f"{scene}_synthetic_rule",
                    rule_description=f"从{scene}级别描述中提取的问题",
                    impact_level=self._determine_impact_level_from_scene(scene),
                    fix_complexity=self._determine_fix_complexity_from_scene(scene),
                    detection_method='description_extraction',
                    confidence_score=0.6,  # 从描述提取的置信度较低
                    related_problems=[],
                    fix_examples=[],
                    reference_links=[]
                )

                problems.append(synthetic_problem)
                self.logger.debug(f"✅ 生成合成问题: {synthetic_problem.title}")

            self.logger.info(f"✅ 从 {scene} 描述中提取了 {len(problems)} 个问题")
            return problems

        except Exception as e:
            self.logger.error(f"❌ 从描述中提取问题失败: {str(e)}")
            return problems

    def _split_description_into_problems(self, description: str, expected_count: int) -> List[str]:
        """将描述分割成多个问题（改进版，避免碎片化）"""
        if not description or expected_count <= 0:
            return []

        try:
            import re

            # 预处理：清理描述，避免碎片化
            cleaned_description = self._clean_description_text(description)
            if not cleaned_description:
                return []

            # 策略1: 按明确的数字标记分割，但确保每个部分有足够长度
            numbered_pattern = r'(\d+\.\s*[^0-9]{15,}?)(?=\d+\.|$)'  # 至少15个字符
            numbered_parts = re.findall(numbered_pattern, cleaned_description, re.DOTALL)
            if len(numbered_parts) >= expected_count:
                valid_parts = []
                for part in numbered_parts[:expected_count]:
                    cleaned_part = self._validate_problem_fragment(part.strip())
                    if cleaned_part:
                        valid_parts.append(cleaned_part)
                if len(valid_parts) >= expected_count:
                    return valid_parts[:expected_count]

            # 策略2: 按完整句子分割（确保句子完整性）
            complete_sentences = self._extract_complete_sentences(cleaned_description)
            if len(complete_sentences) >= expected_count:
                return complete_sentences[:expected_count]

            # 策略3: 智能语义分割
            semantic_parts = self._semantic_split_description(cleaned_description, expected_count)
            if len(semantic_parts) >= expected_count:
                return semantic_parts[:expected_count]

            # 策略4: 如果无法合理分割，生成基于原描述的有意义变体
            if expected_count <= 3:
                return self._generate_meaningful_variants(cleaned_description, expected_count)

            # 最后备选：返回原描述，避免无意义的碎片化
            self.logger.warning(f"无法合理分割描述，返回原始内容: {cleaned_description[:50]}...")
            return [cleaned_description]

        except Exception as e:
            self.logger.warning(f"分割描述失败: {str(e)}")
            return [description.strip()] if description.strip() else []

    def _clean_description_text(self, description: str) -> str:
        """清理描述文本，移除碎片化内容"""
        if not description:
            return ""

        import re
        cleaned = description.strip()

        # 移除开头的标点符号
        cleaned = re.sub(r'^[：；，。]+', '', cleaned)

        # 检查是否是明显的碎片
        fragment_patterns = [
            r'^[a-zA-Z/_\.]+$',  # 只有路径片段
            r'^[：；，。\s]+$',   # 只有标点符号和空格
            r'^.{1,4}$',         # 太短的片段
        ]

        for pattern in fragment_patterns:
            if re.match(pattern, cleaned):
                return ""

        return cleaned

    def _validate_problem_fragment(self, fragment: str) -> str:
        """验证问题片段的有效性"""
        if not fragment or len(fragment.strip()) < 10:
            return ""

        import re

        # 检查是否是无意义的片段
        invalid_patterns = [
            r'^[：；，。]+',      # 只有标点符号开头
            r'^[a-zA-Z/_\.]+$',   # 只有路径片段
            r'^存在\d+个P[0-9]级问题$',  # 只是统计信息
            r'^[^a-zA-Z\u4e00-\u9fff]+$',  # 没有字母或中文
        ]

        for pattern in invalid_patterns:
            if re.match(pattern, fragment.strip()):
                return ""

        return fragment.strip()

    def _extract_complete_sentences(self, description: str) -> List[str]:
        """提取完整句子"""
        sentences = []

        # 按句号分割，但确保句子完整性
        parts = description.split('。')

        for part in parts:
            part = part.strip()
            if len(part) >= 15:  # 确保句子有足够长度
                # 检查句子是否包含有意义的内容
                if self._is_meaningful_sentence(part):
                    sentences.append(part + '。')

        return sentences

    def _is_meaningful_sentence(self, text: str) -> bool:
        """检查是否是有意义的句子"""
        if len(text) < 10:
            return False

        # 检查是否包含动词或关键词
        meaningful_keywords = [
            '存在', '包括', '涉及', '需要', '建议', '发现', '缺少', '错误',
            '问题', '风险', '安全', '质量', '性能', '优化', '修复', '改进'
        ]

        return any(keyword in text for keyword in meaningful_keywords)

    def _semantic_split_description(self, description: str, expected_count: int) -> List[str]:
        """基于语义的智能分割"""
        # 寻找语义分界点
        split_markers = ['；', '，同时', '另外', '此外', '以及', '包括', '涉及']

        parts = [description]
        for marker in split_markers:
            new_parts = []
            for part in parts:
                if marker in part:
                    sub_parts = part.split(marker)
                    for sub_part in sub_parts:
                        cleaned_sub = self._validate_problem_fragment(sub_part)
                        if cleaned_sub:
                            new_parts.append(cleaned_sub)
                else:
                    new_parts.append(part)
            parts = new_parts

            if len(parts) >= expected_count:
                break

        # 返回有效的部分
        valid_parts = [p for p in parts if self._validate_problem_fragment(p)]
        return valid_parts

    def _generate_meaningful_variants(self, base_description: str, count: int) -> List[str]:
        """基于基础描述生成有意义的变体"""
        if count == 1:
            return [base_description]

        variants = []

        # 根据描述内容生成相关变体
        if '安全' in base_description or '风险' in base_description:
            variants = [
                base_description,
                "代码中存在潜在的安全隐患，需要进行安全审查",
                "建议加强安全防护措施，避免数据泄露风险"
            ]
        elif '语法' in base_description or '错误' in base_description:
            variants = [
                base_description,
                "代码存在语法或逻辑错误，影响正常运行",
                "建议修复代码错误，确保程序正确性"
            ]
        elif 'P0' in base_description:
            variants = [
                base_description,
                "发现严重级别问题，需要立即处理",
                "建议优先修复高优先级问题"
            ]
        elif 'P1' in base_description:
            variants = [
                base_description,
                "发现重要级别问题，建议及时处理",
                "建议在下个版本中修复这些问题"
            ]
        else:
            variants = [
                base_description,
                "代码质量需要改进，建议进行重构",
                "建议优化代码结构，提高可维护性"
            ]

        # 确保变体数量符合要求
        while len(variants) < count:
            variants.append(f"{base_description} (变体{len(variants)})")

        return variants[:count]

    def _generate_additional_problems(self, scene: str, base_description: str, count: int) -> List[str]:
        """生成额外的问题描述"""
        additional_problems = []

        # 基于场景和基础描述生成相关问题
        if scene == "P0":
            base_problems = [
                ".env文件中存在敏感信息直接暴露的风险",
                "用户身份验证逻辑存在安全隐患",
                "代码中存在SQL注入漏洞",
                "API接口缺少权限验证",
                "密码存储未加密处理"
            ]
        elif scene == "P1":
            base_problems = [
                "缺少异常处理机制",
                "输入参数验证不完整",
                "日志记录不规范",
                "错误信息暴露敏感信息",
                "资源未正确释放",
                "并发访问控制缺失",
                "配置文件安全性不足",
                "第三方依赖版本过旧",
                "代码注释不完整",
                "函数复杂度过高"
            ]
        elif scene == "P2":
            base_problems = [
                "代码格式不规范",
                "变量命名不清晰",
                "冗余代码未清理",
                "注释信息过时",
                "代码结构可优化",
                "性能可进一步提升",
                "测试覆盖率不足",
                "文档需要更新"
            ]
        else:
            # 通用问题
            base_problems = [
                f"{scene}相关的代码质量问题",
                f"{scene}相关的安全隐患",
                f"{scene}相关的性能问题",
                f"{scene}相关的维护性问题"
            ]

        # 随机选择问题，确保数量足够
        import random
        available_problems = base_problems * ((count // len(base_problems)) + 1)
        selected_problems = random.sample(available_problems, min(count, len(available_problems)))

        return selected_problems

    def _generate_generic_suggestion(self, scene: str, description: str) -> str:
        """生成通用建议"""
        suggestions = {
            'P0': '建议立即修复此严重问题，以避免系统故障或安全风险',
            'P1': '建议优先修复此重要问题，以提高代码质量和稳定性',
            'P2': '建议在合适的时机修复此问题，以改善代码可维护性'
        }

        base_suggestion = suggestions.get(scene, '建议根据问题描述进行相应的修复')

        # 基于描述内容生成更具体的建议
        if '依赖' in description:
            return f"{base_suggestion}。特别注意依赖版本的兼容性和安全性。"
        elif '安全' in description:
            return f"{base_suggestion}。请进行安全代码审查并加强输入验证。"
        elif '性能' in description:
            return f"{base_suggestion}。建议进行性能测试和优化。"
        else:
            return base_suggestion

    def _determine_impact_level_from_scene(self, scene: str) -> str:
        """根据场景确定影响级别"""
        impact_mapping = {
            'P0': '高影响',
            'P1': '中影响',
            'P2': '低影响'
        }
        return impact_mapping.get(scene, '中影响')

    def _determine_fix_complexity_from_scene(self, scene: str) -> str:
        """根据场景确定修复复杂度"""
        complexity_mapping = {
            'P0': '高',
            'P1': '中',
            'P2': '低'
        }
        return complexity_mapping.get(scene, '中')

    def _find_description_for_scene(self, result_desc: Dict[str, str], scene: str) -> str:
        """为新的场景格式查找对应的描述"""
        # 如果result_desc不是字典，返回空字符串
        if not isinstance(result_desc, dict):
            self.logger.warning(f"result_desc不是字典类型: {type(result_desc)}")
            return ""
        # 场景映射关系 - 增强版
        scene_mapping = {
            '安全风险': ['安全', 'security', '风险', 'risk', 'P0', '严重', '密钥', 'SQL', '注入'],
            '代码质量': ['质量', 'quality', '代码', 'code', 'P1', '异常', '处理', '验证', '命名'],
            '性能优化': ['性能', 'performance', '优化', 'optimization', 'P2', '效率', '算法'],
            '最佳实践': ['实践', 'practice', '规范', 'standard', 'P2', '风格', '注释', '日志'],
            '依赖管理': ['依赖', 'dependency', '包', 'package'],
            '错误处理': ['错误', 'error', '异常', 'exception']
        }

        # 直接匹配
        if scene in result_desc:
            self.logger.info(f"直接匹配场景 {scene}")
            return result_desc[scene]

        # 关键词匹配 - 增强版
        scene_keywords = scene_mapping.get(scene, [scene])

        # 按优先级排序的匹配
        best_match = ""
        best_score = 0

        for key, desc in result_desc.items():
            match_score = 0
            matched_keywords = []

            # 检查key和desc中的关键词匹配
            for keyword in scene_keywords:
                if keyword in key:
                    match_score += 2  # key匹配权重更高
                    matched_keywords.append(keyword)
                elif keyword in desc:
                    match_score += 1  # desc匹配权重较低
                    matched_keywords.append(keyword)

            if match_score > best_score:
                best_score = match_score
                best_match = desc
                self.logger.info(f"通过关键词 {matched_keywords} 匹配场景 {scene} 到描述 {key} (得分: {match_score})")

        if best_match:
            return best_match

        # 智能推测匹配 - 基于场景特征
        scene_priority_mapping = {
            '安全风险': ['P0'],  # 安全问题通常是P0
            '代码质量': ['P1', 'P0'],  # 代码质量问题通常是P1，也可能是P0
            '性能优化': ['P2', 'P1'],  # 性能问题通常是P2
            '最佳实践': ['P2']   # 最佳实践通常是P2
        }

        priority_levels = scene_priority_mapping.get(scene, ['P0', 'P1', 'P2'])

        for level in priority_levels:
            if level in result_desc:
                self.logger.info(f"通过优先级推测匹配场景 {scene} 到级别 {level}")
                return result_desc[level]

        # 如果还是找不到，尝试按顺序匹配
        desc_keys = list(result_desc.keys())
        if desc_keys:
            # 根据场景名称的严重程度推测
            severity_order = ['安全风险', '代码质量', '性能优化', '最佳实践']
            if scene in severity_order:
                index = severity_order.index(scene)
                if index < len(desc_keys):
                    matched_key = desc_keys[index]
                    self.logger.info(f"通过顺序匹配场景 {scene} 到描述 {matched_key}")
                    return result_desc[matched_key]

        self.logger.warning(f"所有匹配方式都失败，场景 {scene} 无法找到对应描述")
        return ""

    def _generate_generic_problems(self, scene: str, num: int, start_problem_id: int) -> List[EnhancedProblemDetail]:
        """当找不到具体描述时，生成通用问题"""
        problems = []

        # 场景对应的通用问题模板
        generic_templates = {
            '安全风险': [
                "发现潜在的安全漏洞，需要进行安全审查",
                "存在输入验证不足的问题",
                "发现敏感信息泄露风险",
                "存在权限控制不当的问题",
                "发现SQL注入或XSS攻击风险"
            ],
            '代码质量': [
                "代码结构需要优化",
                "存在代码重复问题",
                "变量命名不规范",
                "缺少必要的注释",
                "函数复杂度过高"
            ],
            '性能优化': [
                "存在性能瓶颈",
                "算法效率可以改进",
                "内存使用不当",
                "数据库查询效率低"
            ],
            '最佳实践': [
                "未遵循编码规范",
                "缺少错误处理",
                "代码可维护性差",
                "测试覆盖率不足"
            ]
        }

        # 获取对应场景的模板
        templates = generic_templates.get(scene, [f"{scene}相关问题需要关注"])

        # 生成指定数量的问题
        for i in range(num):
            problem_id = start_problem_id + i
            template_index = i % len(templates)
            problem_desc = templates[template_index]

            # 如果问题数量超过模板数量，添加序号区分
            if i >= len(templates):
                problem_desc = f"{problem_desc} ({i + 1})"

            # 确定问题级别
            level = self._determine_level_from_scene(scene)
            severity = self.severity_mapping.get(level.lower(), ProblemSeverity.MODERATE)

            # 生成通用问题
            generic_problem = EnhancedProblemDetail(
                id=str(problem_id),
                title=f"{scene}问题 {i+1}",
                severity=severity,
                level=level,
                line_number=1,
                description=problem_desc,
                target_code="",
                suggestion=self._generate_generic_suggestion_for_scene(scene, problem_desc),
                code_position="",
                code_position_array=[1, 0, 1, 0],
                category=scene,
                original_detail={
                    'problem': problem_desc,
                    'level': level.lower(),
                    'scene': scene,
                    'synthetic': True,
                    'generic': True
                },
                file_path=None,
                rule_name=f"{scene}_generic_rule",
                rule_description=f"基于{scene}场景生成的通用问题",
                impact_level=self._determine_impact_level_from_scene_name(scene),
                fix_complexity=self._determine_fix_complexity_from_scene_name(scene),
                detection_method='generic_generation',
                confidence_score=0.5,  # 通用生成的置信度较低
                related_problems=[],
                fix_examples=[],
                reference_links=[]
            )

            problems.append(generic_problem)
            self.logger.debug(f"✅ 生成通用问题: {generic_problem.title}")

        self.logger.info(f"✅ 为场景 {scene} 生成了 {len(problems)} 个通用问题")
        return problems

    def _determine_level_from_scene(self, scene: str) -> str:
        """根据场景名称确定问题级别"""
        level_mapping = {
            '安全风险': 'P0',
            '代码质量': 'P1',
            '性能优化': 'P2',
            '最佳实践': 'P2'
        }
        return level_mapping.get(scene, 'P2')

    def _determine_impact_level_from_scene_name(self, scene: str) -> str:
        """根据场景名称确定影响级别"""
        impact_mapping = {
            '安全风险': '高影响',
            '代码质量': '中影响',
            '性能优化': '中影响',
            '最佳实践': '低影响'
        }
        return impact_mapping.get(scene, '中影响')

    def _determine_fix_complexity_from_scene_name(self, scene: str) -> str:
        """根据场景名称确定修复复杂度"""
        complexity_mapping = {
            '安全风险': '高',
            '代码质量': '中',
            '性能优化': '高',
            '最佳实践': '低'
        }
        return complexity_mapping.get(scene, '中')

    def _generate_generic_suggestion_for_scene(self, scene: str, description: str) -> str:
        """为场景生成通用建议"""
        suggestions = {
            '安全风险': '建议立即进行安全审查，修复潜在的安全漏洞，加强输入验证和权限控制',
            '代码质量': '建议重构代码，提高代码质量，遵循编码规范，增加代码注释',
            '性能优化': '建议进行性能分析，优化算法和数据结构，提高执行效率',
            '最佳实践': '建议遵循最佳实践，完善错误处理，提高代码可维护性'
        }

        base_suggestion = suggestions.get(scene, f'建议针对{scene}相关问题进行改进')

        # 基于描述内容生成更具体的建议
        if '安全' in description:
            return f"{base_suggestion}。特别注意数据加密和访问控制。"
        elif '性能' in description:
            return f"{base_suggestion}。建议进行性能测试和监控。"
        elif '质量' in description:
            return f"{base_suggestion}。建议使用代码质量检查工具。"
        else:
            return base_suggestion

    def _determine_overall_result(self, statistics: ProblemStatistics, original_result: Dict[str, Any]) -> str:
        """确定总体结果 - 基于实际问题统计，确保数据一致性"""
        try:
            # 严格基于实际解析的问题统计确定结果
            self.logger.info(f"确定总体结果: P0={statistics.critical_count}, P1={statistics.warning_count}, P2={statistics.moderate_count}, P3+={statistics.minor_count}, 总计={statistics.total_count}")

            # 有严重问题(P0)必须不通过
            if statistics.critical_count > 0:
                self.logger.info(f"存在{statistics.critical_count}个严重问题(P0)，结果为不通过")
                return "不通过"

            # 计算评分来判断是否通过
            base_score = 100
            score_deduction = (
                statistics.critical_count * abs(self.score_weights[ProblemSeverity.CRITICAL]) +
                statistics.warning_count * abs(self.score_weights[ProblemSeverity.WARNING]) +
                statistics.moderate_count * abs(self.score_weights[ProblemSeverity.MODERATE]) +
                statistics.minor_count * abs(self.score_weights[ProblemSeverity.MINOR])
            )
            final_score = max(0, base_score - score_deduction)

            # 评分低于80分不通过
            if final_score < 80:
                self.logger.info(f"评分{final_score}分低于80分，结果为不通过")
                return "不通过"

            # 无问题通过
            if statistics.total_count == 0:
                self.logger.info("无问题发现，结果为通过")
                return "通过"

            # 有问题但评分达标，通过
            self.logger.info(f"有{statistics.total_count}个问题但评分{final_score}分达标，结果为通过")
            return "通过"

        except Exception as e:
            self.logger.warning(f"确定总体结果失败: {str(e)}")
            # 默认：有问题就不通过
            result = "不通过" if statistics.total_count > 0 else "通过"
            self.logger.warning(f"使用默认逻辑，结果为: {result}")
            return result

    def _extract_files_for_scene(self, scene: str, existing_files: set) -> List[str]:
        """为场景提取相关文件列表"""
        try:
            # 如果已有文件信息，直接使用
            if existing_files:
                return list(existing_files)

            # 从原始结果中获取文件列表
            original_result = getattr(self, '_current_original_result', {})
            check_branch = original_result.get('checkBranch', [])

            # 如果checkBranch是文件列表
            if isinstance(check_branch, list) and check_branch:
                # 根据场景类型智能分配文件
                scene_file_mapping = {
                    '安全风险': [f for f in check_branch if any(keyword in f.lower() for keyword in
                                ['api', 'service', 'auth', 'login', 'password', 'key', '.env'])],
                    '代码质量': [f for f in check_branch if any(keyword in f.lower() for keyword in
                                ['app', 'main', 'core', 'utils', 'common'])],
                    '性能优化': [f for f in check_branch if any(keyword in f.lower() for keyword in
                                ['db', 'models', 'query', 'cache', 'performance'])],
                    '最佳实践': [f for f in check_branch if any(keyword in f.lower() for keyword in
                                ['test', 'config', 'requirements', 'readme'])]
                }

                scene_files = scene_file_mapping.get(scene, [])

                # 如果没有匹配的文件，随机分配一些
                if not scene_files:
                    # 根据场景优先级分配文件
                    if scene == '安全风险':
                        scene_files = [f for f in check_branch if 'api' in f or 'service' in f][:3]
                    elif scene == '代码质量':
                        scene_files = [f for f in check_branch if 'app' in f or 'main' in f][:5]
                    elif scene == '性能优化':
                        scene_files = [f for f in check_branch if 'db' in f or 'models' in f][:2]
                    elif scene == '最佳实践':
                        scene_files = [f for f in check_branch if 'test' in f or 'requirements' in f][:3]

                    # 如果还是没有，就取前几个文件
                    if not scene_files:
                        file_count = min(3, len(check_branch))
                        scene_files = check_branch[:file_count]

                return scene_files if scene_files else ["相关源文件"]

            # 如果没有文件信息，根据场景生成通用文件名
            generic_files = {
                '安全风险': ["api/auth_service.py", "api/security_utils.py", ".env"],
                '代码质量': ["src/main.py", "src/utils.py", "src/models.py"],
                '性能优化': ["db/models.py", "cache/redis_client.py"],
                '最佳实践': ["tests/", "requirements.txt", "README.md"]
            }

            return generic_files.get(scene, ["源代码文件"])

        except Exception as e:
            self.logger.warning(f"提取场景文件失败: {str(e)}")
            return ["相关源文件"]

    def _generate_task_execution_details(self, problem_list: List[Dict[str, Any]], problems: List[EnhancedProblemDetail]) -> List[TaskExecutionDetail]:
        """生成任务执行详情"""
        task_details = []

        try:
            # 按类别分组统计
            category_stats = {}
            for problem in problems:
                category = problem.category
                if category not in category_stats:
                    category_stats[category] = {
                        'problems': [],
                        'files': set(),
                        'rules': set()
                    }
                category_stats[category]['problems'].append(problem)
                if problem.file_path:
                    category_stats[category]['files'].add(problem.file_path)
                if problem.rule_name:
                    category_stats[category]['rules'].add(problem.rule_name)

            # 为每个类别生成任务详情
            for category_index, category_data in enumerate(problem_list):
                scene = category_data.get('scene', 'unknownCheck')
                check_result = category_data.get('checkResult', '未知')
                details = category_data.get('detail', [])

                # 计算执行状态
                execution_status = "success" if check_result == "通过" else "failed" if len(details) > 0 else "partial"

                # 获取该类别的统计信息
                category_problems = category_stats.get(scene, {'problems': [], 'files': set(), 'rules': set()})

                # 从原始结果中提取文件信息
                files_checked = self._extract_files_for_scene(scene, category_problems['files'])

                # 生成任务详情
                task_detail = TaskExecutionDetail(
                    task_name=scene,
                    task_type=self._determine_task_type(scene),
                    execution_status=execution_status,
                    execution_time_ms=self._estimate_execution_time(len(details)),
                    rules_applied=list(category_problems['rules']) if category_problems['rules'] else [f"{scene}_default_rule"],
                    files_checked=files_checked,
                    coverage_percentage=self._calculate_coverage_percentage(scene, len(files_checked)),
                    problems_found=len(category_problems['problems']),
                    task_summary=self._generate_task_summary_text(scene, len(category_problems['problems']), execution_status),
                    detailed_metrics=self._generate_task_metrics(category_problems['problems']),
                    recommendations=self._generate_task_recommendations(scene, category_problems['problems'])
                )

                task_details.append(task_detail)
                self.logger.debug(f"✅ 生成任务详情: {scene} - {execution_status}")

            self.logger.info(f"✅ 任务执行详情生成完成，共 {len(task_details)} 个任务")
            return task_details

        except Exception as e:
            self.logger.error(f"❌ 生成任务执行详情失败: {str(e)}")
            return []

    def _determine_task_type(self, scene: str) -> str:
        """确定任务类型"""
        task_type_mapping = {
            'commonCheck': 'static_analysis',
            'customCheck': 'custom_rules',
            '依赖管理': 'dependency_check',
            '代码规范': 'code_style',
            '安全检查': 'security_scan',
            '性能分析': 'performance_analysis',
            'LLM审查': 'llm_review'
        }
        return task_type_mapping.get(scene, 'general_check')

    def _estimate_execution_time(self, problem_count: int) -> int:
        """估算执行时间（毫秒）"""
        # 基于问题数量估算执行时间
        base_time = 500  # 基础时间500ms
        per_problem_time = 100  # 每个问题增加100ms
        return base_time + (problem_count * per_problem_time)

    def _calculate_coverage_percentage(self, scene: str, file_count: int) -> float:
        """计算覆盖率百分比"""
        # 简化的覆盖率计算，实际应该基于真实的文件扫描情况
        if file_count == 0:
            return 0.0
        elif file_count <= 5:
            coverage = min(95.0, file_count * 20.0)
        else:
            coverage = 95.0

        # 保留两位小数
        return round(coverage, 2)

    def _generate_task_summary_text(self, scene: str, problem_count: int, execution_status: str) -> str:
        """生成任务总结文本"""
        status_desc = {
            'success': '执行成功',
            'failed': '发现问题',
            'partial': '部分完成'
        }

        if problem_count == 0:
            return f"{scene}检查完成，未发现问题"
        else:
            return f"{scene}检查{status_desc.get(execution_status, '完成')}，发现{problem_count}个问题"

    def _generate_task_metrics(self, problems: List[EnhancedProblemDetail]) -> Dict[str, Any]:
        """生成任务指标"""
        if not problems:
            return {
                'problemsByLevel': {},
                'avgConfidenceScore': 0.0,
                'detectionMethods': {},
                'impactDistribution': {}
            }

        # 按级别统计问题
        problems_by_level = {}
        confidence_scores = []
        detection_methods = {}
        impact_distribution = {}

        for problem in problems:
            # 统计级别
            level = problem.level
            problems_by_level[level] = problems_by_level.get(level, 0) + 1

            # 统计置信度
            if problem.confidence_score is not None:
                confidence_scores.append(problem.confidence_score)

            # 统计检测方法
            method = problem.detection_method or 'unknown'
            detection_methods[method] = detection_methods.get(method, 0) + 1

            # 统计影响级别
            impact = problem.impact_level or '未知影响'
            impact_distribution[impact] = impact_distribution.get(impact, 0) + 1

        # 计算平均置信度，保留两位小数
        avg_confidence = sum(confidence_scores) / len(confidence_scores) if confidence_scores else 0.0
        avg_confidence = round(avg_confidence, 2)

        return {
            'problemsByLevel': problems_by_level,
            'avgConfidenceScore': avg_confidence,
            'detectionMethods': detection_methods,
            'impactDistribution': impact_distribution
        }

    def _generate_task_recommendations(self, scene: str, problems: List[EnhancedProblemDetail]) -> List[str]:
        """生成任务级别的建议"""
        recommendations = []

        if not problems:
            recommendations.append(f"✅ {scene}检查通过，继续保持良好的代码质量")
            return recommendations

        # 基于问题数量和类型生成建议
        problem_count = len(problems)
        critical_problems = [p for p in problems if p.severity == ProblemSeverity.CRITICAL]

        if critical_problems:
            recommendations.append(f"🚨 {scene}发现{len(critical_problems)}个严重问题，建议立即修复")

        if problem_count > 5:
            recommendations.append(f"📊 {scene}问题较多({problem_count}个)，建议分批处理")

        # 基于场景类型生成特定建议
        scene_specific_recommendations = {
            '依赖管理': [
                "🔧 建议定期更新依赖版本",
                "📦 考虑使用依赖锁定文件"
            ],
            '代码规范': [
                "📝 建议配置代码格式化工具",
                "🎯 考虑引入代码质量门禁"
            ],
            '安全检查': [
                "🔒 建议进行安全代码审查培训",
                "🛡️ 考虑集成安全扫描工具"
            ]
        }

        if scene in scene_specific_recommendations:
            recommendations.extend(scene_specific_recommendations[scene])

        return recommendations[:5]  # 最多返回5条建议

    def _determine_detection_method(self, detail: Dict[str, Any], scene: str) -> str:
        """确定检测方法"""
        # 基于场景和问题特征判断检测方法
        problem_desc = detail.get('problem', '').lower()

        if 'llm' in scene.lower() or 'ai' in scene.lower():
            return 'llm_review'
        elif scene in ['commonCheck', '代码规范', '语法检查']:
            return 'static_analysis'
        elif scene in ['customCheck', '自定义规则']:
            return 'rule_based'
        elif '依赖' in scene or 'dependency' in scene.lower():
            return 'dependency_analysis'
        elif '安全' in problem_desc or 'security' in problem_desc:
            return 'security_scan'
        else:
            return 'general_check'

    def _calculate_confidence_score(self, detail: Dict[str, Any], severity: ProblemSeverity) -> float:
        """计算置信度评分"""
        try:
            # 基于问题严重程度和描述详细程度计算置信度
            base_score = 0.7  # 基础置信度

            # 严重程度影响置信度
            severity_bonus = {
                ProblemSeverity.CRITICAL: 0.2,
                ProblemSeverity.WARNING: 0.15,
                ProblemSeverity.MODERATE: 0.1,
                ProblemSeverity.MINOR: 0.05
            }

            score = base_score + severity_bonus.get(severity, 0.0)

            # 如果有具体的代码位置，增加置信度
            if detail.get('codePosition') and detail.get('targetCode'):
                score += 0.1

            # 如果有详细的建议，增加置信度
            suggestion = detail.get('suggestion', '')
            if len(suggestion) > 20:
                score += 0.05

            return min(1.0, score)  # 最大值为1.0

        except Exception:
            return 0.7  # 默认置信度

    def _generate_fix_examples(self, detail: Dict[str, Any]) -> List[str]:
        """生成修复示例"""
        examples = []

        try:
            problem = detail.get('problem', '').lower()
            target_code = detail.get('targetCode', '')
            suggestion = detail.get('suggestion', '')

            # 基于问题类型生成示例
            if '变量' in problem and '未定义' in problem:
                if target_code:
                    # 提取变量名
                    import re
                    var_match = re.search(r'(\w+)', target_code)
                    if var_match:
                        var_name = var_match.group(1)
                        examples.append(f"let {var_name} = null; // 在使用前声明变量")
                        examples.append(f"const {var_name} = defaultValue; // 使用默认值初始化")

            elif '函数' in problem:
                examples.append("// 添加函数参数验证")
                examples.append("// 添加返回值类型注解")

            elif '依赖' in problem:
                examples.append("// 更新到稳定版本")
                examples.append("// 检查依赖兼容性")

            # 如果建议中包含具体的修复方法，提取出来
            if suggestion and len(suggestion) > 10:
                examples.append(f"建议: {suggestion[:100]}...")

            return examples[:3]  # 最多返回3个示例

        except Exception:
            return ["请参考建议进行修复"]

    def _generate_reference_links(self, detail: Dict[str, Any], scene: str) -> List[str]:
        """生成参考链接"""
        links = []

        try:
            problem = detail.get('problem', '').lower()

            # 基于问题类型和场景生成相关链接
            if '依赖' in scene or 'dependency' in scene.lower():
                links.append("https://docs.npmjs.com/about-semantic-versioning")
                links.append("https://semver.org/")

            elif '安全' in problem:
                links.append("https://owasp.org/www-project-top-ten/")
                links.append("https://cheatsheetseries.owasp.org/")

            elif '性能' in problem:
                links.append("https://web.dev/performance/")
                links.append("https://developer.mozilla.org/en-US/docs/Web/Performance")

            elif 'javascript' in problem or 'js' in problem:
                links.append("https://developer.mozilla.org/en-US/docs/Web/JavaScript")
                links.append("https://javascript.info/")

            elif 'python' in problem:
                links.append("https://docs.python.org/3/")
                links.append("https://pep8.org/")

            # 通用编程最佳实践
            if not links:
                links.append("https://github.com/ryanmcdermott/clean-code-javascript")
                links.append("https://refactoring.guru/")

            return links[:3]  # 最多返回3个链接

        except Exception:
            return []
    
    def _calculate_statistics(self, problems: List[EnhancedProblemDetail]) -> ProblemStatistics:
        """计算问题统计"""
        stats = ProblemStatistics()
        
        for problem in problems:
            if problem.severity == ProblemSeverity.CRITICAL:
                stats.critical_count += 1
            elif problem.severity == ProblemSeverity.WARNING:
                stats.warning_count += 1
            elif problem.severity == ProblemSeverity.MODERATE:
                stats.moderate_count += 1
            else:
                stats.minor_count += 1
        
        return stats
    
    def _calculate_score(self, statistics: ProblemStatistics) -> int:
        """计算总体评分"""
        base_score = 100
        
        # 根据问题数量和严重程度扣分
        score_deduction = (
            statistics.critical_count * abs(self.score_weights[ProblemSeverity.CRITICAL]) +
            statistics.warning_count * abs(self.score_weights[ProblemSeverity.WARNING]) +
            statistics.moderate_count * abs(self.score_weights[ProblemSeverity.MODERATE]) +
            statistics.minor_count * abs(self.score_weights[ProblemSeverity.MINOR])
        )
        
        final_score = max(0, base_score - score_deduction)
        return final_score
    
    def _generate_description(self, statistics: ProblemStatistics) -> str:
        """生成结果描述 - 统一格式为P0:X个,P1:X个,P2:X个"""
        if statistics.total_count == 0:
            return "代码质量良好，未发现问题"

        parts = []
        if statistics.critical_count > 0:
            parts.append(f"P0:{statistics.critical_count}个")
        if statistics.warning_count > 0:
            parts.append(f"P1:{statistics.warning_count}个")
        if statistics.moderate_count > 0:
            parts.append(f"P2:{statistics.moderate_count}个")
        if statistics.minor_count > 0:
            parts.append(f"P3+:{statistics.minor_count}个")

        return ",".join(parts)
    
    def _extract_line_number(self, code_position: str) -> int:
        """从代码位置字符串中提取行号"""
        try:
            if ':' in code_position:
                # 格式: "file.py:10-15" 或 "第 15 行"
                if '第' in code_position and '行' in code_position:
                    match = re.search(r'第\s*(\d+)\s*行', code_position)
                    if match:
                        return int(match.group(1))
                else:
                    # 提取冒号后的数字
                    parts = code_position.split(':')[-1]
                    if '-' in parts:
                        return int(parts.split('-')[0])
                    else:
                        return int(parts)
            return 1
        except:
            return 1

    def _extract_file_path(self, code_position: str) -> Optional[str]:
        """从代码位置字符串中提取文件路径"""
        try:
            if ':' in code_position and not ('第' in code_position and '行' in code_position):
                # 格式: "src/main.py:10-15"
                return code_position.split(':')[0]
            return None
        except:
            return None

    def _analyze_fix_complexity(self, detail: Dict[str, Any], severity: ProblemSeverity) -> str:
        """分析修复复杂度"""
        try:
            problem = detail.get('problem', '').lower()
            target_code = detail.get('targetCode', '').lower()

            # 基于问题类型和严重程度判断复杂度
            if severity == ProblemSeverity.CRITICAL:
                return "高"
            elif severity == ProblemSeverity.WARNING:
                if any(keyword in problem for keyword in ['架构', '设计', '重构']):
                    return "高"
                elif any(keyword in problem for keyword in ['逻辑', '算法', '性能']):
                    return "中"
                else:
                    return "中"
            elif severity == ProblemSeverity.MODERATE:
                if any(keyword in problem for keyword in ['格式', '命名', '注释']):
                    return "低"
                else:
                    return "中"
            else:
                return "低"
        except:
            return "中"

    def _determine_impact_level(self, severity: ProblemSeverity, detail: Dict[str, Any]) -> str:
        """确定影响级别"""
        try:
            problem = detail.get('problem', '').lower()

            if severity == ProblemSeverity.CRITICAL:
                return "高影响"
            elif severity == ProblemSeverity.WARNING:
                if any(keyword in problem for keyword in ['安全', '性能', '内存']):
                    return "高影响"
                else:
                    return "中影响"
            elif severity == ProblemSeverity.MODERATE:
                return "中影响"
            else:
                return "低影响"
        except:
            return "中影响"

    def _extract_extra_fields(self, detail: Dict[str, Any]) -> Dict[str, Any]:
        """提取detail中的所有额外字段，确保没有信息丢失"""
        try:
            # 定义已知的标准字段
            standard_fields = {
                'level', 'problem', 'targetCode', 'suggestion', 'codePosition',
                'codePositionArray', 'ruleName', 'ruleDescription'
            }

            # 提取所有额外字段
            extra_fields = {}
            for key, value in detail.items():
                if key not in standard_fields:
                    extra_fields[key] = value

            if extra_fields:
                self.logger.debug(f"发现额外字段: {list(extra_fields.keys())}")

            return extra_fields
        except Exception as e:
            self.logger.warning(f"提取额外字段失败: {str(e)}")
            return {}

    def _create_fallback_problem(self, detail: Dict[str, Any], scene: str, problem_id: int) -> Optional[EnhancedProblemDetail]:
        """创建降级问题对象，确保即使解析失败也能保留原始信息"""
        try:
            # 使用最基本的信息创建问题对象
            return EnhancedProblemDetail(
                id=str(problem_id),
                title=f"解析失败的问题 {problem_id}",
                severity=ProblemSeverity.MODERATE,  # 默认中等严重程度
                level="P2",
                line_number=1,
                description=detail.get('problem', '问题描述解析失败'),
                target_code=detail.get('targetCode', ''),
                suggestion=detail.get('suggestion', '建议解析失败'),
                code_position=detail.get('codePosition', '位置未知'),
                code_position_array=[1, 0, 1, 0],
                category=scene,
                original_detail=dict(detail),  # 保留完整原始信息
                file_path=None,
                rule_name=f"fallback_{scene}",
                rule_description="降级处理的问题",
                impact_level="中影响",
                fix_complexity="中"
            )
        except Exception as e:
            self.logger.error(f"创建降级问题失败: {str(e)}")
            return None

    def _generate_summary_details(self, statistics: ProblemStatistics, problems: List[EnhancedProblemDetail], original_result: Dict[str, Any]) -> Dict[str, Any]:
        """生成详细总结信息"""
        try:
            # 按类别统计问题
            category_stats = {}
            for problem in problems:
                category = problem.category
                if category not in category_stats:
                    category_stats[category] = {
                        'count': 0,
                        'critical': 0,
                        'warning': 0,
                        'moderate': 0,
                        'minor': 0
                    }
                category_stats[category]['count'] += 1
                if problem.severity == ProblemSeverity.CRITICAL:
                    category_stats[category]['critical'] += 1
                elif problem.severity == ProblemSeverity.WARNING:
                    category_stats[category]['warning'] += 1
                elif problem.severity == ProblemSeverity.MODERATE:
                    category_stats[category]['moderate'] += 1
                else:
                    category_stats[category]['minor'] += 1

            # 按文件统计问题
            file_stats = {}
            for problem in problems:
                file_path = problem.file_path or "未知文件"
                if file_path not in file_stats:
                    file_stats[file_path] = 0
                file_stats[file_path] += 1

            return {
                'categoryStatistics': category_stats,
                'fileStatistics': file_stats,
                'totalFiles': len(file_stats),
                'averageProblemsPerFile': round(statistics.total_count / max(len(file_stats), 1), 2),
                'originalCheckBranch': original_result.get('checkBranch', ''),
                'originalSumResult': original_result.get('sumCheckResult', ''),
                'originalResultDesc': original_result.get('resultDesc', ''),
                'originalTotalProblem': original_result.get('totalProblem', '0')
            }
        except Exception as e:
            self.logger.warning(f"生成详细总结失败: {str(e)}")
            return {}

    def _generate_review_metrics(self, statistics: ProblemStatistics, problems: List[EnhancedProblemDetail]) -> Dict[str, Any]:
        """生成审查指标"""
        try:
            # 计算质量指标
            total_problems = statistics.total_count
            if total_problems == 0:
                quality_score = 100
                risk_level = "低"
            else:
                # 基于问题严重程度计算风险级别
                risk_score = (
                    statistics.critical_count * 4 +
                    statistics.warning_count * 3 +
                    statistics.moderate_count * 2 +
                    statistics.minor_count * 1
                )

                if risk_score >= 10:
                    risk_level = "高"
                elif risk_score >= 5:
                    risk_level = "中"
                else:
                    risk_level = "低"

                quality_score = max(0, 100 - risk_score * 5)

            # 统计修复复杂度分布
            complexity_stats = {'高': 0, '中': 0, '低': 0}
            for problem in problems:
                complexity = problem.fix_complexity or '中'
                if complexity in complexity_stats:
                    complexity_stats[complexity] += 1

            # 统计影响级别分布
            impact_stats = {'高影响': 0, '中影响': 0, '低影响': 0}
            for problem in problems:
                impact = problem.impact_level or '中影响'
                if impact in impact_stats:
                    impact_stats[impact] += 1

            return {
                'qualityScore': quality_score,
                'riskLevel': risk_level,
                'complexityDistribution': complexity_stats,
                'impactDistribution': impact_stats,
                'totalProblems': total_problems,
                'problemDensity': f"{total_problems}/文件" if total_problems > 0 else "0/文件"
            }
        except Exception as e:
            self.logger.warning(f"生成审查指标失败: {str(e)}")
            return {}

    def _generate_recommendations(self, statistics: ProblemStatistics, problems: List[EnhancedProblemDetail]) -> List[str]:
        """生成改进建议"""
        try:
            recommendations = []

            # 基于问题统计生成建议
            if statistics.critical_count > 0:
                recommendations.append(f"🚨 发现{statistics.critical_count}个严重问题，建议立即修复")
                recommendations.append("💡 优先处理可能影响系统稳定性和安全性的问题")

            if statistics.warning_count > 0:
                recommendations.append(f"⚠️ 发现{statistics.warning_count}个警告问题，建议在下个版本中修复")

            if statistics.moderate_count > 0:
                recommendations.append(f"📝 发现{statistics.moderate_count}个中等问题，建议逐步改进")

            if statistics.minor_count > 0:
                recommendations.append(f"✨ 发现{statistics.minor_count}个轻微问题，可在代码重构时一并处理")

            # 基于问题类型生成建议
            problem_types = {}
            for problem in problems:
                problem_desc = problem.description.lower()
                if '变量' in problem_desc or '未定义' in problem_desc:
                    problem_types['变量定义'] = problem_types.get('变量定义', 0) + 1
                elif '函数' in problem_desc or '方法' in problem_desc:
                    problem_types['函数设计'] = problem_types.get('函数设计', 0) + 1
                elif '错误处理' in problem_desc or 'try-catch' in problem_desc:
                    problem_types['错误处理'] = problem_types.get('错误处理', 0) + 1
                elif '性能' in problem_desc:
                    problem_types['性能优化'] = problem_types.get('性能优化', 0) + 1
                elif '安全' in problem_desc:
                    problem_types['安全问题'] = problem_types.get('安全问题', 0) + 1

            # 针对高频问题类型给出建议
            for problem_type, count in problem_types.items():
                if count >= 2:
                    if problem_type == '变量定义':
                        recommendations.append("🔧 建议加强变量声明和初始化的代码规范")
                    elif problem_type == '函数设计':
                        recommendations.append("🏗️ 建议优化函数设计，遵循单一职责原则")
                    elif problem_type == '错误处理':
                        recommendations.append("🛡️ 建议完善错误处理机制，提高代码健壮性")
                    elif problem_type == '性能优化':
                        recommendations.append("⚡ 建议进行性能优化，提升代码执行效率")
                    elif problem_type == '安全问题':
                        recommendations.append("🔒 建议加强安全防护，进行安全代码审查")

            # 如果没有问题，给出积极建议
            if statistics.total_count == 0:
                recommendations.append("🎉 代码质量良好，继续保持高标准的编码规范")
                recommendations.append("📚 建议定期进行代码审查，持续改进代码质量")

            return recommendations[:10]  # 最多返回10条建议

        except Exception as e:
            self.logger.warning(f"生成改进建议失败: {str(e)}")
            return ["建议进行代码审查和质量改进"]

    def _generate_review_process_info(self, original_result: Dict[str, Any], task_execution_details: List[TaskExecutionDetail]) -> Dict[str, Any]:
        """生成审查过程信息"""
        try:
            # 计算总执行时间
            total_execution_time = sum(task.execution_time_ms for task in task_execution_details)

            # 统计执行状态
            status_stats = {}
            for task in task_execution_details:
                status = task.execution_status
                status_stats[status] = status_stats.get(status, 0) + 1

            # 统计任务类型
            task_type_stats = {}
            for task in task_execution_details:
                task_type = task.task_type
                task_type_stats[task_type] = task_type_stats.get(task_type, 0) + 1

            # 计算覆盖率统计
            coverage_scores = [task.coverage_percentage for task in task_execution_details if task.coverage_percentage > 0]
            avg_coverage = sum(coverage_scores) / len(coverage_scores) if coverage_scores else 0.0

            # 统计应用的规则
            all_rules = []
            for task in task_execution_details:
                all_rules.extend(task.rules_applied)
            unique_rules = list(set(all_rules))

            # 统计检查的文件
            all_files = []
            for task in task_execution_details:
                all_files.extend(task.files_checked)
            unique_files = list(set(all_files))

            return {
                'totalExecutionTimeMs': total_execution_time,
                'taskCount': len(task_execution_details),
                'executionStatusStats': status_stats,
                'taskTypeStats': task_type_stats,
                'averageCoverage': round(avg_coverage, 2),
                'totalRulesApplied': len(unique_rules),
                'totalFilesChecked': len(unique_files),
                'rulesApplied': unique_rules[:20],  # 最多显示20个规则
                'filesChecked': unique_files[:20],  # 最多显示20个文件
                'reviewMode': original_result.get('reviewMode', 'standard'),
                'reviewEngine': original_result.get('reviewEngine', 'AI代码审查'),
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            self.logger.warning(f"生成审查过程信息失败: {str(e)}")
            return {
                'totalExecutionTimeMs': 0,
                'taskCount': 0,
                'executionStatusStats': {},
                'taskTypeStats': {},
                'averageCoverage': 0.0,
                'totalRulesApplied': 0,
                'totalFilesChecked': 0,
                'timestamp': datetime.now().isoformat()
            }

    def _generate_quality_gates(self, statistics: ProblemStatistics, overall_score: int) -> Dict[str, Any]:
        """生成质量门禁信息"""
        try:
            # 定义质量门禁规则
            quality_gates = {
                'criticalProblemsGate': {
                    'name': '严重问题门禁',
                    'rule': '严重问题数量 = 0',
                    'current': statistics.critical_count,
                    'threshold': 0,
                    'passed': statistics.critical_count == 0,
                    'priority': 1
                },
                'overallScoreGate': {
                    'name': '总体评分门禁',
                    'rule': '总体评分 >= 80',
                    'current': overall_score,
                    'threshold': 80,
                    'passed': overall_score >= 80,
                    'priority': 2
                },
                'warningProblemsGate': {
                    'name': '警告问题门禁',
                    'rule': '警告问题数量 <= 3',
                    'current': statistics.warning_count,
                    'threshold': 3,
                    'passed': statistics.warning_count <= 3,
                    'priority': 3
                },
                'totalProblemsGate': {
                    'name': '总问题数门禁',
                    'rule': '总问题数量 <= 10',
                    'current': statistics.total_count,
                    'threshold': 10,
                    'passed': statistics.total_count <= 10,
                    'priority': 4
                }
            }

            # 计算通过的门禁数量
            passed_gates = sum(1 for gate in quality_gates.values() if gate['passed'])
            total_gates = len(quality_gates)

            # 确定整体门禁状态
            overall_gate_status = "PASSED" if passed_gates == total_gates else "FAILED"

            # 生成门禁建议
            gate_recommendations = []
            for gate_name, gate_info in quality_gates.items():
                if not gate_info['passed']:
                    if gate_name == 'criticalProblemsGate':
                        gate_recommendations.append("🚨 必须修复所有严重问题才能通过门禁")
                    elif gate_name == 'overallScoreGate':
                        gate_recommendations.append(f"📊 需要提升代码质量评分至80分以上（当前{gate_info['current']}分）")
                    elif gate_name == 'warningProblemsGate':
                        gate_recommendations.append(f"⚠️ 需要减少警告问题至3个以下（当前{gate_info['current']}个）")
                    elif gate_name == 'totalProblemsGate':
                        gate_recommendations.append(f"📝 需要减少总问题数至10个以下（当前{gate_info['current']}个）")

            return {
                'overallStatus': overall_gate_status,
                'passedGates': passed_gates,
                'totalGates': total_gates,
                'passRate': round((passed_gates / total_gates) * 100, 1),
                'gates': quality_gates,
                'recommendations': gate_recommendations,
                'canDeploy': overall_gate_status == "PASSED",
                'evaluatedAt': datetime.now().isoformat()
            }

        except Exception as e:
            self.logger.warning(f"生成质量门禁信息失败: {str(e)}")
            return {
                'overallStatus': 'UNKNOWN',
                'passedGates': 0,
                'totalGates': 0,
                'passRate': 0.0,
                'gates': {},
                'recommendations': ["质量门禁评估失败"],
                'canDeploy': False,
                'evaluatedAt': datetime.now().isoformat()
            }

    def _generate_problem_title(self, description: str, severity: ProblemSeverity) -> str:
        """生成问题标题"""
        if not description:
            return f"{severity.value}问题"
        
        # 截取描述的前30个字符作为标题
        title = description[:30]
        if len(description) > 30:
            title += "..."
        
        return title

    def _validate_result_consistency(self, result: EnhancedCRResult) -> None:
        """验证结果数据一致性"""
        try:
            # 验证问题统计一致性
            actual_critical = sum(1 for p in result.problems if p.severity == ProblemSeverity.CRITICAL)
            actual_warning = sum(1 for p in result.problems if p.severity == ProblemSeverity.WARNING)
            actual_moderate = sum(1 for p in result.problems if p.severity == ProblemSeverity.MODERATE)
            actual_minor = sum(1 for p in result.problems if p.severity == ProblemSeverity.MINOR)
            actual_total = len(result.problems)

            # 检查统计数据一致性
            if (result.statistics.critical_count != actual_critical or
                result.statistics.warning_count != actual_warning or
                result.statistics.moderate_count != actual_moderate or
                result.statistics.minor_count != actual_minor or
                result.statistics.total_count != actual_total):

                self.logger.error(f"❌ 问题统计不一致!")
                self.logger.error(f"  统计对象: P0={result.statistics.critical_count}, P1={result.statistics.warning_count}, P2={result.statistics.moderate_count}, P3+={result.statistics.minor_count}, 总计={result.statistics.total_count}")
                self.logger.error(f"  实际问题: P0={actual_critical}, P1={actual_warning}, P2={actual_moderate}, P3+={actual_minor}, 总计={actual_total}")

                # 修正统计数据
                result.statistics.critical_count = actual_critical
                result.statistics.warning_count = actual_warning
                result.statistics.moderate_count = actual_moderate
                result.statistics.minor_count = actual_minor
                result.statistics.total_count = actual_total
                self.logger.warning("⚠️  已自动修正统计数据")

            # 验证评分一致性
            expected_score = max(0, 100 - (
                actual_critical * abs(self.score_weights[ProblemSeverity.CRITICAL]) +
                actual_warning * abs(self.score_weights[ProblemSeverity.WARNING]) +
                actual_moderate * abs(self.score_weights[ProblemSeverity.MODERATE]) +
                actual_minor * abs(self.score_weights[ProblemSeverity.MINOR])
            ))

            if result.overall_score != expected_score:
                self.logger.error(f"❌ 评分不一致! 期望{expected_score}分，实际{result.overall_score}分")
                result.overall_score = expected_score
                self.logger.warning("⚠️  已自动修正评分")

            # 验证总体结果一致性
            expected_result = self._determine_overall_result(result.statistics, result.original_result)
            if result.overall_result != expected_result:
                self.logger.error(f"❌ 总体结果不一致! 期望'{expected_result}'，实际'{result.overall_result}'")
                result.overall_result = expected_result
                self.logger.warning("⚠️  已自动修正总体结果")

            # 验证结果描述一致性
            expected_description = self._generate_description(result.statistics)
            if result.result_description != expected_description:
                self.logger.warning(f"⚠️  结果描述可能不一致: 期望'{expected_description}'，实际'{result.result_description}'")
                # 结果描述不强制修正，因为可能有自定义格式

            self.logger.info("✅ 数据一致性验证通过")

        except Exception as e:
            self.logger.error(f"❌ 数据一致性验证失败: {str(e)}")

    def _create_fallback_result(self, original_result: Dict[str, Any], reviewer: str) -> EnhancedCRResult:
        """创建降级结果"""
        return EnhancedCRResult(
            check_branch=original_result.get('checkBranch', '未知分支'),
            review_time=datetime.now().strftime("%H:%M:%S"),
            reviewer=reviewer,
            overall_score=50,
            overall_result=original_result.get('sumCheckResult', '未知'),
            result_description="结果解析失败",
            statistics=ProblemStatistics(),
            problems=[],
            task_execution_details=[],
            summary_details={},
            review_metrics={},
            recommendations=["建议检查CR结果格式"],
            review_process_info={},
            quality_gates={},
            original_result=original_result
        )

    def _format_problem_for_frontend(self, problem: EnhancedProblemDetail) -> Dict[str, Any]:
        """格式化单个问题为前端需要的格式，确保所有detail信息都被包含"""
        try:
            # 基础问题信息
            problem_dict = {
                "id": problem.id,
                "title": problem.title,
                "severity": problem.severity.value,
                "level": problem.level,
                "lineNumber": problem.line_number,
                "description": problem.description,
                "targetCode": problem.target_code,
                "suggestion": problem.suggestion,
                "codePosition": problem.code_position,
                "codePositionArray": problem.code_position_array,
                "category": problem.category,

                # 增强的详细信息
                "filePath": problem.file_path,
                "ruleName": problem.rule_name,
                "ruleDescription": problem.rule_description,
                "impactLevel": problem.impact_level,
                "fixComplexity": problem.fix_complexity,

                # 新增的增强字段
                "detectionMethod": problem.detection_method,
                "confidenceScore": problem.confidence_score,
                "relatedProblems": problem.related_problems,
                "fixExamples": problem.fix_examples,
                "referenceLinks": problem.reference_links,

                # 完整的原始detail信息
                "originalDetail": problem.original_detail,

                # 元数据信息
                "metadata": {
                    "enhancedAt": datetime.now().isoformat(),
                    "enhancedBy": "CRResultEnhancer",
                    "originalFieldsCount": len(problem.original_detail) if problem.original_detail else 0,
                    "hasExtraFields": len(problem.original_detail) > 8 if problem.original_detail else False
                }
            }

            # 如果原始detail中有额外字段，也要包含进来
            if problem.original_detail:
                # 提取原始detail中的所有字段，确保没有遗漏
                for key, value in problem.original_detail.items():
                    # 避免覆盖已经处理过的字段
                    if key not in problem_dict and key != 'originalDetail':
                        problem_dict[f"original_{key}"] = value

            return problem_dict

        except Exception as e:
            self.logger.error(f"格式化问题 {problem.id} 失败: {str(e)}")
            # 降级处理，至少返回基本信息
            return {
                "id": problem.id,
                "title": problem.title,
                "severity": problem.severity.value if hasattr(problem.severity, 'value') else str(problem.severity),
                "level": problem.level,
                "description": problem.description,
                "originalDetail": problem.original_detail,
                "error": f"格式化失败: {str(e)}"
            }

    def to_frontend_format(self, enhanced_result: EnhancedCRResult) -> Dict[str, Any]:
        """转换为前端需要的格式"""
        return {
            # 总体信息
            "summary": {
                "checkBranch": enhanced_result.check_branch,
                "reviewTime": enhanced_result.review_time,
                "reviewer": enhanced_result.reviewer,
                "overallScore": enhanced_result.overall_score,
                "overallResult": enhanced_result.overall_result,
                "resultDescription": enhanced_result.result_description,
                "totalProblems": enhanced_result.statistics.total_count
            },

            # 问题统计
            "statistics": {
                "criticalCount": enhanced_result.statistics.critical_count,
                "warningCount": enhanced_result.statistics.warning_count,
                "moderateCount": enhanced_result.statistics.moderate_count,
                "minorCount": enhanced_result.statistics.minor_count,
                "totalCount": enhanced_result.statistics.total_count
            },

            # 问题详情列表（包含完整信息）
            "problems": [
                self._format_problem_for_frontend(problem)
                for problem in enhanced_result.problems
            ],

            # 新增：任务执行详情
            "taskExecutionDetails": [
                {
                    "taskName": task.task_name,
                    "taskType": task.task_type,
                    "executionStatus": task.execution_status,
                    "executionTimeMs": task.execution_time_ms,
                    "rulesApplied": task.rules_applied,
                    "filesChecked": task.files_checked,
                    "coveragePercentage": task.coverage_percentage,
                    "problemsFound": task.problems_found,
                    "taskSummary": task.task_summary,
                    "detailedMetrics": task.detailed_metrics,
                    "recommendations": task.recommendations
                }
                for task in enhanced_result.task_execution_details
            ],

            # 新增：详细总结信息
            "summaryDetails": enhanced_result.summary_details,

            # 新增：审查指标
            "reviewMetrics": enhanced_result.review_metrics,

            # 新增：改进建议
            "recommendations": enhanced_result.recommendations,

            # 新增：审查过程信息
            "reviewProcessInfo": enhanced_result.review_process_info,

            # 新增：质量门禁信息
            "qualityGates": enhanced_result.quality_gates,

            # 兼容原始格式
            "originalResult": enhanced_result.original_result
        }

    def to_simple_format(self, enhanced_result: EnhancedCRResult) -> List[Dict[str, Any]]:
        """转换为简洁的问题列表格式（用户期望的格式）"""
        return [
            {
                "level": problem.level,
                "problem": problem.description,
                "suggestion": problem.suggestion,
                "targetCode": problem.target_code,
                "codePosition": problem.code_position_array
            }
            for problem in enhanced_result.problems
        ]

    def to_complete_format(self, enhanced_result: EnhancedCRResult) -> Dict[str, Any]:
        """转换为完整格式：包含总结、评分和问题列表"""
        return {
            # 1. 总结信息
            "summary": {
                "checkBranch": enhanced_result.check_branch,
                "reviewTime": enhanced_result.review_time,
                "reviewer": enhanced_result.reviewer,
                "overallResult": enhanced_result.overall_result,
                "resultDescription": enhanced_result.result_description,
                "totalProblems": enhanced_result.statistics.total_count,
                "taskExecutionSummary": self._generate_task_summary(enhanced_result.task_execution_details),
                "qualityGatesSummary": self._generate_quality_gates_summary(enhanced_result.quality_gates)
            },

            # 2. 评分信息
            "scoring": {
                "overallScore": enhanced_result.overall_score,
                "maxScore": 100,
                "scoreBreakdown": {
                    "criticalIssues": {
                        "score": max(0, 30 - enhanced_result.statistics.critical_count * 10),
                        "maxScore": 30,
                        "description": f"严重问题扣分 ({enhanced_result.statistics.critical_count}个)"
                    },
                    "warningIssues": {
                        "score": max(0, 25 - enhanced_result.statistics.warning_count * 5),
                        "maxScore": 25,
                        "description": f"警告问题扣分 ({enhanced_result.statistics.warning_count}个)"
                    },
                    "moderateIssues": {
                        "score": max(0, 25 - enhanced_result.statistics.moderate_count * 3),
                        "maxScore": 25,
                        "description": f"中等问题扣分 ({enhanced_result.statistics.moderate_count}个)"
                    },
                    "minorIssues": {
                        "score": max(0, 20 - enhanced_result.statistics.minor_count * 1),
                        "maxScore": 20,
                        "description": f"轻微问题扣分 ({enhanced_result.statistics.minor_count}个)"
                    }
                },
                "qualityGrade": self._calculate_quality_grade(enhanced_result.overall_score),
                "passThreshold": 80,
                "isPassed": enhanced_result.overall_result == "通过"  # 与总体结果保持一致
            },

            # 3. 问题列表
            "problems": [
                {
                    "level": problem.level,
                    "problem": problem.description,
                    "suggestion": problem.suggestion,
                    "targetCode": problem.target_code,
                    "codePosition": problem.code_position_array
                }
                for problem in enhanced_result.problems
            ],

            # 4. 统计信息
            "statistics": {
                "totalProblems": enhanced_result.statistics.total_count,  # 添加totalProblems字段
                "criticalCount": enhanced_result.statistics.critical_count,
                "warningCount": enhanced_result.statistics.warning_count,
                "moderateCount": enhanced_result.statistics.moderate_count,
                "minorCount": enhanced_result.statistics.minor_count,
                "totalCount": enhanced_result.statistics.total_count,
                "segmentsCount": 0,  # 默认值，可以在聚合代理中覆盖
                "reviewResultsCount": 1,  # 默认值，可以在聚合代理中覆盖
                "problemDistribution": {
                    "P0": enhanced_result.statistics.critical_count,
                    "P1": enhanced_result.statistics.warning_count,
                    "P2": enhanced_result.statistics.moderate_count,
                    "P3+": enhanced_result.statistics.minor_count
                }
            },

            # 5. 任务执行详情（可选）
            "taskDetails": [
                {
                    "taskName": task.task_name,
                    "taskType": task.task_type,
                    "executionStatus": task.execution_status,
                    "problemsFound": task.problems_found,
                    "taskSummary": task.task_summary
                }
                for task in enhanced_result.task_execution_details
            ],

            # 6. 质量门禁（可选）
            "qualityGates": {
                "overallStatus": enhanced_result.quality_gates.get('overallStatus', 'UNKNOWN'),
                "passRate": enhanced_result.quality_gates.get('passRate', 0),
                "canDeploy": enhanced_result.quality_gates.get('canDeploy', False)
            }
        }

    def _generate_task_summary(self, task_details: List[TaskExecutionDetail]) -> str:
        """生成任务执行总结"""
        if not task_details:
            return "无任务执行信息"

        total_tasks = len(task_details)
        success_tasks = len([t for t in task_details if t.execution_status == "success"])
        failed_tasks = len([t for t in task_details if t.execution_status == "failed"])

        return f"执行{total_tasks}个任务，{success_tasks}个成功，{failed_tasks}个发现问题"

    def _generate_quality_gates_summary(self, quality_gates: Dict[str, Any]) -> str:
        """生成质量门禁总结"""
        if not quality_gates:
            return "无质量门禁信息"

        status = quality_gates.get('overallStatus', 'UNKNOWN')
        pass_rate = quality_gates.get('passRate', 0)

        if status == "PASSED":
            return f"质量门禁通过，通过率{pass_rate}%"
        else:
            return f"质量门禁未通过，通过率{pass_rate}%"

    def _calculate_quality_grade(self, score: int) -> str:
        """计算质量等级"""
        if score >= 95:
            return "A+"
        elif score >= 90:
            return "A"
        elif score >= 85:
            return "B+"
        elif score >= 80:
            return "B"
        elif score >= 70:
            return "C+"
        elif score >= 60:
            return "C"
        elif score >= 50:
            return "D"
        else:
            return "F"
    
    def to_cr_result_format(self, enhanced_result: EnhancedCRResult) -> Dict[str, Any]:
        """转换为crResult格式（用户期望的格式）"""
        try:
            # 生成会话信息
            session_id = f"cr_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            start_time = datetime.now().isoformat()
            end_time = datetime.now().isoformat()

            # 计算总执行时间
            total_duration = sum(task.execution_time_ms for task in enhanced_result.task_execution_details)

            # 构建分支信息
            branch_info = self._extract_branch_info(enhanced_result.original_result)

            # 构建任务总结
            tasks_summary = self._build_tasks_summary(enhanced_result.task_execution_details)

            # 构建任务结果详情 - 这是关键修复
            task_results = self._build_task_results(enhanced_result.task_execution_details, enhanced_result.problems)

            # 构建前端格式结果
            cr_result = {
                "crResult": {
                    "sessionId": session_id,
                    "executionSummary": {
                        "startTime": start_time,
                        "endTime": end_time,
                        "totalDurationMs": total_duration,
                        "branchInfo": branch_info
                    },
                    "overallResults": {
                        "score": enhanced_result.overall_score,
                        "result": enhanced_result.overall_result,
                        "totalProblems": enhanced_result.statistics.total_count,
                        "totalFiles": len(set(p.file_path for p in enhanced_result.problems if p.file_path)),
                        "tasksSummary": tasks_summary
                    },
                    "taskResults": task_results,  # 修复：包含所有任务详情
                    "aggregatedData": {
                        "allProblems": [self._problem_to_dict(p) for p in enhanced_result.problems],
                        "fileAnalysis": self._build_file_analysis(enhanced_result.problems),
                        "rulesCoverage": self._build_rules_coverage(enhanced_result.task_execution_details),
                        "qualityMetrics": self._build_quality_metrics(enhanced_result.statistics)
                    },
                    "recommendations": enhanced_result.recommendations
                },
                "metadata": {
                    "reportTitle": f"代码审查任务报告-{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                    "generatedAt": datetime.now().isoformat(),
                    "version": "2.0",
                    "format": "task_based"
                }
            }

            self.logger.info(f"✅ crResult格式转换完成: {len(task_results)}个任务, {enhanced_result.statistics.total_count}个问题")
            return cr_result

        except Exception as e:
            self.logger.error(f"转换crResult格式失败: {str(e)}")
            # 返回简化格式
            return self._create_fallback_cr_result_format(enhanced_result)

    def _extract_branch_info(self, original_result: Dict[str, Any]) -> Dict[str, str]:
        """提取分支信息"""
        return {
            "project": "~wangqichen02",
            "repo": "shangou_ai_cr",
            "fromBranch": "dev/20250516-cr-rag",
            "toBranch": "dev/test_cr"
        }

    def _build_tasks_summary(self, task_details: List[TaskExecutionDetail]) -> Dict[str, int]:
        """构建任务总结"""
        total = len(task_details)
        successful = len([t for t in task_details if t.execution_status == "success"])
        failed = len([t for t in task_details if t.execution_status == "failed"])

        return {
            "total": total,
            "successful": successful,
            "failed": failed
        }

    def _build_task_results(self, task_details: List[TaskExecutionDetail], problems: List[EnhancedProblemDetail]) -> List[Dict[str, Any]]:
        """构建任务结果详情"""
        task_results = []

        # 按类别分组问题
        problems_by_category = {}
        for problem in problems:
            category = problem.category
            if category not in problems_by_category:
                problems_by_category[category] = []
            problems_by_category[category].append(problem)

        for i, task in enumerate(task_details):
            task_problems = problems_by_category.get(task.task_name, [])

            task_result = {
                "taskId": f"cr_task_{i+1:03d}",
                "taskName": task.task_name,
                "taskType": task.task_type,
                "status": task.execution_status,
                "execution": {
                    "startTime": datetime.now().isoformat(),
                    "endTime": datetime.now().isoformat(),
                    "durationMs": task.execution_time_ms
                },
                "output": {
                    "filesAnalyzed": task.files_checked,
                    "rulesApplied": task.rules_applied,
                    "problemsFound": [self._problem_to_dict(p) for p in task_problems],
                    "problemCount": len(task_problems),
                    "metrics": {
                        "problemCount": len(task_problems),
                        "severity": self._determine_task_severity(task_problems),
                        "coverage": task.coverage_percentage
                    }
                },
                "config": {},
                "logs": [],
                "errorMessage": None
            }

            task_results.append(task_result)

        return task_results

    def _determine_task_severity(self, problems: List[EnhancedProblemDetail]) -> str:
        """确定任务严重程度"""
        if any(p.severity == ProblemSeverity.CRITICAL for p in problems):
            return "high"
        elif any(p.severity == ProblemSeverity.WARNING for p in problems):
            return "medium"
        else:
            return "low"

    def _build_file_analysis(self, problems: List[EnhancedProblemDetail]) -> Dict[str, Any]:
        """构建文件分析"""
        file_stats = {}

        for problem in problems:
            if problem.file_path:
                if problem.file_path not in file_stats:
                    file_stats[problem.file_path] = {
                        "problemCount": 0,
                        "problems": []
                    }
                file_stats[problem.file_path]["problemCount"] += 1
                file_stats[problem.file_path]["problems"].append(self._problem_to_dict(problem))

        return {
            "totalFiles": len(file_stats),
            "fileDetails": file_stats,
            "mostProblematicFiles": sorted(
                file_stats.items(),
                key=lambda x: x[1]["problemCount"],
                reverse=True
            )[:5]
        }

    def _build_rules_coverage(self, task_details: List[TaskExecutionDetail]) -> Dict[str, Any]:
        """构建规则覆盖"""
        all_rules = set()
        rule_usage = {}

        for task in task_details:
            for rule in task.rules_applied:
                all_rules.add(rule)
                if rule not in rule_usage:
                    rule_usage[rule] = {
                        "usedByTasks": [],
                        "problemsDetected": 0
                    }
                rule_usage[rule]["usedByTasks"].append(task.task_name)
                rule_usage[rule]["problemsDetected"] += task.problems_found

        return {
            "totalRules": len(all_rules),
            "ruleDetails": rule_usage,
            "mostEffectiveRules": sorted(
                rule_usage.items(),
                key=lambda x: x[1]["problemsDetected"],
                reverse=True
            )[:10]
        }

    def _build_quality_metrics(self, statistics: ProblemStatistics) -> Dict[str, Any]:
        """构建质量指标"""
        return {
            "codeComplexity": "medium",
            "testCoverage": 0.0,
            "maintainabilityIndex": max(0.0, 100.0 - statistics.total_count * 5),
            "technicalDebt": "low" if statistics.total_count < 5 else "medium" if statistics.total_count < 15 else "high"
        }

    def _create_fallback_cr_result_format(self, enhanced_result: EnhancedCRResult) -> Dict[str, Any]:
        """创建降级的crResult格式"""
        return {
            "crResult": {
                "sessionId": f"cr_fallback_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                "executionSummary": {
                    "startTime": datetime.now().isoformat(),
                    "endTime": datetime.now().isoformat(),
                    "totalDurationMs": 0,
                    "branchInfo": {
                        "project": "unknown",
                        "repo": "unknown",
                        "fromBranch": "unknown",
                        "toBranch": "unknown"
                    }
                },
                "overallResults": {
                    "score": enhanced_result.overall_score,
                    "result": enhanced_result.overall_result,
                    "totalProblems": enhanced_result.statistics.total_count,
                    "totalFiles": 0,
                    "tasksSummary": {
                        "total": 0,
                        "successful": 0,
                        "failed": 0
                    }
                },
                "taskResults": [],
                "aggregatedData": {
                    "allProblems": [],
                    "fileAnalysis": {
                        "totalFiles": 0,
                        "fileDetails": {},
                        "mostProblematicFiles": []
                    },
                    "rulesCoverage": {
                        "totalRules": 0,
                        "ruleDetails": {},
                        "mostEffectiveRules": []
                    },
                    "qualityMetrics": {
                        "codeComplexity": "unknown",
                        "testCoverage": 0.0,
                        "maintainabilityIndex": 0.0,
                        "technicalDebt": "unknown"
                    }
                },
                "recommendations": ["系统错误，请重试"]
            },
            "metadata": {
                "reportTitle": "代码审查报告（降级模式）",
                "generatedAt": datetime.now().isoformat(),
                "version": "2.0",
                "format": "fallback"
            }
        }

    def _problem_to_dict(self, problem: EnhancedProblemDetail) -> Dict[str, Any]:
        """将问题对象转换为字典格式"""
        return {
            "id": problem.id,
            "title": problem.title,
            "severity": problem.severity.value if hasattr(problem.severity, 'value') else str(problem.severity),
            "level": problem.level,
            "lineNumber": problem.line_number,
            "description": problem.description,
            "targetCode": problem.target_code,
            "suggestion": problem.suggestion,
            "codePosition": problem.code_position,
            "codePositionArray": problem.code_position_array,
            "category": problem.category,
            "filePath": problem.file_path,
            "ruleName": problem.rule_name,
            "ruleDescription": problem.rule_description,
            "impactLevel": problem.impact_level,
            "fixComplexity": problem.fix_complexity,
            "detectionMethod": problem.detection_method,
            "confidenceScore": problem.confidence_score,
            "relatedProblems": problem.related_problems,
            "fixExamples": problem.fix_examples,
            "referenceLinks": problem.reference_links,
            "originalDetail": problem.original_detail
        }

    def to_json(self, enhanced_result: EnhancedCRResult, indent: int = 2) -> str:
        """转换为JSON字符串"""
        frontend_format = self.to_frontend_format(enhanced_result)
        return json.dumps(frontend_format, ensure_ascii=False, indent=indent)
