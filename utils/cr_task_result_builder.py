#!/usr/bin/env python3
"""
CR任务结果构建器
专注于构建每个CR任务的具体产出结果
"""

import json
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
from dataclasses import dataclass


@dataclass
class CRTaskResult:
    """单个CR任务的结果"""
    task_id: str
    task_name: str
    task_type: str  # static_analysis, security_scan, performance_check, code_style, etc.
    status: str  # success, failed, partial, skipped
    start_time: str
    end_time: str
    duration_ms: int
    
    # 任务产出
    files_analyzed: List[str]
    rules_applied: List[str]
    problems_found: List[Dict[str, Any]]
    metrics: Dict[str, Any]
    
    # 任务配置
    config: Dict[str, Any]
    
    # 任务日志
    logs: List[str]
    error_message: Optional[str] = None


class CRTaskResultBuilder:
    """CR任务结果构建器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.tasks = []
        self.overall_start_time = None
        self.overall_end_time = None
    
    def start_session(self):
        """开始CR会话"""
        self.overall_start_time = datetime.now().isoformat()
        self.tasks = []
        self.logger.info("🚀 CR任务会话开始")
    
    def add_task_result(self, task_result: CRTaskResult):
        """添加任务结果"""
        self.tasks.append(task_result)
        self.logger.info(f"✅ 添加任务结果: {task_result.task_name} - {task_result.status}")
    
    def end_session(self):
        """结束CR会话"""
        self.overall_end_time = datetime.now().isoformat()
        self.logger.info("🏁 CR任务会话结束")
    
    def build_cr_result(self, branch_info: Dict[str, Any]) -> Dict[str, Any]:
        """构建完整的CR结果"""
        if not self.overall_end_time:
            self.end_session()
        
        # 计算总体统计
        total_problems = sum(len(task.problems_found) for task in self.tasks)
        total_files = len(set(file for task in self.tasks for file in task.files_analyzed))
        total_duration = sum(task.duration_ms for task in self.tasks)
        
        # 按级别统计问题
        problem_stats = self._calculate_problem_statistics()
        
        # 计算总体评分
        overall_score = self._calculate_overall_score(problem_stats)
        
        # 确定总体结果
        overall_result = "通过" if total_problems == 0 else "不通过"
        
        cr_result = {
            "session": {
                "sessionId": f"cr_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                "startTime": self.overall_start_time,
                "endTime": self.overall_end_time,
                "totalDurationMs": total_duration,
                "branchInfo": branch_info
            },
            
            "summary": {
                "overallScore": overall_score,
                "overallResult": overall_result,
                "totalProblems": total_problems,
                "totalFiles": total_files,
                "totalTasks": len(self.tasks),
                "successfulTasks": len([t for t in self.tasks if t.status == "success"]),
                "failedTasks": len([t for t in self.tasks if t.status == "failed"])
            },
            
            "statistics": problem_stats,
            
            "tasks": [self._format_task_result(task) for task in self.tasks],
            
            "aggregatedResults": {
                "allProblems": self._aggregate_all_problems(),
                "fileAnalysis": self._aggregate_file_analysis(),
                "rulesCoverage": self._aggregate_rules_coverage(),
                "qualityMetrics": self._aggregate_quality_metrics()
            },
            
            "recommendations": self._generate_recommendations(problem_stats, total_problems)
        }
        
        return cr_result
    
    def _format_task_result(self, task: CRTaskResult) -> Dict[str, Any]:
        """格式化单个任务结果"""
        return {
            "taskId": task.task_id,
            "taskName": task.task_name,
            "taskType": task.task_type,
            "status": task.status,
            "execution": {
                "startTime": task.start_time,
                "endTime": task.end_time,
                "durationMs": task.duration_ms
            },
            "output": {
                "filesAnalyzed": task.files_analyzed,
                "rulesApplied": task.rules_applied,
                "problemsFound": task.problems_found,
                "problemCount": len(task.problems_found),
                "metrics": task.metrics
            },
            "config": task.config,
            "logs": task.logs[-10:] if task.logs else [],  # 只保留最后10条日志
            "errorMessage": task.error_message
        }
    
    def _calculate_problem_statistics(self) -> Dict[str, Any]:
        """计算问题统计"""
        stats = {
            "criticalCount": 0,    # P0
            "warningCount": 0,     # P1
            "moderateCount": 0,    # P2
            "minorCount": 0,       # P3
            "totalCount": 0
        }
        
        for task in self.tasks:
            for problem in task.problems_found:
                level = problem.get("level", "P2")
                if level == "P0":
                    stats["criticalCount"] += 1
                elif level == "P1":
                    stats["warningCount"] += 1
                elif level == "P2":
                    stats["moderateCount"] += 1
                elif level == "P3":
                    stats["minorCount"] += 1
                stats["totalCount"] += 1
        
        return stats
    
    def _calculate_overall_score(self, problem_stats: Dict[str, Any]) -> int:
        """计算总体评分"""
        base_score = 100
        
        # 根据问题严重程度扣分
        score_deduction = (
            problem_stats["criticalCount"] * 20 +    # P0问题扣20分
            problem_stats["warningCount"] * 10 +     # P1问题扣10分
            problem_stats["moderateCount"] * 5 +     # P2问题扣5分
            problem_stats["minorCount"] * 2          # P3问题扣2分
        )
        
        final_score = max(0, base_score - score_deduction)
        return final_score
    
    def _aggregate_all_problems(self) -> List[Dict[str, Any]]:
        """聚合所有问题"""
        all_problems = []
        problem_id = 1
        
        for task in self.tasks:
            for problem in task.problems_found:
                enhanced_problem = problem.copy()
                enhanced_problem["id"] = str(problem_id)
                enhanced_problem["sourceTask"] = task.task_name
                enhanced_problem["sourceTaskType"] = task.task_type
                enhanced_problem["detectedAt"] = task.end_time
                all_problems.append(enhanced_problem)
                problem_id += 1
        
        return all_problems
    
    def _aggregate_file_analysis(self) -> Dict[str, Any]:
        """聚合文件分析结果"""
        file_stats = {}
        
        for task in self.tasks:
            for file_path in task.files_analyzed:
                if file_path not in file_stats:
                    file_stats[file_path] = {
                        "analyzedByTasks": [],
                        "problemCount": 0,
                        "problems": []
                    }
                
                file_stats[file_path]["analyzedByTasks"].append(task.task_name)
                
                # 统计该文件的问题
                file_problems = [p for p in task.problems_found 
                               if file_path in p.get("targetCode", "") or 
                                  file_path in p.get("codePosition", "")]
                file_stats[file_path]["problemCount"] += len(file_problems)
                file_stats[file_path]["problems"].extend(file_problems)
        
        return {
            "totalFiles": len(file_stats),
            "fileDetails": file_stats,
            "mostProblematicFiles": sorted(
                file_stats.items(), 
                key=lambda x: x[1]["problemCount"], 
                reverse=True
            )[:5]
        }
    
    def _aggregate_rules_coverage(self) -> Dict[str, Any]:
        """聚合规则覆盖情况"""
        all_rules = set()
        rule_usage = {}
        
        for task in self.tasks:
            for rule in task.rules_applied:
                all_rules.add(rule)
                if rule not in rule_usage:
                    rule_usage[rule] = {
                        "usedByTasks": [],
                        "problemsDetected": 0
                    }
                rule_usage[rule]["usedByTasks"].append(task.task_name)
                
                # 统计该规则检测到的问题数
                rule_problems = [p for p in task.problems_found 
                               if rule in p.get("ruleName", "")]
                rule_usage[rule]["problemsDetected"] += len(rule_problems)
        
        return {
            "totalRules": len(all_rules),
            "ruleDetails": rule_usage,
            "mostEffectiveRules": sorted(
                rule_usage.items(),
                key=lambda x: x[1]["problemsDetected"],
                reverse=True
            )[:10]
        }
    
    def _aggregate_quality_metrics(self) -> Dict[str, Any]:
        """聚合质量指标"""
        metrics = {
            "codeComplexity": "medium",
            "testCoverage": 0.0,
            "maintainabilityIndex": 0.0,
            "technicalDebt": "low"
        }
        
        # 从各个任务的metrics中聚合
        complexity_scores = []
        coverage_scores = []
        maintainability_scores = []
        
        for task in self.tasks:
            task_metrics = task.metrics
            if "complexity" in task_metrics:
                complexity_scores.append(task_metrics["complexity"])
            if "coverage" in task_metrics:
                coverage_scores.append(task_metrics["coverage"])
            if "maintainability" in task_metrics:
                maintainability_scores.append(task_metrics["maintainability"])
        
        # 计算平均值
        if coverage_scores:
            metrics["testCoverage"] = sum(coverage_scores) / len(coverage_scores)
        if maintainability_scores:
            metrics["maintainabilityIndex"] = sum(maintainability_scores) / len(maintainability_scores)
        
        # 根据问题数量评估技术债务
        total_problems = sum(len(task.problems_found) for task in self.tasks)
        if total_problems == 0:
            metrics["technicalDebt"] = "none"
        elif total_problems <= 5:
            metrics["technicalDebt"] = "low"
        elif total_problems <= 15:
            metrics["technicalDebt"] = "medium"
        else:
            metrics["technicalDebt"] = "high"
        
        return metrics
    
    def _generate_recommendations(self, problem_stats: Dict[str, Any], total_problems: int) -> List[str]:
        """生成建议"""
        recommendations = []
        
        if total_problems == 0:
            recommendations.append("🎉 代码质量良好，未发现明显问题")
            recommendations.append("📚 建议继续保持良好的编码习惯")
        else:
            if problem_stats["criticalCount"] > 0:
                recommendations.append(f"🚨 发现{problem_stats['criticalCount']}个严重问题，建议立即修复")
            
            if problem_stats["warningCount"] > 0:
                recommendations.append(f"⚠️ 发现{problem_stats['warningCount']}个重要问题，建议优先处理")
            
            if problem_stats["moderateCount"] > 0:
                recommendations.append(f"📝 发现{problem_stats['moderateCount']}个一般问题，建议逐步改进")
            
            recommendations.append("🔍 建议定期进行代码审查，持续改进代码质量")
        
        return recommendations


def create_sample_task_result() -> CRTaskResult:
    """创建示例任务结果"""
    return CRTaskResult(
        task_id="task_001",
        task_name="静态代码分析",
        task_type="static_analysis",
        status="success",
        start_time="2025-06-04T14:30:00",
        end_time="2025-06-04T14:30:05",
        duration_ms=5000,
        files_analyzed=["api/apps/main_app.py", "utils/cr_utils.py"],
        rules_applied=["python_security_001", "python_style_002"],
        problems_found=[
            {
                "level": "P1",
                "problem": "函数复杂度过高，建议拆分",
                "suggestion": "将大函数拆分为多个小函数",
                "targetCode": "def complex_function():",
                "codePosition": [45, 0, 65, 0],
                "ruleName": "python_complexity_001"
            }
        ],
        metrics={
            "complexity": 7.5,
            "coverage": 85.0,
            "maintainability": 72.0
        },
        config={
            "rules_enabled": ["security", "style", "complexity"],
            "severity_threshold": "P2"
        },
        logs=[
            "开始静态代码分析",
            "分析文件: api/apps/main_app.py",
            "应用规则: python_security_001",
            "发现1个问题",
            "分析完成"
        ]
    )
