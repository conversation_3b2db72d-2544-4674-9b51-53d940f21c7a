"""
线程池管理器 - 统一管理所有线程池资源
"""

import threading
import atexit
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import Dict, List, Any, Optional
import time


class ThreadPoolManager:
    """线程池管理器 - 确保所有线程池正确关闭"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not hasattr(self, 'initialized'):
            self.pools: Dict[str, ThreadPoolExecutor] = {}
            self.initialized = True
            # 注册退出时的清理函数
            atexit.register(self.cleanup_all)
    
    def get_pool(self, name: str, max_workers: int = 3) -> ThreadPoolExecutor:
        """获取或创建线程池"""
        if name not in self.pools:
            self.pools[name] = ThreadPoolExecutor(
                max_workers=max_workers,
                thread_name_prefix=f"CR-{name}"
            )
            print(f"[ThreadPoolManager] 创建线程池: {name}, max_workers={max_workers}")
        return self.pools[name]
    
    def shutdown_pool(self, name: str, wait: bool = True, timeout: float = 5.0):
        """关闭指定的线程池"""
        if name in self.pools:
            pool = self.pools[name]
            try:
                print(f"[ThreadPoolManager] 关闭线程池: {name}")
                # 兼容不同Python版本的shutdown方法
                try:
                    pool.shutdown(wait=wait, timeout=timeout)
                except TypeError:
                    # 较老版本的Python不支持timeout参数
                    pool.shutdown(wait=wait)
                del self.pools[name]
            except Exception as e:
                print(f"[ThreadPoolManager] 关闭线程池失败 {name}: {e}")
    
    def cleanup_all(self):
        """清理所有线程池"""
        print("[ThreadPoolManager] 开始清理所有线程池")
        for name in list(self.pools.keys()):
            self.shutdown_pool(name, wait=False, timeout=2.0)
        print("[ThreadPoolManager] 线程池清理完成")
    
    def safe_submit(self, pool_name: str, func, *args, max_workers: int = 3, timeout: float = 5.0, **kwargs):
        """安全提交任务到线程池"""
        try:
            pool = self.get_pool(pool_name, max_workers)
            future = pool.submit(func, *args, **kwargs)
            return future
        except RuntimeError as e:
            if "cannot schedule new futures after shutdown" in str(e):
                print(f"[ThreadPoolManager] 线程池已关闭，跳过任务: {pool_name}")
                return None
            raise e
    
    def safe_parallel_execute(self, pool_name: str, tasks: List[tuple], 
                            max_workers: int = 3, timeout: float = 5.0) -> List[Any]:
        """安全并行执行任务"""
        if not tasks:
            return []
        
        results = []
        
        try:
            pool = self.get_pool(pool_name, max_workers)
            
            # 提交所有任务
            future_to_task = {}
            for task in tasks:
                if len(task) >= 2:
                    func, args = task[0], task[1:]
                    future = pool.submit(func, *args)
                    future_to_task[future] = task
                else:
                    print(f"[ThreadPoolManager] 无效任务格式: {task}")
            
            # 收集结果
            for future in as_completed(future_to_task, timeout=timeout):
                try:
                    result = future.result()
                    results.append(result)
                except Exception as e:
                    task = future_to_task[future]
                    print(f"[ThreadPoolManager] 任务执行失败 {task}: {e}")
                    results.append(None)
            
        except Exception as e:
            if "cannot schedule new futures after shutdown" in str(e):
                print(f"[ThreadPoolManager] 线程池已关闭，返回空结果: {pool_name}")
                return []
            print(f"[ThreadPoolManager] 并行执行失败: {e}")
        
        return results


# 全局线程池管理器实例
thread_pool_manager = ThreadPoolManager()


def safe_parallel_knowledge_query(keywords: List[str], query_func, max_workers: int = 3, 
                                 timeout: float = 5.0) -> List[str]:
    """安全的并行知识查询"""
    if not keywords:
        return []
    
    # 准备任务
    tasks = [(query_func, keyword) for keyword in keywords]
    
    # 执行并行查询
    results = thread_pool_manager.safe_parallel_execute(
        "knowledge_query", tasks, max_workers, timeout
    )
    
    # 过滤有效结果
    valid_results = [r for r in results if r is not None]
    return valid_results


def safe_with_thread_pool(pool_name: str, max_workers: int = 3):
    """安全的线程池上下文管理器装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except RuntimeError as e:
                if "cannot schedule new futures after shutdown" in str(e):
                    print(f"[ThreadPoolManager] 线程池已关闭，跳过执行: {func.__name__}")
                    return None
                raise e
        return wrapper
    return decorator


class SafeThreadPoolExecutor:
    """安全的线程池执行器包装器"""
    
    def __init__(self, pool_name: str, max_workers: int = 3):
        self.pool_name = pool_name
        self.max_workers = max_workers
        self.manager = thread_pool_manager
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        # 不需要手动关闭，由管理器统一管理
        pass
    
    def submit(self, func, *args, **kwargs):
        """提交任务"""
        return self.manager.safe_submit(
            self.pool_name, func, *args, 
            max_workers=self.max_workers, **kwargs
        )
    
    def map(self, func, iterable, timeout=None):
        """映射执行"""
        tasks = [(func, item) for item in iterable]
        return self.manager.safe_parallel_execute(
            self.pool_name, tasks, self.max_workers, timeout or 5.0
        )


def create_safe_thread_pool(pool_name: str, max_workers: int = 3) -> SafeThreadPoolExecutor:
    """创建安全的线程池"""
    return SafeThreadPoolExecutor(pool_name, max_workers)


# 兼容性函数
def safe_as_completed(futures, timeout=None):
    """安全的as_completed包装"""
    try:
        return as_completed(futures, timeout=timeout)
    except RuntimeError as e:
        if "cannot schedule new futures after shutdown" in str(e):
            print("[ThreadPoolManager] 线程池已关闭，返回空迭代器")
            return iter([])
        raise e
