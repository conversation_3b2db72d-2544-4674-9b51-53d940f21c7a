"""
CR提示词构建器 - 根据规则配置生成灵活的提示词
"""

import json
from typing import Dict, List, Any, Optional
from utils.cr_rule_config import CRRuleSet, ProblemLevel, cr_rule_manager
from langchain_core.output_parsers import PydanticOutputParser
from core.types.cr_types import CodeReviewProblemsResponse


class CRPromptBuilder:
    """CR提示词构建器"""
    
    def __init__(self, rule_set: CRRuleSet):
        self.rule_set = rule_set
    
    def build_rules_section(self) -> str:
        """构建规则部分"""
        rules_text = f"## {self.rule_set.language.title()} 代码审查规则（{self.rule_set.business}）\n\n"
        
        # 按级别分组规则
        p0_rules = self.rule_set.get_rules_by_level(ProblemLevel.P0)
        p1_rules = self.rule_set.get_rules_by_level(ProblemLevel.P1)
        p2_rules = self.rule_set.get_rules_by_level(ProblemLevel.P2)
        
        # P0规则
        if p0_rules:
            rules_text += "### P0（严重问题，必须修复）\n"
            for rule in p0_rules:
                rules_text += f"- {rule.id}: {rule.description}\n"
                if rule.examples:
                    examples = "、".join(rule.examples[:3])  # 最多显示3个例子
                    rules_text += f"  示例：{examples}\n"
            rules_text += "\n"
        
        # P1规则
        if p1_rules:
            rules_text += "### P1（重要问题，建议修复）\n"
            for rule in p1_rules:
                rules_text += f"- {rule.id}: {rule.description}\n"
                if rule.examples:
                    examples = "、".join(rule.examples[:3])
                    rules_text += f"  示例：{examples}\n"
            rules_text += "\n"
        
        # P2规则
        if p2_rules:
            rules_text += "### P2（一般问题，建议优化）\n"
            for rule in p2_rules:
                rules_text += f"- {rule.id}: {rule.description}\n"
                if rule.examples:
                    examples = "、".join(rule.examples[:3])
                    rules_text += f"  示例：{examples}\n"
            rules_text += "\n"
        
        return rules_text
    
    def build_fast_mode_prompt(self, diff_content: str, full_code: str,
                              upstream_str: str = "无", downstream_str: str = "无") -> str:
        """构建Fast模式提示词（纯LLM）"""

        prompt = f"""你是一名资深的{self.rule_set.language}代码审查专家。请基于你的专业知识快速审查以下代码：

{self.build_rules_section()}

=== 代码信息 ===
代码变更: {diff_content}
完整代码: {full_code}
上游依赖: {upstream_str}
下游依赖: {downstream_str}

=== 审查要求 ===
1. 快速识别明显的代码问题
2. 重点关注：安全漏洞、性能问题、代码规范、逻辑错误
3. 基于你的专业经验给出建议
4. 提供精确的4点坐标定位

{self.rule_set.custom_instructions}

=== 输出格式 ===
请直接输出Python字典格式，不要使用JSON。格式如下：

{{
    "problems": [
        {{
            "level": "P0",  # P0/P1/P2
            "problem": "完整的问题描述，至少20个字符，描述具体的问题",
            "suggestion": "具体的修改建议，包含明确的修复步骤",
            "targetCode": "问题代码片段",
            "codePosition": [startLine, startColumn, endLine, endColumn]
        }}
    ]
}}

注意：
- 如果代码质量良好，返回 {{"problems": []}}
- codePosition格式：[startLine, startColumn, endLine, endColumn]
- 直接输出字典，不要包含任何其他文字
- 依靠你的专业知识，无需外部知识库
- 问题描述必须完整，不能是碎片化的文本
- 每个问题描述至少包含：问题类型、具体位置、影响范围
- 避免输出如"：1. .env文"这样的碎片化内容
"""
        return prompt
    
    def build_standard_mode_decision_prompt(self, diff_content: str, full_code: str) -> str:
        """构建Standard模式决策提示词"""

        # 提取规则关键词用于决策
        all_keywords = []
        for rule in self.rule_set.rules:
            all_keywords.extend(rule.keywords)
        keywords_str = "、".join(set(all_keywords))

        prompt = f"""作为{self.rule_set.language}代码审查专家，请分析以下代码并决策是否需要查询知识库：

代码变更: {diff_content}
完整代码: {full_code}

请分析：
1. 代码涉及的技术领域和复杂度
2. 是否涉及以下需要专业知识的问题：{keywords_str}
3. 你的现有知识是否足够进行全面审查

请直接输出Python字典格式：
{{
  "need_knowledge": True,  # True/False
  "reason": "决策原因",
  "focus_areas": ["需要重点关注的领域"],
  "confidence": "high"  # high/medium/low
}}

直接输出字典，不要包含任何其他文字。
"""
        return prompt
    
    def build_standard_mode_review_prompt(self, diff_content: str, full_code: str,
                                        upstream_str: str, downstream_str: str,
                                        knowledge_content: str, focus_areas: List[str]) -> str:
        """构建Standard模式审查提示词"""

        focus_rules = []
        for area in focus_areas:
            for rule in self.rule_set.rules:
                if any(keyword in area for keyword in rule.keywords):
                    focus_rules.append(rule)

        focus_rules_text = ""
        if focus_rules:
            focus_rules_text = "=== 重点关注规则 ===\n"
            for rule in focus_rules[:5]:  # 最多显示5个重点规则
                focus_rules_text += f"- {rule.id}: {rule.description}\n"
            focus_rules_text += "\n"

        prompt = f"""基于智能决策和知识库信息，进行平衡的{self.rule_set.language}代码审查：

{self.build_rules_section()}

{focus_rules_text}

=== 代码信息 ===
代码变更: {diff_content}
完整代码: {full_code}
上游依赖: {upstream_str}
下游依赖: {downstream_str}

=== 重点关注领域 ===
{', '.join(focus_areas)}

=== 知识库信息 ===
{knowledge_content if knowledge_content else "基于专业经验进行审查"}

=== 审查要求 ===
1. 重点关注指定领域的问题
2. 结合知识库信息（如有）进行深入分析
3. 平衡审查深度和效率
4. 提供精确的代码位置定位

{self.rule_set.custom_instructions}

=== 输出格式 ===
请直接输出Python字典格式：

{{
    "problems": [
        {{
            "level": "P0",  # P0/P1/P2
            "problem": "问题描述",
            "suggestion": "修改建议",
            "targetCode": "问题代码片段",
            "codePosition": [startLine, startColumn, endLine, endColumn]
        }}
    ]
}}

直接输出字典，不要包含任何其他文字。
"""
        return prompt
    
    def build_deep_mode_analysis_prompt(self, diff_content: str, full_code: str) -> str:
        """构建Deep模式深度分析提示词"""

        # 提取高风险关键词
        high_risk_keywords = []
        for rule in self.rule_set.get_rules_by_level(ProblemLevel.P0):
            high_risk_keywords.extend(rule.keywords)

        prompt = f"""作为资深{self.rule_set.language}架构师和安全专家，请对以下代码进行深度分析：

代码变更: {diff_content}
完整代码: {full_code}

请进行多维度深度分析：
1. 架构设计合理性
2. 安全风险评估（重点关注：{', '.join(set(high_risk_keywords))}）
3. 性能影响分析
4. 可维护性评估
5. 业务逻辑正确性
6. 异常处理完整性

请直接输出Python字典格式：
{{
  "complexity_level": "medium",  # low/medium/high/critical
  "security_risk": "medium",     # low/medium/high/critical
  "performance_impact": "low",   # low/medium/high/critical
  "need_knowledge": True,        # True/False
  "knowledge_areas": ["安全", "性能", "架构"],
  "critical_points": ["关键审查点"],
  "thinking_chain": "深度思考过程"
}}

直接输出字典，不要包含任何其他文字。
"""
        return prompt
    
    def build_deep_mode_review_prompt(self, diff_content: str, full_code: str,
                                    upstream_str: str, downstream_str: str,
                                    knowledge_content: str, deep_analysis: Dict) -> str:
        """构建Deep模式深度审查提示词"""
        
        parser = PydanticOutputParser(pydantic_object=CodeReviewProblemsResponse)
        
        critical_points = deep_analysis.get("critical_points", [])
        thinking_chain = deep_analysis.get("thinking_chain", "")
        
        prompt = f"""基于深度分析结果，进行全面深入的{self.rule_set.language}代码审查：

{self.build_rules_section()}

=== 代码信息 ===
代码变更: {diff_content}
完整代码: {full_code}
上游依赖: {upstream_str}
下游依赖: {downstream_str}

=== 深度分析结果 ===
复杂度: {deep_analysis.get('complexity_level', 'unknown')}
安全风险: {deep_analysis.get('security_risk', 'unknown')}
性能影响: {deep_analysis.get('performance_impact', 'unknown')}
关键点: {', '.join(critical_points)}

=== 思考链路 ===
{thinking_chain}

=== 知识库信息 ===
{knowledge_content}

=== 深度审查要求 ===
1. 基于深度分析结果进行全面审查
2. 重点关注识别出的关键点
3. 考虑长期维护和扩展性
4. 评估潜在的边界情况和异常场景
5. 提供详细的改进建议

{self.rule_set.custom_instructions}

{parser.get_format_instructions()}
"""
        return prompt
    
    def build_self_review_prompt(self, initial_problems: List[Dict], diff_content: str,
                               full_code: str, knowledge_content: str) -> str:
        """构建自检复审提示词"""

        prompt = f"""作为{self.rule_set.language}代码审查质量控制专家，请对以下初步审查结果进行自检复审：

{self.build_rules_section()}

=== 原始代码 ===
{diff_content}

=== 初步审查结果 ===
{json.dumps(initial_problems, ensure_ascii=False, indent=2)}

=== 知识库参考 ===
{knowledge_content}

=== 自检要求 ===
1. 验证每个问题的准确性和必要性
2. 检查是否有遗漏的重要问题
3. 评估问题级别是否合适
4. 确认修改建议的可行性
5. 去除误报和重复问题

请直接输出Python字典格式的最终审查结果：

{{
    "problems": [
        {{
            "level": "P0",  # P0/P1/P2
            "problem": "问题描述",
            "suggestion": "修改建议",
            "targetCode": "问题代码片段",
            "codePosition": [startLine, startColumn, endLine, endColumn]
        }}
    ]
}}

如果某个问题不准确或不重要，请移除。
如果发现新问题，请添加。
直接输出字典，不要包含任何其他文字。
"""
        return prompt


class CRPromptFactory:
    """CR提示词工厂"""
    
    @staticmethod
    def create_builder(language: str, business: str = "default") -> CRPromptBuilder:
        """创建提示词构建器"""
        rule_set = cr_rule_manager.get_rule_set(language, business)
        if not rule_set:
            raise ValueError(f"未找到规则集: {language}_{business}")
        
        return CRPromptBuilder(rule_set)
    
    @staticmethod
    def create_custom_builder(rule_set: CRRuleSet) -> CRPromptBuilder:
        """创建自定义提示词构建器"""
        return CRPromptBuilder(rule_set)
    
    @staticmethod
    def list_available_languages() -> List[str]:
        """列出可用的语言"""
        rule_sets = cr_rule_manager.list_available_rule_sets()
        languages = set()
        for rule_set_key in rule_sets:
            language = rule_set_key.split('_')[0]
            languages.add(language)
        return list(languages)
    
    @staticmethod
    def list_available_businesses(language: str) -> List[str]:
        """列出指定语言的可用业务类型"""
        rule_sets = cr_rule_manager.list_available_rule_sets()
        businesses = set()
        for rule_set_key in rule_sets:
            parts = rule_set_key.split('_')
            if parts[0] == language:
                business = '_'.join(parts[1:])
                businesses.add(business)
        return list(businesses)
