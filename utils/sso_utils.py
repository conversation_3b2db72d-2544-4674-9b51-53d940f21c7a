import hmac
import hashlib
import base64
import requests
import logging
from datetime import datetime
from enum import Enum
from core import settings


class RequestMethod(Enum):
    GET = 'GET'
    POST = 'POST'
    HEAD = 'HEAD'
    PATCH = 'PATCH'
    PUT = 'PUT'
    DELETE = 'DELETE'
    OPTIONS = 'OPTIONS'

def is_request_method(method):
    """检查是否为合法的请求方法"""
    for enum in RequestMethod:
        if method == enum.value:
            return True
    return False

def build_header(uri, method, client_id, secret, loc):
    """
    构建SSO BA认证头部
    :param uri: 接口URI
    :param method: 请求方法
    :param client_id: 客户端ID
    :param secret: 客户端密钥
    :param loc: 位置前缀
    :return: 认证头部字典
    """
    # 参数校验
    if uri is None or uri.strip() == '':
        raise TypeError('uri must not be None or empty!')
    if client_id is None or client_id.strip() == '':
        raise TypeError('client_id must not be None or empty!')
    if secret is None or secret.strip() == '':
        raise TypeError('secret must not be None or empty!')
    if method is None or method.strip() == '':
        raise TypeError('method must not be None or empty!')
    if not is_request_method(method):
        raise TypeError('method must be a request method')

    # 判断是否有location，没有则添加
    if loc is not None and loc.strip() != '':
        if not uri.startswith(loc):
            uri = loc + uri
    
    now_time = datetime.utcnow().strftime('%a, %d %b %Y %H:%M:%S GMT')
    string_sign = method + ' %s\n%s' % (uri, now_time)
    sign = hmac.new(bytes(secret, 'utf-8'), bytes(string_sign, 'utf-8'), hashlib.sha1).digest()
    signature = str(base64.b64encode(sign), 'utf-8').replace("\n", '')
    
    return {
        'Date': now_time,
        'Authorization': 'MWS' + ' ' + client_id + ':' + signature,
        'Content-Type': 'application/json;charset=UTF-8'
    }

def get_ssoid(code):
    """
    根据code获取ssoid
    :param code: SSO授权码
    :return: 包含accessToken的字典或None
    """
    if code is None or code.strip() == '':
        raise ValueError('code must not be empty')
    
    config = settings.SSO_CONFIG
    url = config.get('sso_host') + config.get('auth_uri')
    t = int(datetime.now().timestamp() * 1000)
    params = {'code': code, 't': t}
    
    # 构建BA认证头部
    headers = build_header(
        config.get('auth_uri'),
        RequestMethod.GET.value,
        config.get('client_id'),
        config.get('secret'),
        '/sson'
    )
    
    # 调用sso通过code查询ssoid接口
    try:
        result = requests.get(url=url, params=params, headers=headers)
        if result.status_code != 200:
            logging.error(f"获取ssoid失败，状态码: {result.status_code}, 响应: {result.text}")
            return None
        
        data = result.json()
        logging.debug(f"获取ssoid响应: {data}")
        
        if data and data['code'] == 200 and data['data'] and data['data'].get('accessToken'):
            return data['data']
        return None
    except Exception as e:
        logging.exception(f"获取ssoid异常: {str(e)}")
        return None

def get_user_info(ssoid):
    """
    根据ssoid获取用户信息
    :param ssoid: SSO访问令牌
    :return: 用户信息字典或None
    """
    if ssoid is None or ssoid.strip() == '':
        raise ValueError('ssoid must not be empty')
    
    config = settings.SSO_CONFIG
    url = config.get('api_host') + config.get('user_info_uri')
    
    # 构建BA认证头部
    headers = build_header(
        config.get('user_info_uri'),
        RequestMethod.POST.value,
        config.get('client_id'),
        config.get('secret'),
        '/open'
    )
    
    params = {'accessToken': ssoid}
    
    # 调用sso的查询用户信息接口
    try:
        result = requests.post(url=url, json=params, headers=headers)
        if result.status_code != 200:
            logging.error(f"获取用户信息失败，状态码: {result.status_code}, 响应: {result.text}")
            return None
        
        data = result.json()
        logging.debug(f"获取用户信息响应: {data}")
        
        if data and data['code'] == 200 and data['data']:
            return data['data']
        return None
    except Exception as e:
        logging.exception(f"获取用户信息异常: {str(e)}")
        return None 