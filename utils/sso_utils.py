"""
SSO工具函数
用于处理SSO认证相关的操作
合并了api/utils/sso_utils.py和utils/sso_utils.py的功能
"""

import hmac
import hashlib
import base64
import requests
import logging
import os
from datetime import datetime
from enum import Enum
from typing import Dict, Optional
from core import settings


class RequestMethod(Enum):
    """HTTP请求方法枚举"""
    GET = 'GET'
    POST = 'POST'
    HEAD = 'HEAD'
    PATCH = 'PATCH'
    PUT = 'PUT'
    DELETE = 'DELETE'
    OPTIONS = 'OPTIONS'


def is_request_method(method):
    """检查是否为合法的请求方法"""
    for enum in RequestMethod:
        if method == enum.value:
            return True
    return False


def get_sso_config() -> Dict:
    """
    获取SSO配置
    支持多环境配置（test、st、prod）
    """
    # 从环境变量获取当前环境
    env = os.getenv('INF_BOM_ENV', 'test')

    # 如果settings中有SSO_CONFIG，优先使用
    if hasattr(settings, 'SSO_CONFIG') and settings.SSO_CONFIG:
        return settings.SSO_CONFIG

    # 否则使用内置配置
    sso_config = {
        "login_uri": "/login",
        "auth_uri": "/oauth2.0/access-token",
        "user_info_uri": "/api/session/userinfo",
        "logout_uri": "/oauth2.0/logout",
        "env": {
            "test": {
                "client_id": "d7885a354f",
                "secret": "a5b0392c3cdf40f28e3c9a1742fae749",
                "sso_host": "https://ssosv.it.test.sankuai.com/sson",
                "api_host": "https://ssosv.it.test.sankuai.com/open"
            },
            "st": {
                "client_id": "d7885a354f",
                "secret": "a5b0392c3cdf40f28e3c9a1742fae749",
                "sso_host": "https://ssosv.it.st.sankuai.com/sson",
                "api_host": "https://ssosv.it.st.sankuai.com/open"
            },
            "prod": {
                "client_id": "d7885a354f",
                "secret": "a5b0392c3cdf40f28e3c9a1742fae749",
                "sso_host": "https://ssosv.sankuai.com/sson",
                "api_host": "https://ssosv.sankuai.com/open"
            }
        }
    }

    # 合并当前环境的配置
    current_env_config = sso_config["env"].get(env, sso_config["env"]["test"])
    result = {**sso_config, **current_env_config}

    return result

def build_header(uri: str, method: str, client_id: str, secret: str, loc: str = None) -> Dict[str, str]:
    """
    构建SSO BA认证头部

    Args:
        uri: 请求URI
        method: HTTP方法
        client_id: 客户端ID
        secret: 客户端密钥
        loc: 位置前缀

    Returns:
        认证头部字典
    """
    # 参数校验
    if uri is None or uri.strip() == '':
        raise TypeError('uri must not be None or empty!')
    if client_id is None or client_id.strip() == '':
        raise TypeError('client_id must not be None or empty!')
    if secret is None or secret.strip() == '':
        raise TypeError('secret must not be None or empty!')
    if method is None or method.strip() == '':
        raise TypeError('method must not be None or empty!')
    if not is_request_method(method):
        raise TypeError('method must be a request method')

    # 判断是否有location，没有则添加
    if loc is not None and loc.strip() != '':
        if not uri.startswith(loc):
            uri = loc + uri

    now_time = datetime.utcnow().strftime('%a, %d %b %Y %H:%M:%S GMT')
    string_sign = method + ' %s\\n%s' % (uri, now_time)
    sign = hmac.new(bytes(secret, 'utf-8'), bytes(string_sign, 'utf-8'), hashlib.sha1).digest()
    signature = str(base64.b64encode(sign), 'utf-8').replace("\\n", '')

    return {
        'Date': now_time,
        'Authorization': 'MWS' + ' ' + client_id + ':' + signature,
        'Content-Type': 'application/json;charset=UTF-8'
    }

def get_ssoid(code: str) -> Optional[Dict]:
    """
    根据code获取ssoid

    Args:
        code: 授权码

    Returns:
        包含accessToken的字典，失败返回None
    """
    if code is None or code.strip() == '':
        raise ValueError('code must not be empty')

    config = get_sso_config()
    url = config.get('sso_host') + config.get('auth_uri')
    t = int(datetime.now().timestamp() * 1000)
    params = {'code': code, 't': t}

    headers = build_header(
        config.get('auth_uri'),
        RequestMethod.GET.value,
        config.get('client_id'),
        config.get('secret'),
        '/sson'
    )

    try:
        logging.info(f"请求SSO获取token: {url}")
        result = requests.get(url=url, params=params, headers=headers)
        logging.info(f"SSO响应状态码: {result.status_code}")

        if result.status_code == 200:
            data = result.json()
            logging.info(f"SSO响应数据: {data}")
            if data and data.get('code') == 200 and data.get('data'):
                return data['data']
        else:
            logging.error(f"SSO请求失败: {result.status_code}, {result.text}")
        return None
    except Exception as e:
        logging.exception(f"获取ssoid异常: {str(e)}")
        return None

def get_user_info(ssoid: str) -> Optional[Dict]:
    """
    根据ssoid获取用户信息

    Args:
        ssoid: SSO访问令牌

    Returns:
        用户信息字典，失败返回None
    """
    if ssoid is None or ssoid.strip() == '':
        raise ValueError('ssoid must not be empty')

    config = get_sso_config()
    url = config.get('api_host') + config.get('user_info_uri')

    headers = build_header(
        config.get('user_info_uri'),
        RequestMethod.POST.value,
        config.get('client_id'),
        config.get('secret'),
        '/open'
    )

    params = {'accessToken': ssoid}

    try:
        logging.info(f"请求SSO获取用户信息: {url}")
        result = requests.post(url=url, json=params, headers=headers, timeout=10)
        logging.info(f"SSO用户信息响应状态码: {result.status_code}")

        if result.status_code == 200:
            data = result.json()
            logging.info(f"SSO用户信息响应: {data}")
            if data and data.get('code') == 200 and data.get('data'):
                return data['data']
        else:
            logging.error(f"SSO用户信息请求失败: {result.status_code}, {result.text}")
        return None
    except Exception as e:
        logging.exception(f"获取用户信息异常: {str(e)}")
        return None


# 为了保持向后兼容性，添加别名
get_sso_user_info = get_user_info


def build_sso_login_url(redirect_uri: str) -> str:
    """
    构建SSO登录URL

    Args:
        redirect_uri: 回调地址

    Returns:
        SSO登录URL
    """
    config = get_sso_config()
    client_id = config.get("client_id")
    t = int(datetime.now().timestamp() * 1000)
    url = config.get('sso_host') + config.get('login_uri')
    url += f'?client_id={client_id}&redirect_uri={redirect_uri}&t={str(t)}'

    return url