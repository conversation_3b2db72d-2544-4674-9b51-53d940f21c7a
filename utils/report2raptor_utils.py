import os
from typing import Dict, List, Optional

import requests

# Raptor 环境配置
RAPTOR_REPORT_HOST = {
    'test': 'catfront.51ping.com',
    'prod': 'catfront.dianping.com',
}


def get_raptor_report_url(env: str) -> str:
    host = RAPTOR_REPORT_HOST.get(env, 'catfront.dianping.com')
    return f'https://{host}/api/metric'


def simple_report2raptor(
        env: str,
        metrics: List[dict],
        project_name: str,
        page_id: Optional[str] = None,
        trace_id: Optional[str] = None
) -> bool:
    report_url = get_raptor_report_url(env)
    request_params = {
        'v': '2',
        'sdk': '1.2.10',
        'p': project_name,
        'unionId': trace_id,
        'pageId': page_id
    }
    # 移除None值
    request_params = {k: v for k, v in request_params.items() if v is not None}
    try:
        response = requests.post(
            report_url,
            params=request_params,
            data={'data': str(metrics)},
            headers={'content-type': 'application/x-www-form-urlencoded'}
        )
        success = response.status_code == 200
        if not success:
            print(f'[simple_report2raptor] Report failed: {response.text}')
        return success
    except Exception as e:
        print('[simple_report2raptor] Error:', e)
        return False


def report2raptor(name: str, value: float, tags: Dict[str, str], msg: Optional[str] = None) -> bool:
    env = os.environ.get('INF_BOM_ENV', 'prod')
    # 转换成 Raptor 需要的格式
    tvs = {k: str(v) for k, v in tags.items()}
    if msg:
        tvs['msg'] = msg
    metric = {
        'key': name,
        'tvs': tvs,
        'vs': [value]
    }
    return simple_report2raptor(env, [metric], project_name='com.sankuai.yunzhuan.devhelper')
