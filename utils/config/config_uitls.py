#
#  Copyright 2024 The InfiniFlow Authors. All Rights Reserved.
#
#  Licensed under the Apache License, Version 2.0 (the "License");
#  you may not use this file except in compliance with the License.
#  You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
#

import os
import yaml
import logging
from utils.file_utils import get_project_base_directory

logger = logging.getLogger(__name__)

# 默认配置文件路径
BASE_CONFIG_PATH = os.path.join(get_project_base_directory(), "conf", "envs", "base.yaml")

# 优先使用环境变量，否则默认为test
ENV = os.environ.get("SERVER_ENV", "test")


def deep_update(source, overrides):
    """
    递归更新嵌套字典
    """
    for key, value in overrides.items():
        if isinstance(value, dict) and key in source and isinstance(source[key], dict):
            source[key] = deep_update(source[key], value)
        else:
            source[key] = value
    return source


def load_config():
    """
    加载配置文件
    1. 首先加载基础配置
    2. 然后加载特定环境配置覆盖基础配置
    3. 最后使用环境变量覆盖配置
    
    返回合并后的配置对象
    """
    # 加载基础配置
    try:
        with open(BASE_CONFIG_PATH, 'r') as f:
            config = yaml.safe_load(f) or {}
    except Exception as e:
        logger.warning(f"加载基础配置失败: {e}")
        config = {}

    # 加载环境特定配置
    env_config_path = os.path.join(get_project_base_directory(), "conf", "envs", f"{ENV}.yaml")
    try:
        if os.path.exists(env_config_path):
            with open(env_config_path, 'r') as f:
                env_config = yaml.safe_load(f) or {}
                config = deep_update(config, env_config)
    except Exception as e:
        logger.warning(f"加载环境配置失败: {e}")

    return config


def get_config(component=None, default=None):
    """
    获取指定组件的配置
    
    参数:
        component: 组件名称，如果为None则返回整个配置
        default: 如果组件配置不存在，返回的默认值
        
    返回:
        组件配置或默认值
    """
    config = load_config()

    if component is None:
        return config

    return config.get(component, default)


def get_es_config():
    """
    获取ES配置
    """
    # 获取基础配置
    es_config = get_config("elasticsearch", {})

    # 添加公司通用配置
    company_config = get_config("company", {})
    if company_config:
        es_config["app_key"] = company_config.get("appkey")

    # 环境变量覆盖
    env_mappings = {
        "ES_USE_COMPANY_INFRA": "use_company_infra",
        "ES_CLUSTER_NAME": "cluster_name",
        "ES_APP_KEY": "app_key",
        "ES_ACCESS_KEY": "access_key",
        "ES_DISCOVERY_URL": "discovery_url",
        "ES_PORT": "port",
        "ES_USE_SSL": "use_ssl",
        "ES_VERIFY_CERTS": "verify_certs"
    }

    for env_var, config_key in env_mappings.items():
        env_value = os.environ.get(env_var)
        if env_value is not None:
            # 处理布尔值
            if env_value.lower() in ["true", "false"]:
                es_config[config_key] = env_value.lower() == "true"
            # 处理整数
            elif config_key == "port" and env_value.isdigit():
                es_config[config_key] = int(env_value)
            # 处理字符串
            else:
                es_config[config_key] = env_value

    return es_config


def get_zebra_config():
    """
    获取Zebra MySQL代理配置
    """
    # 获取基础配置
    zebra_config = get_config("zebra", {})

    # 添加公司通用配置
    company_config = get_config("company", {})
    if company_config:
        zebra_config["app_key"] = company_config.get("appkey")

    # 环境变量覆盖
    env_mappings = {
        "ZEBRA_APP_KEY": "app_key",
        "ZEBRA_REF_KEY": "ref_key",
        "ZEBRA_POOL_SIZE": "pool_size",
        "ZEBRA_POOL_TIMEOUT": "pool_timeout",
        "ZEBRA_POOL_RECYCLE": "pool_recycle"
    }

    for env_var, config_key in env_mappings.items():
        env_value = os.environ.get(env_var)
        if env_value is not None:
            # 处理整数
            if config_key in ["pool_size", "pool_timeout", "pool_recycle"] and env_value.isdigit():
                zebra_config[config_key] = int(env_value)
            # 处理字符串
            else:
                zebra_config[config_key] = env_value

    return zebra_config


def get_mss_s3_config():
    """
    获取美团MSS S3存储配置
    """
    # 获取基础配置
    mss_s3_config = get_config("mss_s3", {})

    # 添加公司通用配置
    company_config = get_config("company", {})
    if company_config:
        mss_s3_config["appkey"] = company_config.get("appkey")

    # 环境变量覆盖
    env_mappings = {
        "MSS_APPKEY": "appkey",
        "MSS_ACCESS_KEY": "access_key",
        "MSS_SECRET_KEY": "secret_key",
        "MSS_ENDPOINT": "endpoint",
        "MSS_USE_HTTPS": "use_https",
        "MSS_USE_DIRECT_CONN": "use_direct_conn"
    }

    for env_var, config_key in env_mappings.items():
        env_value = os.environ.get(env_var)
        if env_value is not None:
            # 处理布尔值
            if config_key in ["use_https", "use_direct_conn"] and env_value.lower() in ["true", "false"]:
                mss_s3_config[config_key] = env_value.lower() == "true"
            # 处理字符串
            else:
                mss_s3_config[config_key] = env_value

    return mss_s3_config


def get_squirrel_config():
    """
    获取美团Squirrel Redis配置
    只保留初始化SquirrelClient所需的三个必要参数
    """
    # 获取基础配置
    squirrel_config = get_config("squirrel", {})

    # 添加公司通用配置
    company_config = get_config("company", {})
    if company_config:
        squirrel_config["appkey"] = company_config.get("appkey")

    # 处理配置路径
    if "proxy_config_path" in squirrel_config:
        # 如果是相对路径，转换为绝对路径
        if not os.path.isabs(squirrel_config["proxy_config_path"]):
            squirrel_config["proxy_config_path"] = os.path.join(
                get_project_base_directory(),
                squirrel_config["proxy_config_path"]
            )

    # 环境变量覆盖 - 只保留必要参数
    env_mappings = {
        "MT_REDIS_CLUSTER_NAME": "cluster_name",
        "MT_REDIS_PROXY_CONFIG_PATH": "proxy_config_path",
        "MT_REDIS_APPKEY": "appkey"
    }

    for env_var, config_key in env_mappings.items():
        env_value = os.environ.get(env_var)
        if env_value is not None:
            squirrel_config[config_key] = env_value

    # 确保必要的参数存在
    result = {
        "cluster_name": squirrel_config.get("cluster_name", "redis-sg-fission-activity_qa"),
        "proxy_config_path": squirrel_config.get("proxy_config_path", os.path.join(
            os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "conf/squirrel/squirrel-proxy.conf")),
        "appkey": squirrel_config.get("appkey", "com.sankuai.sg.ai")
    }

    return result


# 初始加载配置
_CONFIG = load_config()
