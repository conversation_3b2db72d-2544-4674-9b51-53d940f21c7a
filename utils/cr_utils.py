"""
代码审查工具模块 - 统一的工具函数
消除各CR链中的重复代码
"""

from typing import List, Dict, Any, Optional
import re
from core.analyzers.code_position_analyzer import CodePositionAnalyzer


class CRUtils:
    """代码审查工具类"""
    
    @staticmethod
    def is_valid_position(position) -> bool:
        """验证位置格式是否正确"""
        return (isinstance(position, list) and 
                len(position) == 4 and 
                all(isinstance(x, int) for x in position))
    
    @staticmethod
    def enhance_single_problem_position(problem: Dict, code_content: str) -> Dict:
        """为单个问题增强位置信息"""
        if not code_content:
            return problem
        
        try:
            analyzer = CodePositionAnalyzer(code_content)
            enhanced_problem = problem.copy()
            
            target_code = problem.get('targetCode', '')
            problem_desc = problem.get('problem', '')
            
            # 尝试多种方式定位代码位置
            position = CRUtils._find_code_position(analyzer, target_code, problem_desc)
            
            if position:
                enhanced_problem['codePosition'] = position.to_array()
                print(f"[CRUtils] 找到精确位置: {position.to_array()}")
            else:
                enhanced_problem['codePosition'] = [1, 0, 1, 0]
                print(f"[CRUtils] 未找到精确位置，使用默认值")
            
            return enhanced_problem
            
        except Exception as e:
            print(f"[CRUtils] 位置增强失败: {e}")
            problem['codePosition'] = [1, 0, 1, 0]
            return problem
    
    @staticmethod
    def enhance_problems_with_positions(problems: List[Dict], code_content: str) -> List[Dict]:
        """批量增强问题位置信息"""
        if not problems or not code_content:
            return problems
        
        enhanced_problems = []
        analyzer = CodePositionAnalyzer(code_content)
        
        for problem in problems:
            try:
                enhanced_problem = problem.copy()
                
                # 获取问题相关信息
                target_code = problem.get('targetCode', '')
                problem_desc = problem.get('problem', '')
                
                # 尝试多种方式定位代码位置
                position = CRUtils._find_code_position(analyzer, target_code, problem_desc)
                
                if position:
                    enhanced_problem['codePosition'] = position.to_array()
                    print(f"[CRUtils] 为问题找到精确位置: {position.to_array()}")
                else:
                    # 保持原有的codePosition或设置默认值
                    if 'codePosition' not in enhanced_problem:
                        enhanced_problem['codePosition'] = [1, 0, 1, 0]
                    print(f"[CRUtils] 未找到精确位置，使用默认值")
                
                enhanced_problems.append(enhanced_problem)
                
            except Exception as e:
                print(f"[CRUtils] 处理问题时出错: {e}")
                # 出错时保持原问题不变
                enhanced_problems.append(problem)
        
        return enhanced_problems
    
    @staticmethod
    def _find_code_position(analyzer: CodePositionAnalyzer, target_code: str, problem_desc: str):
        """统一的代码位置查找逻辑"""
        position = None
        
        # 1. 如果targetCode包含异常声明，尝试定位异常
        if 'throws Exception' in target_code or 'except' in target_code:
            if 'throws Exception' in target_code:
                method_match = re.search(r'(\w+)\s*\([^)]*\)\s*throws\s+Exception', target_code)
                if method_match:
                    method_name = method_match.group(1)
                    position = analyzer.find_exception_declaration_position(method_name)
            elif 'except' in target_code:
                position = analyzer.find_text_position('except')
        
        # 2. 如果targetCode包含注解，尝试定位注解
        elif target_code.startswith('@'):
            annotation_name = target_code.split('(')[0][1:]  # 去掉@和参数
            position = analyzer.find_annotation_position(annotation_name)
        
        # 3. 如果targetCode包含方法调用，尝试定位方法调用
        elif '.' in target_code and '(' in target_code and not target_code.startswith('def '):
            method_call = target_code.split('(')[0].strip()
            position = analyzer.find_method_call_position(method_call)
        
        # 4. 如果targetCode包含函数定义，尝试定位函数
        elif target_code.startswith('def ') or 'def ' in target_code:
            func_match = re.search(r'def\s+(\w+)', target_code)
            if func_match:
                func_name = func_match.group(1)
                position = analyzer.find_method_signature_position(func_name)
        
        # 5. 如果targetCode包含类声明，尝试定位类
        elif 'class ' in target_code:
            class_match = re.search(r'class\s+(\w+)', target_code)
            if class_match:
                class_name = class_match.group(1)
                position = analyzer.find_class_declaration_position(class_name)
        
        # 6. 如果targetCode包含变量赋值，尝试定位变量
        elif '=' in target_code and not target_code.startswith('if ') and not target_code.startswith('elif '):
            var_match = re.search(r'(\w+)\s*=', target_code)
            if var_match:
                var_name = var_match.group(1)
                position = analyzer.find_variable_declaration_position(var_name)
        
        # 7. 如果targetCode包含import语句，尝试定位import
        elif target_code.startswith('import ') or target_code.startswith('from '):
            position = analyzer.find_text_position(target_code.strip())
        
        # 8. 特殊处理：环境变量访问
        elif 'os.environ' in target_code:
            position = analyzer.find_text_position('os.environ')
        
        # 9. 特殊处理：try-except块
        elif problem_desc and ('异常处理' in problem_desc or 'except' in problem_desc.lower()):
            position = analyzer.find_text_position('except') or analyzer.find_text_position('try')
        
        # 10. 最后尝试直接文本搜索（使用targetCode的关键部分）
        if not position and target_code:
            # 尝试搜索targetCode中的关键词
            keywords = target_code.split()
            for keyword in keywords:
                if len(keyword) > 3 and keyword.isalnum():  # 过滤掉短词和符号
                    position = analyzer.find_text_position(keyword)
                    if position:
                        break
            
            # 如果还是找不到，尝试搜索整个targetCode
            if not position:
                position = analyzer.find_text_position(target_code.strip())
        
        return position
    
    @staticmethod
    def convert_to_dict_format(structured_problems, full_code: str) -> List[Dict]:
        """转换Pydantic对象为字典格式并增强位置"""
        problems = []
        
        for problem in structured_problems:
            problem_dict = {
                "level": problem.level.value.upper(),
                "problem": problem.problem,
                "suggestion": problem.suggestion,
                "targetCode": problem.targetCode,
                "codePosition": problem.codePosition
            }
            
            # 验证和增强位置信息
            if not CRUtils.is_valid_position(problem_dict.get('codePosition')):
                problem_dict = CRUtils.enhance_single_problem_position(problem_dict, full_code)
            
            problems.append(problem_dict)
        
        return problems


class KnowledgeQueryUtils:
    """知识库查询工具类"""
    
    @staticmethod
    def query_devmind_chunks(devmind_service, keyword: str, dataset_ids: List[str], 
                           page_size: int = 3, similarity_threshold: float = 0.3,
                           vector_similarity_weight: float = 0.5) -> str:
        """统一的DevMind知识库查询方法"""
        try:
            if not devmind_service:
                return f"{keyword}: 无知识库连接"
            
            # 使用DevMind服务的retrieve_chunks方法
            response = devmind_service.retrieve_chunks(
                question=keyword,
                dataset_ids=dataset_ids,
                page_size=page_size,
                similarity_threshold=similarity_threshold,
                vector_similarity_weight=vector_similarity_weight
            )
            
            # 解析响应，提取chunks内容
            chunks = response.get('data', {}).get('chunks', [])
            if chunks:
                # 合并chunk的内容
                contents = []
                for chunk in chunks:
                    content = chunk.get('content', '').strip()
                    if content:
                        contents.append(content[:150])  # 限制每个chunk长度
                
                if contents:
                    combined_content = ' | '.join(contents)
                    return f"{keyword}: {combined_content}"
            
            return f"{keyword}: 未找到相关知识"
            
        except Exception as e:
            print(f"[KnowledgeQueryUtils] 知识查询异常 {keyword}: {e}")
            return f"{keyword}: 查询失败"
    
    @staticmethod
    async def async_query_devmind_chunks(devmind_service, keyword: str, dataset_ids: List[str],
                                       page_size: int = 3, similarity_threshold: float = 0.3,
                                       vector_similarity_weight: float = 0.5,
                                       keyword_match: bool = True, highlight: bool = False) -> str:
        """异步的DevMind知识库查询方法"""
        try:
            if not devmind_service:
                return f"{keyword}: 无知识库连接"
            
            # 使用DevMind服务的aretrieve_chunks异步方法
            response = await devmind_service.aretrieve_chunks(
                question=keyword,
                dataset_ids=dataset_ids,
                page_size=page_size,
                similarity_threshold=similarity_threshold,
                vector_similarity_weight=vector_similarity_weight,
                keyword=keyword_match,
                highlight=highlight
            )
            
            # 解析响应，提取chunks内容
            chunks = response.get('data', {}).get('chunks', [])
            if chunks:
                # 合并chunk的内容
                contents = []
                for chunk in chunks:
                    content = chunk.get('content', '').strip()
                    if content:
                        contents.append(content[:150])  # 限制每个chunk长度
                
                if contents:
                    combined_content = ' | '.join(contents)
                    return f"{keyword}: {combined_content}"
            
            return f"{keyword}: 未找到相关知识"
            
        except Exception as e:
            print(f"[KnowledgeQueryUtils] 异步知识查询异常 {keyword}: {e}")
            return f"{keyword}: 查询失败"


class PerformanceUtils:
    """性能监控工具类"""
    
    @staticmethod
    def monitor_performance(func):
        """性能监控装饰器"""
        def wrapper(*args, **kwargs):
            import time
            start_time = time.time()
            result = func(*args, **kwargs)
            elapsed = time.time() - start_time
            print(f"[性能监控] {func.__name__} 耗时: {elapsed:.2f}s")
            return result
        return wrapper
    
    @staticmethod
    def create_performance_stats() -> Dict[str, Any]:
        """创建性能统计字典"""
        return {
            "total_time": 0.0,
            "llm_calls": 0,
            "knowledge_queries": 0,
            "cache_hits": 0,
            "self_review_rounds": 0
        }


class PromptUtils:
    """Prompt工具类"""
    
    @staticmethod
    def build_structured_output_instructions(parser) -> str:
        """构建结构化输出指令"""
        return f"""
{parser.get_format_instructions()}

注意：
- 如果代码质量良好且无明显问题，返回空数组
- codePosition使用4点坐标格式：[startLine, startColumn, endLine, endColumn]
- 在targetCode中提供精确的代码片段
"""
    
    @staticmethod
    def build_code_info_section(diff_content: str, full_code: str, 
                               upstream_str: str = "无", downstream_str: str = "无") -> str:
        """构建代码信息部分"""
        return f"""
=== 代码信息 ===
代码变更: {diff_content}
完整代码: {full_code}
上游依赖: {upstream_str}
下游依赖: {downstream_str}
"""
