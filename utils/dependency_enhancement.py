
"""
依赖分析增强模块

用于改进现有的依赖分析功能，确保能够正确识别和解析代码依赖关系
"""

import logging
from typing import Dict, List, Any, Optional

class DependencyAnalysisEnhancer:
    """依赖分析增强器"""
    
    def __init__(self, chunk_service):
        self.chunk_service = chunk_service
        self.logger = logging.getLogger(__name__)
    
    def enhance_function_index_building(self) -> Dict[str, Any]:
        """增强函数索引构建"""
        self.logger.info("开始增强函数索引构建...")
        
        try:
            # 确保项目信息有效
            if (not self.chunk_service.project or not self.chunk_service.repo or
                self.chunk_service.project in ("default", "unknown") or
                self.chunk_service.repo in ("default", "unknown")):
                
                self.logger.warning("项目信息无效，使用默认值")
                self.chunk_service.project = "enhanced_project"
                self.chunk_service.repo = "enhanced_repo"
            
            # 强制重新构建索引
            func_index = self.chunk_service.build_func_index(use_cache=False)
            
            if not func_index:
                self.logger.warning("函数索引为空，尝试手动构建...")
                func_index = self._manual_build_index()
            
            return func_index
            
        except Exception as e:
            self.logger.error(f"增强函数索引构建失败: {e}")
            return {}
    
    def _manual_build_index(self) -> Dict[str, Any]:
        """手动构建函数索引"""
        self.logger.info("开始手动构建函数索引...")
        
        func_index = {}
        
        try:
            # 获取所有代码文件
            suffixes = ['.py', '.java', '.js', '.ts']
            files = self.chunk_service.get_core_code_files(suffixes)
            
            self.logger.info(f"找到 {len(files)} 个代码文件")
            
            for file_path in files:
                try:
                    chunks = self.chunk_service.chunk_code_file(file_path)
                    
                    for chunk in chunks:
                        if 'name' in chunk and chunk['name']:
                            # 创建唯一键
                            unique_key = f"{file_path}:{chunk['name']}"
                            func_index[unique_key] = chunk
                            
                            # 同时创建全局键（如果函数名唯一）
                            global_key = f"global:{chunk['name']}"
                            if global_key not in func_index:
                                func_index[global_key] = chunk
                    
                    self.logger.info(f"处理文件 {file_path}: {len(chunks)} 个块")
                    
                except Exception as e:
                    self.logger.warning(f"处理文件 {file_path} 失败: {e}")
                    continue
            
            self.logger.info(f"手动构建完成，共 {len(func_index)} 个函数")
            return func_index
            
        except Exception as e:
            self.logger.error(f"手动构建函数索引失败: {e}")
            return {}
    
    def enhance_dependency_resolution(self, segments: List[Dict]) -> List[Dict]:
        """增强依赖解析"""
        self.logger.info("开始增强依赖解析...")
        
        enhanced_segments = []
        
        for segment in segments:
            try:
                enhanced_segment = self._enhance_single_segment(segment)
                enhanced_segments.append(enhanced_segment)
            except Exception as e:
                self.logger.warning(f"增强片段失败: {e}")
                enhanced_segments.append(segment)
        
        return enhanced_segments
    
    def _enhance_single_segment(self, segment: Dict) -> Dict:
        """增强单个片段的依赖信息"""
        # 如果已有依赖信息，直接返回
        if (segment.get('upstream') or segment.get('downstream') or
            segment.get('upstream_code') or segment.get('downstream_code')):
            return segment
        
        # 尝试从代码内容中提取依赖
        content = segment.get('content', '')
        file_path = segment.get('file', '')
        
        if content and file_path.endswith('.py'):
            upstream, downstream = self._extract_python_dependencies(content, file_path)
            segment['upstream'] = upstream
            segment['downstream'] = downstream
            
            # 尝试获取依赖代码
            segment['upstream_code'] = self._get_dependency_code(upstream)
            segment['downstream_code'] = self._get_dependency_code(downstream)
        
        return segment
    
    def _extract_python_dependencies(self, content: str, file_path: str) -> tuple:
        """从Python代码中提取依赖关系"""
        import ast
        import re
        
        upstream = []
        downstream = []
        
        try:
            # 使用正则表达式提取函数调用
            function_calls = re.findall(r'\b([a-zA-Z_][a-zA-Z0-9_]*)\s*\$', content)
            upstream.extend(function_calls)
            
            # 使用AST解析更精确的依赖
            try:
                tree = ast.parse(content)
                for node in ast.walk(tree):
                    if isinstance(node, ast.Call):
                        if isinstance(node.func, ast.Name):
                            upstream.append(node.func.id)
                        elif isinstance(node.func, ast.Attribute):
                            upstream.append(node.func.attr)
            except:
                pass
            
            # 去重
            upstream = list(set(upstream))
            
            # 过滤内置函数
            builtin_functions = {'print', 'len', 'str', 'int', 'float', 'list', 'dict', 'set', 'tuple'}
            upstream = [func for func in upstream if func not in builtin_functions]
            
        except Exception as e:
            self.logger.debug(f"提取依赖失败: {e}")
        
        return upstream, downstream
    
    def _get_dependency_code(self, dependencies: List[str]) -> Dict[str, str]:
        """获取依赖代码"""
        dependency_code = {}
        
        if not hasattr(self.chunk_service, 'func_index') or not self.chunk_service.func_index:
            return dependency_code
        
        for dep in dependencies:
            # 尝试多种键格式
            possible_keys = [
                f"global:{dep}",
                dep,
            ]
            
            # 添加文件特定的键
            for key in self.chunk_service.func_index.keys():
                if key.endswith(f":{dep}"):
                    possible_keys.append(key)
            
            for key in possible_keys:
                if key in self.chunk_service.func_index:
                    chunk = self.chunk_service.func_index[key]
                    if isinstance(chunk, dict) and 'content' in chunk:
                        dependency_code[dep] = chunk['content']
                        break
        
        return dependency_code

# 使用示例
def apply_dependency_enhancement(chunk_service):
    """应用依赖分析增强"""
    enhancer = DependencyAnalysisEnhancer(chunk_service)
    
    # 增强函数索引构建
    func_index = enhancer.enhance_function_index_building()
    chunk_service.func_index = func_index
    
    return enhancer
