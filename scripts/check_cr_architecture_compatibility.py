#!/usr/bin/env python3
"""
CR架构兼容性检查脚本
检查新旧架构的兼容性状态，识别潜在问题并提供解决建议
"""

import sys
import os
import logging
from typing import Dict, List, Any
from datetime import datetime

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

class CRArchitectureChecker:
    """CR架构兼容性检查器"""
    
    def __init__(self):
        self.logger = setup_logging()
        self.issues = []
        self.warnings = []
        self.recommendations = []
    
    def check_service_availability(self) -> Dict[str, bool]:
        """检查服务可用性"""
        services_status = {}
        
        # 检查新架构服务
        try:
            from services.cr_service import CRService
            services_status['new_cr_service'] = True
            self.logger.info("✅ 新架构CRService可用")
        except ImportError as e:
            services_status['new_cr_service'] = False
            self.issues.append(f"新架构CRService不可用: {str(e)}")
        
        # 检查旧架构服务
        try:
            from api.service.cr_lc_service import CrLCService
            services_status['old_cr_service'] = True
            self.logger.info("✅ 旧架构CrLCService可用")
        except ImportError as e:
            services_status['old_cr_service'] = False
            self.issues.append(f"旧架构CrLCService不可用: {str(e)}")
        
        # 检查Agent系统
        try:
            from core.agents.agent_factory import get_agent_factory
            factory = get_agent_factory()
            agent_types = factory.list_agent_types()
            services_status['agent_system'] = len(agent_types) > 0
            self.logger.info(f"✅ Agent系统可用，支持类型: {agent_types}")
        except Exception as e:
            services_status['agent_system'] = False
            self.issues.append(f"Agent系统不可用: {str(e)}")
        
        # 检查编排器系统
        try:
            from core.orchestrator.workflow_manager import get_workflow_manager
            manager = get_workflow_manager()
            orchestrator_types = manager.list_orchestrator_types()
            services_status['orchestrator_system'] = len(orchestrator_types) > 0
            self.logger.info(f"✅ 编排器系统可用，支持类型: {orchestrator_types}")
        except Exception as e:
            services_status['orchestrator_system'] = False
            self.issues.append(f"编排器系统不可用: {str(e)}")
        
        return services_status
    
    def check_dependency_services(self) -> Dict[str, bool]:
        """检查依赖服务"""
        dependencies_status = {}
        
        # 检查LLM服务
        try:
            from core.service_registry import get_llm_service
            llm_service = get_llm_service()
            dependencies_status['llm_service'] = llm_service is not None
            if llm_service:
                self.logger.info("✅ LLM服务可用")
            else:
                self.warnings.append("LLM服务不可用，可能影响新架构功能")
        except Exception as e:
            dependencies_status['llm_service'] = False
            self.warnings.append(f"LLM服务检查失败: {str(e)}")
        
        # 检查Git服务
        try:
            from core.service_registry import get_git_service
            git_service = get_git_service()
            dependencies_status['git_service'] = git_service is not None
            if git_service:
                self.logger.info("✅ Git服务可用")
            else:
                self.warnings.append("Git服务不可用，可能影响代码差异分析")
        except Exception as e:
            dependencies_status['git_service'] = False
            self.warnings.append(f"Git服务检查失败: {str(e)}")
        
        # 检查DevMind服务
        try:
            from core.service_registry import get_service
            devmind_service = get_service('devmind_service')
            dependencies_status['devmind_service'] = devmind_service is not None
            if devmind_service:
                self.logger.info("✅ DevMind服务可用")
            else:
                self.warnings.append("DevMind服务不可用，可能影响知识库检索")
        except Exception as e:
            dependencies_status['devmind_service'] = False
            self.warnings.append(f"DevMind服务检查失败: {str(e)}")
        
        return dependencies_status
    
    def check_configuration_compatibility(self) -> Dict[str, Any]:
        """检查配置兼容性"""
        config_status = {}
        
        # 检查CR模式配置
        try:
            from services.cr_service import CRService
            from unittest.mock import Mock
            
            # 创建模拟服务实例
            mock_services = {
                'horn_service': Mock(),
                'kms_service': Mock(),
                'dx_service': Mock(),
                'git_service': Mock(),
                'devmind_service': Mock()
            }
            
            cr_service = CRService(**mock_services)
            
            # 测试CR模式设置
            test_modes = ["fast", "standard", "deep", "async_fast"]
            supported_modes = []
            
            for mode in test_modes:
                try:
                    cr_service.set_cr_mode(mode)
                    current_mode = cr_service.get_cr_mode()
                    if current_mode == mode:
                        supported_modes.append(mode)
                except Exception as e:
                    self.warnings.append(f"CR模式 {mode} 设置失败: {str(e)}")
            
            config_status['supported_cr_modes'] = supported_modes
            self.logger.info(f"✅ 支持的CR模式: {supported_modes}")
            
        except Exception as e:
            config_status['supported_cr_modes'] = []
            self.issues.append(f"CR模式配置检查失败: {str(e)}")
        
        return config_status
    
    def check_result_enhancer(self) -> bool:
        """检查结果增强器"""
        try:
            from utils.cr_result_enhancer import CRResultEnhancer
            
            enhancer = CRResultEnhancer()
            
            # 测试基本功能
            test_result = {
                "checkBranch": "test",
                "sumCheckResult": "通过",
                "resultDesc": "测试",
                "totalProblem": "0",
                "problemList": []
            }
            
            enhanced = enhancer.enhance_cr_result(test_result)
            frontend_format = enhancer.to_frontend_format(enhanced)
            
            # 检查必要字段
            required_fields = ['summary', 'statistics', 'problems', 'summaryDetails', 'reviewMetrics', 'recommendations']
            missing_fields = [field for field in required_fields if field not in frontend_format]
            
            if missing_fields:
                self.warnings.append(f"结果增强器缺少字段: {missing_fields}")
                return False
            else:
                self.logger.info("✅ 结果增强器功能正常")
                return True
                
        except Exception as e:
            self.issues.append(f"结果增强器检查失败: {str(e)}")
            return False
    
    def generate_recommendations(self, services_status: Dict[str, bool], dependencies_status: Dict[str, bool]):
        """生成改进建议"""
        
        # 基于服务状态生成建议
        if not services_status.get('new_cr_service', False):
            self.recommendations.append("🔧 修复新架构CRService导入问题")
        
        if not services_status.get('agent_system', False):
            self.recommendations.append("🤖 检查Agent系统配置和依赖")
        
        if not services_status.get('orchestrator_system', False):
            self.recommendations.append("🎭 检查编排器系统配置和依赖")
        
        # 基于依赖状态生成建议
        if not dependencies_status.get('llm_service', False):
            self.recommendations.append("🧠 配置LLM服务以启用新架构功能")
        
        if not dependencies_status.get('git_service', False):
            self.recommendations.append("📁 配置Git服务以支持代码差异分析")
        
        # 架构迁移建议
        if services_status.get('old_cr_service', False) and not services_status.get('new_cr_service', False):
            self.recommendations.append("🔄 考虑完成新架构迁移")
        
        if services_status.get('new_cr_service', False) and services_status.get('old_cr_service', False):
            self.recommendations.append("⚖️ 建议配置新旧架构的平滑切换机制")
    
    def run_full_check(self) -> Dict[str, Any]:
        """运行完整检查"""
        self.logger.info("🔍 开始CR架构兼容性检查...")
        
        # 检查各个组件
        services_status = self.check_service_availability()
        dependencies_status = self.check_dependency_services()
        config_status = self.check_configuration_compatibility()
        enhancer_status = self.check_result_enhancer()
        
        # 生成建议
        self.generate_recommendations(services_status, dependencies_status)
        
        # 汇总结果
        result = {
            'timestamp': datetime.now().isoformat(),
            'services_status': services_status,
            'dependencies_status': dependencies_status,
            'config_status': config_status,
            'enhancer_status': enhancer_status,
            'issues': self.issues,
            'warnings': self.warnings,
            'recommendations': self.recommendations,
            'overall_health': self._calculate_overall_health(services_status, dependencies_status)
        }
        
        return result
    
    def _calculate_overall_health(self, services_status: Dict[str, bool], dependencies_status: Dict[str, bool]) -> str:
        """计算整体健康状态"""
        total_checks = len(services_status) + len(dependencies_status)
        passed_checks = sum(services_status.values()) + sum(dependencies_status.values())
        
        health_ratio = passed_checks / total_checks if total_checks > 0 else 0
        
        if health_ratio >= 0.8:
            return "良好"
        elif health_ratio >= 0.6:
            return "一般"
        else:
            return "需要改进"

def main():
    """主函数"""
    print("🚀 CR架构兼容性检查")
    print("=" * 60)
    
    # 添加项目根目录到路径
    project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    if project_root not in sys.path:
        sys.path.insert(0, project_root)
    
    checker = CRArchitectureChecker()
    result = checker.run_full_check()
    
    # 输出结果
    print(f"\n📊 检查结果 (整体健康状态: {result['overall_health']})")
    print("-" * 40)
    
    print("\n🔧 服务状态:")
    for service, status in result['services_status'].items():
        status_icon = "✅" if status else "❌"
        print(f"   {status_icon} {service}: {'可用' if status else '不可用'}")
    
    print("\n🔗 依赖状态:")
    for dep, status in result['dependencies_status'].items():
        status_icon = "✅" if status else "⚠️"
        print(f"   {status_icon} {dep}: {'可用' if status else '不可用'}")
    
    if result['issues']:
        print("\n❌ 发现的问题:")
        for issue in result['issues']:
            print(f"   • {issue}")
    
    if result['warnings']:
        print("\n⚠️ 警告:")
        for warning in result['warnings']:
            print(f"   • {warning}")
    
    if result['recommendations']:
        print("\n💡 改进建议:")
        for rec in result['recommendations']:
            print(f"   • {rec}")
    
    # 保存详细报告
    import json
    report_file = f"cr_architecture_check_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(result, f, ensure_ascii=False, indent=2)
    
    print(f"\n📄 详细报告已保存到: {report_file}")
    
    # 返回状态码
    if result['overall_health'] == "良好":
        print("\n🎉 CR架构兼容性检查通过！")
        return 0
    else:
        print("\n⚠️ CR架构存在兼容性问题，请查看建议进行改进")
        return 1

if __name__ == "__main__":
    sys.exit(main())
