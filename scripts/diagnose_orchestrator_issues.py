#!/usr/bin/env python3
"""
编排器问题诊断脚本
深入分析编排器系统无法使用的具体原因
"""

import sys
import os
import logging
import traceback
from typing import Dict, List, Any, Optional
from datetime import datetime

def setup_logging():
    """设置详细日志"""
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

class OrchestratorDiagnostic:
    """编排器诊断器"""
    
    def __init__(self):
        self.logger = setup_logging()
        self.issues = []
        self.warnings = []
        self.debug_info = []
    
    def check_imports(self) -> Dict[str, bool]:
        """检查关键模块导入"""
        import_status = {}
        
        # 检查编排器相关导入
        modules_to_check = [
            'core.orchestrator.workflow_manager',
            'core.orchestrator.cr_orchestrator', 
            'core.orchestrator.base_orchestrator',
            'core.agents.agent_factory',
            'core.service_registry'
        ]
        
        for module in modules_to_check:
            try:
                __import__(module)
                import_status[module] = True
                self.logger.info(f"✅ 模块导入成功: {module}")
            except ImportError as e:
                import_status[module] = False
                self.issues.append(f"模块导入失败: {module} - {str(e)}")
                self.logger.error(f"❌ 模块导入失败: {module} - {str(e)}")
            except Exception as e:
                import_status[module] = False
                self.issues.append(f"模块导入异常: {module} - {str(e)}")
                self.logger.error(f"❌ 模块导入异常: {module} - {str(e)}")
        
        return import_status
    
    def check_workflow_manager(self) -> Dict[str, Any]:
        """检查工作流管理器"""
        manager_status = {}
        
        try:
            from core.orchestrator.workflow_manager import get_workflow_manager
            
            # 获取管理器实例
            manager = get_workflow_manager()
            manager_status['instance_created'] = True
            self.logger.info("✅ 工作流管理器实例创建成功")
            
            # 检查编排器类型注册
            orchestrator_types = manager.list_orchestrator_types()
            manager_status['orchestrator_types'] = orchestrator_types
            manager_status['has_cr_orchestrator'] = 'cr_orchestrator' in orchestrator_types
            
            if orchestrator_types:
                self.logger.info(f"✅ 已注册编排器类型: {orchestrator_types}")
            else:
                self.warnings.append("没有注册任何编排器类型")
                self.logger.warning("⚠️ 没有注册任何编排器类型")
            
            # 检查编排器实例
            instances = manager.list_orchestrator_instances()
            manager_status['instance_count'] = len(instances)
            manager_status['instances'] = instances
            
            if instances:
                self.logger.info(f"✅ 当前编排器实例: {list(instances.keys())}")
            else:
                self.debug_info.append("当前没有活跃的编排器实例")
                self.logger.info("ℹ️ 当前没有活跃的编排器实例")
            
        except Exception as e:
            manager_status['instance_created'] = False
            manager_status['error'] = str(e)
            self.issues.append(f"工作流管理器检查失败: {str(e)}")
            self.logger.error(f"❌ 工作流管理器检查失败: {str(e)}")
            traceback.print_exc()
        
        return manager_status
    
    def check_cr_orchestrator_creation(self) -> Dict[str, Any]:
        """检查CR编排器创建"""
        creation_status = {}
        
        try:
            from core.orchestrator.workflow_manager import get_workflow_manager
            from unittest.mock import Mock
            
            manager = get_workflow_manager()
            
            # 创建模拟服务
            mock_services = {
                'llm_service': Mock(),
                'devmind_service': Mock(),
                'chunk_service': None,  # 允许为None
                'git_service': Mock()
            }
            
            self.logger.info("🔧 尝试创建CR编排器...")
            
            # 尝试创建CR编排器
            orchestrator = manager.create_orchestrator(
                'cr_orchestrator',
                'test_cr_orchestrator',
                {'cr_mode': 'standard'},
                **mock_services
            )
            
            creation_status['creation_success'] = True
            creation_status['orchestrator_id'] = 'test_cr_orchestrator'
            creation_status['orchestrator_status'] = orchestrator.get_status()
            
            self.logger.info("✅ CR编排器创建成功")
            
            # 测试编排器方法
            try:
                orchestrator.set_cr_mode('fast')
                creation_status['mode_setting_success'] = True
                self.logger.info("✅ CR模式设置成功")
            except Exception as e:
                creation_status['mode_setting_success'] = False
                creation_status['mode_setting_error'] = str(e)
                self.warnings.append(f"CR模式设置失败: {str(e)}")
            
            # 清理测试编排器
            manager.remove_orchestrator('test_cr_orchestrator')
            
        except Exception as e:
            creation_status['creation_success'] = False
            creation_status['error'] = str(e)
            self.issues.append(f"CR编排器创建失败: {str(e)}")
            self.logger.error(f"❌ CR编排器创建失败: {str(e)}")
            traceback.print_exc()
        
        return creation_status
    
    def check_agent_factory(self) -> Dict[str, Any]:
        """检查Agent工厂"""
        factory_status = {}
        
        try:
            from core.agents.agent_factory import get_agent_factory
            
            factory = get_agent_factory()
            factory_status['factory_created'] = True
            self.logger.info("✅ Agent工厂创建成功")
            
            # 检查Agent类型
            agent_types = factory.list_agent_types()
            factory_status['agent_types'] = agent_types
            factory_status['has_cr_agents'] = any('cr' in agent_type.lower() for agent_type in agent_types)
            
            if agent_types:
                self.logger.info(f"✅ 可用Agent类型: {agent_types}")
            else:
                self.warnings.append("没有注册任何Agent类型")
                self.logger.warning("⚠️ 没有注册任何Agent类型")
            
            # 尝试创建CR Agent集合
            try:
                from unittest.mock import Mock
                
                mock_services = {
                    'llm_service': Mock(),
                    'devmind_service': Mock(),
                    'chunk_service': None,
                    'git_service': Mock()
                }
                
                agents = factory.create_cr_agent_set(
                    mock_services['llm_service'],
                    mock_services['devmind_service'],
                    mock_services['chunk_service'],
                    mock_services['git_service'],
                    {}
                )
                
                factory_status['cr_agent_set_creation'] = True
                factory_status['cr_agent_count'] = len(agents) if agents else 0
                self.logger.info(f"✅ CR Agent集合创建成功，包含 {len(agents) if agents else 0} 个Agent")
                
            except Exception as e:
                factory_status['cr_agent_set_creation'] = False
                factory_status['cr_agent_set_error'] = str(e)
                self.warnings.append(f"CR Agent集合创建失败: {str(e)}")
                self.logger.warning(f"⚠️ CR Agent集合创建失败: {str(e)}")
        
        except Exception as e:
            factory_status['factory_created'] = False
            factory_status['error'] = str(e)
            self.issues.append(f"Agent工厂检查失败: {str(e)}")
            self.logger.error(f"❌ Agent工厂检查失败: {str(e)}")
            traceback.print_exc()
        
        return factory_status
    
    def check_service_dependencies(self) -> Dict[str, Any]:
        """检查服务依赖"""
        deps_status = {}
        
        # 检查服务注册表
        try:
            from core.service_registry import get_service, list_services
            
            available_services = list_services()
            deps_status['available_services'] = available_services
            deps_status['service_count'] = len(available_services)
            
            self.logger.info(f"✅ 可用服务: {available_services}")
            
            # 检查关键服务
            key_services = ['llm_service', 'git_service', 'devmind_service']
            for service_name in key_services:
                try:
                    service = get_service(service_name)
                    deps_status[f'{service_name}_available'] = service is not None
                    if service:
                        self.logger.info(f"✅ {service_name} 可用")
                    else:
                        self.warnings.append(f"{service_name} 不可用")
                        self.logger.warning(f"⚠️ {service_name} 不可用")
                except Exception as e:
                    deps_status[f'{service_name}_available'] = False
                    deps_status[f'{service_name}_error'] = str(e)
                    self.warnings.append(f"{service_name} 获取失败: {str(e)}")
        
        except Exception as e:
            deps_status['registry_error'] = str(e)
            self.issues.append(f"服务注册表检查失败: {str(e)}")
            self.logger.error(f"❌ 服务注册表检查失败: {str(e)}")
        
        return deps_status
    
    def simulate_cr_service_initialization(self) -> Dict[str, Any]:
        """模拟CR服务初始化过程"""
        simulation_status = {}
        
        try:
            from services.cr_service import CRService
            from unittest.mock import Mock
            
            self.logger.info("🔧 模拟CR服务初始化...")
            
            # 创建模拟服务
            mock_services = {
                'horn_service': Mock(),
                'kms_service': Mock(),
                'dx_service': Mock(),
                'git_service': Mock(),
                'devmind_service': Mock()
            }
            
            # 创建CR服务实例
            cr_service = CRService(**mock_services)
            simulation_status['cr_service_created'] = True
            self.logger.info("✅ CR服务实例创建成功")
            
            # 模拟初始化过程
            try:
                # 这里不能直接调用initialize()因为它是异步的
                # 我们手动检查关键步骤
                
                # 检查工作流管理器
                workflow_manager = cr_service.workflow_manager
                simulation_status['workflow_manager_available'] = workflow_manager is not None
                
                if workflow_manager:
                    self.logger.info("✅ 工作流管理器可用")
                else:
                    self.warnings.append("工作流管理器不可用")
                
                # 检查配置
                config = cr_service.config
                simulation_status['config'] = config
                self.logger.info(f"✅ CR服务配置: {config}")
                
            except Exception as e:
                simulation_status['initialization_error'] = str(e)
                self.warnings.append(f"CR服务初始化模拟失败: {str(e)}")
                self.logger.warning(f"⚠️ CR服务初始化模拟失败: {str(e)}")
        
        except Exception as e:
            simulation_status['cr_service_created'] = False
            simulation_status['error'] = str(e)
            self.issues.append(f"CR服务模拟失败: {str(e)}")
            self.logger.error(f"❌ CR服务模拟失败: {str(e)}")
            traceback.print_exc()
        
        return simulation_status
    
    def run_full_diagnosis(self) -> Dict[str, Any]:
        """运行完整诊断"""
        self.logger.info("🔍 开始编排器系统诊断...")
        
        diagnosis_result = {
            'timestamp': datetime.now().isoformat(),
            'import_status': self.check_imports(),
            'workflow_manager_status': self.check_workflow_manager(),
            'cr_orchestrator_status': self.check_cr_orchestrator_creation(),
            'agent_factory_status': self.check_agent_factory(),
            'service_dependencies_status': self.check_service_dependencies(),
            'cr_service_simulation': self.simulate_cr_service_initialization(),
            'issues': self.issues,
            'warnings': self.warnings,
            'debug_info': self.debug_info
        }
        
        return diagnosis_result
    
    def generate_recommendations(self, diagnosis_result: Dict[str, Any]) -> List[str]:
        """生成修复建议"""
        recommendations = []
        
        # 基于导入状态生成建议
        import_status = diagnosis_result.get('import_status', {})
        failed_imports = [module for module, status in import_status.items() if not status]
        if failed_imports:
            recommendations.append(f"🔧 修复模块导入问题: {', '.join(failed_imports)}")
        
        # 基于工作流管理器状态生成建议
        manager_status = diagnosis_result.get('workflow_manager_status', {})
        if not manager_status.get('instance_created', False):
            recommendations.append("🔧 修复工作流管理器创建问题")
        
        if not manager_status.get('has_cr_orchestrator', False):
            recommendations.append("🔧 确保CR编排器类型正确注册")
        
        # 基于编排器创建状态生成建议
        orchestrator_status = diagnosis_result.get('cr_orchestrator_status', {})
        if not orchestrator_status.get('creation_success', False):
            recommendations.append("🔧 修复CR编排器创建问题")
        
        # 基于Agent工厂状态生成建议
        factory_status = diagnosis_result.get('agent_factory_status', {})
        if not factory_status.get('factory_created', False):
            recommendations.append("🔧 修复Agent工厂问题")
        
        if not factory_status.get('cr_agent_set_creation', False):
            recommendations.append("🔧 修复CR Agent集合创建问题")
        
        # 基于服务依赖状态生成建议
        deps_status = diagnosis_result.get('service_dependencies_status', {})
        if not deps_status.get('llm_service_available', False):
            recommendations.append("🧠 配置LLM服务")
        
        if not deps_status.get('git_service_available', False):
            recommendations.append("📁 配置Git服务")
        
        # 通用建议
        if self.issues:
            recommendations.append("🔍 查看详细错误日志并逐一修复问题")
        
        if not recommendations:
            recommendations.append("🎉 编排器系统状态良好，无需修复")
        
        return recommendations

def main():
    """主函数"""
    print("🔍 编排器系统诊断")
    print("=" * 60)
    
    # 添加项目根目录到路径
    project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    if project_root not in sys.path:
        sys.path.insert(0, project_root)
    
    diagnostic = OrchestratorDiagnostic()
    result = diagnostic.run_full_diagnosis()
    
    # 生成建议
    recommendations = diagnostic.generate_recommendations(result)
    result['recommendations'] = recommendations
    
    # 输出结果
    print(f"\n📊 诊断结果")
    print("-" * 40)
    
    # 显示关键状态
    print(f"\n🔧 关键组件状态:")
    print(f"   - 模块导入: {'✅' if all(result['import_status'].values()) else '❌'}")
    print(f"   - 工作流管理器: {'✅' if result['workflow_manager_status'].get('instance_created') else '❌'}")
    print(f"   - CR编排器: {'✅' if result['cr_orchestrator_status'].get('creation_success') else '❌'}")
    print(f"   - Agent工厂: {'✅' if result['agent_factory_status'].get('factory_created') else '❌'}")
    
    if result['issues']:
        print(f"\n❌ 发现的问题:")
        for issue in result['issues']:
            print(f"   • {issue}")
    
    if result['warnings']:
        print(f"\n⚠️ 警告:")
        for warning in result['warnings']:
            print(f"   • {warning}")
    
    print(f"\n💡 修复建议:")
    for rec in recommendations:
        print(f"   • {rec}")
    
    # 保存详细报告
    import json
    report_file = f"orchestrator_diagnosis_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(result, f, ensure_ascii=False, indent=2)
    
    print(f"\n📄 详细报告已保存到: {report_file}")
    
    # 返回状态码
    if not result['issues']:
        print(f"\n🎉 编排器系统诊断完成，状态良好！")
        return 0
    else:
        print(f"\n⚠️ 编排器系统存在问题，请查看建议进行修复")
        return 1

if __name__ == "__main__":
    sys.exit(main())
