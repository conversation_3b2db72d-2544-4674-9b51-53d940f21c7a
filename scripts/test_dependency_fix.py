#!/usr/bin/env python3
"""
测试依赖解析修复效果的脚本
"""

import os
import sys
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def test_chunk_service_with_real_diff():
    """使用真实的diff测试ChunkService"""
    logger = logging.getLogger(__name__)
    logger.info("=" * 60)
    logger.info("测试ChunkService依赖解析修复效果")
    logger.info("=" * 60)
    
    try:
        from services.chunk_service import ChunkService
        
        # 模拟GitService
        class TestGitService:
            def __init__(self):
                self.base_dir = "/tmp/test_project"
            
            def list_repo_files(self, project, repo, branch=None, suffixes=None):
                return [
                    "src/main.py",
                    "src/utils.py",
                    "src/service.py"
                ]
            
            def read_file(self, project, repo, file_path):
                if file_path == "src/main.py":
                    return '''
def main():
    """主函数"""
    result = process_data("test")
    user = get_user(1)
    return result, user

def startup():
    """启动函数"""
    return main()
'''
                elif file_path == "src/utils.py":
                    return '''
def process_data(data):
    """处理数据"""
    return clean_data(data).upper()

def clean_data(data):
    """清理数据"""
    return data.strip()

def validate_input(data):
    """验证输入"""
    return len(data) > 0
'''
                elif file_path == "src/service.py":
                    return '''
def get_user(user_id):
    """获取用户"""
    if validate_input(str(user_id)):
        return {"id": user_id, "name": "User"}
    return None

def create_user(data):
    """创建用户"""
    return process_data(data)
'''
                return ""
            
            def get_repo_path(self, project, repo):
                return f"/tmp/{project}/{repo}"
        
        # 创建ChunkService实例
        git_service = TestGitService()
        chunk_service = ChunkService(
            git_service=git_service,
            project="test_project",
            repo="test_repo",
            branch="main"
        )
        
        logger.info("✅ ChunkService实例创建成功")
        
        # 测试diff分析
        test_diff = '''
diff --git a/src/main.py b/src/main.py
index 1234567..abcdefg 100644
--- a/src/main.py
+++ b/src/main.py
@@ -1,8 +1,10 @@
 def main():
     """主函数"""
-    result = process_data("test")
+    result = process_data("test_data")
+    print(f"处理结果: {result}")
     user = get_user(1)
+    log_action("main_called")
     return result, user
 
 def startup():
'''
        
        logger.info("开始测试diff分析...")
        segments = chunk_service.enrich_diff_segments_with_context(test_diff, max_dep_depth=2)
        
        logger.info(f"✅ diff分析完成，生成 {len(segments)} 个片段")
        
        # 检查依赖解析结果
        total_upstream = 0
        total_downstream = 0
        
        for i, segment in enumerate(segments):
            upstream_count = len(segment.get('upstream', []))
            downstream_count = len(segment.get('downstream', []))
            upstream_code_count = len(segment.get('upstream_code', {}))
            downstream_code_count = len(segment.get('downstream_code', {}))
            
            total_upstream += upstream_count
            total_downstream += downstream_count
            
            logger.info(f"片段 {i+1}:")
            logger.info(f"  文件: {segment.get('file', 'N/A')}")
            logger.info(f"  上游依赖: {upstream_count} 个")
            logger.info(f"  下游依赖: {downstream_count} 个")
            logger.info(f"  上游代码: {upstream_code_count} 个")
            logger.info(f"  下游代码: {downstream_code_count} 个")
            
            if upstream_count > 0:
                logger.info(f"  上游依赖列表: {segment.get('upstream', [])}")
            
            if downstream_count > 0:
                logger.info(f"  下游依赖列表: {segment.get('downstream', [])}")
        
        # 评估修复效果
        logger.info("\n" + "=" * 60)
        logger.info("修复效果评估")
        logger.info("=" * 60)
        
        if total_upstream > 0 or total_downstream > 0:
            logger.info("🎉 依赖解析修复成功！")
            logger.info(f"   总上游依赖: {total_upstream} 个")
            logger.info(f"   总下游依赖: {total_downstream} 个")
            return True
        else:
            logger.warning("⚠️  依赖解析仍然为空，需要进一步调试")
            return False
            
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def test_manual_dependency_extraction():
    """测试手动依赖提取功能"""
    logger = logging.getLogger(__name__)
    logger.info("\n" + "=" * 60)
    logger.info("测试手动依赖提取功能")
    logger.info("=" * 60)
    
    try:
        from services.chunk_service import ChunkService
        
        # 创建一个简单的ChunkService实例
        chunk_service = ChunkService(None, "test", "test")
        
        # 测试代码
        test_code = '''
def main():
    result = process_data("test")
    user = get_user(1)
    validated = validate_input(result)
    if validated:
        save_result(result)
    return result

def helper():
    return format_output("helper")
'''
        
        # 提取依赖
        upstream, downstream = chunk_service._extract_python_dependencies(test_code, "test.py")
        
        logger.info(f"✅ 依赖提取完成")
        logger.info(f"   上游依赖: {upstream}")
        logger.info(f"   下游依赖: {downstream}")
        
        expected_functions = ['process_data', 'get_user', 'validate_input', 'save_result', 'format_output']
        found_functions = [func for func in expected_functions if func in upstream]
        
        logger.info(f"   期望函数: {expected_functions}")
        logger.info(f"   找到函数: {found_functions}")
        logger.info(f"   识别率: {len(found_functions)}/{len(expected_functions)} = {len(found_functions)/len(expected_functions)*100:.1f}%")
        
        return len(found_functions) > 0
        
    except Exception as e:
        logger.error(f"❌ 手动依赖提取测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """主函数"""
    logger = setup_logging()
    logger.info("开始测试依赖解析修复效果...")
    
    success_count = 0
    total_tests = 2
    
    # 测试1: ChunkService diff分析
    if test_chunk_service_with_real_diff():
        success_count += 1
    
    # 测试2: 手动依赖提取
    if test_manual_dependency_extraction():
        success_count += 1
    
    # 总结
    logger.info("\n" + "=" * 60)
    logger.info("测试总结")
    logger.info("=" * 60)
    logger.info(f"测试通过: {success_count}/{total_tests}")
    logger.info(f"成功率: {success_count/total_tests*100:.1f}%")
    
    if success_count == total_tests:
        logger.info("🎉 所有测试通过！依赖解析修复成功")
    elif success_count > 0:
        logger.info("⚠️  部分测试通过，需要进一步优化")
    else:
        logger.error("❌ 所有测试失败，需要重新检查修复方案")
    
    return success_count == total_tests

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)