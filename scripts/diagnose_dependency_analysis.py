#!/usr/bin/env python3
"""
依赖解析功能诊断脚本

用于诊断为什么上下游依赖为空的问题
"""

import os
import sys
import logging
from typing import Dict, List, Any

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def check_imports():
    """检查关键模块导入情况"""
    logger = logging.getLogger(__name__)
    logger.info("=" * 60)
    logger.info("1. 检查关键模块导入情况")
    logger.info("=" * 60)
    
    import_results = {}
    
    # 检查常量模块
    try:
        from consts.chunk_consts import (
            SUPPORTED_CODE_SUFFIXES, DEFAULT_CACHE_TTL, DEFAULT_MAX_DEP_DEPTH
        )
        import_results['chunk_consts'] = True
        logger.info(f"✅ 常量模块导入成功")
        logger.info(f"   - 支持的文件类型: {SUPPORTED_CODE_SUFFIXES}")
        logger.info(f"   - 默认缓存TTL: {DEFAULT_CACHE_TTL}")
        logger.info(f"   - 默认依赖深度: {DEFAULT_MAX_DEP_DEPTH}")
    except ImportError as e:
        import_results['chunk_consts'] = False
        logger.error(f"❌ 常量模块导入失败: {e}")
    
    # 检查工具函数模块
    try:
        from utils.chunk_utils import (
            is_import_only_block, detect_source_roots, find_unique_key_for_dep
        )
        import_results['chunk_utils'] = True
        logger.info(f"✅ 工具函数模块导入成功")
    except ImportError as e:
        import_results['chunk_utils'] = False
        logger.error(f"❌ 工具函数模块导入失败: {e}")
    
    # 检查依赖分析器
    try:
        from core.analyzers.dependency_analyzer import DependencyAnalyzer
        import_results['dependency_analyzer'] = True
        logger.info(f"✅ 依赖分析器导入成功")
    except ImportError as e:
        import_results['dependency_analyzer'] = False
        logger.error(f"❌ 依赖分析器导入失败: {e}")
    
    # 检查代码解析器
    try:
        from common.code_parsers import CodeParserFactory
        import_results['code_parsers'] = True
        logger.info(f"✅ 代码解析器导入成功")
    except ImportError as e:
        try:
            from core.parsers import CodeParserFactory
            import_results['code_parsers'] = True
            logger.info(f"✅ 代码解析器导入成功 (core.parsers)")
        except ImportError as e2:
            import_results['code_parsers'] = False
            logger.error(f"❌ 代码解析器导入失败: {e}, {e2}")
    
    # 检查缓存模块
    try:
        from core.cache.chunk_cache import ChunkCache
        import_results['chunk_cache'] = True
        logger.info(f"✅ 缓存模块导入成功")
    except ImportError as e:
        try:
            from common.chunk_cache import ChunkCache
            import_results['chunk_cache'] = True
            logger.info(f"✅ 缓存模块导入成功 (common)")
        except ImportError as e2:
            import_results['chunk_cache'] = False
            logger.error(f"❌ 缓存模块导入失败: {e}, {e2}")
    
    return import_results

def check_chunk_service_initialization():
    """检查ChunkService初始化情况"""
    logger = logging.getLogger(__name__)
    logger.info("\n" + "=" * 60)
    logger.info("2. 检查ChunkService初始化情况")
    logger.info("=" * 60)
    
    try:
        from services.chunk_service import ChunkService
        logger.info("✅ ChunkService导入成功")
        
        # 模拟GitService
        class MockGitService:
            def __init__(self):
                self.base_dir = "/tmp/test_project"
            
            def list_repo_files(self, project, repo, branch=None, suffixes=None):
                # 返回一些测试文件
                return [
                    "src/main.py",
                    "src/utils.py", 
                    "tests/test_main.py"
                ]
            
            def read_file(self, project, repo, file_path):
                # 返回简单的Python代码
                if file_path == "src/main.py":
                    return '''
def main():
    """主函数"""
    result = utils.process_data("test")
    return result

def helper_function():
    """辅助函数"""
    return "helper"
'''
                elif file_path == "src/utils.py":
                    return '''
def process_data(data):
    """处理数据"""
    return data.upper()

def validate_input(input_data):
    """验证输入"""
    return len(input_data) > 0
'''
                return ""
            
            def get_repo_path(self, project, repo):
                return f"/tmp/{project}/{repo}"
        
        # 创建ChunkService实例
        git_service = MockGitService()
        chunk_service = ChunkService(
            git_service=git_service,
            project="test_project",
            repo="test_repo",
            branch="main"
        )
        
        logger.info("✅ ChunkService实例创建成功")
        logger.info(f"   - 项目: {chunk_service.project}")
        logger.info(f"   - 仓库: {chunk_service.repo}")
        logger.info(f"   - 分支: {chunk_service.branch}")
        logger.info(f"   - chunk_utils_available: {chunk_service.chunk_utils_available}")
        
        return chunk_service, git_service
        
    except Exception as e:
        logger.error(f"❌ ChunkService初始化失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return None, None

def test_function_index_building(chunk_service):
    """测试函数索引构建"""
    logger = logging.getLogger(__name__)
    logger.info("\n" + "=" * 60)
    logger.info("3. 测试函数索引构建")
    logger.info("=" * 60)
    
    if not chunk_service:
        logger.error("❌ ChunkService未初始化，跳过测试")
        return {}
    
    try:
        # 构建函数索引
        func_index = chunk_service.build_func_index()
        
        logger.info(f"✅ 函数索引构建完成")
        logger.info(f"   - 索引大小: {len(func_index)}")
        
        if func_index:
            logger.info("   - 索引内容预览:")
            for i, (key, value) in enumerate(list(func_index.items())[:5]):
                logger.info(f"     {i+1}. {key}: {type(value)}")
                if isinstance(value, dict):
                    logger.info(f"        - 文件: {value.get('file', 'N/A')}")
                    logger.info(f"        - 类型: {value.get('type', 'N/A')}")
                    logger.info(f"        - 上游依赖: {len(value.get('upstream', []))}")
                    logger.info(f"        - 下游依赖: {len(value.get('downstream', []))}")
        else:
            logger.warning("⚠️  函数索引为空")
        
        return func_index
        
    except Exception as e:
        logger.error(f"❌ 函数索引构建失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return {}

def test_dependency_analyzer(func_index):
    """测试依赖分析器"""
    logger = logging.getLogger(__name__)
    logger.info("\n" + "=" * 60)
    logger.info("4. 测试依赖分析器")
    logger.info("=" * 60)
    
    if not func_index:
        logger.error("❌ 函数索引为空，跳过依赖分析器测试")
        return
    
    try:
        from core.analyzers.dependency_analyzer import DependencyAnalyzer
        
        analyzer = DependencyAnalyzer(func_index)
        logger.info("✅ 依赖分析器创建成功")
        
        # 测试函数代码获取
        if func_index:
            test_func_name = list(func_index.keys())[0]
            logger.info(f"   - 测试函数: {test_func_name}")
            
            code = analyzer.get_func_code_by_name(test_func_name, "test_file.py")
            if code:
                logger.info(f"   - 获取代码成功: {len(code)} 字符")
                logger.info(f"   - 代码预览: {code[:100]}...")
            else:
                logger.warning(f"   - 未能获取函数代码")
        
        # 测试多层级依赖收集
        if func_index:
            test_chunk = list(func_index.values())[0]
            if isinstance(test_chunk, dict):
                logger.info("   - 测试多层级依赖收集...")
                
                upstream_deps = analyzer.collect_multi_level_deps(test_chunk, 'upstream', max_depth=2)
                downstream_deps = analyzer.collect_multi_level_deps(test_chunk, 'downstream', max_depth=2)
                
                logger.info(f"   - 上游依赖: {len(upstream_deps)} 个")
                logger.info(f"   - 下游依赖: {len(downstream_deps)} 个")
                
                if upstream_deps:
                    logger.info("   - 上游依赖详情:")
                    for dep_name, dep_code in list(upstream_deps.items())[:3]:
                        logger.info(f"     * {dep_name}: {len(dep_code)} 字符")
                
                if downstream_deps:
                    logger.info("   - 下游依赖详情:")
                    for dep_name, dep_code in list(downstream_deps.items())[:3]:
                        logger.info(f"     * {dep_name}: {len(dep_code)} 字符")
        
    except Exception as e:
        logger.error(f"❌ 依赖分析器测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())

def test_code_parsing():
    """测试代码解析"""
    logger = logging.getLogger(__name__)
    logger.info("\n" + "=" * 60)
    logger.info("5. 测试代码解析")
    logger.info("=" * 60)
    
    test_code = '''
def main():
    """主函数"""
    result = process_data("test")
    helper_result = helper_function()
    return result + helper_result

def process_data(data):
    """处理数据"""
    validated = validate_input(data)
    if validated:
        return data.upper()
    return ""

def helper_function():
    """辅助函数"""
    return "helper"

def validate_input(input_data):
    """验证输入"""
    return len(input_data) > 0
'''
    
    try:
        # 尝试导入代码解析器
        try:
            from common.code_parsers import CodeParserFactory
        except ImportError:
            from core.parsers import CodeParserFactory
        
        parser = CodeParserFactory.get_parser("test.py")
        if parser:
            logger.info("✅ Python解析器获取成功")
            
            chunks = parser.parse_chunks(test_code, "test.py", resolve_code=True)
            logger.info(f"   - 解析出 {len(chunks)} 个代码块")
            
            for i, chunk in enumerate(chunks):
                logger.info(f"   - 块 {i+1}: {chunk.get('name', 'N/A')}")
                logger.info(f"     * 类型: {chunk.get('type', 'N/A')}")
                logger.info(f"     * 行数: {chunk.get('start_line', 0)}-{chunk.get('end_line', 0)}")
                logger.info(f"     * 上游依赖: {chunk.get('upstream', [])}")
                logger.info(f"     * 下游依赖: {chunk.get('downstream', [])}")
        else:
            logger.error("❌ 无法获取Python解析器")
            
    except Exception as e:
        logger.error(f"❌ 代码解析测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())

def test_diff_analysis(chunk_service):
    """测试diff分析"""
    logger = logging.getLogger(__name__)
    logger.info("\n" + "=" * 60)
    logger.info("6. 测试diff分析")
    logger.info("=" * 60)
    
    if not chunk_service:
        logger.error("❌ ChunkService未初始化，跳过diff分析测试")
        return
    
    # 模拟diff内容
    test_diff = '''
diff --git a/src/main.py b/src/main.py
index 1234567..abcdefg 100644
--- a/src/main.py
+++ b/src/main.py
@@ -1,8 +1,10 @@
 def main():
     """主函数"""
-    result = utils.process_data("test")
+    result = utils.process_data("test_data")
+    print(f"处理结果: {result}")
     return result
 
 def helper_function():
     """辅助函数"""
+    print("调用辅助函数")
     return "helper"
'''
    
    try:
        segments = chunk_service.enrich_diff_segments_with_context(test_diff, max_dep_depth=2)
        
        logger.info(f"✅ diff分析完成")
        logger.info(f"   - 生成片段数: {len(segments)}")
        
        for i, segment in enumerate(segments):
            logger.info(f"   - 片段 {i+1}:")
            logger.info(f"     * 文件: {segment.get('file', 'N/A')}")
            logger.info(f"     * 类型: {segment.get('type', 'N/A')}")
            logger.info(f"     * 行数: {segment.get('start_line', 0)}-{segment.get('end_line', 0)}")
            logger.info(f"     * 上游依赖: {len(segment.get('upstream', []))} 个")
            logger.info(f"     * 下游依赖: {len(segment.get('downstream', []))} 个")
            logger.info(f"     * 上游代码: {len(segment.get('upstream_code', {}))} 个")
            logger.info(f"     * 下游代码: {len(segment.get('downstream_code', {}))} 个")
            
            # 显示依赖详情
            upstream = segment.get('upstream', [])
            if upstream:
                logger.info(f"     * 上游依赖列表: {upstream}")
            
            downstream = segment.get('downstream', [])
            if downstream:
                logger.info(f"     * 下游依赖列表: {downstream}")
        
    except Exception as e:
        logger.error(f"❌ diff分析测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())

def generate_diagnosis_report(import_results, func_index):
    """生成诊断报告"""
    logger = logging.getLogger(__name__)
    logger.info("\n" + "=" * 60)
    logger.info("7. 诊断报告")
    logger.info("=" * 60)
    
    # 统计导入成功率
    total_imports = len(import_results)
    successful_imports = sum(1 for success in import_results.values() if success)
    import_success_rate = (successful_imports / total_imports) * 100 if total_imports > 0 else 0
    
    logger.info(f"📊 模块导入情况:")
    logger.info(f"   - 总模块数: {total_imports}")
    logger.info(f"   - 成功导入: {successful_imports}")
    logger.info(f"   - 成功率: {import_success_rate:.1f}%")
    
    # 分析问题
    logger.info(f"\n🔍 问题分析:")
    
    if not import_results.get('chunk_utils', False):
        logger.warning("   ⚠️  工具函数模块导入失败 - 这可能导致chunk_utils_available=False")
    
    if not import_results.get('dependency_analyzer', False):
        logger.warning("   ⚠️  依赖分析器导入失败 - 这会导致依赖分析功能不可用")
    
    if not import_results.get('code_parsers', False):
        logger.warning("   ⚠️  代码解析器导入失败 - 这会影响代码解析和依赖识别")
    
    if not func_index:
        logger.warning("   ⚠️  函数索引为空 - 这是依赖为空的主要原因")
        logger.info("      可能原因:")
        logger.info("      1. 项目信息无效 (project/repo为default或unknown)")
        logger.info("      2. 文件列表为空")
        logger.info("      3. 代码解析失败")
        logger.info("      4. 依赖分析器初始化失败")
    
    # 提供解决建议
    logger.info(f"\n💡 解决建议:")
    
    if import_success_rate < 100:
        logger.info("   1. 检查模块路径和依赖安装")
        logger.info("   2. 确保所有必要的Python包已安装")
        logger.info("   3. 检查项目结构是否完整")
    
    if not func_index:
        logger.info("   4. 检查项目和仓库名称是否有效")
        logger.info("   5. 确保代码文件存在且可读")
        logger.info("   6. 检查代码解析器是否正常工作")
        logger.info("   7. 验证依赖分析器初始化过程")
    
    logger.info("   8. 查看详细日志以获取更多信息")
    logger.info("   9. 考虑使用基础模式作为备选方案")

def main():
    """主函数"""
    logger = setup_logging()
    logger.info("开始依赖解析功能诊断...")
    
    try:
        # 1. 检查模块导入
        import_results = check_imports()
        
        # 2. 检查ChunkService初始化
        chunk_service, git_service = check_chunk_service_initialization()
        
        # 3. 测试函数索引构建
        func_index = test_function_index_building(chunk_service)
        
        # 4. 测试依赖分析器
        test_dependency_analyzer(func_index)
        
        # 5. 测试代码解析
        test_code_parsing()
        
        # 6. 测试diff分析
        test_diff_analysis(chunk_service)
        
        # 7. 生成诊断报告
        generate_diagnosis_report(import_results, func_index)
        
        logger.info("\n" + "=" * 60)
        logger.info("✅ 诊断完成!")
        logger.info("=" * 60)
        
    except Exception as e:
        logger.error(f"❌ 诊断过程中出现错误: {e}")
        import traceback
        logger.error(traceback.format_exc())

if __name__ == "__main__":
    main()