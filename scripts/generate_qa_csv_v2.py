#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通知内容QA问答对生成器 V2
将企业通知内容转换为QA问答对的CSV文件，便于用户查询和召回
"""

import csv
import os
from datetime import datetime
from typing import List, Dict, Tuple


class NotificationQAGenerator:
    """通知内容QA问答对生成器"""
    
    def __init__(self):
        self.qa_pairs: List[Dict[str, str]] = []
    
    def add_qa_pair(self, question: str, answer: str, date: str = "", category: str = ""):
        """添加QA问答对"""
        self.qa_pairs.append({
            "日期": date,
            "分类": category,
            "问题": question,
            "答案": answer
        })
    
    def generate_qa_from_notifications(self):
        """从通知内容生成QA问答对"""
        
        # 6月5日深圳国补下线通知
        shenzhen_notice = """紧急通知！！
深圳市商务局通知，国补以旧换新的八大类+N，线上平台统一6月6日0点下线。线上平台3C部分可以保留，但需要保障仅能售卖深圳本地。

请各方向今日内完成：
1、@孙凯 @张倩 麻烦凯哥、倩倩按照 国补（品类）下线SOP 组织深圳笔记本国补活动配置及标签规则删除。cc@孙云飞
2、@陈腾 @韩天坛 @李剑楠 请产研评估是否关闭深圳家电品类功能，也关注资格解绑功能的添加
3、@冯义 请调整深圳会场及营销资源
4、@夏雨 请客服侧知悉，沿用系统升级的话术应对用户进线
5、@徐坤鹏 @温东炬 @李建霞 @尹月 @郭辽林 请各方向知悉"""
        
        # 生成深圳相关的QA对
        self.add_qa_pair(
            "6月5日深圳国补政策有什么重要变化？",
            shenzhen_notice,
            "6月5日",
            "政策变更"
        )
        
        self.add_qa_pair(
            "深圳国补以旧换新什么时候下线？",
            "深圳市商务局通知，国补以旧换新的八大类+N，线上平台统一6月6日0点下线。线上平台3C部分可以保留，但需要保障仅能售卖深圳本地。",
            "6月5日",
            "时间节点"
        )
        
        self.add_qa_pair(
            "6月5日深圳国补下线通知中涉及哪些负责人和具体任务？",
            "涉及负责人及任务：1、孙凯、张倩负责深圳笔记本国补活动配置及标签规则删除；2、陈腾、韩天坛、李剑楠负责评估关闭深圳家电品类功能；3、冯义负责调整深圳会场及营销资源；4、夏雨负责客服话术；5、徐坤鹏等多人需要知悉。",
            "6月5日",
            "任务分工"
        )
        
        # 6月6日江苏新国补通知
        jiangsu_notice = """江苏新国补通知
结合昨天跟商务厅、银联沟通的结论，同步一下新国补方案的最后版本

1. 江苏银联为了区别自有资金和国有资金的对账数据，仍需要使用新国补方案支持美团（即需要配置新的优惠券ID、但可以走原有国补领券核销接口、sn 调用等链路 均不变）；
2. 江苏银联今天提工单配置给到总银联，后面我找总银联的人加急处理；按照最快下周一晚上银联能上线的时间去推进；
3. 江苏银联按照实时核销金额100w每天的限额控制.

按照最新的结论，辛苦@李剑楠 对下我们涉及的改动点；"""
        
        self.add_qa_pair(
            "6月6日江苏新国补方案的具体内容是什么？",
            jiangsu_notice,
            "6月6日",
            "方案更新"
        )
        
        self.add_qa_pair(
            "江苏银联新国补方案的技术实现方式是什么？",
            "江苏银联需要配置新的优惠券ID来区别自有资金和国有资金的对账数据，但可以走原有国补领券核销接口、sn调用等链路均不变。",
            "6月6日",
            "技术实现"
        )
        
        self.add_qa_pair(
            "江苏银联新国补的上线时间和限额是多少？",
            "按照最快下周一晚上银联能上线的时间去推进，江苏银联按照实时核销金额100w每天的限额控制。",
            "6月6日",
            "时间限额"
        )
        
        # 6月6日吉林通知
        jilin_notice = """吉林：6月7日0时起，家电部分全面暂停（线上+线下)，数码3C部分实行每日限量5000张券，其中线上3c全省一天最多1500张（所有线上平台共享额度），每日03:00-24:00间领券+核销。"""
        
        self.add_qa_pair(
            "6月6日吉林省国补政策调整的具体内容是什么？",
            jilin_notice,
            "6月6日",
            "政策调整"
        )
        
        self.add_qa_pair(
            "吉林省6月7日起家电和数码3C的政策有什么不同？",
            "家电部分全面暂停（线上+线下），数码3C部分实行每日限量5000张券，其中线上3C全省一天最多1500张（所有线上平台共享额度）。",
            "6月6日",
            "品类差异"
        )
        
        self.add_qa_pair(
            "吉林省数码3C券的领取和核销时间是什么？",
            "每日03:00-24:00间领券+核销。",
            "6月6日",
            "时间规则"
        )
        
        # 6月6日江西开城通知
        jiangxi_notice = """【江西开城技术侧确认】
【方案】
银联模式，复用银总接口。
B端改造发版支持，C端、履约、支付配置支持。
具体：1. 江西国补系统对接
@张行 @陈昊 @吴耀祖 @高玉鑫 @文子龙 @钟栋成 也请各位领域主R评估下，如涉及其他改造同步我下。

【排期】
暂定6月19日服务上线，6月20日线上全流程测试店验证，6月23日业务白名单验收。

【动作】
银联沟通。获取云闪付领券链接、正式活动id+优惠码。6月19日之前，越早越好。@洪小霞
B端改造。研发迭代、测试验证、服务上线。6月19日上线。@高玉鑫 @徐贤辉
领域配置。辛苦领域主r按期完成st&线上配置，交叉review。6月19日，届时再通知大家。@张行 @吴耀祖 @高玉鑫 @庄新安 @桂子艺 @钟栋成 @陈华 @张艺烁 @吴晓虹 @文子龙
线上验证。组织线上测试店验证，6月20日。@赵雪霞
业务验收。

辛苦各位圈到的同学确认下是否有问题，没问题就按此路径和排期支持。"""
        
        self.add_qa_pair(
            "6月6日江西开城项目的技术方案是什么？",
            "银联模式，复用银总接口。B端改造发版支持，C端、履约、支付配置支持。具体包括江西国补系统对接。",
            "6月6日",
            "技术方案"
        )
        
        self.add_qa_pair(
            "江西开城项目的完整时间排期是怎样的？",
            "6月19日服务上线，6月20日线上全流程测试店验证，6月23日业务白名单验收。",
            "6月6日",
            "项目排期"
        )
        
        self.add_qa_pair(
            "江西开城项目中各个负责人的具体任务分工是什么？",
            "洪小霞负责银联沟通获取云闪付链接；高玉鑫、徐贤辉负责B端改造；张行等多位领域主R负责配置；赵雪霞负责线上验证。涉及的评估人员包括张行、陈昊、吴耀祖、高玉鑫、文子龙、钟栋成等。",
            "6月6日",
            "任务分工"
        )
        
        # 综合性问题
        self.add_qa_pair(
            "6月5日-6月6日期间有哪些重要的国补政策变化？",
            "主要包括：1、深圳国补6月6日0点下线；2、江苏新国补方案确定，需配置新优惠券ID；3、吉林6月7日起家电全面暂停，3C限量；4、江西开城项目确定技术方案和排期。",
            "6月5日-6月6日",
            "政策汇总"
        )
        
        self.add_qa_pair(
            "这几天通知中涉及的主要技术改造工作有哪些？",
            "主要技术工作包括：1、深圳国补活动配置删除；2、江苏新优惠券ID配置；3、江西B端改造发版、C端履约支付配置；4、各地家电品类功能评估关闭。",
            "6月5日-6月6日",
            "技术改造"
        )
        
        self.add_qa_pair(
            "6月中下旬有哪些重要的项目节点？",
            "重要节点：6月19日江西服务上线和江苏银联上线；6月20日江西线上测试验证；6月23日江西业务白名单验收。",
            "6月5日-6月6日",
            "项目节点"
        )
    
    def save_to_csv(self, filename: str = "notification_qa_pairs.csv"):
        """保存完整版QA问答对到CSV文件"""
        if not self.qa_pairs:
            print("没有QA问答对数据，请先生成数据")
            return
        
        # 确保scripts目录存在
        os.makedirs("scripts", exist_ok=True)
        filepath = os.path.join("scripts", filename)
        
        with open(filepath, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = ["日期", "分类", "问题", "答案"]
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            
            # 写入表头
            writer.writeheader()
            
            # 写入数据
            for qa_pair in self.qa_pairs:
                writer.writerow(qa_pair)
        
        print(f"完整版QA问答对已保存到: {filepath}")
        print(f"共生成 {len(self.qa_pairs)} 条QA问答对")
    
    def save_to_csv_compact(self, filename: str = "notification_qa_pairs_compact.csv"):
        """保存简洁版QA问答对到CSV文件（只包含问题和答案）"""
        if not self.qa_pairs:
            print("没有QA问答对数据，请先生成数据")
            return
        
        # 确保scripts目录存在
        os.makedirs("scripts", exist_ok=True)
        filepath = os.path.join("scripts", filename)
        
        with open(filepath, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = ["问题", "答案"]
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            
            # 写入表头
            writer.writeheader()
            
            # 写入数据（只保留问题和答案）
            for qa_pair in self.qa_pairs:
                writer.writerow({
                    "问题": qa_pair["问题"],
                    "答案": qa_pair["答案"]
                })
        
        print(f"简洁版QA问答对已保存到: {filepath}")
        print(f"共生成 {len(self.qa_pairs)} 条简洁版QA问答对")
    
    def print_summary(self):
        """打印生成的QA对摘要"""
        if not self.qa_pairs:
            print("没有QA问答对数据")
            return
        
        print("\n=== QA问答对生成摘要 ===")
        print(f"总计生成: {len(self.qa_pairs)} 条QA问答对")
        
        # 按分类统计
        categories = {}
        dates = {}
        
        for qa in self.qa_pairs:
            category = qa["分类"]
            date = qa["日期"]
            
            categories[category] = categories.get(category, 0) + 1
            dates[date] = dates.get(date, 0) + 1
        
        print("\n按分类统计:")
        for category, count in categories.items():
            print(f"  {category}: {count} 条")
        
        print("\n按日期统计:")
        for date, count in dates.items():
            print(f"  {date}: {count} 条")
        
        print("\n前3个问题示例:")
        for i, qa in enumerate(self.qa_pairs[:3]):
            print(f"{i+1}. [{qa['日期']}] {qa['问题']}")


def main():
    """主函数"""
    print("🚀 开始生成通知内容QA问答对...")
    
    # 创建生成器实例
    generator = NotificationQAGenerator()
    
    # 生成QA问答对
    generator.generate_qa_from_notifications()
    
    # 打印摘要
    generator.print_summary()
    
    # 保存到CSV文件（完整版）
    generator.save_to_csv()
    
    # 保存简洁版CSV文件
    generator.save_to_csv_compact()
    
    print("\n✅ QA问答对生成完成！")
    print("📁 生成了两个版本的CSV文件：")
    print("   - notification_qa_pairs.csv (完整版，包含日期和分类)")
    print("   - notification_qa_pairs_compact.csv (简洁版，仅包含问题和答案)")


if __name__ == "__main__":
    main()