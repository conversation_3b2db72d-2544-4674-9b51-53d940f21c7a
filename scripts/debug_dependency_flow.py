#!/usr/bin/env python3
"""
详细调试依赖解析流程的脚本
"""

import os
import sys
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def setup_logging():
    """设置详细日志"""
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def debug_chunk_service_flow():
    """调试ChunkService的完整流程"""
    logger = logging.getLogger(__name__)
    logger.info("=" * 80)
    logger.info("开始调试ChunkService依赖解析流程")
    logger.info("=" * 80)
    
    try:
        from services.chunk_service import ChunkService
        
        # 模拟GitService
        class DebugGitService:
            def __init__(self):
                self.base_dir = "/tmp/test_project"
            
            def list_repo_files(self, project, repo, branch=None, suffixes=None):
                files = ["src/main.py", "src/utils.py"]
                logger.info(f"GitService.list_repo_files返回: {files}")
                return files
            
            def read_file(self, project, repo, file_path):
                if file_path == "src/main.py":
                    code = '''
def main():
    """主函数"""
    result = process_data("test")
    user = get_user(1)
    return result, user

def startup():
    """启动函数"""
    return main()
'''
                elif file_path == "src/utils.py":
                    code = '''
def process_data(data):
    """处理数据"""
    return data.upper()

def get_user(user_id):
    """获取用户"""
    return {"id": user_id}
'''
                else:
                    code = ""
                
                logger.info(f"GitService.read_file({file_path}) 返回 {len(code)} 字符")
                return code
            
            def get_repo_path(self, project, repo):
                return f"/tmp/{project}/{repo}"
        
        # 创建ChunkService实例
        git_service = DebugGitService()
        chunk_service = ChunkService(
            git_service=git_service,
            project="debug_project",
            repo="debug_repo",
            branch="main"
        )
        
        logger.info("✅ ChunkService实例创建成功")
        logger.info(f"   - chunk_utils_available: {chunk_service.chunk_utils_available}")
        
        # 步骤1: 测试函数索引构建
        logger.info("\n" + "=" * 60)
        logger.info("步骤1: 测试函数索引构建")
        logger.info("=" * 60)
        
        func_index = chunk_service.build_func_index(use_cache=False)
        logger.info(f"函数索引构建结果: {len(func_index)} 个函数")
        
        if func_index:
            logger.info("函数索引内容:")
            for i, (key, value) in enumerate(list(func_index.items())[:5]):
                logger.info(f"  {i+1}. {key}: {type(value)}")
                if isinstance(value, dict):
                    logger.info(f"     - 名称: {value.get('name', 'N/A')}")
                    logger.info(f"     - 文件: {value.get('file', 'N/A')}")
                    logger.info(f"     - 上游: {value.get('upstream', [])}")
                    logger.info(f"     - 下游: {value.get('downstream', [])}")
        
        # 步骤2: 测试单文件分块
        logger.info("\n" + "=" * 60)
        logger.info("步骤2: 测试单文件分块")
        logger.info("=" * 60)
        
        chunks = chunk_service.chunk_code_file("src/main.py")
        logger.info(f"单文件分块结果: {len(chunks)} 个块")
        
        for i, chunk in enumerate(chunks):
            logger.info(f"  块 {i+1}:")
            logger.info(f"    - 名称: {chunk.get('name', 'N/A')}")
            logger.info(f"    - 类型: {chunk.get('type', 'N/A')}")
            logger.info(f"    - 上游: {chunk.get('upstream', [])}")
            logger.info(f"    - 下游: {chunk.get('downstream', [])}")
        
        # 步骤3: 测试依赖提取
        logger.info("\n" + "=" * 60)
        logger.info("步骤3: 测试依赖提取")
        logger.info("=" * 60)
        
        test_code = '''
def main():
    result = process_data("test")
    user = get_user(1)
    return result, user
'''
        
        upstream, downstream = chunk_service._extract_python_dependencies(test_code, "test.py")
        logger.info(f"依赖提取结果:")
        logger.info(f"  - 上游: {upstream}")
        logger.info(f"  - 下游: {downstream}")
        
        # 步骤4: 测试diff分析
        logger.info("\n" + "=" * 60)
        logger.info("步骤4: 测试diff分析")
        logger.info("=" * 60)
        
        test_diff = '''
diff --git a/src/main.py b/src/main.py
index 1234567..abcdefg 100644
--- a/src/main.py
+++ b/src/main.py
@@ -1,5 +1,7 @@
 def main():
     """主函数"""
-    result = process_data("test")
+    result = process_data("test_data")
+    print(f"结果: {result}")
     user = get_user(1)
+    log_action("main_called")
     return result, user
'''
        
        logger.info("开始diff分析...")
        segments = chunk_service.enrich_diff_segments_with_context(test_diff, max_dep_depth=2)
        
        logger.info(f"diff分析结果: {len(segments)} 个片段")
        
        for i, segment in enumerate(segments):
            logger.info(f"  片段 {i+1}:")
            logger.info(f"    - 文件: {segment.get('file', 'N/A')}")
            logger.info(f"    - 类型: {segment.get('type', 'N/A')}")
            logger.info(f"    - 内容长度: {len(segment.get('content', ''))}")
            logger.info(f"    - 上游依赖: {segment.get('upstream', [])}")
            logger.info(f"    - 下游依赖: {segment.get('downstream', [])}")
            logger.info(f"    - 上游代码: {len(segment.get('upstream_code', {}))}")
            logger.info(f"    - 下游代码: {len(segment.get('downstream_code', {}))}")
            
            # 显示内容片段
            content = segment.get('content', '')
            if content:
                logger.info(f"    - 内容预览: {content[:100]}...")
        
        # 步骤5: 测试_create_enhanced_segment方法
        logger.info("\n" + "=" * 60)
        logger.info("步骤5: 测试_create_enhanced_segment方法")
        logger.info("=" * 60)
        
        test_content_lines = [
            'def main():',
            '    """主函数"""',
            '    result = process_data("test_data")',
            '    print(f"结果: {result}")',
            '    user = get_user(1)',
            '    log_action("main_called")',
            '    return result, user'
        ]
        
        enhanced_segment = chunk_service._create_enhanced_segment("src/main.py", test_content_lines)
        logger.info(f"增强片段结果:")
        logger.info(f"  - 文件: {enhanced_segment.get('file', 'N/A')}")
        logger.info(f"  - 类型: {enhanced_segment.get('type', 'N/A')}")
        logger.info(f"  - 上游依赖: {enhanced_segment.get('upstream', [])}")
        logger.info(f"  - 下游依赖: {enhanced_segment.get('downstream', [])}")
        logger.info(f"  - 上游代码: {len(enhanced_segment.get('upstream_code', {}))}")
        logger.info(f"  - 下游代码: {len(enhanced_segment.get('downstream_code', {}))}")
        
        # 总结
        logger.info("\n" + "=" * 80)
        logger.info("调试总结")
        logger.info("=" * 80)
        
        has_func_index = len(func_index) > 0
        has_chunks = len(chunks) > 0
        has_dependencies = len(upstream) > 0
        has_segment_deps = any(len(s.get('upstream', [])) > 0 for s in segments)
        has_enhanced_deps = len(enhanced_segment.get('upstream', [])) > 0
        
        logger.info(f"✅ 函数索引构建: {'成功' if has_func_index else '失败'} ({len(func_index)} 个)")
        logger.info(f"✅ 单文件分块: {'成功' if has_chunks else '失败'} ({len(chunks)} 个)")
        logger.info(f"✅ 依赖提取: {'成功' if has_dependencies else '失败'} ({len(upstream)} 个)")
        logger.info(f"⚠️  diff片段依赖: {'成功' if has_segment_deps else '失败'}")
        logger.info(f"✅ 增强片段依赖: {'成功' if has_enhanced_deps else '失败'} ({len(enhanced_segment.get('upstream', []))} 个)")
        
        if has_enhanced_deps and not has_segment_deps:
            logger.warning("🔍 问题定位: _create_enhanced_segment工作正常，但diff分析流程中没有调用或结果被覆盖")
        
        return {
            'func_index': has_func_index,
            'chunks': has_chunks,
            'dependencies': has_dependencies,
            'segment_deps': has_segment_deps,
            'enhanced_deps': has_enhanced_deps
        }
        
    except Exception as e:
        logger.error(f"❌ 调试过程中出现错误: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return {}

def main():
    """主函数"""
    logger = setup_logging()
    logger.info("开始详细调试依赖解析流程...")
    
    results = debug_chunk_service_flow()
    
    if results:
        success_count = sum(1 for v in results.values() if v)
        total_count = len(results)
        
        logger.info(f"\n调试结果: {success_count}/{total_count} 项功能正常")
        
        if success_count == total_count:
            logger.info("🎉 所有功能正常，依赖解析应该工作")
        elif success_count > total_count // 2:
            logger.info("⚠️  大部分功能正常，需要定位具体问题")
        else:
            logger.error("❌ 多个功能异常，需要全面检查")
    
    return results

if __name__ == "__main__":
    main()