from typing import Dict, List, Optional, Any


class CreateDocParams:
    """
    创建学城文档的参数
    """

    def __init__(
            self,
            title: str,
            content: str,
            operator_emp_id: Optional[str] = None,
            parent_id: Optional[str] = None,
            template_id: Optional[str] = None,
            space_id: Optional[str] = None,
            copy_from_content_id=None,
            **kwargs
    ):
        self.title = title
        self.content = content
        self.operatorEmpId = int(operator_emp_id) if operator_emp_id else None
        self.templateId = template_id
        self.parentId = parent_id
        self.spaceId = space_id
        self.copyFromContentId = copy_from_content_id
        self.__dict__.update(kwargs)

    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典
        """
        return {k: v for k, v in self.__dict__.items() if v is not None}
