from flask import jsonify, request
from typing import Dict, Any, Optional


def pr_info_handler(ctx) -> Dict[str, Any]:
    """
    处理PR信息请求
    
    Args:
        ctx: 请求上下文，可以是Flask的request或Koa的Context
        
    Returns:
        Dict[str, Any]: PR信息和差异
    """
    try:
        # 从请求中获取参数
        if hasattr(ctx, 'args'):  # Flask风格
            project = ctx.args.get('project')
            repo = ctx.args.get('repo')
            pr_id = ctx.args.get('id')
        elif hasattr(ctx, 'params'):  # Koa风格
            project = ctx.params.get('project')
            repo = ctx.params.get('repo')
            pr_id = ctx.params.get('id')
        else:
            # 如果都不是，尝试从ctx直接获取
            project = getattr(ctx, 'project', None)
            repo = getattr(ctx, 'repo', None)
            pr_id = getattr(ctx, 'id', None)

        # 模拟PR信息
        pr_info = {
            'id': pr_id,
            'project': project,
            'repo': repo,
            'fromRef': {
                'displayId': 'feature/test'
            },
            'toRef': {
                'displayId': 'master'
            }
        }

        # 模拟差异信息
        diff = ("diff --git a/file.txt b/file.txt\nindex 1234567..abcdefg 100644\n--- a/file.txt\n+++ b/file.txt\n@@ "
                "-1,3 +1,4 @@\n line1\n+new line\n line2\n line3")

        return {
            'prInfo': pr_info,
            'diff': diff
        }
    except Exception as e:
        return {
            'success': False,
            'error': str(e)
        }
