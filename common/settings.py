#
#  Copyright 2024 The InfiniFlow Authors. All Rights Reserved.
#
#  Licensed under the Apache License, Version 2.0 (the "License");
#  you may not use this file except in compliance with the License.
#  You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
#
import os
from enum import IntEnum, Enum

from utils import get_base_config
from consts.service_consts import SERVICE_NAME

LIGHTEN = int(os.environ.get('LIGHTEN', "0"))

LLM = None
LLM_FACTORY = None
LLM_BASE_URL = None
CHAT_MDL = ""
EMBEDDING_MDL = ""
RERANK_MDL = ""
ASR_MDL = ""
IMAGE2TEXT_MDL = ""
API_KEY = None
PARSERS = None
HOST_IP = None
HOST_PORT = None
SECRET_KEY = None

DATABASE_TYPE = os.getenv("DB_TYPE", 'mysql')
DATABASE = {
    'name': os.getenv('DB_NAME', 'test'),
    'user': os.getenv('DB_USER', 'root'),
    'password': os.getenv('DB_PASSWORD', ''),
    'host': os.getenv('DB_HOST', 'localhost'),
    'port': int(os.getenv('DB_PORT', 3306)),
}

# authentication
AUTHENTICATION_CONF = None

# client
CLIENT_AUTHENTICATION = None
HTTP_APP_KEY = None
GITHUB_OAUTH = None
FEISHU_OAUTH = None
SSO_CONFIG = None

DOC_ENGINE = None
docStoreConn = None

retrievaler = None
kg_retrievaler = None

# 文档自动更新相关配置
AUTO_UPDATE_API_KEY = os.environ.get('AUTO_UPDATE_API_KEY', 'change-me-in-production')


def init_settings():
    global DATABASE_TYPE, DATABASE
    DATABASE_TYPE = os.getenv("DB_TYPE", 'mysql')

    global API_KEY, PARSERS, HOST_IP, HOST_PORT, SECRET_KEY

    HOST_IP = get_base_config(SERVICE_NAME, {}).get("host", "127.0.0.1")
    HOST_PORT = get_base_config(SERVICE_NAME, {}).get("http_port")

    SECRET_KEY = os.getenv('SECRET_KEY', 'your-secret-key')

    global AUTHENTICATION_CONF, CLIENT_AUTHENTICATION, HTTP_APP_KEY, GITHUB_OAUTH, FEISHU_OAUTH, SSO_CONFIG
    # authentication
    AUTHENTICATION_CONF = get_base_config("authentication", {})

    # client
    CLIENT_AUTHENTICATION = AUTHENTICATION_CONF.get(
        "client", {}).get(
        "switch", False)
    HTTP_APP_KEY = AUTHENTICATION_CONF.get("client", {}).get("http_app_key")
    GITHUB_OAUTH = get_base_config("oauth", {}).get("github")
    FEISHU_OAUTH = get_base_config("oauth", {}).get("feishu")

    # SSO配置
    sso_config = get_base_config("oauth", {}).get("sso", {})
    if sso_config:
        # 获取当前环境
        current_env = os.environ.get("SERVER_ENV", "test")

        # 基础通用配置
        sso_base_config = {
            'login_uri': sso_config.get('login_uri', '/login'),
            'auth_uri': sso_config.get('auth_uri', '/oauth2.0/access-token'),
            'user_info_uri': sso_config.get('user_info_uri', '/api/session/userinfo'),
            'logout_uri': sso_config.get('logout_uri', '/oauth2.0/logout')
        }

        # 获取环境特定配置
        env_configs = sso_config.get('env', {})
        env_specific_config = env_configs.get(current_env, env_configs.get('test', {}))

        # 合并配置
        SSO_CONFIG = {**sso_base_config, **env_specific_config}

        # 如果没有获取到环境特定配置，使用旧配置结构兼容处理
        if not env_specific_config:
            SSO_CONFIG = {
                'client_id': sso_config.get('client_id', 'd7885a354f'),
                'secret': sso_config.get('secret', 'a5b0392c3cdf40f28e3c9a1742fae749'),
                'sso_host': sso_config.get('sso_host', 'http://ssosv.it.test.sankuai.com/sson'),
                'api_host': sso_config.get('api_host', 'http://ssosv.it.test.sankuai.com/open'),
                'login_uri': sso_config.get('login_uri', '/login'),
                'auth_uri': sso_config.get('auth_uri', '/oauth2.0/access-token'),
                'user_info_uri': sso_config.get('user_info_uri', '/api/session/userinfo'),
                'logout_uri': sso_config.get('logout_uri', '/oauth2.0/logout')
            }
    else:
        # 默认测试配置
        SSO_CONFIG = {
            'client_id': 'd7885a354f',
            'secret': 'a5b0392c3cdf40f28e3c9a1742fae749',
            'sso_host': 'http://ssosv.it.test.sankuai.com/sson',
            'api_host': 'http://ssosv.it.test.sankuai.com/open',
            'login_uri': '/login',
            'auth_uri': '/oauth2.0/access-token',
            'user_info_uri': '/api/session/userinfo',
            'logout_uri': '/oauth2.0/logout'
        }

    global DOC_ENGINE, docStoreConn, retrievaler, kg_retrievaler
    DOC_ENGINE = os.environ.get('DOC_ENGINE', "elasticsearch")
    lower_case_doc_engine = DOC_ENGINE.lower()
    if lower_case_doc_engine == "elasticsearch":
        # 检查是否使用公司ES8集群
        pass
        # docStoreConn = mt_es_conn.MTESConnection()
    else:
        raise Exception(f"Not supported doc engine: {DOC_ENGINE}")


class CustomEnum(Enum):
    @classmethod
    def valid(cls, value):
        try:
            cls(value)
            return True
        except BaseException:
            return False

    @classmethod
    def values(cls):
        return [member.value for member in cls.__members__.values()]

    @classmethod
    def names(cls):
        return [member.name for member in cls.__members__.values()]


class RetCode(IntEnum, CustomEnum):
    SUCCESS = 0
    NOT_EFFECTIVE = 10
    EXCEPTION_ERROR = 100
    ARGUMENT_ERROR = 101
    DATA_ERROR = 102
    OPERATING_ERROR = 103
    CONNECTION_ERROR = 105
    RUNNING = 106
    PERMISSION_ERROR = 108
    AUTHENTICATION_ERROR = 109
    UNAUTHORIZED = 401
    SERVER_ERROR = 500
    FORBIDDEN = 403
    NOT_FOUND = 404
