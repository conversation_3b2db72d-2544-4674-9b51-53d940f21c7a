"""
代码审查编排器

负责编排整个代码审查工作流程
"""

import asyncio
from typing import Any, Dict, Optional, List

from core.agents.agent_factory import get_agent_factory
from .base_orchestrator import BaseOrchestrator, WorkflowResult


class CROrchestrator(BaseOrchestrator):
    """代码审查编排器"""

    def __init__(self,
                 llm_service,
                 devmind_service,
                 chunk_service,
                 git_service,
                 config: Optional[Dict[str, Any]] = None):
        """
        初始化CR编排器
        Args:
            llm_service: LLM服务
            devmind_service: DevMind服务
            chunk_service: 代码分块服务
            git_service: Git服务
            config: 编排器配置
        """
        super().__init__("cr_orchestrator", config)
        self.llm_service = llm_service
        self.devmind_service = devmind_service
        self.chunk_service = chunk_service
        self.git_service = git_service
        # 默认配置
        self.default_config = {
            'cr_mode': 'standard',  # fast, standard, deep
            'enable_parallel_processing': True,
            'max_concurrent_tasks': 5,
            'enable_performance_monitoring': True,
            'timeout_seconds': 600
        }
        # 合并配置
        self.config = {**self.default_config, **self.config}
        # 初始化Agent
        self._init_agents()
    def _init_agents(self):
        """初始化所需的Agent"""
        factory = get_agent_factory()
        # 创建Agent集合（允许chunk_service为None，稍后动态更新）
        self.agents = factory.create_cr_agent_set(
            self.llm_service,
            self.devmind_service,
            self.chunk_service,  # 可能为None
            self.git_service,
            {
                'diff_analysis': {
                    'enable_context_enrichment': True,
                    'include_dependencies': True
                },
                'llm_review': {
                    'cr_mode': self.config.get('cr_mode', 'standard'),
                    'enable_performance_monitoring': self.config.get('enable_performance_monitoring', True)
                },
                'result_aggregation': {
                    'preserve_details': True,
                    'merge_strategy': 'intelligent'
                }
            }
        )
        self.logger.info("CR编排器Agent初始化完成")

    def update_chunk_service(self, chunk_service):
        """
        更新编排器和相关Agent的chunk_service

        Args:
            chunk_service: 新的代码分块服务实例
        """
        self.chunk_service = chunk_service

        # 更新DiffAnalysisAgent的chunk_service
        if 'diff_analysis' in self.agents:
            diff_agent = self.agents['diff_analysis']
            if hasattr(diff_agent, 'update_chunk_service'):
                diff_agent.update_chunk_service(chunk_service)
                self.logger.info("已更新DiffAnalysisAgent的chunk_service")
            else:
                # 直接设置属性
                diff_agent.chunk_service = chunk_service
                self.logger.info("已直接更新DiffAnalysisAgent的chunk_service属性")

        self.logger.info("编排器chunk_service更新完成")
    def validate_input(self, input_data: Dict[str, Any]) -> bool:
        """
        验证输入数据
        Args:
            input_data: 输入数据
        Returns:
            bool: 验证是否通过
        """
        required_fields = ['code_diff']
        for field in required_fields:
            if field not in input_data:
                self.logger.error(f"缺少必需字段: {field}")
                return False
        if not input_data['code_diff'].strip():
            self.logger.error("代码差异内容不能为空")
            return False
        return True
    async def execute_workflow(self, input_data: Dict[str, Any]) -> WorkflowResult:
        """
        执行代码审查工作流
        Args:
            input_data: 输入数据，包含代码差异和选项
        Returns:
            WorkflowResult: 工作流执行结果
        """
        try:
            code_diff = input_data['code_diff']
            options = input_data.get('options', {})
            self.logger.info("开始执行代码审查工作流")
            # 第一步：代码差异分析
            self.logger.info("步骤1: 代码差异分析")
            diff_result = await self.agents['diff_analysis'].run({
                'code_diff': code_diff,
                'options': options
            })
            if not diff_result.success:
                raise RuntimeError(f"代码差异分析失败: {diff_result.error}")
            segments = diff_result.data['segments']
            self.logger.info(f"代码差异分析完成，共生成 {len(segments)} 个片段")
            # 第二步：并行LLM审查
            self.logger.info("步骤2: 并行LLM代码审查")
            review_results = await self._parallel_llm_review(segments, options)
            self.logger.info(f"LLM审查完成，共审查 {len(review_results)} 个片段")
            print("review_results: ", review_results)
            # 第三步：结果聚合和增强
            self.logger.info("步骤3: 结果聚合和增强")
            aggregation_result = await self.agents['result_aggregation'].run({
                'results': review_results,
                'options': options,
                'segments': segments,  # 传递代码片段信息
                'metadata': {
                    'cr_mode': self.config.get('cr_mode'),
                    'parallel_processing': self.config.get('enable_parallel_processing'),
                    'segments_count': len(segments),
                    'review_results_count': len(review_results)
                }
            })

            if not aggregation_result.success:
                raise RuntimeError(f"结果聚合失败: {aggregation_result.error}")

            # 获取增强后的最终结果
            final_result = aggregation_result.data

            # 确保结果包含UI展示所需的完整信息
            if not self._validate_final_result(final_result):
                self.logger.warning("最终结果格式不完整，进行补充")
                final_result = self._supplement_result_format(final_result, review_results, segments)
            self.logger.info("代码审查工作流执行完成")
            return WorkflowResult(
                success=True,
                data=final_result,
                metadata={
                    'segments_count': len(segments),
                    'review_results_count': len(review_results),
                    'cr_mode': self.config.get('cr_mode'),
                    'parallel_processing': self.config.get('enable_parallel_processing'),
                    'segments': segments  # 传递segments信息用于日志输出
                }
            )
        except Exception as e:
            error_msg = f"代码审查工作流执行失败: {str(e)}"
            self.logger.error(error_msg)
            return WorkflowResult(
                success=False,
                error=error_msg
            )

    async def _parallel_llm_review(self,
                                 segments: List[Dict[str, Any]],
                                 options: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        并行执行LLM代码审查
        Args:
            segments: 代码片段列表
            options: 选项参数
        Returns:
            List[Dict]: 审查结果列表
        """
        if not self.config.get('enable_parallel_processing', True):
            # 串行处理
            results = []
            for segment in segments:
                result = await self.agents['llm_review'].run(segment)
                if result.success:
                    results.append(result.data)
                else:
                    self.logger.warning(f"片段审查失败: {result.error}")
            return results
        # 并行处理
        max_concurrent = self.config.get('max_concurrent_tasks', 5)
        semaphore = asyncio.Semaphore(max_concurrent)
        async def review_segment(segment):
            async with semaphore:
                result = await self.agents['llm_review'].run(segment)
                return result.data if result.success else None
        # 创建并发任务
        tasks = [review_segment(segment) for segment in segments]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        # 过滤有效结果
        valid_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                self.logger.warning(f"片段 {i} 审查异常: {str(result)}")
            elif result is not None:
                valid_results.append(result)
        self.logger.info(f"并行审查完成，有效结果: {len(valid_results)}/{len(segments)}")
        return valid_results
    def set_cr_mode(self, mode: str):
        """
        设置CR模式
        Args:
            mode: CR模式 (fast, standard, deep)
        """
        valid_modes = ["fast", "standard", "deep"]
        if mode not in valid_modes:
            raise ValueError(f"无效的CR模式: {mode}，支持的模式: {valid_modes}")
        self.config['cr_mode'] = mode
        # 更新LLM审查Agent的配置
        if 'llm_review' in self.agents:
            self.agents['llm_review'].set_cr_mode(mode)
        self.logger.info(f"CR模式已设置为: {mode}")
    def get_cr_mode(self) -> str:
        """获取当前CR模式"""
        return self.config.get('cr_mode', 'standard')
    def enable_parallel_processing(self, enable: bool = True):
        """启用/禁用并行处理"""
        self.config['enable_parallel_processing'] = enable
        self.logger.info(f"并行处理已{'启用' if enable else '禁用'}")
    def set_max_concurrent_tasks(self, max_tasks: int):
        """设置最大并发任务数"""
        if max_tasks <= 0:
            raise ValueError("最大并发任务数必须大于0")
        self.config['max_concurrent_tasks'] = max_tasks
        self.logger.info(f"最大并发任务数已设置为: {max_tasks}")

    def _validate_final_result(self, result: Dict[str, Any]) -> bool:
        """
        验证最终结果是否包含UI展示所需的完整信息

        Args:
            result: 最终结果

        Returns:
            bool: 验证是否通过
        """
        required_fields = [
            'summary',           # 总结信息
            'scoring',           # 评分信息
            'problems',          # 问题列表
            'statistics',        # 统计信息
            'reviewMetrics',     # 审查指标（使用驼峰命名与聚合代理一致）
            'recommendations'    # 改进建议
        ]

        for field in required_fields:
            if field not in result:
                self.logger.warning(f"最终结果缺少必需字段: {field}")
                return False

        # 验证评分信息的完整性
        scoring = result.get('scoring', {})
        if not isinstance(scoring, dict) or 'overallScore' not in scoring:
            self.logger.warning("评分信息不完整")
            return False

        # 验证问题列表格式
        problems = result.get('problems', [])
        if not isinstance(problems, list):
            self.logger.warning("问题列表格式不正确")
            return False

        return True

    def _is_complete_format(self, result: Dict[str, Any]) -> bool:
        """检查结果是否已经是完整格式"""
        required_fields = ['summary', 'scoring', 'statistics', 'problems']
        return all(field in result for field in required_fields)

    def _supplement_result_format(self,
                                result: Dict[str, Any],
                                review_results: List[Dict[str, Any]],
                                segments: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        补充结果格式，确保包含UI展示所需的完整信息

        Args:
            result: 原始结果
            review_results: 审查结果列表
            segments: 代码片段列表

        Returns:
            Dict: 补充后的完整结果
        """
        try:
            # 检查result是否已经是完整格式（由聚合代理处理过）
            if self._is_complete_format(result):
                self.logger.info("结果已经是完整格式，直接添加元数据")
                # 只添加原始审查结果信息，避免重复调用enhance_cr_result
                result['originalReviewResults'] = review_results
                result['segmentsInfo'] = {
                    'count': len(segments),
                    'segments': segments
                }
                return result
            else:
                self.logger.info("结果格式不完整，使用降级方案")
                # 修复点：优先用 result['originalReviewResults']，保证统计准确
                if 'originalReviewResults' in result and result['originalReviewResults']:
                    review_results = result['originalReviewResults']
                return self._create_basic_result_format(result, review_results, segments)
        except Exception as e:
            self.logger.error(f"结果格式补充失败: {str(e)}")
            # 返回基础格式的结果
            return self._create_basic_result_format(result, review_results, segments)

    def _create_basic_result_format(self,
                                  result: Dict[str, Any],
                                  review_results: List[Dict[str, Any]],
                                  segments: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        创建基础格式的结果（当增强器不可用时的降级方案）

        Args:
            result: 原始结果
            review_results: 审查结果列表
            segments: 代码片段列表

        Returns:
            Dict: 基础格式的结果
        """
        from datetime import datetime

        # 统计问题信息
        total_problems = 0
        critical_count = 0
        warning_count = 0
        moderate_count = 0
        minor_count = 0
        all_problems = []

        for review_result in review_results:
            if 'problemList' in review_result:
                for problem_group in review_result['problemList']:
                    if 'detail' in problem_group and isinstance(problem_group['detail'], list):
                        for problem in problem_group['detail']:
                            all_problems.append(problem)
                            level = problem.get('level', '').lower()
                            if level == 'p0':
                                critical_count += 1
                            elif level == 'p1':
                                warning_count += 1
                            elif level == 'p2':
                                moderate_count += 1
                            else:
                                minor_count += 1

        total_problems = critical_count + warning_count + moderate_count + minor_count

        # 生成结构化问题分布
        problem_distribution = {
            'P0': critical_count,
            'P1': warning_count,
            'P2': moderate_count,
            'P3+': minor_count
        }

        # 生成结构化任务执行统计
        def _basic_task_execution_summary(results):
            total_tasks = len(results)
            successful_tasks = 0
            tasks_with_problems = 0
            for r in results:
                is_successful = False
                sum_check_result = r.get('sumCheckResult')
                if sum_check_result and sum_check_result.strip():
                    is_successful = True
                total_problem = r.get('totalProblem')
                if total_problem is not None:
                    is_successful = True
                problem_list = r.get('problemList')
                if problem_list and isinstance(problem_list, list) and len(problem_list) > 0:
                    is_successful = True
                if not is_successful and r and isinstance(r, dict) and len(r) > 0:
                    is_successful = True
                if is_successful:
                    successful_tasks += 1
                    has_problems = False
                    try:
                        if total_problem is not None:
                            problem_count = int(total_problem)
                            if problem_count > 0:
                                has_problems = True
                    except (ValueError, TypeError):
                        pass
                    if not has_problems and problem_list:
                        for problem_category in problem_list:
                            if isinstance(problem_category, dict):
                                details = problem_category.get('detail', [])
                                if details and isinstance(details, list) and len(details) > 0:
                                    has_problems = True
                                    break
                                try:
                                    num = problem_category.get('num', '0')
                                    if int(num) > 0:
                                        has_problems = True
                                        break
                                except (ValueError, TypeError):
                                    pass
                    if not has_problems and sum_check_result:
                        if sum_check_result.strip() == '不通过':
                            has_problems = True
                    if has_problems:
                        tasks_with_problems += 1
            return f"执行{total_tasks}个任务，{successful_tasks}个成功，{tasks_with_problems}个发现问题"

        # 生成结构化质量门禁统计
        def _basic_quality_gates_summary(total_problems, critical_count, warning_count, moderate_count, minor_count):
            gates = {
                'critical_gate': critical_count == 0,
                'warning_gate': warning_count <= 5,
                'total_gate': total_problems <= 20
            }
            passed_gates = sum(gates.values())
            total_gates = len(gates)
            pass_rate = round(passed_gates / total_gates * 100, 1)
            gate_status = "通过" if passed_gates == total_gates else "未通过"
            return f"质量门禁{gate_status}，通过率{pass_rate}%"

        # 计算整体评分 - 统一评分标准
        overall_score = max(0, 100 - (critical_count * 25 + warning_count * 15 + moderate_count * 10 + minor_count * 5))

        # 确定总体结果 - 确保数据一致性
        if critical_count > 0:
            overall_result = "不通过"  # 有严重问题必须不通过
        elif overall_score < 80:
            overall_result = "不通过"  # 评分低于80分不通过
        elif total_problems == 0:
            overall_result = "通过"  # 无问题通过
        else:
            overall_result = "通过"  # 有问题但评分达标，通过

        # 结构化结果描述（可选，建议前端不用）
        if total_problems == 0:
            result_description = "代码质量良好，未发现问题"
        else:
            desc_parts = []
            if critical_count > 0:
                desc_parts.append(f"P0:{critical_count}个")
            if warning_count > 0:
                desc_parts.append(f"P1:{warning_count}个")
            if moderate_count > 0:
                desc_parts.append(f"P2:{moderate_count}个")
            if minor_count > 0:
                desc_parts.append(f"P3+:{minor_count}个")
            result_description = ",".join(desc_parts)

        return {
            # 总结信息
            'summary': {
                'checkBranch': result.get('checkBranch', 'unknown'),
                'reviewTime': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'reviewer': 'AI代码审查',
                'overallResult': overall_result,
                'resultDescription': result_description,
                'totalProblems': total_problems,
                'taskExecutionSummary': _basic_task_execution_summary(review_results),
                'qualityGatesSummary': _basic_quality_gates_summary(total_problems, critical_count, warning_count, moderate_count, minor_count),
                'problemDistribution': problem_distribution
            },

            # 评分信息
            'scoring': {
                'overallScore': overall_score,
                'maxScore': 100,
                'dimensions': {
                    'criticalIssues': {
                        'score': max(0, 40 - critical_count * 20),
                        'maxScore': 40,
                        'description': f"严重问题扣分 ({critical_count}个)"
                    },
                    'warningIssues': {
                        'score': max(0, 30 - warning_count * 10),
                        'maxScore': 30,
                        'description': f"警告问题扣分 ({warning_count}个)"
                    },
                    'moderateIssues': {
                        'score': max(0, 20 - moderate_count * 5),
                        'maxScore': 20,
                        'description': f"中等问题扣分 ({moderate_count}个)"
                    },
                    'minorIssues': {
                        'score': max(0, 10 - minor_count * 2),
                        'maxScore': 10,
                        'description': f"轻微问题扣分 ({minor_count}个)"
                    }
                },
                'qualityGrade': 'A' if overall_score >= 90 else 'B' if overall_score >= 80 else 'C' if overall_score >= 70 else 'D',
                'passThreshold': 80,
                'isPassed': overall_score >= 80 and critical_count == 0
            },

            # 问题列表
            'problems': [
                {
                    'level': problem.get('level', ''),
                    'problem': problem.get('problem', ''),
                    'suggestion': problem.get('suggestion', ''),
                    'targetCode': problem.get('targetCode', ''),
                    'codePosition': problem.get('codePosition', [])
                }
                for problem in all_problems
            ],

            # 统计信息
            'statistics': {
                'totalProblems': total_problems,
                'criticalCount': critical_count,
                'warningCount': warning_count,
                'moderateCount': moderate_count,
                'minorCount': minor_count,
                'segmentsCount': len(segments),
                'reviewResultsCount': len(review_results),
                'problemDistribution': problem_distribution
            },

            # 审查指标
            'reviewMetrics': {
                'qualityScore': overall_score,
                'riskLevel': '高' if critical_count > 0 else '中' if warning_count > 2 else '低',
                'totalProblems': total_problems,
                'problemDensity': f"{total_problems}/文件"
            },

            # 改进建议
            'recommendations': [
                "优先修复所有严重问题" if critical_count > 0 else None,
                "关注警告级别问题的修复" if warning_count > 0 else None,
                "建议进行代码重构以提高质量" if total_problems > 10 else None,
                "保持良好的代码质量" if total_problems == 0 else None
            ],

            # 保留原始数据
            'originalResult': result,
            'originalReviewResults': review_results,
            'segmentsInfo': {
                'count': len(segments),
                'segments': segments
            }
        }
