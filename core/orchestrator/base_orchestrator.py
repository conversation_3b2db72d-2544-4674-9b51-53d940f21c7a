"""
编排器基类

定义工作流编排的统一接口和基础功能
"""

import logging
import asyncio
from abc import ABC, abstractmethod
from typing import Any, Dict, Optional, List
from dataclasses import dataclass
from enum import Enum
from datetime import datetime


class WorkflowStatus(Enum):
    """工作流状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class WorkflowResult:
    """工作流执行结果"""
    success: bool
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    execution_time: Optional[float] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'success': self.success,
            'data': self.data,
            'error': self.error,
            'metadata': self.metadata,
            'execution_time': self.execution_time
        }


class BaseOrchestrator(ABC):
    """编排器基类，定义工作流编排的统一接口"""
    
    def __init__(self, name: str, config: Optional[Dict[str, Any]] = None) -> None:
        """
        初始化编排器
        
        Args:
            name: 编排器名称
            config: 编排器配置
            :rtype: None
        """
        self.name = name
        self.config = config or {}
        self.logger = logging.getLogger(f"orchestrator.{name}")
        self.status = WorkflowStatus.PENDING
        self._execution_count = 0
        self._error_count = 0
        self._start_time: Optional[datetime] = None
        self._end_time: Optional[datetime] = None
        
    @abstractmethod
    async def execute_workflow(self, input_data: Dict[str, Any]) -> WorkflowResult:
        """
        执行工作流
        
        Args:
            input_data: 输入数据
            
        Returns:
            WorkflowResult: 工作流执行结果
        """
        pass
    
    @abstractmethod
    def validate_input(self, input_data: Dict[str, Any]) -> bool:
        """
        验证输入数据
        
        Args:
            input_data: 输入数据
            
        Returns:
            bool: 验证是否通过
        """
        pass
    
    async def run(self, input_data: Dict[str, Any]) -> WorkflowResult:
        """
        运行工作流，包含状态管理和错误处理
        
        Args:
            input_data: 输入数据
            
        Returns:
            WorkflowResult: 工作流执行结果
        """
        try:
            self.status = WorkflowStatus.RUNNING
            self._execution_count += 1
            self._start_time = datetime.now()
            
            # 验证输入
            if not self.validate_input(input_data):
                raise ValueError(f"编排器 {self.name} 输入数据验证失败")
            
            self.logger.info(f"编排器 {self.name} 开始执行，第 {self._execution_count} 次")
            
            # 执行工作流
            result = await self.execute_workflow(input_data)
            
            self._end_time = datetime.now()
            execution_time = (self._end_time - self._start_time).total_seconds()
            result.execution_time = execution_time
            
            if result.success:
                self.status = WorkflowStatus.COMPLETED
                self.logger.info(f"编排器 {self.name} 执行成功，耗时 {execution_time:.2f}s")
            else:
                self.status = WorkflowStatus.FAILED
                self._error_count += 1
                self.logger.error(f"编排器 {self.name} 执行失败: {result.error}")
            
            return result
            
        except Exception as e:
            self.status = WorkflowStatus.FAILED
            self._error_count += 1
            self._end_time = datetime.now()
            
            execution_time = None
            if self._start_time:
                execution_time = (self._end_time - self._start_time).total_seconds()
            
            error_msg = f"编排器 {self.name} 执行异常: {str(e)}"
            self.logger.error(error_msg)
            
            return WorkflowResult(
                success=False,
                error=error_msg,
                execution_time=execution_time,
                metadata={
                    'execution_count': self._execution_count,
                    'error_count': self._error_count
                }
            )
    
    def get_status(self) -> Dict[str, Any]:
        """
        获取编排器状态信息
        
        Returns:
            Dict: 状态信息
        """
        return {
            'name': self.name,
            'status': self.status.value,
            'execution_count': self._execution_count,
            'error_count': self._error_count,
            'start_time': self._start_time.isoformat() if self._start_time else None,
            'end_time': self._end_time.isoformat() if self._end_time else None,
            'config': self.config
        }
    
    def reset(self):
        """重置编排器状态"""
        self.status = WorkflowStatus.PENDING
        self._execution_count = 0
        self._error_count = 0
        self._start_time = None
        self._end_time = None
        self.logger.info(f"编排器 {self.name} 状态已重置")
    
    def update_config(self, config: Dict[str, Any]):
        """
        更新编排器配置
        
        Args:
            config: 新的配置
        """
        self.config.update(config)
        self.logger.info(f"编排器 {self.name} 配置已更新")
    
    async def cancel(self):
        """取消工作流执行"""
        if self.status == WorkflowStatus.RUNNING:
            self.status = WorkflowStatus.CANCELLED
            self.logger.info(f"编排器 {self.name} 已取消执行")
    
    def is_running(self) -> bool:
        """检查是否正在运行"""
        return self.status == WorkflowStatus.RUNNING
    
    def is_completed(self) -> bool:
        """检查是否已完成"""
        return self.status == WorkflowStatus.COMPLETED
    
    def is_failed(self) -> bool:
        """检查是否失败"""
        return self.status == WorkflowStatus.FAILED
