"""
任务调度器

负责任务的调度、优先级管理和资源分配
"""

import asyncio
import logging
from typing import Any, Dict, Optional, List, Callable
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime
from queue import PriorityQueue
import uuid


class TaskPriority(Enum):
    """任务优先级枚举"""
    LOW = 3
    NORMAL = 2
    HIGH = 1
    URGENT = 0


class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class Task:
    """任务数据类"""
    id: str
    name: str
    func: Callable
    args: tuple = field(default_factory=tuple)
    kwargs: dict = field(default_factory=dict)
    priority: TaskPriority = TaskPriority.NORMAL
    created_at: datetime = field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    status: TaskStatus = TaskStatus.PENDING
    result: Any = None
    error: Optional[str] = None
    retry_count: int = 0
    max_retries: int = 0
    timeout: Optional[float] = None
    
    def __lt__(self, other):
        """用于优先级队列排序"""
        if self.priority.value != other.priority.value:
            return self.priority.value < other.priority.value
        return self.created_at < other.created_at


class TaskScheduler:
    """任务调度器"""
    
    def __init__(self, 
                 max_workers: int = 5,
                 config: Optional[Dict[str, Any]] = None):
        """
        初始化任务调度器
        
        Args:
            max_workers: 最大工作线程数
            config: 调度器配置
        """
        self.max_workers = max_workers
        self.config = config or {}
        self.logger = logging.getLogger("task_scheduler")
        
        # 任务队列和状态管理
        self._task_queue = PriorityQueue()
        self._running_tasks: Dict[str, asyncio.Task] = {}
        self._completed_tasks: Dict[str, Task] = {}
        self._task_registry: Dict[str, Task] = {}
        
        # 调度器状态
        self._is_running = False
        self._worker_semaphore = asyncio.Semaphore(max_workers)
        self._scheduler_task: Optional[asyncio.Task] = None
        
        # 统计信息
        self._total_tasks = 0
        self._completed_count = 0
        self._failed_count = 0
        self._cancelled_count = 0
    
    async def start(self):
        """启动任务调度器"""
        if self._is_running:
            self.logger.warning("任务调度器已在运行")
            return
        
        self._is_running = True
        self._scheduler_task = asyncio.create_task(self._scheduler_loop())
        self.logger.info(f"任务调度器已启动，最大工作线程数: {self.max_workers}")
    
    async def stop(self):
        """停止任务调度器"""
        if not self._is_running:
            return
        
        self._is_running = False
        
        # 取消调度器任务
        if self._scheduler_task:
            self._scheduler_task.cancel()
            try:
                await self._scheduler_task
            except asyncio.CancelledError:
                pass
        
        # 取消所有运行中的任务
        cancel_tasks = []
        for task_id, task in self._running_tasks.items():
            task.cancel()
            cancel_tasks.append(task)
        
        if cancel_tasks:
            await asyncio.gather(*cancel_tasks, return_exceptions=True)
        
        self.logger.info("任务调度器已停止")
    
    def submit_task(self, 
                   name: str,
                   func: Callable,
                   args: tuple = (),
                   kwargs: Optional[Dict[str, Any]] = None,
                   priority: TaskPriority = TaskPriority.NORMAL,
                   max_retries: int = 0,
                   timeout: Optional[float] = None) -> str:
        """
        提交任务
        
        Args:
            name: 任务名称
            func: 任务函数
            args: 位置参数
            kwargs: 关键字参数
            priority: 任务优先级
            max_retries: 最大重试次数
            timeout: 超时时间（秒）
            
        Returns:
            str: 任务ID
        """
        task_id = str(uuid.uuid4())
        kwargs = kwargs or {}
        
        task = Task(
            id=task_id,
            name=name,
            func=func,
            args=args,
            kwargs=kwargs,
            priority=priority,
            max_retries=max_retries,
            timeout=timeout
        )
        
        self._task_registry[task_id] = task
        self._task_queue.put(task)
        self._total_tasks += 1
        
        self.logger.info(f"任务已提交: {name} (ID: {task_id}, 优先级: {priority.name})")
        return task_id
    
    async def _scheduler_loop(self):
        """调度器主循环"""
        self.logger.info("调度器主循环已启动")
        
        try:
            while self._is_running:
                # 检查是否有可用的工作线程
                if len(self._running_tasks) >= self.max_workers:
                    await asyncio.sleep(0.1)
                    continue
                
                # 从队列获取任务
                try:
                    task = self._task_queue.get_nowait()
                except:
                    await asyncio.sleep(0.1)
                    continue
                
                # 启动任务
                await self._start_task(task)
                
        except asyncio.CancelledError:
            self.logger.info("调度器主循环被取消")
        except Exception as e:
            self.logger.error(f"调度器主循环异常: {str(e)}")
    
    async def _start_task(self, task: Task):
        """启动单个任务"""
        task.status = TaskStatus.RUNNING
        task.started_at = datetime.now()
        
        # 创建任务协程
        task_coroutine = self._execute_task(task)
        asyncio_task = asyncio.create_task(task_coroutine)
        
        self._running_tasks[task.id] = asyncio_task
        
        self.logger.info(f"任务开始执行: {task.name} (ID: {task.id})")
    
    async def _execute_task(self, task: Task):
        """执行任务"""
        async with self._worker_semaphore:
            try:
                # 设置超时
                if task.timeout:
                    result = await asyncio.wait_for(
                        self._run_task_func(task),
                        timeout=task.timeout
                    )
                else:
                    result = await self._run_task_func(task)
                
                # 任务成功完成
                task.status = TaskStatus.COMPLETED
                task.result = result
                task.completed_at = datetime.now()
                self._completed_count += 1
                
                self.logger.info(f"任务执行成功: {task.name} (ID: {task.id})")
                
            except asyncio.TimeoutError:
                # 任务超时
                task.status = TaskStatus.FAILED
                task.error = f"任务超时 ({task.timeout}s)"
                task.completed_at = datetime.now()
                self._failed_count += 1
                
                self.logger.error(f"任务执行超时: {task.name} (ID: {task.id})")
                
                # 检查是否需要重试
                if task.retry_count < task.max_retries:
                    await self._retry_task(task)
                
            except asyncio.CancelledError:
                # 任务被取消
                task.status = TaskStatus.CANCELLED
                task.completed_at = datetime.now()
                self._cancelled_count += 1
                
                self.logger.info(f"任务被取消: {task.name} (ID: {task.id})")
                
            except Exception as e:
                # 任务执行异常
                task.status = TaskStatus.FAILED
                task.error = str(e)
                task.completed_at = datetime.now()
                self._failed_count += 1
                
                self.logger.error(f"任务执行异常: {task.name} (ID: {task.id}), 错误: {str(e)}")
                
                # 检查是否需要重试
                if task.retry_count < task.max_retries:
                    await self._retry_task(task)
            
            finally:
                # 清理运行中的任务
                if task.id in self._running_tasks:
                    del self._running_tasks[task.id]
                
                # 移动到已完成任务
                self._completed_tasks[task.id] = task
    
    async def _run_task_func(self, task: Task):
        """运行任务函数"""
        if asyncio.iscoroutinefunction(task.func):
            return await task.func(*task.args, **task.kwargs)
        else:
            # 在线程池中运行同步函数
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(None, task.func, *task.args, **task.kwargs)
    
    async def _retry_task(self, task: Task):
        """重试任务"""
        task.retry_count += 1
        task.status = TaskStatus.PENDING
        task.started_at = None
        task.completed_at = None
        task.result = None
        task.error = None
        
        # 重新加入队列
        self._task_queue.put(task)
        
        self.logger.info(f"任务重试: {task.name} (ID: {task.id}), 第 {task.retry_count} 次重试")
    
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        获取任务状态
        
        Args:
            task_id: 任务ID
            
        Returns:
            Dict: 任务状态信息
        """
        task = self._task_registry.get(task_id)
        if not task:
            return None
        
        return {
            'id': task.id,
            'name': task.name,
            'status': task.status.value,
            'priority': task.priority.name,
            'created_at': task.created_at.isoformat(),
            'started_at': task.started_at.isoformat() if task.started_at else None,
            'completed_at': task.completed_at.isoformat() if task.completed_at else None,
            'retry_count': task.retry_count,
            'max_retries': task.max_retries,
            'error': task.error
        }
    
    def get_scheduler_stats(self) -> Dict[str, Any]:
        """
        获取调度器统计信息
        
        Returns:
            Dict: 统计信息
        """
        return {
            'is_running': self._is_running,
            'max_workers': self.max_workers,
            'running_tasks': len(self._running_tasks),
            'pending_tasks': self._task_queue.qsize(),
            'total_tasks': self._total_tasks,
            'completed_count': self._completed_count,
            'failed_count': self._failed_count,
            'cancelled_count': self._cancelled_count
        }
