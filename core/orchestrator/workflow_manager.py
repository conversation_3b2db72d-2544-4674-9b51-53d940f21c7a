"""
工作流管理器

负责管理多个编排器和工作流的生命周期
"""

import logging
import asyncio
from typing import Any, Dict, Optional, List, Type
from datetime import datetime
from .base_orchestrator import BaseOrchestrator, WorkflowResult
from .cr_orchestrator import CROrchestrator


class WorkflowManager:
    """工作流管理器"""

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化工作流管理器
        
        Args:
            config: 管理器配置
        """
        self.config = config or {}
        self.logger = logging.getLogger("workflow_manager")
        
        # 编排器注册表
        self._orchestrator_registry: Dict[str, Type[BaseOrchestrator]] = {}
        self._orchestrator_instances: Dict[str, BaseOrchestrator] = {}
        self._running_workflows: Dict[str, asyncio.Task] = {}
        
        # 注册内置编排器
        self._register_builtin_orchestrators()
    
    def _register_builtin_orchestrators(self):
        """注册内置编排器类型"""
        self._orchestrator_registry.update({
            'cr_orchestrator': CROrchestrator
        })
        
        self.logger.info(f"已注册 {len(self._orchestrator_registry)} 个内置编排器类型")
    
    def register_orchestrator_type(self, 
                                 orchestrator_type: str, 
                                 orchestrator_class: Type[BaseOrchestrator]):
        """
        注册新的编排器类型
        
        Args:
            orchestrator_type: 编排器类型名称
            orchestrator_class: 编排器类
        """
        if not issubclass(orchestrator_class, BaseOrchestrator):
            raise ValueError(f"编排器类 {orchestrator_class} 必须继承自 BaseOrchestrator")
        
        self._orchestrator_registry[orchestrator_type] = orchestrator_class
        self.logger.info(f"已注册编排器类型: {orchestrator_type}")
    
    def create_orchestrator(self, 
                          orchestrator_type: str,
                          orchestrator_id: Optional[str] = None,
                          config: Optional[Dict[str, Any]] = None,
                          **kwargs) -> BaseOrchestrator:
        """
        创建编排器实例
        
        Args:
            orchestrator_type: 编排器类型
            orchestrator_id: 编排器实例ID
            config: 编排器配置
            **kwargs: 其他参数
            
        Returns:
            BaseOrchestrator: 编排器实例
        """
        if orchestrator_type not in self._orchestrator_registry:
            raise ValueError(f"未知的编排器类型: {orchestrator_type}")
        
        orchestrator_class = self._orchestrator_registry[orchestrator_type]
        orchestrator_id = orchestrator_id or f"{orchestrator_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        try:
            # 根据编排器类型传递不同的参数
            if orchestrator_type == 'cr_orchestrator':
                # 导入CROrchestrator类型以避免类型检查警告
                from core.orchestrator.cr_orchestrator import CROrchestrator
                orchestrator: CROrchestrator = orchestrator_class(
                    llm_service=kwargs.get('llm_service'),
                    devmind_service=kwargs.get('devmind_service'),
                    chunk_service=kwargs.get('chunk_service'),
                    git_service=kwargs.get('git_service'),
                    config=config
                )
            else:
                # 通用创建方式 - 假设其他编排器遵循BaseOrchestrator的构造函数签名
                orchestrator = orchestrator_class(name=orchestrator_type, config=config)
            
            # 缓存编排器实例
            self._orchestrator_instances[orchestrator_id] = orchestrator
            
            self.logger.info(f"已创建编排器: {orchestrator_type} (ID: {orchestrator_id})")
            return orchestrator
            
        except Exception as e:
            error_msg = f"创建编排器失败: {orchestrator_type}, 错误: {str(e)}"
            self.logger.error(error_msg)
            raise RuntimeError(error_msg)
    
    def get_orchestrator(self, orchestrator_id: str) -> Optional[BaseOrchestrator]:
        """
        获取编排器实例
        
        Args:
            orchestrator_id: 编排器实例ID
            
        Returns:
            BaseOrchestrator: 编排器实例，如果不存在返回None
        """
        return self._orchestrator_instances.get(orchestrator_id)
    
    async def execute_workflow(self, 
                             orchestrator_id: str,
                             input_data: Dict[str, Any],
                             workflow_id: Optional[str] = None) -> WorkflowResult:
        """
        执行工作流
        
        Args:
            orchestrator_id: 编排器实例ID
            input_data: 输入数据
            workflow_id: 工作流ID
            
        Returns:
            WorkflowResult: 工作流执行结果
        """
        orchestrator = self.get_orchestrator(orchestrator_id)
        if not orchestrator:
            return WorkflowResult(
                success=False,
                error=f"编排器不存在: {orchestrator_id}"
            )
        
        workflow_id = workflow_id or f"workflow_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}"
        
        try:
            self.logger.info(f"开始执行工作流: {workflow_id} (编排器: {orchestrator_id})")
            
            # 创建工作流任务
            task = asyncio.create_task(orchestrator.run(input_data))
            self._running_workflows[workflow_id] = task
            
            # 等待执行完成
            result = await task
            
            # 清理完成的工作流
            if workflow_id in self._running_workflows:
                del self._running_workflows[workflow_id]
            
            self.logger.info(f"工作流执行完成: {workflow_id}, 成功: {result.success}")
            return result
            
        except asyncio.CancelledError:
            self.logger.info(f"工作流被取消: {workflow_id}")
            return WorkflowResult(
                success=False,
                error="工作流被取消"
            )
        except Exception as e:
            error_msg = f"工作流执行异常: {workflow_id}, 错误: {str(e)}"
            self.logger.error(error_msg)
            
            # 清理失败的工作流
            if workflow_id in self._running_workflows:
                del self._running_workflows[workflow_id]
            
            return WorkflowResult(
                success=False,
                error=error_msg
            )
    
    async def cancel_workflow(self, workflow_id: str) -> bool:
        """
        取消工作流执行
        
        Args:
            workflow_id: 工作流ID
            
        Returns:
            bool: 是否成功取消
        """
        if workflow_id not in self._running_workflows:
            self.logger.warning(f"工作流不存在或未运行: {workflow_id}")
            return False
        
        task = self._running_workflows[workflow_id]
        task.cancel()
        
        try:
            await task
        except asyncio.CancelledError:
            pass
        
        del self._running_workflows[workflow_id]
        self.logger.info(f"工作流已取消: {workflow_id}")
        return True
    
    def list_orchestrator_types(self) -> List[str]:
        """
        列出所有可用的编排器类型
        
        Returns:
            List[str]: 编排器类型列表
        """
        return list(self._orchestrator_registry.keys())
    
    def list_orchestrator_instances(self) -> Dict[str, Dict[str, Any]]:
        """
        列出所有编排器实例的状态
        
        Returns:
            Dict: 编排器实例状态信息
        """
        return {
            orchestrator_id: orchestrator.get_status()
            for orchestrator_id, orchestrator in self._orchestrator_instances.items()
        }
    
    def list_running_workflows(self) -> List[str]:
        """
        列出所有正在运行的工作流
        
        Returns:
            List[str]: 工作流ID列表
        """
        return list(self._running_workflows.keys())
    
    def remove_orchestrator(self, orchestrator_id: str) -> bool:
        """
        移除编排器实例
        
        Args:
            orchestrator_id: 编排器实例ID
            
        Returns:
            bool: 是否成功移除
        """
        if orchestrator_id in self._orchestrator_instances:
            del self._orchestrator_instances[orchestrator_id]
            self.logger.info(f"已移除编排器: {orchestrator_id}")
            return True
        return False
    
    def reset_all_orchestrators(self):
        """重置所有编排器实例的状态"""
        for orchestrator in self._orchestrator_instances.values():
            orchestrator.reset()
        self.logger.info("已重置所有编排器状态")
    
    async def shutdown(self):
        """关闭工作流管理器，取消所有运行中的工作流"""
        self.logger.info("开始关闭工作流管理器")
        
        # 取消所有运行中的工作流
        cancel_tasks = []
        for workflow_id in list(self._running_workflows.keys()):
            cancel_tasks.append(self.cancel_workflow(workflow_id))
        
        if cancel_tasks:
            await asyncio.gather(*cancel_tasks, return_exceptions=True)
        
        # 清理资源
        self._orchestrator_instances.clear()
        self._running_workflows.clear()
        
        self.logger.info("工作流管理器已关闭")


# 全局工作流管理器实例
_workflow_manager: Optional[WorkflowManager] = None


def get_workflow_manager() -> WorkflowManager:
    """获取全局工作流管理器实例"""
    global _workflow_manager
    if _workflow_manager is None:
        _workflow_manager = WorkflowManager()
    return _workflow_manager
