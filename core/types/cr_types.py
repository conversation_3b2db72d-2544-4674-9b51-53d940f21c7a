from dataclasses import dataclass, field
from enum import Enum
from typing import List
from pydantic import BaseModel


class ProblemLevel(str, Enum):
    P0 = 'p0'
    P1 = 'p1'
    P2 = 'p2'
    P3 = 'p3'
    P4 = 'p4'
    P5 = 'p5'
    P6 = 'p6'


class CheckResult(str, Enum):
    PASS = '通过'
    FAIL = '不通过'


@dataclass
class CodeReviewProblemDetail:
    targetCode: str  # 问题代码
    problem: str  # 问题描述
    suggestion: str  # 修改建议
    level: ProblemLevel  # 问题级别
    codePosition: List[int] = field(default_factory=lambda: [1, 0, 1, 0])  # 代码位置 [startLine, startColumn, endLine, endColumn]


class CodeReviewProblemDetailPydantic(BaseModel):
    """Pydantic版本的问题详情，用于LLM结构化输出"""
    targetCode: str  # 问题代码
    problem: str  # 问题描述
    suggestion: str  # 修改建议
    level: ProblemLevel  # 问题级别
    codePosition: List[int] = [1, 0, 1, 0]  # 代码位置 [startLine, startColumn, endLine, endColumn]


class CodeReviewProblemsResponse(BaseModel):
    """代码审查问题列表响应"""
    problems: List[CodeReviewProblemDetailPydantic] = []


@dataclass
class CodeReviewProblemCategory:
    scene: str  # 检查项名称
    num: str  # 问题数量
    checkResult: CheckResult  # 检查结果
    detail: List[CodeReviewProblemDetail] = field(default_factory=list)  # 问题详情列表


@dataclass
class CodeReviewResult:
    checkBranch: str  # 分支名
    sumCheckResult: CheckResult  # 总体检查结果
    resultDesc: str  # 问题类别及简要描述
    totalProblem: str  # 问题总数
    originResult: str  # 原始cr结果
    problemList: List[CodeReviewProblemCategory] = field(default_factory=list)  # 问题分类列表
