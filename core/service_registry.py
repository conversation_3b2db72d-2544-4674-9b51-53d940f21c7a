"""
服务注册表

提供全局服务访问接口，确保新旧架构的兼容性
"""

import logging
from typing import Dict, Optional, Any, List

# 延迟导入以避免循环导入
def _get_service_manager():
    """延迟导入服务管理器"""
    try:
        from services.service_manager import get_service_manager
        return get_service_manager()
    except ImportError:
        return None

def _import_service_class(module_name: str, class_name: str):
    """延迟导入服务类"""
    try:
        module = __import__(module_name, fromlist=[class_name])
        return getattr(module, class_name)
    except ImportError:
        return None

# 全局服务实例缓存
_service_cache: Dict[str, Any] = {}
_logger = logging.getLogger(__name__)
_initialization_in_progress = False


def get_service(service_name: str) -> Optional[Any]:
    """
    获取服务实例

    Args:
        service_name: 服务名称

    Returns:
        服务实例，如果不存在返回None
    """
    # 首先从缓存获取（优先级更高，避免重复创建）
    if service_name in _service_cache:
        return _service_cache[service_name]

    # 然后尝试从服务管理器获取
    manager = _get_service_manager()
    if manager:
        service = manager.get_service(service_name)
        if service:
            # 将从管理器获取的服务也缓存起来
            _service_cache[service_name] = service
            return service

    return None


def register_service(service_name: str, service_instance: Any) -> None:
    """
    注册服务实例到缓存

    Args:
        service_name: 服务名称
        service_instance: 服务实例
    """
    _service_cache[service_name] = service_instance
    _logger.debug(f"服务已注册到缓存: {service_name}")


def set_initialization_in_progress(in_progress: bool):
    """设置初始化进行中状态"""
    global _initialization_in_progress
    _initialization_in_progress = in_progress


def _initialize_service_sync(service):
    """同步初始化服务"""
    try:
        if hasattr(service, '_sync_initialize'):
            service._sync_initialize()
        elif hasattr(service, 'initialize'):
            # 对于异步初始化方法，尝试同步调用
            import asyncio
            try:
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    # 如果事件循环正在运行，跳过初始化（稍后会被正确初始化）
                    _logger.debug(f"跳过服务 {service.name} 的初始化，事件循环正在运行")
                    return
                else:
                    loop.run_until_complete(service.initialize())
            except RuntimeError:
                # 没有事件循环，创建一个新的
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    loop.run_until_complete(service.initialize())
                finally:
                    loop.close()
        _logger.debug(f"服务 {service.name} 初始化成功")
    except Exception as e:
        _logger.error(f"服务 {service.name} 初始化失败: {str(e)}")


def get_git_service():
    """获取Git服务实例"""
    service = get_service("git_service")
    if service:
        return service

    # 延迟导入GitService类
    GitService = _import_service_class("services.git_service", "GitService")
    if not GitService:
        _logger.error("无法导入GitService类")
        return None

    # 创建新实例并缓存
    _logger.debug("Git服务未在服务管理器中找到，创建新实例")
    git_service = GitService()

    # 同步初始化Git服务
    try:
        if hasattr(git_service, '_sync_initialize'):
            git_service._sync_initialize()
        _logger.debug("Git服务初始化成功")
    except Exception as e:
        _logger.error(f"Git服务初始化失败: {str(e)}")

    register_service("git_service", git_service)
    return git_service


def get_chunk_service(project: str, repo: str, branch: str = "main"):
    """
    获取代码分块服务实例

    Args:
        project: 项目名称
        repo: 仓库名称
        branch: 分支名称

    Returns:
        代码分块服务实例
    """
    git_service = get_git_service()
    if not git_service:
        _logger.error("无法获取Git服务，无法创建代码分块服务")
        return None

    # 延迟导入ChunkService类
    ChunkService = _import_service_class("services.chunk_service", "ChunkService")
    if not ChunkService:
        _logger.error("无法导入ChunkService类")
        return None

    # 创建新的ChunkService实例（因为它依赖于特定的项目/仓库）
    chunk_service = ChunkService(git_service, project, repo, branch)
    return chunk_service


def get_devtools_service():
    """获取开发工具服务实例"""
    service = get_service("devtools_service")
    if service:
        return service

    # 延迟导入DevtoolsService类
    DevtoolsService = _import_service_class("services.devtools_service", "DevtoolsService")
    if not DevtoolsService:
        _logger.error("无法导入DevtoolsService类")
        return None

    # 创建新实例并缓存
    _logger.debug("开发工具服务未在服务管理器中找到，创建新实例")
    kms_service = get_kms_service()
    if not kms_service:
        _logger.error("无法获取KMS服务，无法创建开发工具服务")
        return None
    devtools_service = DevtoolsService(kms_service)
    register_service("devtools_service", devtools_service)
    return devtools_service


def get_daxiang_service():
    """获取大象服务实例"""
    service = get_service("daxiang_service")
    if service:
        return service

    # 延迟导入DaXiangService类
    DaXiangService = _import_service_class("services.daxiang_service", "DaXiangService")
    if not DaXiangService:
        _logger.error("无法导入DaXiangService类")
        return None

    # 创建新实例并缓存
    _logger.debug("大象服务未在服务管理器中找到，创建新实例")
    kms_service = get_kms_service()
    if not kms_service:
        _logger.error("无法获取KMS服务，无法创建大象服务")
        return None
    daxiang_service = DaXiangService(kms_service)
    register_service("daxiang_service", daxiang_service)
    return daxiang_service


def get_km_service():
    """获取学城服务实例"""
    service = get_service("km_service")
    if service:
        return service

    # 延迟导入KmService类
    KmService = _import_service_class("services.km_service", "KmService")
    if not KmService:
        _logger.error("无法导入KmService类")
        return None

    # 创建新实例并缓存
    _logger.debug("学城服务未在服务管理器中找到，创建新实例")
    kms_service = get_kms_service()
    daxiang_service = get_daxiang_service()
    org_service = get_org_service()

    if not all([kms_service, daxiang_service, org_service]):
        _logger.error("无法获取依赖服务，无法创建学城服务")
        return None

    km_service = KmService(kms_service, daxiang_service, org_service)
    register_service("km_service", km_service)
    return km_service


def get_kms_service():
    """获取KMS服务实例"""
    service = get_service("kms_service")
    if service:
        return service

    # 延迟导入KmsService类
    KmsService = _import_service_class("services.kms_service", "KmsService")
    if not KmsService:
        _logger.error("无法导入KmsService类")
        return None

    # 创建新实例并缓存
    _logger.debug("KMS服务未在服务管理器中找到，创建新实例")
    kms_service = KmsService()

    # 初始化服务
    _initialize_service_sync(kms_service)

    register_service("kms_service", kms_service)
    return kms_service


def get_org_service():
    """获取组织架构服务实例"""
    service = get_service("org_service")
    if service:
        return service

    # 延迟导入OrgService类
    OrgService = _import_service_class("services.org_service", "OrgService")
    if not OrgService:
        _logger.error("无法导入OrgService类")
        return None

    # 创建新实例并缓存
    _logger.debug("组织架构服务未在服务管理器中找到，创建新实例")
    org_service = OrgService()

    # 初始化服务
    _initialize_service_sync(org_service)

    register_service("org_service", org_service)
    return org_service


def get_mcp_service():
    """获取MCP服务实例"""
    service = get_service("mcp_service")
    if service:
        return service

    _logger.warning("MCP服务未在服务管理器中找到")
    return None


def get_cr_service():
    """获取CR服务实例"""
    service = get_service("cr_service")
    if service:
        return service

    # 如果没有找到，尝试创建新的CR服务实例
    if not _initialization_in_progress:
        try:
            # 延迟导入CRService类
            CRService = _import_service_class("services.cr_service", "CRService")
            if not CRService:
                _logger.error("无法导入CRService类")
                return None

            cr_service = CRService()
            register_service("cr_service", cr_service)
            _logger.info("已创建新的CR服务实例")
            return cr_service
        except Exception as e:
            _logger.error(f"创建CR服务失败: {str(e)}")

    _logger.warning("CR服务未在服务管理器中找到")
    return None


def list_available_services() -> Dict[str, str]:
    """
    列出所有可用的服务

    Returns:
        服务名称到状态的映射
    """
    services = {}

    # 从服务管理器获取
    manager = _get_service_manager()
    if manager:
        for service_name in manager.list_services():
            services[service_name] = "managed"

    # 从缓存获取
    for service_name in _service_cache.keys():
        if service_name not in services:
            services[service_name] = "cached"

    return services


def list_services() -> List[str]:
    """
    列出所有可用的服务名称

    Returns:
        服务名称列表
    """
    return list(list_available_services().keys())


def clear_service_cache() -> None:
    """清理服务缓存"""
    global _service_cache
    _service_cache.clear()
    _logger.info("服务缓存已清理")


# 兼容性函数，保持与原有代码的兼容性
def get_legacy_git_service():
    """兼容性函数：获取Git服务"""
    return get_git_service()


def get_legacy_chunk_service(git_service, project, repo, branch=None):
    """兼容性函数：获取代码分块服务"""
    # 延迟导入ChunkService类
    ChunkService = _import_service_class("services.chunk_service", "ChunkService")
    if not ChunkService:
        _logger.error("无法导入ChunkService类")
        return None
    return ChunkService(git_service, project, repo, branch or "main")


def get_legacy_devtools_service():
    """兼容性函数：获取开发工具服务"""
    return get_devtools_service()


def get_llm_service():
    """获取LLM服务"""
    service = get_service('llm_service')
    if not service:
        # 尝试创建新架构的LLM服务
        try:
            from services.llm_service import LLMService
            service = LLMService()
            # 异步初始化服务
            _initialize_service_sync(service)
            register_service('llm_service', service)
            _logger.info("已创建新的LLM服务实例")
        except Exception as e:
            _logger.warning(f"创建LLM服务失败: {str(e)}")
    return service


def get_sso_service():
    """获取SSO服务"""
    service = get_service('sso_service')
    if not service:
        # 尝试创建新架构的SSO服务
        try:
            from services.sso_service import SSOService
            service = SSOService()
            # 异步初始化服务
            _initialize_service_sync(service)
            register_service('sso_service', service)
            _logger.info("已创建新的SSO服务实例")
        except Exception as e:
            _logger.warning(f"创建SSO服务失败: {str(e)}")
    return service


def get_git_service():
    """获取Git服务"""
    service = get_service('git_service')
    if not service:
        # 尝试创建新架构的Git服务
        try:
            from services.git_service import GitService
            service = GitService()
            # 异步初始化服务
            _initialize_service_sync(service)
            register_service('git_service', service)
            _logger.info("已创建新的Git服务实例")
        except Exception as e:
            _logger.warning(f"创建Git服务失败: {str(e)}")
    return service
