"""
文件工具类

按照第一阶段架构重构要求，将原有文件工具迁移到新架构
"""

import json
import os
import threading
import sys
from pathlib import Path
from typing import Any, Dict, Optional, Union
from cachetools import LRUCache, cached
from ruamel.yaml import YAML

# 全局锁
LOCK_KEY_pdfplumber = "global_shared_lock_pdfplumber"
if LOCK_KEY_pdfplumber not in sys.modules:
    sys.modules[LOCK_KEY_pdfplumber] = threading.Lock()


def get_project_base_directory(*args) -> str:
    """
    获取项目根目录
    
    Args:
        *args: 子路径
        
    Returns:
        项目根目录路径
    """
    project_base = os.getenv("PROJECT_BASE") or os.getenv("DEPLOY_BASE")
    
    if project_base is None:
        project_base = str(Path(__file__).resolve().parent.parent)
    
    if args:
        return os.path.join(project_base, *args)
    return project_base


def get_home_cache_dir() -> str:
    """
    获取用户缓存目录
    
    Returns:
        缓存目录路径
    """
    cache_dir = Path.home() / ".cache"
    cache_dir.mkdir(exist_ok=True)
    return str(cache_dir)


@cached(cache=LRUCache(maxsize=10))
def load_json_conf(conf_path: Union[str, Path]) -> Dict[str, Any]:
    """
    加载JSON配置文件（带缓存）
    
    Args:
        conf_path: 配置文件路径
        
    Returns:
        配置字典
        
    Raises:
        EnvironmentError: 加载失败时抛出
    """
    if os.path.isabs(conf_path):
        json_conf_path = conf_path
    else:
        json_conf_path = os.path.join(get_project_base_directory(), conf_path)
    
    try:
        with open(json_conf_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        raise EnvironmentError(f"加载JSON配置文件失败: {json_conf_path}") from e


def dump_json_conf(config_data: Dict[str, Any], conf_path: Union[str, Path]) -> None:
    """
    保存JSON配置文件
    
    Args:
        config_data: 配置数据
        conf_path: 配置文件路径
        
    Raises:
        EnvironmentError: 保存失败时抛出
    """
    if os.path.isabs(conf_path):
        json_conf_path = conf_path
    else:
        json_conf_path = os.path.join(get_project_base_directory(), conf_path)
    
    # 确保目录存在
    os.makedirs(os.path.dirname(json_conf_path), exist_ok=True)
    
    try:
        with open(json_conf_path, "w", encoding='utf-8') as f:
            json.dump(config_data, f, indent=4, ensure_ascii=False)
    except Exception as e:
        raise EnvironmentError(f"保存JSON配置文件失败: {json_conf_path}") from e


def load_json_conf_real_time(conf_path: Union[str, Path]) -> Dict[str, Any]:
    """
    实时加载JSON配置文件（不使用缓存）
    
    Args:
        conf_path: 配置文件路径
        
    Returns:
        配置字典
        
    Raises:
        EnvironmentError: 加载失败时抛出
    """
    if os.path.isabs(conf_path):
        json_conf_path = conf_path
    else:
        json_conf_path = os.path.join(get_project_base_directory(), conf_path)
    
    try:
        with open(json_conf_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        raise EnvironmentError(f"加载JSON配置文件失败: {json_conf_path}") from e


def load_yaml_conf(conf_path: Union[str, Path]) -> Dict[str, Any]:
    """
    加载YAML配置文件
    
    Args:
        conf_path: 配置文件路径
        
    Returns:
        配置字典
        
    Raises:
        EnvironmentError: 加载失败时抛出
    """
    if not os.path.isabs(conf_path):
        conf_path = os.path.join(get_project_base_directory(), conf_path)
    
    try:
        with open(conf_path, 'r', encoding='utf-8') as f:
            yaml = YAML(typ='safe', pure=True)
            return yaml.load(f)
    except Exception as e:
        raise EnvironmentError(f"加载YAML配置文件失败: {conf_path}") from e


def rewrite_yaml_conf(conf_path: Union[str, Path], config: Dict[str, Any]) -> None:
    """
    重写YAML配置文件
    
    Args:
        conf_path: 配置文件路径
        config: 配置数据
        
    Raises:
        EnvironmentError: 保存失败时抛出
    """
    if not os.path.isabs(conf_path):
        conf_path = os.path.join(get_project_base_directory(), conf_path)
    
    # 确保目录存在
    os.makedirs(os.path.dirname(conf_path), exist_ok=True)
    
    try:
        with open(conf_path, "w", encoding='utf-8') as f:
            yaml = YAML(typ="safe")
            yaml.dump(config, f)
    except Exception as e:
        raise EnvironmentError(f"保存YAML配置文件失败: {conf_path}") from e


def ensure_directory_exists(directory: Union[str, Path]) -> None:
    """
    确保目录存在
    
    Args:
        directory: 目录路径
    """
    Path(directory).mkdir(parents=True, exist_ok=True)


def safe_file_read(file_path: Union[str, Path], encoding: str = 'utf-8') -> Optional[str]:
    """
    安全读取文件内容
    
    Args:
        file_path: 文件路径
        encoding: 文件编码
        
    Returns:
        文件内容，读取失败返回None
    """
    try:
        with open(file_path, 'r', encoding=encoding) as f:
            return f.read()
    except Exception:
        return None


def safe_file_write(file_path: Union[str, Path], content: str, encoding: str = 'utf-8') -> bool:
    """
    安全写入文件内容
    
    Args:
        file_path: 文件路径
        content: 文件内容
        encoding: 文件编码
        
    Returns:
        是否写入成功
    """
    try:
        # 确保目录存在
        ensure_directory_exists(os.path.dirname(file_path))
        
        with open(file_path, 'w', encoding=encoding) as f:
            f.write(content)
        return True
    except Exception:
        return False


def get_file_size(file_path: Union[str, Path]) -> int:
    """
    获取文件大小
    
    Args:
        file_path: 文件路径
        
    Returns:
        文件大小（字节），文件不存在返回-1
    """
    try:
        return os.path.getsize(file_path)
    except Exception:
        return -1


def is_file_exists(file_path: Union[str, Path]) -> bool:
    """
    检查文件是否存在
    
    Args:
        file_path: 文件路径
        
    Returns:
        文件是否存在
    """
    return os.path.isfile(file_path)


def is_directory_exists(directory: Union[str, Path]) -> bool:
    """
    检查目录是否存在
    
    Args:
        directory: 目录路径
        
    Returns:
        目录是否存在
    """
    return os.path.isdir(directory)


def get_file_extension(file_path: Union[str, Path]) -> str:
    """
    获取文件扩展名
    
    Args:
        file_path: 文件路径
        
    Returns:
        文件扩展名（包含点号）
    """
    return Path(file_path).suffix


def get_file_name_without_extension(file_path: Union[str, Path]) -> str:
    """
    获取不带扩展名的文件名
    
    Args:
        file_path: 文件路径
        
    Returns:
        不带扩展名的文件名
    """
    return Path(file_path).stem


# 兼容性函数，保持与原有代码的兼容性
def get_rag_directory(*args):
    """兼容性函数"""
    return get_project_base_directory(*args)


def get_rag_python_directory(*args):
    """兼容性函数"""
    return get_project_base_directory("python", *args)
