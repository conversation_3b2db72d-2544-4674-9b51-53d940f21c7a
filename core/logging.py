"""
统一日志配置

按照第一阶段架构重构要求，将原有日志工具迁移到新架构
"""

import os
import logging
from pathlib import Path
from logging.handlers import RotatingFileHandler
from typing import Dict, Optional

from config.base_config import MonitoringConfig

initialized_root_logger = False


def get_project_base_directory() -> str:
    """获取项目根目录"""
    return str(Path(__file__).resolve().parent.parent)


class ZebraGlobalFilter(logging.Filter):
    """Zebra日志过滤器，用于全局过滤ZebraProxy相关的警告日志"""
    
    def filter(self, record):
        if not hasattr(record, 'msg'):
            return True
            
        try:
            message = record.getMessage()
        except:
            message = str(record.msg)
            
        # 过滤包含关键词的日志消息
        keywords = ["Unchange", "Zebra.CrossRegion"]
        for keyword in keywords:
            if keyword in message:
                return False
                
        return True


class ServiceLogFilter(logging.Filter):
    """服务日志过滤器，为日志记录添加服务信息"""
    
    def __init__(self, service_name: str):
        super().__init__()
        self.service_name = service_name
    
    def filter(self, record):
        record.service_name = self.service_name
        return True


def init_logging(config: MonitoringConfig, service_name: str = "shangou_ai_cr") -> None:
    """
    初始化日志系统
    
    Args:
        config: 监控配置
        service_name: 服务名称
    """
    global initialized_root_logger
    if initialized_root_logger:
        return
    initialized_root_logger = True

    logger = logging.getLogger()
    logger.handlers.clear()
    
    # 设置日志级别
    log_level = getattr(logging, config.log_level.upper(), logging.INFO)
    logger.setLevel(log_level)
    
    # 创建日志目录
    if config.log_file:
        log_path = Path(config.log_file)
    else:
        log_dir = Path(get_project_base_directory()) / "logs"
        log_dir.mkdir(exist_ok=True)
        log_path = log_dir / f"{service_name}.log"
    
    log_path.parent.mkdir(parents=True, exist_ok=True)
    
    # 日志格式
    log_format = "%(asctime)-15s %(levelname)-8s [%(service_name)s] %(name)s.%(funcName)s %(process)d %(message)s"
    formatter = logging.Formatter(log_format)
    
    # 文件处理器
    file_handler = RotatingFileHandler(
        log_path, 
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5
    )
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)
    
    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # 捕获警告
    logging.captureWarnings(True)
    
    # 添加过滤器
    zebra_filter = ZebraGlobalFilter()
    service_filter = ServiceLogFilter(service_name)
    
    logger.addFilter(zebra_filter)
    logger.addFilter(service_filter)
    
    for handler in logger.handlers:
        handler.addFilter(zebra_filter)
        handler.addFilter(service_filter)
    
    # 设置第三方库日志级别
    _configure_third_party_loggers()
    
    logger.info(f"日志系统初始化完成 - 文件: {log_path}, 级别: {config.log_level}")


def _configure_third_party_loggers():
    """配置第三方库的日志级别"""
    # 从环境变量读取自定义日志级别
    log_levels_env = os.environ.get("LOG_LEVELS", "")
    pkg_levels = {}
    
    for pkg_name_level in log_levels_env.split(","):
        terms = pkg_name_level.split("=")
        if len(terms) != 2:
            continue
        pkg_name, pkg_level = terms[0].strip(), terms[1].strip()
        pkg_level = getattr(logging, pkg_level.upper(), logging.INFO)
        pkg_levels[pkg_name] = pkg_level
    
    # 默认第三方库日志级别
    default_levels = {
        'peewee': logging.WARNING,
        'pdfminer': logging.WARNING,
        'urllib3': logging.WARNING,
        'requests': logging.WARNING,
        'git': logging.WARNING,
        'octo_rpc': logging.ERROR,
        'mcp': logging.INFO
    }
    
    # 合并配置
    for pkg_name, level in default_levels.items():
        if pkg_name not in pkg_levels:
            pkg_levels[pkg_name] = level
    
    # 应用配置
    for pkg_name, level in pkg_levels.items():
        pkg_logger = logging.getLogger(pkg_name)
        pkg_logger.setLevel(level)


def get_logger(name: str) -> logging.Logger:
    """
    获取指定名称的日志记录器
    
    Args:
        name: 日志记录器名称
        
    Returns:
        日志记录器实例
    """
    return logging.getLogger(name)


def setup_service_logger(service_name: str, log_level: str = "INFO") -> logging.Logger:
    """
    为服务设置专用的日志记录器
    
    Args:
        service_name: 服务名称
        log_level: 日志级别
        
    Returns:
        服务专用的日志记录器
    """
    logger = logging.getLogger(f"service.{service_name}")
    logger.setLevel(getattr(logging, log_level.upper(), logging.INFO))
    
    # 添加服务过滤器
    service_filter = ServiceLogFilter(service_name)
    logger.addFilter(service_filter)
    
    return logger


# 兼容性函数，保持与原有代码的兼容性
def initRootLogger(logfile_basename: str, log_format: str = None):
    """
    初始化根日志记录器（兼容性函数）
    
    Args:
        logfile_basename: 日志文件基础名称
        log_format: 日志格式（已废弃，使用新的格式）
    """
    from config.base_config import MonitoringConfig
    
    config = MonitoringConfig(
        log_level="INFO",
        log_file=f"logs/{logfile_basename}.log"
    )
    
    init_logging(config, logfile_basename)
