"""
数据库连接管理

按照第一阶段架构重构要求，统一数据库连接管理
"""

import logging
import os
from typing import Optional, Dict, Any

from config.base_config import DatabaseConfig, ZebraConfig

# 可选依赖
try:
    import peewee
    from peewee import MySQLDatabase, PostgresqlDatabase, SqliteDatabase
    PEEWEE_AVAILABLE = True
except ImportError:
    PEEWEE_AVAILABLE = False

try:
    from zebra_python_client import ZebraClient
    ZEBRA_AVAILABLE = True
except ImportError:
    ZEBRA_AVAILABLE = False

logger = logging.getLogger(__name__)

# 全局数据库连接实例
_database_instance: Optional[Any] = None
_zebra_client: Optional[Any] = None


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, config: DatabaseConfig):
        self.config = config
        self.database = None
        self.zebra_client = None
        
    def initialize(self) -> bool:
        """初始化数据库连接"""
        try:
            if self.config.default_connection == "zebra":
                return self._init_zebra_connection()
            else:
                return self._init_standard_connection()
        except Exception as e:
            logger.error(f"数据库初始化失败: {str(e)}")
            return False
    
    def _init_zebra_connection(self) -> bool:
        """初始化Zebra连接"""
        if not ZEBRA_AVAILABLE:
            logger.error("Zebra客户端不可用，请安装zebra-python-client")
            return False
        
        try:
            zebra_config = self.config.zebra
            self.zebra_client = ZebraClient(
                app_key=zebra_config.app_key,
                ref_keys=zebra_config.ref_keys,
                pool_size=zebra_config.pool_size,
                pool_timeout=zebra_config.pool_timeout,
                pool_recycle=zebra_config.pool_recycle
            )
            
            # 测试连接
            with self.zebra_client.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT 1")
                cursor.fetchone()
            
            logger.info("Zebra数据库连接初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"Zebra数据库连接初始化失败: {str(e)}")
            return False
    
    def _init_standard_connection(self) -> bool:
        """初始化标准数据库连接"""
        if not PEEWEE_AVAILABLE:
            logger.error("Peewee ORM不可用，请安装peewee")
            return False
        
        try:
            if self.config.type.lower() == "mysql":
                self.database = MySQLDatabase(
                    database=self.config.database,
                    host=self.config.host,
                    port=self.config.port,
                    user=self.config.username,
                    password=self.config.password,
                    charset='utf8mb4',
                    autocommit=True,
                    max_connections=self.config.pool_size
                )
            elif self.config.type.lower() == "postgresql":
                self.database = PostgresqlDatabase(
                    database=self.config.database,
                    host=self.config.host,
                    port=self.config.port,
                    user=self.config.username,
                    password=self.config.password,
                    max_connections=self.config.pool_size
                )
            elif self.config.type.lower() == "sqlite":
                self.database = SqliteDatabase(self.config.database)
            else:
                raise ValueError(f"不支持的数据库类型: {self.config.type}")
            
            # 测试连接
            self.database.connect()
            self.database.execute_sql("SELECT 1")
            self.database.close()
            
            logger.info(f"{self.config.type}数据库连接初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"标准数据库连接初始化失败: {str(e)}")
            return False
    
    def get_connection(self):
        """获取数据库连接"""
        if self.config.default_connection == "zebra":
            if not self.zebra_client:
                raise RuntimeError("Zebra客户端未初始化")
            return self.zebra_client.get_connection()
        else:
            if not self.database:
                raise RuntimeError("数据库连接未初始化")
            return self.database
    
    def execute_sql(self, sql: str, params: Optional[tuple] = None):
        """执行SQL语句"""
        if self.config.default_connection == "zebra":
            with self.zebra_client.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(sql, params or ())
                return cursor.fetchall()
        else:
            return self.database.execute_sql(sql, params or ())
    
    def close(self):
        """关闭数据库连接"""
        try:
            if self.zebra_client:
                self.zebra_client.close()
                self.zebra_client = None
            
            if self.database and not self.database.is_closed():
                self.database.close()
                self.database = None
                
            logger.info("数据库连接已关闭")
        except Exception as e:
            logger.error(f"关闭数据库连接失败: {str(e)}")


def init_database(config: DatabaseConfig) -> DatabaseManager:
    """
    初始化数据库连接
    
    Args:
        config: 数据库配置
        
    Returns:
        数据库管理器实例
    """
    global _database_instance
    
    manager = DatabaseManager(config)
    if manager.initialize():
        _database_instance = manager
        return manager
    else:
        raise RuntimeError("数据库初始化失败")


def get_database() -> Optional[DatabaseManager]:
    """获取全局数据库实例"""
    return _database_instance


def get_database_connection():
    """获取数据库连接（兼容性函数）"""
    if _database_instance:
        return _database_instance.get_connection()
    return None


def execute_sql(sql: str, params: Optional[tuple] = None):
    """执行SQL语句（兼容性函数）"""
    if _database_instance:
        return _database_instance.execute_sql(sql, params)
    raise RuntimeError("数据库未初始化")


def close_database():
    """关闭数据库连接"""
    global _database_instance
    if _database_instance:
        _database_instance.close()
        _database_instance = None


# 兼容性函数，保持与原有代码的兼容性
def get_db():
    """兼容性函数：获取数据库连接"""
    return get_database_connection()


def init_db(config_dict: Dict[str, Any]):
    """兼容性函数：初始化数据库"""
    config = DatabaseConfig(**config_dict)
    return init_database(config)
