"""
智能分层代码审查链
Fast: 纯LLM能力
Standard: LLM智能决策是否检索知识库
Deep: 深度思考链路 + 自检复审
"""

from typing import List, Dict, Any
import time
import json

from langchain_core.runnables import RunnableLambda, RunnableSequence
# 移除不再使用的导入
# from langchain_core.output_parsers import PydanticOutputParser
# from core.types.cr_types import CodeReviewProblemsResponse
from utils.cr_utils import CRUtils, KnowledgeQueryUtils
from utils.cr_prompt_builder import CRPromptFactory, CRPromptBuilder
from utils.json_repair_utils import JSONRepairUtils, repair_cr_json_output


class IntelligentCRChain:
    """智能分层代码审查链"""
    
    def __init__(self, llm, devmind_service, dataset_ids: List[str], mode: str = "standard",
                 language: str = "python", business: str = "default"):
        self.llm = llm
        self.devmind_service = devmind_service
        self.dataset_ids = dataset_ids
        self.mode = mode
        self.language = language
        self.business = business

        # 创建提示词构建器
        try:
            self.prompt_builder = CRPromptFactory.create_builder(language, business)
            print(f"[IntelligentCR] 使用规则集: {language}_{business}")
        except ValueError as e:
            print(f"[IntelligentCR] 规则集不存在，使用默认Python规则: {e}")
            self.prompt_builder = CRPromptFactory.create_builder("python", "default")

        # 性能统计
        self.performance_stats = {
            "total_time": 0.0,
            "llm_calls": 0,
            "knowledge_queries": 0,
            "self_review_rounds": 0
        }
    
    def build_chain(self) -> RunnableSequence:
        """根据模式构建不同的CR链"""
        
        if self.mode == "fast":
            return self._build_fast_chain()
        elif self.mode == "standard":
            return self._build_standard_chain()
        elif self.mode == "deep":
            return self._build_deep_chain()
        else:
            raise ValueError(f"不支持的模式: {self.mode}")
    
    def _build_fast_chain(self) -> RunnableSequence:
        """构建Fast模式链：纯LLM能力"""
        fast_review = RunnableLambda(self._fast_llm_review)
        return fast_review
    
    def _build_standard_chain(self) -> RunnableSequence:
        """构建Standard模式链：LLM智能决策"""
        intelligent_review = RunnableLambda(self._intelligent_review)
        return intelligent_review
    
    def _build_deep_chain(self) -> RunnableSequence:
        """构建Deep模式链：深度思考 + 自检复审"""
        deep_review = RunnableLambda(self._deep_review_with_self_check)
        return deep_review
    
    def _fast_llm_review(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """Fast模式：纯LLM快速审查"""
        start_time = time.time()
        
        diff_content = inputs.get("diff_content", "")
        full_code = inputs.get("full_code", "")
        upstream_str = inputs.get("upstream_str", "无")
        downstream_str = inputs.get("downstream_str", "无")
        
        print(f"[IntelligentCR] Fast模式: 纯LLM快速审查 ({self.language})")

        # 使用配置化的提示词构建器
        fast_prompt = self.prompt_builder.build_fast_mode_prompt(
            diff_content, full_code, upstream_str, downstream_str
        )
        
        try:
            result = self.llm.invoke(fast_prompt).content
            self.performance_stats["llm_calls"] += 1

            # 解析Python字典格式的输出
            parsed_dict = self._parse_dict_string(result)
            problems = parsed_dict.get("problems", [])

            # 增强位置信息
            enhanced_problems = []
            for problem in problems:
                enhanced_problem = CRUtils.enhance_single_problem_position(problem, full_code)
                enhanced_problems.append(enhanced_problem)

            print(f"[IntelligentCR] Fast模式完成，发现 {len(enhanced_problems)} 个问题")
            problems = enhanced_problems

        except Exception as e:
            print(f"[IntelligentCR] Fast模式失败: {e}")
            print(f"[IntelligentCR] 原始结果: {result[:200] if 'result' in locals() else 'N/A'}...")
            problems = []
        
        elapsed = time.time() - start_time
        self.performance_stats["total_time"] = elapsed
        
        return {
            **inputs,
            "final_judge": result,
            "problems": problems,
            "mode": "fast",
            "performance_stats": self.performance_stats
        }
    
    def _intelligent_review(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """Standard模式：LLM智能决策是否需要知识库"""
        start_time = time.time()
        
        diff_content = inputs.get("diff_content", "")
        full_code = inputs.get("full_code", "")
        upstream_str = inputs.get("upstream_str", "无")
        downstream_str = inputs.get("downstream_str", "无")
        
        print("[IntelligentCR] Standard模式: LLM智能决策审查")
        
        # 第一步：LLM决策是否需要知识库
        decision_prompt = self.prompt_builder.build_standard_mode_decision_prompt(
            diff_content, full_code
        )
        
        try:
            decision_result = self.llm.invoke(decision_prompt).content
            self.performance_stats["llm_calls"] += 1

            # 解析Python字典格式的决策结果
            decision = self._parse_dict_string(decision_result)

            need_knowledge = decision.get("need_knowledge", False)
            focus_areas = decision.get("focus_areas", [])

            print(f"[IntelligentCR] LLM决策: {'需要' if need_knowledge else '不需要'}知识库")
            print(f"[IntelligentCR] 关注领域: {focus_areas}")

        except Exception as e:
            print(f"[IntelligentCR] 决策失败: {e}")
            print(f"[IntelligentCR] 原始决策结果: {decision_result[:200] if 'decision_result' in locals() else 'N/A'}...")
            need_knowledge = True  # 默认使用知识库
            focus_areas = ["代码质量"]
            decision = {
                "need_knowledge": need_knowledge,
                "reason": "决策解析失败，使用默认配置",
                "focus_areas": focus_areas,
                "confidence": "low"
            }
        
        # 第二步：根据决策进行审查
        knowledge_content = ""
        if need_knowledge and self.devmind_service:
            knowledge_content = self._query_knowledge_for_areas(focus_areas)
        
        # 第三步：最终审查
        problems = self._perform_standard_review(
            diff_content, full_code, upstream_str, downstream_str, 
            knowledge_content, focus_areas
        )
        
        elapsed = time.time() - start_time
        self.performance_stats["total_time"] = elapsed
        
        return {
            **inputs,
            "problems": problems,
            "mode": "standard",
            "decision": decision,
            "knowledge_used": need_knowledge,
            "performance_stats": self.performance_stats
        }
    
    def _deep_review_with_self_check(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """Deep模式：深度思考链路 + 自检复审"""
        start_time = time.time()
        
        diff_content = inputs.get("diff_content", "")
        full_code = inputs.get("full_code", "")
        upstream_str = inputs.get("upstream_str", "无")
        downstream_str = inputs.get("downstream_str", "无")
        
        print("[IntelligentCR] Deep模式: 深度思考链路 + 自检复审")
        
        # 第一步：深度分析和知识需求评估
        deep_analysis = self._deep_analysis_phase(diff_content, full_code)
        
        # 第二步：知识库查询（如果需要）
        knowledge_content = ""
        if deep_analysis.get("need_knowledge", True):
            knowledge_areas = deep_analysis.get("knowledge_areas", [])
            knowledge_content = self._query_comprehensive_knowledge(knowledge_areas)
        
        # 第三步：初步深度审查
        initial_problems = self._perform_deep_review(
            diff_content, full_code, upstream_str, downstream_str,
            knowledge_content, deep_analysis
        )
        
        # 第四步：自检复审（只有在有问题时才进行）
        if initial_problems:
            final_problems = self._self_review_phase(
                initial_problems, diff_content, full_code, knowledge_content
            )
        else:
            final_problems = initial_problems
            print("[IntelligentCR] 无问题需要自检，跳过复审阶段")
        
        elapsed = time.time() - start_time
        self.performance_stats["total_time"] = elapsed
        
        return {
            **inputs,
            "problems": final_problems,
            "mode": "deep",
            "deep_analysis": deep_analysis,
            "initial_problems_count": len(initial_problems),
            "final_problems_count": len(final_problems),
            "performance_stats": self.performance_stats
        }
    
    def _deep_analysis_phase(self, diff_content: str, full_code: str) -> Dict[str, Any]:
        """深度分析阶段"""
        print(f"[IntelligentCR] 深度分析阶段 ({self.language})")

        analysis_prompt = self.prompt_builder.build_deep_mode_analysis_prompt(
            diff_content, full_code
        )
        
        try:
            result = self.llm.invoke(analysis_prompt).content
            self.performance_stats["llm_calls"] += 1

            # 解析Python字典格式的分析结果
            analysis = self._parse_dict_string(result)

            print(f"[IntelligentCR] 复杂度: {analysis.get('complexity_level', 'unknown')}")
            print(f"[IntelligentCR] 安全风险: {analysis.get('security_risk', 'unknown')}")

            return analysis

        except Exception as e:
            print(f"[IntelligentCR] 深度分析失败: {e}")
            print(f"[IntelligentCR] 原始结果: {result[:200] if 'result' in locals() else 'N/A'}...")
            return {
                "complexity_level": "medium",
                "need_knowledge": True,
                "knowledge_areas": ["代码质量", "安全"],
                "critical_points": ["代码审查"],
                "thinking_chain": "深度分析失败，使用默认配置"
            }
    
    def _query_knowledge_for_areas(self, focus_areas: List[str]) -> str:
        """为特定领域查询知识"""
        if not focus_areas or not self.devmind_service:
            return ""

        knowledge_results = []
        for area in focus_areas[:3]:  # 限制查询数量
            result = KnowledgeQueryUtils.query_devmind_chunks(
                self.devmind_service, area, self.dataset_ids,
                page_size=2, similarity_threshold=0.3
            )
            knowledge_results.append(result)
            self.performance_stats["knowledge_queries"] += 1

        return "\n".join(knowledge_results)
    
    def _query_comprehensive_knowledge(self, knowledge_areas: List[str]) -> str:
        """深度模式的全面知识查询"""
        if not knowledge_areas or not self.devmind_service:
            return ""

        knowledge_results = []
        for area in knowledge_areas:
            result = KnowledgeQueryUtils.query_devmind_chunks(
                self.devmind_service, area, self.dataset_ids,
                page_size=3, similarity_threshold=0.2, vector_similarity_weight=0.5
            )
            knowledge_results.append(result)
            self.performance_stats["knowledge_queries"] += 1

        return "\n".join(knowledge_results)
    
    def _perform_standard_review(self, diff_content: str, full_code: str,
                                upstream_str: str, downstream_str: str,
                                knowledge_content: str, focus_areas: List[str]) -> List[Dict]:
        """执行标准审查"""

        review_prompt = self.prompt_builder.build_standard_mode_review_prompt(
            diff_content, full_code, upstream_str, downstream_str,
            knowledge_content, focus_areas
        )
        
        try:
            result = self.llm.invoke(review_prompt).content
            self.performance_stats["llm_calls"] += 1

            # 解析Python字典格式的审查结果
            parsed_result = self._parse_dict_string(result)
            problems = parsed_result.get("problems", [])

            # 增强位置信息
            enhanced_problems = []
            for problem in problems:
                enhanced_problem = CRUtils.enhance_single_problem_position(problem, full_code)
                enhanced_problems.append(enhanced_problem)

            print(f"[IntelligentCR] 标准审查完成，发现 {len(enhanced_problems)} 个问题")
            return enhanced_problems

        except Exception as e:
            print(f"[IntelligentCR] 标准审查失败: {e}")
            print(f"[IntelligentCR] 原始结果: {result[:200] if 'result' in locals() else 'N/A'}...")
            return []
    
    def _perform_deep_review(self, diff_content: str, full_code: str,
                           upstream_str: str, downstream_str: str,
                           knowledge_content: str, deep_analysis: Dict) -> List[Dict]:
        """执行深度审查"""

        deep_prompt = self.prompt_builder.build_deep_mode_review_prompt(
            diff_content, full_code, upstream_str, downstream_str,
            knowledge_content, deep_analysis
        )

        try:
            result = self.llm.invoke(deep_prompt).content
            self.performance_stats["llm_calls"] += 1

            # 解析Python字典格式的深度审查结果
            parsed_result = self._parse_dict_string(result)
            problems = parsed_result.get("problems", [])

            # 增强位置信息
            enhanced_problems = []
            for problem in problems:
                enhanced_problem = CRUtils.enhance_single_problem_position(problem, full_code)
                enhanced_problems.append(enhanced_problem)

            print(f"[IntelligentCR] 深度审查完成，发现 {len(enhanced_problems)} 个问题")
            print(f"[IntelligentCR] 深度审查结果: {enhanced_problems}")
            return enhanced_problems

        except Exception as e:
            print(f"[IntelligentCR] 深度审查失败: {e}")
            print(f"[IntelligentCR] 原始结果: {result[:200] if 'result' in locals() else 'N/A'}...")
            return []
    
    def _self_review_phase(self, initial_problems: List[Dict], diff_content: str,
                          full_code: str, knowledge_content: str) -> List[Dict]:
        """自检复审阶段"""
        print("[IntelligentCR] 自检复审阶段")
        
        if not initial_problems:
            return initial_problems
        
        self_review_prompt = self.prompt_builder.build_self_review_prompt(
            initial_problems, diff_content, full_code, knowledge_content
        )
        
        try:
            result = self.llm.invoke(self_review_prompt).content
            self.performance_stats["llm_calls"] += 1
            self.performance_stats["self_review_rounds"] += 1

            # 解析Python字典格式的自检结果
            parsed_result = self._parse_dict_string(result)
            final_problems = parsed_result.get("problems", initial_problems)

            # 确保格式正确
            if isinstance(final_problems, list):
                # 增强位置信息
                enhanced_problems = []
                for problem in final_problems:
                    if isinstance(problem, dict):
                        enhanced_problem = CRUtils.enhance_single_problem_position(problem, full_code)
                        enhanced_problems.append(enhanced_problem)

                print(f"[IntelligentCR] 自检复审完成: {len(initial_problems)} -> {len(enhanced_problems)} 个问题")
                print(json.dumps(enhanced_problems, ensure_ascii=False,  indent=2))
                return enhanced_problems

        except Exception as e:
            print(f"[IntelligentCR] 自检复审失败: {e}")
            print(f"[IntelligentCR] 原始自检结果: {result[:200] if 'result' in locals() else 'N/A'}...")
        
        # 如果自检失败，返回原始结果
        return initial_problems
    
    # 移除不再使用的方法
    # def _convert_to_dict_format(self, structured_problems, full_code: str) -> List[Dict]:
    #     """转换为字典格式并增强位置"""
    #     return CRUtils.convert_to_dict_format(structured_problems, full_code)

    def _parse_dict_string(self, dict_str: str) -> dict:
        """安全解析Python字典字符串，集成JSON修复功能"""
        import re
        import ast

        if not dict_str:
            return {}

        print(f"[IntelligentCR] 开始解析字典字符串，长度: {len(dict_str)}")

        try:
            # 首先尝试使用JSON修复工具
            repaired_result = repair_cr_json_output(dict_str)
            if repaired_result and isinstance(repaired_result, dict) and "problems" in repaired_result:
                print(f"[IntelligentCR] JSON修复工具解析成功")
                return repaired_result
        except Exception as e:
            print(f"[IntelligentCR] JSON修复工具失败: {e}")

        try:
            # 第一步：清理字符串
            cleaned = dict_str.strip()

            # 移除控制字符
            cleaned = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\x9f]', '', cleaned)

            # 第二步：提取字典部分
            # 寻找最外层的大括号
            start_idx = cleaned.find('{')
            if start_idx == -1:
                print(f"[IntelligentCR] 未找到字典开始标记: {cleaned[:100]}...")
                return self._create_default_dict()

            # 寻找匹配的结束大括号
            bracket_count = 0
            end_idx = -1
            for i in range(start_idx, len(cleaned)):
                if cleaned[i] == '{':
                    bracket_count += 1
                elif cleaned[i] == '}':
                    bracket_count -= 1
                    if bracket_count == 0:
                        end_idx = i
                        break

            if end_idx == -1:
                print(f"[IntelligentCR] 未找到字典结束标记: {cleaned[:100]}...")
                # 尝试使用JSON修复工具的提取功能
                json_repairer = JSONRepairUtils()
                extracted_result = json_repairer.extract_and_repair_json(cleaned)
                if extracted_result:
                    print(f"[IntelligentCR] JSON提取修复成功")
                    return extracted_result
                return self._create_default_dict()

            dict_part = cleaned[start_idx:end_idx + 1]

            # 第三步：清理注释
            # 移除 # 注释
            lines = dict_part.split('\n')
            cleaned_lines = []
            for line in lines:
                # 简单处理：移除 # 后面的内容（不在字符串内的）
                in_string = False
                quote_char = None
                cleaned_line = ""
                i = 0
                while i < len(line):
                    char = line[i]
                    if not in_string and char == '#':
                        # 遇到注释，停止处理这一行
                        break
                    elif char in ['"', "'"]:
                        if not in_string:
                            in_string = True
                            quote_char = char
                        elif char == quote_char:
                            # 检查是否是转义的引号
                            if i > 0 and line[i-1] != '\\':
                                in_string = False
                                quote_char = None
                    cleaned_line += char
                    i += 1
                cleaned_lines.append(cleaned_line)

            dict_part = '\n'.join(cleaned_lines)

            # 第四步：使用ast.literal_eval安全解析
            try:
                result = ast.literal_eval(dict_part)
                if isinstance(result, dict):
                    return result
                else:
                    print(f"[IntelligentCR] 解析结果不是字典: {type(result)}")
                    return self._create_default_dict()
            except (ValueError, SyntaxError) as e:
                print(f"[IntelligentCR] ast.literal_eval失败: {e}")
                # 尝试JSON修复工具作为备用
                json_repairer = JSONRepairUtils()
                repaired_result = json_repairer.repair_json_string(dict_part)
                try:
                    import json
                    result = json.loads(repaired_result)
                    if isinstance(result, dict):
                        print(f"[IntelligentCR] JSON修复备用解析成功")
                        return result
                except:
                    pass
                # 最后尝试原有的备用解析
                return self._fallback_parse(dict_part)

        except Exception as e:
            print(f"[IntelligentCR] 字典解析异常: {e}")
            print(f"[IntelligentCR] 原始内容: {dict_str[:200]}...")
            return self._create_default_dict()

    def _fallback_parse(self, dict_str: str) -> dict:
        """备用解析方法"""
        import re

        print(f"[IntelligentCR] 进入备用解析，字符串长度: {len(dict_str)}")

        try:
            # 尝试修复常见问题
            fixed = dict_str

            # 修复True/False为Python格式
            fixed = re.sub(r'\btrue\b', 'True', fixed, flags=re.IGNORECASE)
            fixed = re.sub(r'\bfalse\b', 'False', fixed, flags=re.IGNORECASE)
            fixed = re.sub(r'\bnull\b', 'None', fixed, flags=re.IGNORECASE)

            # 尝试修复不完整的字符串
            # 如果字符串以逗号结尾，移除它
            fixed = re.sub(r',\s*$', '', fixed.strip())

            # 如果字符串没有正确关闭，尝试添加缺失的括号
            if fixed.count('{') > fixed.count('}'):
                missing_braces = fixed.count('{') - fixed.count('}')
                fixed += '}' * missing_braces
                print(f"[IntelligentCR] 添加了 {missing_braces} 个缺失的右括号")

            # 如果字符串没有正确关闭数组，尝试添加缺失的方括号
            if fixed.count('[') > fixed.count(']'):
                missing_brackets = fixed.count('[') - fixed.count(']')
                fixed += ']' * missing_brackets
                print(f"[IntelligentCR] 添加了 {missing_brackets} 个缺失的右方括号")

            # 再次尝试ast.literal_eval
            import ast
            result = ast.literal_eval(fixed)
            if isinstance(result, dict):
                print(f"[IntelligentCR] 备用解析成功")
                return result

        except Exception as e:
            print(f"[IntelligentCR] 备用解析也失败: {e}")
            print(f"[IntelligentCR] 修复后的字符串: {fixed[:200]}...")

        # 尝试JSON解析作为最后的尝试
        try:
            import json
            # 再次尝试修复JSON格式
            json_fixed = dict_str

            # 修复JSON格式的布尔值
            json_fixed = re.sub(r'\bTrue\b', 'true', json_fixed)
            json_fixed = re.sub(r'\bFalse\b', 'false', json_fixed)
            json_fixed = re.sub(r'\bNone\b', 'null', json_fixed)

            # 尝试修复不完整的JSON
            if json_fixed.count('{') > json_fixed.count('}'):
                missing_braces = json_fixed.count('{') - json_fixed.count('}')
                json_fixed += '}' * missing_braces

            if json_fixed.count('[') > json_fixed.count(']'):
                missing_brackets = json_fixed.count('[') - json_fixed.count(']')
                json_fixed += ']' * missing_brackets

            result = json.loads(json_fixed)
            if isinstance(result, dict):
                print(f"[IntelligentCR] JSON备用解析成功")
                return result

        except Exception as e:
            print(f"[IntelligentCR] JSON备用解析也失败: {e}")

        # 最后的兜底方案：正则表达式提取
        print(f"[IntelligentCR] 使用正则表达式兜底方案")
        return self._regex_extract_dict(dict_str)

    def _regex_extract_dict(self, text: str) -> dict:
        """使用正则表达式从文本中提取字典信息"""
        import re

        result = {}
        print(f"[IntelligentCR] 使用正则表达式提取，文本长度: {len(text)}")

        # 提取problems数组 - 增强版本，处理不完整的JSON
        problems = []

        # 方法1：尝试提取完整的problems数组
        problems_match = re.search(r'"problems"\s*:\s*\[(.*?)\]', text, re.DOTALL)
        if problems_match:
            problems_text = problems_match.group(1)
            print(f"[IntelligentCR] 找到完整的problems数组")

            # 提取每个问题对象
            problem_pattern = r'\{\s*"level"\s*:\s*"([^"]+)"[^}]*"problem"\s*:\s*"([^"]+)"[^}]*"suggestion"\s*:\s*"([^"]+)"[^}]*\}'
            for match in re.finditer(problem_pattern, problems_text):
                problem = {
                    "level": match.group(1),
                    "problem": match.group(2),
                    "suggestion": match.group(3),
                    "targetCode": "",
                    "codePosition": [1, 0, 1, 10]
                }
                problems.append(problem)
        else:
            # 方法2：尝试提取不完整的problems数组
            print(f"[IntelligentCR] 未找到完整problems数组，尝试提取不完整数组")

            # 查找problems开始位置
            problems_start = re.search(r'"problems"\s*:\s*\[', text)
            if problems_start:
                start_pos = problems_start.end() - 1  # 包含 [
                remaining_text = text[start_pos:]

                # 尝试提取所有可能的问题对象，即使数组没有正确关闭
                problem_objects = re.findall(r'\{[^}]*"level"[^}]*"problem"[^}]*"suggestion"[^}]*\}', remaining_text, re.DOTALL)

                for obj_text in problem_objects:
                    try:
                        # 提取字段
                        level_match = re.search(r'"level"\s*:\s*"([^"]+)"', obj_text)
                        problem_match = re.search(r'"problem"\s*:\s*"([^"]+)"', obj_text)
                        suggestion_match = re.search(r'"suggestion"\s*:\s*"([^"]+)"', obj_text)
                        target_code_match = re.search(r'"targetCode"\s*:\s*"([^"]*)"', obj_text)

                        if level_match and problem_match and suggestion_match:
                            problem = {
                                "level": level_match.group(1),
                                "problem": problem_match.group(1),
                                "suggestion": suggestion_match.group(1),
                                "targetCode": target_code_match.group(1) if target_code_match else "",
                                "codePosition": [1, 0, 1, 10]
                            }
                            problems.append(problem)
                    except Exception as e:
                        print(f"[IntelligentCR] 解析问题对象失败: {e}")
                        continue

            # 方法3：如果还是没有找到，尝试更宽松的匹配
            if not problems:
                print(f"[IntelligentCR] 尝试更宽松的问题提取")

                # 查找所有包含level、problem、suggestion的文本块
                loose_pattern = r'(?:level|问题级别)["\s:]*([p0-9]+|严重|警告|中等|轻微)[^}]*(?:problem|问题)["\s:]*([^"]+)[^}]*(?:suggestion|建议)["\s:]*([^"]+)'
                for match in re.finditer(loose_pattern, text, re.IGNORECASE | re.DOTALL):
                    try:
                        level = match.group(1).lower()
                        if level in ['严重', 'critical']:
                            level = 'p0'
                        elif level in ['警告', 'warning']:
                            level = 'p1'
                        elif level in ['中等', 'moderate']:
                            level = 'p2'
                        elif level in ['轻微', 'minor']:
                            level = 'p3'

                        problem = {
                            "level": level,
                            "problem": match.group(2).strip(),
                            "suggestion": match.group(3).strip(),
                            "targetCode": "",
                            "codePosition": [1, 0, 1, 10]
                        }
                        problems.append(problem)
                    except Exception as e:
                        print(f"[IntelligentCR] 宽松匹配失败: {e}")
                        continue

        result["problems"] = problems
        print(f"[IntelligentCR] 提取到 {len(problems)} 个问题")

        # 提取其他字段
        for field in ["complexity_level", "security_risk", "performance_impact", "thinking_chain"]:
            pattern = f'"{field}"\\s*:\\s*"([^"]+)"'
            match = re.search(pattern, text)
            if match:
                result[field] = match.group(1)

        # 提取布尔字段
        need_knowledge_match = re.search(r'"need_knowledge"\s*:\s*(True|False|true|false)', text, re.IGNORECASE)
        if need_knowledge_match:
            result["need_knowledge"] = need_knowledge_match.group(1).lower() in ['true', 'True']

        # 提取focus_areas字段
        focus_areas = []
        areas_match = re.search(r'"focus_areas"\s*:\s*\[(.*?)\]', text, re.DOTALL)
        if areas_match:
            areas_text = areas_match.group(1)
            area_matches = re.findall(r'"([^"]+)"', areas_text)
            focus_areas.extend(area_matches)

        if focus_areas:
            result["focus_areas"] = focus_areas

        return result

    def _create_default_dict(self) -> dict:
        """创建默认字典"""
        return {
            "problems": [],
            "complexity_level": "medium",
            "security_risk": "medium",
            "need_knowledge": True
        }

    def _extract_json_from_text(self, text: str) -> str:
        """从文本中提取结构化信息并转换为JSON"""
        import re

        # 尝试从文本中提取关键信息
        result = {}

        # 提取复杂度级别
        complexity_match = re.search(r'complexity[_\s]*level["\s]*:?\s*["\']?(\w+)', text, re.IGNORECASE)
        if complexity_match:
            result["complexity_level"] = complexity_match.group(1)

        # 提取安全风险
        security_match = re.search(r'security[_\s]*risk["\s]*:?\s*["\']?(\w+)', text, re.IGNORECASE)
        if security_match:
            result["security_risk"] = security_match.group(1)

        # 提取性能影响
        performance_match = re.search(r'performance[_\s]*impact["\s]*:?\s*["\']?(\w+)', text, re.IGNORECASE)
        if performance_match:
            result["performance_impact"] = performance_match.group(1)

        # 提取是否需要知识库
        need_knowledge_match = re.search(r'need[_\s]*knowledge["\s]*:?\s*(\w+)', text, re.IGNORECASE)
        if need_knowledge_match:
            value = need_knowledge_match.group(1).lower()
            result["need_knowledge"] = value in ['true', 'yes', '是', '需要']

        # 提取知识领域
        knowledge_areas = []
        areas_match = re.search(r'knowledge[_\s]*areas["\s]*:?\s*\[(.*?)\]', text, re.IGNORECASE | re.DOTALL)
        if areas_match:
            areas_text = areas_match.group(1)
            # 提取引号内的内容
            area_matches = re.findall(r'["\']([^"\']+)["\']', areas_text)
            knowledge_areas.extend(area_matches)

        if knowledge_areas:
            result["knowledge_areas"] = knowledge_areas

        # 提取关键点
        critical_points = []
        points_match = re.search(r'critical[_\s]*points["\s]*:?\s*\[(.*?)\]', text, re.IGNORECASE | re.DOTALL)
        if points_match:
            points_text = points_match.group(1)
            # 提取引号内的内容
            point_matches = re.findall(r'["\']([^"\']+)["\']', points_text)
            critical_points.extend(point_matches)

        if critical_points:
            result["critical_points"] = critical_points

        # 提取思考链
        thinking_match = re.search(r'thinking[_\s]*chain["\s]*:?\s*["\']([^"\']+)', text, re.IGNORECASE)
        if thinking_match:
            result["thinking_chain"] = thinking_match.group(1)

        # 如果没有提取到任何信息，返回错误信息
        if not result:
            result = {
                "error": "无法从文本中提取结构化信息",
                "text_preview": text[:200] if len(text) > 200 else text
            }

        # 确保有基本字段
        if "complexity_level" not in result:
            result["complexity_level"] = "medium"
        if "security_risk" not in result:
            result["security_risk"] = "medium"
        if "need_knowledge" not in result:
            result["need_knowledge"] = True

        import json
        return json.dumps(result, ensure_ascii=False)

    # 位置相关函数已移至CRUtils，删除重复代码


def build_intelligent_cr_chain(llm, devmind_service, dataset_ids: List[str], mode: str,
                             language: str = "python", business: str = "default") -> RunnableSequence:
    """构建智能分层CR链"""
    cr_chain = IntelligentCRChain(llm, devmind_service, dataset_ids, mode, language, business)
    return cr_chain.build_chain()
