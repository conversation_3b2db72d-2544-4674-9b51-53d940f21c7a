"""
异步快速高质量的代码审查链
使用DevMind服务的aretrieve_chunks方法进行异步知识检索
"""

from typing import List, Dict, Any, Optional
import time
import json
import asyncio
from utils.thread_pool_manager import create_safe_thread_pool

from langchain_core.runnables import RunnableLambda, RunnableSequence
from langchain_core.output_parsers import PydanticOutputParser
from core.analyzers.code_position_analyzer import CodePositionAnalyzer
from core.types.cr_types import CodeReviewProblemsResponse


class AsyncFastCRChain:
    """异步快速高质量的代码审查链"""
    
    def __init__(self, llm, devmind_service, dataset_ids: List[str]):
        self.llm = llm
        self.devmind_service = devmind_service
        self.dataset_ids = dataset_ids
        
        # 性能优化配置
        self.knowledge_cache = {}
        self.max_knowledge_queries = 3  # 限制知识查询数量
        self.query_timeout = 5  # 异步查询超时时间
        
    def build_chain(self) -> RunnableSequence:
        """构建异步快速CR链"""
        
        # 步骤1: 快速问题识别
        quick_analysis = RunnableLambda(self._quick_analysis)
        
        # 步骤2: 异步智能知识增强
        async_knowledge_enhancement = RunnableLambda(self._async_knowledge_enhancement)
        
        # 步骤3: 精确问题定位
        precise_review = RunnableLambda(self._precise_review)
        
        return quick_analysis | async_knowledge_enhancement | precise_review
    
    def _quick_analysis(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """步骤1: 快速问题识别"""
        start_time = time.time()
        
        diff_content = inputs.get("diff_content", "")
        full_code = inputs.get("full_code", "")
        
        print("[AsyncFastCR] 步骤1: 快速问题识别")
        
        # 快速分析prompt
        quick_prompt = f"""
作为代码审查专家，快速分析以下代码的潜在问题：

代码变更: {diff_content}
完整代码: {full_code}

请识别：
1. 明显的代码问题（安全、性能、规范）
2. 需要查询知识库的关键词
3. 代码风险等级

JSON格式输出：
{{
  "issues": ["问题1", "问题2"],
  "keywords": ["关键词1", "关键词2"],
  "risk_level": "low|medium|high",
  "needs_knowledge": true/false
}}
"""
        
        try:
            result = self.llm.invoke(quick_prompt).content
            analysis = json.loads(result)
            print(f"[AsyncFastCR] 快速分析完成: {analysis.get('risk_level', 'unknown')}风险")
        except Exception as e:
            print(f"[AsyncFastCR] 快速分析失败: {e}")
            analysis = {
                "issues": [],
                "keywords": ["代码质量"],
                "risk_level": "medium",
                "needs_knowledge": True
            }
        
        elapsed = time.time() - start_time
        print(f"[AsyncFastCR] 步骤1耗时: {elapsed:.2f}s")
        
        return {**inputs, "quick_analysis": analysis}
    
    def _async_knowledge_enhancement(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """步骤2: 异步智能知识增强"""
        start_time = time.time()
        
        quick_analysis = inputs.get("quick_analysis", {})
        
        print("[AsyncFastCR] 步骤2: 异步智能知识增强")
        
        # 判断是否需要知识查询
        if not quick_analysis.get("needs_knowledge", True):
            print("[AsyncFastCR] 跳过知识查询")
            return {**inputs, "knowledge": "无需额外知识"}
        
        # 异步并行查询知识库
        keywords = quick_analysis.get("keywords", [])[:self.max_knowledge_queries]
        knowledge = asyncio.run(self._async_parallel_knowledge_query(keywords))
        
        elapsed = time.time() - start_time
        print(f"[AsyncFastCR] 步骤2耗时: {elapsed:.2f}s")
        
        return {**inputs, "knowledge": knowledge}
    
    async def _async_parallel_knowledge_query(self, keywords: List[str]) -> str:
        """异步并行知识查询"""
        if not keywords:
            return "无相关知识"
        
        knowledge_results = []
        
        # 创建异步任务
        tasks = []
        for keyword in keywords:
            if keyword in self.knowledge_cache:
                knowledge_results.append(self.knowledge_cache[keyword])
                continue
            
            task = asyncio.create_task(self._async_query_single_knowledge(keyword))
            tasks.append((keyword, task))
        
        # 等待所有任务完成（带超时）
        try:
            for keyword, task in tasks:
                result = await asyncio.wait_for(task, timeout=self.query_timeout)
                knowledge_results.append(result)
                # 缓存结果
                self.knowledge_cache[keyword] = result
                
        except asyncio.TimeoutError:
            print(f"[AsyncFastCR] 知识查询超时")
        except Exception as e:
            print(f"[AsyncFastCR] 异步知识查询失败: {e}")
        
        return "\n".join(knowledge_results) if knowledge_results else "无相关知识"
    
    async def _async_query_single_knowledge(self, keyword: str) -> str:
        """异步查询单个知识点"""
        try:
            if self.devmind_service:
                # 使用DevMind服务的aretrieve_chunks异步方法
                response = await self.devmind_service.aretrieve_chunks(
                    question=keyword,
                    dataset_ids=self.dataset_ids,
                    page_size=3,  # 每个关键词只取3个结果
                    similarity_threshold=0.3,  # 提高相似度阈值
                    vector_similarity_weight=0.5,
                    keyword=True,  # 启用关键词匹配
                    highlight=False  # 不需要高亮
                )
                
                # 解析响应，提取chunks内容
                chunks = response.get('data', {}).get('chunks', [])
                if chunks:
                    # 合并前几个chunk的内容
                    contents = []
                    for chunk in chunks[:2]:  # 只取前2个最相关的
                        content = chunk.get('content', '').strip()
                        if content:
                            contents.append(content[:150])  # 限制每个chunk长度
                    
                    if contents:
                        combined_content = ' | '.join(contents)
                        return f"{keyword}: {combined_content}"
                
                return f"{keyword}: 未找到相关知识"
            return f"{keyword}: 无知识库连接"
        except Exception as e:
            print(f"[AsyncFastCR] 异步知识查询异常 {keyword}: {e}")
            return f"{keyword}: 查询失败"
    
    def _precise_review(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """步骤3: 精确问题定位"""
        start_time = time.time()
        
        diff_content = inputs.get("diff_content", "")
        full_code = inputs.get("full_code", "")
        upstream_str = inputs.get("upstream_str", "无")
        downstream_str = inputs.get("downstream_str", "无")
        quick_analysis = inputs.get("quick_analysis", {})
        knowledge = inputs.get("knowledge", "")
        
        print("[AsyncFastCR] 步骤3: 精确问题定位")
        
        # 创建结构化输出解析器
        parser = PydanticOutputParser(pydantic_object=CodeReviewProblemsResponse)
        
        # 聚焦的审查prompt
        issues = quick_analysis.get("issues", [])
        risk_level = quick_analysis.get("risk_level", "medium")
        
        precise_prompt = f"""
基于快速分析和异步知识库检索，进行精确的代码审查：

=== 代码信息 ===
变更: {diff_content}
完整代码: {full_code}
上游依赖: {upstream_str}
下游依赖: {downstream_str}

=== 快速分析结果 ===
发现问题: {', '.join(issues)}
风险等级: {risk_level}

=== 异步知识库信息 ===
{knowledge}

=== 审查要求 ===
1. 重点关注已识别的问题: {', '.join(issues)}
2. 结合异步检索的知识库给出具体建议
3. 提供精确的4点坐标定位
4. 如果无明显问题，返回空数组

{parser.get_format_instructions()}
"""
        
        try:
            result = self.llm.invoke(precise_prompt).content
            
            # 解析结果
            structured_response = parser.parse(result)
            problems = self._convert_to_dict_format(structured_response.problems, full_code)
            
            print(f"[AsyncFastCR] 发现 {len(problems)} 个问题")
            
        except Exception as e:
            print(f"[AsyncFastCR] 精确审查失败: {e}")
            # Fallback处理
            try:
                problems = json.loads(result)
                problems = self._enhance_positions(problems, full_code)
            except:
                problems = []
        
        elapsed = time.time() - start_time
        print(f"[AsyncFastCR] 步骤3耗时: {elapsed:.2f}s")
        
        return {
            **inputs,
            "final_judge": result,
            "problems": problems
        }
    
    def _convert_to_dict_format(self, structured_problems, full_code: str) -> List[Dict]:
        """转换为字典格式并增强位置"""
        problems = []
        
        for problem in structured_problems:
            problem_dict = {
                "level": problem.level.value.upper(),
                "problem": problem.problem,
                "suggestion": problem.suggestion,
                "targetCode": problem.targetCode,
                "codePosition": problem.codePosition
            }
            
            # 验证和增强位置信息
            if not self._is_valid_position(problem_dict.get('codePosition')):
                problem_dict = self._enhance_position(problem_dict, full_code)
            
            problems.append(problem_dict)
        
        return problems
    
    def _is_valid_position(self, position) -> bool:
        """验证位置格式是否正确"""
        return (isinstance(position, list) and 
                len(position) == 4 and 
                all(isinstance(x, int) for x in position))
    
    def _enhance_position(self, problem: Dict, code_content: str) -> Dict:
        """增强单个问题的位置信息"""
        try:
            analyzer = CodePositionAnalyzer(code_content)
            target_code = problem.get('targetCode', '')
            
            position = analyzer.find_text_position(target_code.strip())
            if position:
                problem['codePosition'] = position.to_array()
            else:
                problem['codePosition'] = [1, 0, 1, 0]
                
        except Exception as e:
            print(f"[AsyncFastCR] 位置增强失败: {e}")
            problem['codePosition'] = [1, 0, 1, 0]
        
        return problem
    
    def _enhance_positions(self, problems: List[Dict], code_content: str) -> List[Dict]:
        """批量增强位置信息"""
        return [self._enhance_position(p, code_content) for p in problems]


def build_async_fast_cr_chain(llm, devmind_service, dataset_ids: List[str]) -> RunnableSequence:
    """构建异步快速CR链"""
    cr_chain = AsyncFastCRChain(llm, devmind_service, dataset_ids)
    return cr_chain.build_chain()


# 使用示例
def create_async_production_cr_chain(llm, devmind_service, dataset_ids: List[str]):
    """创建异步生产环境的CR链"""
    
    # 配置优化参数
    chain_config = {
        "max_knowledge_queries": 3,  # 限制知识查询数量
        "query_timeout": 5,          # 异步查询超时时间
        "enable_cache": True,        # 启用缓存
        "async_processing": True     # 启用异步处理
    }
    
    print(f"[AsyncFastCR] 创建异步生产CR链，配置: {chain_config}")
    
    return build_async_fast_cr_chain(llm, devmind_service, dataset_ids)
