"""
快速高质量的代码审查链
兼顾速度和质量的优化版本
"""

from typing import List, Dict, Any
import time
import json
from utils.thread_pool_manager import create_safe_thread_pool, safe_as_completed

from langchain_core.runnables import RunnableLambda, RunnableSequence
from langchain_core.output_parsers import PydanticOutputParser
from core.types.cr_types import CodeReviewProblemsResponse
from utils.cr_utils import CRUtils, KnowledgeQueryUtils


class FastCRChain:
    """快速高质量的代码审查链"""

    def __init__(self, llm, devmind_service, dataset_ids: List[str]):
        self.llm = llm
        self.devmind_service = devmind_service
        self.dataset_ids = dataset_ids

        # 性能优化配置
        self.knowledge_cache = {}
        self.max_knowledge_queries = 3  # 限制知识查询数量
        self.query_timeout = 3  # 知识查询超时时间

    def build_chain(self) -> RunnableSequence:
        """构建快速CR链"""

        # 步骤1: 快速问题识别
        quick_analysis = RunnableLambda(self._quick_analysis)

        # 步骤2: 智能知识增强
        knowledge_enhancement = RunnableLambda(self._knowledge_enhancement)

        # 步骤3: 精确问题定位
        precise_review = RunnableLambda(self._precise_review)

        return quick_analysis | knowledge_enhancement | precise_review

    def _quick_analysis(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """步骤1: 快速问题识别"""
        start_time = time.time()

        diff_content = inputs.get("diff_content", "")
        full_code = inputs.get("full_code", "")

        print("[FastCR] 步骤1: 快速问题识别")

        # 快速分析prompt
        quick_prompt = f"""
作为代码审查专家，快速分析以下代码的潜在问题：

代码变更: {diff_content}
完整代码: {full_code}

请识别：
1. 明显的代码问题（安全、性能、规范）
2. 需要查询知识库的关键词
3. 代码风险等级

JSON格式输出：
{{
  "issues": ["问题1", "问题2"],
  "keywords": ["关键词1", "关键词2"],
  "risk_level": "low|medium|high",
  "needs_knowledge": true/false
}}
"""

        try:
            result = self.llm.invoke(quick_prompt).content
            analysis = json.loads(result)
            print(f"[FastCR] 快速分析完成: {analysis.get('risk_level', 'unknown')}风险")
        except Exception as e:
            print(f"[FastCR] 快速分析失败: {e}")
            analysis = {
                "issues": [],
                "keywords": ["代码质量"],
                "risk_level": "medium",
                "needs_knowledge": True
            }

        elapsed = time.time() - start_time
        print(f"[FastCR] 步骤1耗时: {elapsed:.2f}s")

        return {**inputs, "quick_analysis": analysis}

    def _knowledge_enhancement(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """步骤2: 智能知识增强"""
        start_time = time.time()

        quick_analysis = inputs.get("quick_analysis", {})

        print("[FastCR] 步骤2: 智能知识增强")

        # 判断是否需要知识查询
        if not quick_analysis.get("needs_knowledge", True):
            print("[FastCR] 跳过知识查询")
            return {**inputs, "knowledge": "无需额外知识"}

        # 并行查询知识库
        keywords = quick_analysis.get("keywords", [])[:self.max_knowledge_queries]
        knowledge = self._parallel_knowledge_query(keywords)

        elapsed = time.time() - start_time
        print(f"[FastCR] 步骤2耗时: {elapsed:.2f}s")

        return {**inputs, "knowledge": knowledge}

    def _parallel_knowledge_query(self, keywords: List[str]) -> str:
        """并行知识查询"""
        if not keywords:
            return "无相关知识"

        knowledge_results = []

        # 使用安全的线程池
        with create_safe_thread_pool("fast_cr_knowledge", max_workers=3) as executor:
            # 提交查询任务
            future_to_keyword = {}
            for keyword in keywords:
                if keyword in self.knowledge_cache:
                    knowledge_results.append(self.knowledge_cache[keyword])
                    continue

                future = executor.submit(self._query_single_knowledge, keyword)
                if future:  # 检查是否成功提交
                    future_to_keyword[future] = keyword

            # 收集结果（带超时）
            for future in safe_as_completed(future_to_keyword, timeout=self.query_timeout):
                keyword = future_to_keyword[future]
                try:
                    result = future.result()
                    knowledge_results.append(result)
                    self.knowledge_cache[keyword] = result
                except Exception as e:
                    print(f"[FastCR] 知识查询失败 {keyword}: {e}")

        return "\n".join(knowledge_results) if knowledge_results else "无相关知识"

    def _query_single_knowledge(self, keyword: str) -> str:
        """查询单个知识点"""
        return KnowledgeQueryUtils.query_devmind_chunks(
            self.devmind_service, keyword, self.dataset_ids,
            page_size=3, similarity_threshold=0.3, vector_similarity_weight=0.5
        )

    def _precise_review(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """步骤3: 精确问题定位"""
        start_time = time.time()

        diff_content = inputs.get("diff_content", "")
        full_code = inputs.get("full_code", "")
        upstream_str = inputs.get("upstream_str", "无")
        downstream_str = inputs.get("downstream_str", "无")
        quick_analysis = inputs.get("quick_analysis", {})
        knowledge = inputs.get("knowledge", "")

        print("[FastCR] 步骤3: 精确问题定位")

        # 创建结构化输出解析器
        parser = PydanticOutputParser(pydantic_object=CodeReviewProblemsResponse)

        # 聚焦的审查prompt
        issues = quick_analysis.get("issues", [])
        risk_level = quick_analysis.get("risk_level", "medium")

        precise_prompt = f"""
基于快速分析和知识库，进行精确的代码审查：

=== 代码信息 ===
变更: {diff_content}
完整代码: {full_code}
上游依赖: {upstream_str}
下游依赖: {downstream_str}

=== 快速分析结果 ===
发现问题: {', '.join(issues)}
风险等级: {risk_level}

=== 知识库信息 ===
{knowledge}

=== 审查要求 ===
1. 重点关注已识别的问题: {', '.join(issues)}
2. 结合知识库给出具体建议
3. 提供精确的4点坐标定位
4. 如果无明显问题，返回空数组

{parser.get_format_instructions()}
"""

        try:
            result = self.llm.invoke(precise_prompt).content

            # 解析结果
            structured_response = parser.parse(result)
            problems = self._convert_to_dict_format(structured_response.problems, full_code)

            print(f"[FastCR] 发现 {len(problems)} 个问题")

        except Exception as e:
            print(f"[FastCR] 精确审查失败: {e}")
            # Fallback处理
            try:
                problems = json.loads(result)
                problems = self._enhance_positions(problems, full_code)
            except:
                problems = []

        elapsed = time.time() - start_time
        print(f"[FastCR] 步骤3耗时: {elapsed:.2f}s")

        return {
            **inputs,
            "final_judge": result,
            "problems": problems
        }

    def _convert_to_dict_format(self, structured_problems, full_code: str) -> List[Dict]:
        """转换为字典格式并增强位置"""
        return CRUtils.convert_to_dict_format(structured_problems, full_code)

    # 位置相关函数已移至CRUtils，删除重复代码


def build_fast_cr_chain(llm, devmind_service, dataset_ids: List[str]) -> RunnableSequence:
    """构建快速CR链"""
    cr_chain = FastCRChain(llm, devmind_service, dataset_ids)
    return cr_chain.build_chain()


# 性能监控装饰器
def monitor_performance(func):
    """性能监控装饰器"""
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        elapsed = time.time() - start_time
        print(f"[性能监控] {func.__name__} 耗时: {elapsed:.2f}s")
        return result
    return wrapper


# 使用示例
def create_production_cr_chain(llm, devmind_service, dataset_ids: List[str]):
    """创建生产环境的CR链"""

    # 配置优化参数
    chain_config = {
        "max_knowledge_queries": 3,  # 限制知识查询数量
        "query_timeout": 3,          # 查询超时时间
        "enable_cache": True,        # 启用缓存
        "parallel_processing": True   # 启用并行处理
    }

    print(f"[FastCR] 创建生产CR链，配置: {chain_config}")

    return build_fast_cr_chain(llm, devmind_service, dataset_ids)