"""
优化的代码审查链 - 兼顾质量和速度
"""

from typing import List, Dict, Any, Optional
import time
# 使用安全的线程池管理器
# from concurrent.futures import ThreadPoolExecutor, as_completed

from langchain_core.runnables import RunnableLambda, RunnableSerializable
from langchain_core.output_parsers import PydanticOutputParser
from core.types.cr_types import CodeReviewProblemsResponse
from utils.cr_utils import CRUtils, KnowledgeQueryUtils


class OptimizedCRChain:
    """优化的代码审查链"""
    
    def __init__(self, llm, devmind_service, dataset_ids: List[str], 
                 max_retrieval_rounds: int = 2, chain_options: Optional[Dict[str, Any]] = None):
        self.llm = llm
        self.devmind_service = devmind_service
        self.dataset_ids = dataset_ids
        self.max_retrieval_rounds = max_retrieval_rounds
        self.chain_options = chain_options or {}
        
        # 缓存机制
        self.knowledge_cache = {}
        self.analysis_cache = {}
        
        # 性能统计
        self.performance_stats = {
            "total_time": 0.0,
            "preliminary_time": 0.0,
            "knowledge_time": 0.0,
            "final_time": 0.0,
            "cache_hits": 0
        }
    
    def build_chain(self) -> RunnableSerializable[dict[str, Any], dict[str, Any]]:
        """构建优化的CR链"""
        
        # 步骤1: 快速初步审查（并行）
        preliminary_review = RunnableLambda(self._preliminary_review)
        
        # 步骤2: 智能知识召回（按需）
        knowledge_retrieval = RunnableLambda(self._smart_knowledge_retrieval)
        
        # 步骤3: 最终精确审查（聚焦）
        final_review = RunnableLambda(self._final_review)
        
        # 构建链
        chain = preliminary_review | knowledge_retrieval | final_review
        
        return chain
    
    def _preliminary_review(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """步骤1: 快速初步审查 - 识别代码意图和潜在问题"""
        start_time = time.time()
        
        diff_content = inputs.get("diff_content", "")
        full_code = inputs.get("full_code", "")
        
        print("[优化CR] 步骤1: 快速初步审查")
        
        # 快速分析prompt - 重点关注效率
        preliminary_prompt = f"""
你是一名高级代码审查专家。请对以下代码进行快速初步分析：

代码变更:
{diff_content}

完整代码:
{full_code}

请快速识别：
1. 代码意图和功能
2. 明显的问题类型（安全、性能、规范等）
3. 需要深入分析的关键点

输出格式（JSON）：
{{
  "code_intent": "代码的主要功能和意图",
  "obvious_issues": ["明显问题1", "明显问题2"],
  "analysis_focus": ["需要重点分析的方面1", "需要重点分析的方面2"],
  "risk_level": "low|medium|high",
  "needs_deep_analysis": true/false
}}

要求：快速分析，重点突出，不要过度详细。
"""
        
        try:
            result = self.llm.invoke(preliminary_prompt).content
            print(f"[优化CR] 初步审查结果: {result[:200]}...")
            
            # 解析结果
            import json
            preliminary_analysis = json.loads(result)
            
            # 缓存分析结果
            cache_key = hash(diff_content + full_code)
            self.analysis_cache[cache_key] = preliminary_analysis
            
        except Exception as e:
            print(f"[优化CR] 初步审查失败: {e}")
            preliminary_analysis = {
                "code_intent": "代码分析",
                "obvious_issues": [],
                "analysis_focus": ["代码质量"],
                "risk_level": "medium",
                "needs_deep_analysis": True
            }
        
        self.performance_stats["preliminary_time"] = time.time() - start_time
        
        return {
            **inputs,
            "preliminary_analysis": preliminary_analysis
        }
    
    def _smart_knowledge_retrieval(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """步骤2: 智能知识召回 - 基于意图和问题召回相关知识"""
        start_time = time.time()
        
        preliminary_analysis = inputs.get("preliminary_analysis", {})
        
        print("[优化CR] 步骤2: 智能知识召回")
        
        # 判断是否需要深度分析
        if not preliminary_analysis.get("needs_deep_analysis", True):
            print("[优化CR] 跳过知识召回 - 代码质量良好")
            return {
                **inputs,
                "retrieved_knowledge": "无需额外知识库查询"
            }
        
        # 构建知识查询关键词
        query_keywords = []
        
        # 基于代码意图
        code_intent = preliminary_analysis.get("code_intent", "")
        if code_intent:
            query_keywords.append(code_intent)
        
        # 基于明显问题
        obvious_issues = preliminary_analysis.get("obvious_issues", [])
        query_keywords.extend(obvious_issues)
        
        # 基于分析焦点
        analysis_focus = preliminary_analysis.get("analysis_focus", [])
        query_keywords.extend(analysis_focus)
        
        # 并行知识召回
        retrieved_knowledge = self._parallel_knowledge_retrieval(query_keywords)
        
        self.performance_stats["knowledge_time"] = time.time() - start_time
        
        return {
            **inputs,
            "retrieved_knowledge": retrieved_knowledge
        }
    
    def _parallel_knowledge_retrieval(self, query_keywords: List[str]) -> str:
        """并行知识召回"""
        if not query_keywords:
            return "无相关知识"
        
        knowledge_results = []
        
        # 使用安全的线程池并行查询
        from utils.thread_pool_manager import create_safe_thread_pool, safe_as_completed

        with create_safe_thread_pool("optimized_cr_knowledge", max_workers=3) as executor:
            future_to_keyword = {}

            for keyword in query_keywords[:3]:  # 限制查询数量以控制速度
                # 检查缓存
                if keyword in self.knowledge_cache:
                    knowledge_results.append(self.knowledge_cache[keyword])
                    self.performance_stats["cache_hits"] += 1
                    continue

                # 提交查询任务
                future = executor.submit(self._query_knowledge, keyword)
                if future:  # 检查是否成功提交
                    future_to_keyword[future] = keyword

            # 收集结果
            for future in safe_as_completed(future_to_keyword, timeout=5):  # 5秒超时
                keyword = future_to_keyword[future]
                try:
                    result = future.result()
                    knowledge_results.append(result)
                    # 缓存结果
                    self.knowledge_cache[keyword] = result
                except Exception as e:
                    print(f"[优化CR] 知识查询失败 {keyword}: {e}")
        
        return "\n".join(knowledge_results) if knowledge_results else "无相关知识"
    
    def _query_knowledge(self, keyword: str) -> str:
        """查询单个知识点"""
        return KnowledgeQueryUtils.query_devmind_chunks(
            self.devmind_service, keyword, self.dataset_ids,
            page_size=2, similarity_threshold=0.3, vector_similarity_weight=0.5
        )
    
    def _final_review(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """步骤3: 最终精确审查 - 基于知识进行深度分析"""
        global result
        start_time = time.time()
        
        diff_content = inputs.get("diff_content", "")
        full_code = inputs.get("full_code", "")
        upstream_str = inputs.get("upstream_str", "无")
        downstream_str = inputs.get("downstream_str", "无")
        preliminary_analysis = inputs.get("preliminary_analysis", {})
        retrieved_knowledge = inputs.get("retrieved_knowledge", "")
        
        print("[优化CR] 步骤3: 最终精确审查")
        
        # 创建结构化输出解析器
        parser = PydanticOutputParser(pydantic_object=CodeReviewProblemsResponse)
        
        # 构建聚焦的最终审查prompt
        risk_level = preliminary_analysis.get("risk_level", "medium")
        analysis_focus = preliminary_analysis.get("analysis_focus", [])
        
        final_prompt = f"""
你是一名企业级代码审查专家。基于初步分析和知识库信息，进行最终精确审查。

=== 代码信息 ===
代码变更: {diff_content}
完整代码: {full_code}
正向依赖: {upstream_str}
反向依赖: {downstream_str}

=== 初步分析结果 ===
风险级别: {risk_level}
分析重点: {', '.join(analysis_focus)}
代码意图: {preliminary_analysis.get('code_intent', '未知')}

=== 相关知识库 ===
{retrieved_knowledge}

=== 审查要求 ===
1. 重点关注: {', '.join(analysis_focus)}
2. 结合知识库内容进行深度分析
3. 提供精确的代码位置定位
4. 给出具体可行的修改建议

{parser.get_format_instructions()}

注意：
- 如果代码质量良好且无明显问题，返回空数组
- codePosition使用4点坐标格式：[startLine, startColumn, endLine, endColumn]
- 在targetCode中提供精确的代码片段
"""
        
        try:
            result = self.llm.invoke(final_prompt).content
            print(f"[优化CR] 最终审查结果: {result[:200]}...")
            
            # 解析结果
            structured_response = parser.parse(result)
            structured_problems = structured_response.problems
            
            # 转换为字典格式并进行位置增强
            enhanced_problems = []
            for problem in structured_problems:
                problem_dict = {
                    "level": problem.level.value.upper(),
                    "problem": problem.problem,
                    "suggestion": problem.suggestion,
                    "targetCode": problem.targetCode,
                    "codePosition": problem.codePosition
                }
                
                # 如果需要，进行位置增强
                if (not isinstance(problem_dict.get('codePosition'), list) or
                    len(problem_dict['codePosition']) != 4):
                    enhanced_problem = CRUtils.enhance_single_problem_position(problem_dict, full_code)
                    enhanced_problems.append(enhanced_problem)
                else:
                    enhanced_problems.append(problem_dict)
            
            print(f"[优化CR] 最终问题数量: {len(enhanced_problems)}")
            
        except Exception as e:
            print(f"[优化CR] 最终审查失败: {e}")
            # Fallback到JSON解析
            try:
                import json
                problems = json.loads(result)
                enhanced_problems = CRUtils.enhance_problems_with_positions(problems, full_code)
            except:
                enhanced_problems = []
        
        self.performance_stats["final_time"] = time.time() - start_time
        self.performance_stats["total_time"] = (
            self.performance_stats["preliminary_time"] + 
            self.performance_stats["knowledge_time"] + 
            self.performance_stats["final_time"]
        )
        
        # 输出性能统计
        print(f"[优化CR] 性能统计: 总时间{self.performance_stats['total_time']:.2f}s, "
              f"缓存命中{self.performance_stats['cache_hits']}次")
        
        return {
            **inputs,
            "final_judge": result,
            "problems": enhanced_problems,
            "performance_stats": self.performance_stats
        }
    
    # 位置增强函数已移至CRUtils，删除重复代码


def build_optimized_cr_chain(llm, devmind_service, dataset_ids: List[str], 
                           max_retrieval_rounds: int = 2,
                           chain_options: Optional[Dict[str, Any]] = None) -> RunnableSerializable[
    dict[str, Any], dict[str, Any]]:
    """构建优化的CR链"""
    cr_chain = OptimizedCRChain(llm, devmind_service, dataset_ids, max_retrieval_rounds, chain_options)
    return cr_chain.build_chain()
