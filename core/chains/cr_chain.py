import json
from typing import List, Dict, Any, Optional

from langchain_core.runnables import RunnableLambda, RunnableSequence
from langchain_core.output_parsers import PydanticOutputParser
from core.analyzers.code_position_analyzer import CodePositionAnalyzer
from core.types.cr_types import CodeReviewProblemsResponse


def enhance_problems_with_positions(problems: List[Dict], code_content: str) -> List[Dict]:
    """
    为问题列表增强精确的代码位置信息
    注意：此函数保留用于向后兼容，新代码请使用CRUtils.enhance_problems_with_positions
    """
    if not problems or not code_content:
        return problems

    enhanced_problems = []
    analyzer = CodePositionAnalyzer(code_content)

    for problem in problems:
        try:
            enhanced_problem = problem.copy()

            # 获取问题相关信息
            target_code = problem.get('targetCode', '')
            problem_desc = problem.get('problem', '')
            level = problem.get('level', 'P2')

            # 尝试多种方式定位代码位置
            position = None
            import re

            # 1. 如果targetCode包含异常声明，尝试定位异常
            if 'throws Exception' in target_code or 'except' in target_code:
                if 'throws Exception' in target_code:
                    method_match = re.search(r'(\w+)\s*\([^)]*\)\s*throws\s+Exception', target_code)
                    if method_match:
                        method_name = method_match.group(1)
                        position = analyzer.find_exception_declaration_position(method_name)
                elif 'except' in target_code:
                    position = analyzer.find_text_position('except')

            # 2. 如果targetCode包含注解，尝试定位注解
            elif target_code.startswith('@'):
                annotation_name = target_code.split('(')[0][1:]  # 去掉@和参数
                position = analyzer.find_annotation_position(annotation_name)

            # 3. 如果targetCode包含方法调用，尝试定位方法调用
            elif '.' in target_code and '(' in target_code and not target_code.startswith('def '):
                method_call = target_code.split('(')[0].strip()
                position = analyzer.find_method_call_position(method_call)

            # 4. 如果targetCode包含函数定义，尝试定位函数
            elif target_code.startswith('def ') or 'def ' in target_code:
                func_match = re.search(r'def\s+(\w+)', target_code)
                if func_match:
                    func_name = func_match.group(1)
                    position = analyzer.find_method_signature_position(func_name)

            # 5. 如果targetCode包含类声明，尝试定位类
            elif 'class ' in target_code:
                class_match = re.search(r'class\s+(\w+)', target_code)
                if class_match:
                    class_name = class_match.group(1)
                    position = analyzer.find_class_declaration_position(class_name)

            # 6. 如果targetCode包含变量赋值，尝试定位变量
            elif '=' in target_code and not target_code.startswith('if ') and not target_code.startswith('elif '):
                var_match = re.search(r'(\w+)\s*=', target_code)
                if var_match:
                    var_name = var_match.group(1)
                    position = analyzer.find_variable_declaration_position(var_name)

            # 7. 如果targetCode包含import语句，尝试定位import
            elif target_code.startswith('import ') or target_code.startswith('from '):
                position = analyzer.find_text_position(target_code.strip())

            # 8. 特殊处理：环境变量访问
            elif 'os.environ' in target_code:
                position = analyzer.find_text_position('os.environ')

            # 9. 特殊处理：try-except块
            elif problem_desc and ('异常处理' in problem_desc or 'except' in problem_desc.lower()):
                position = analyzer.find_text_position('except') or analyzer.find_text_position('try')

            # 10. 最后尝试直接文本搜索（使用targetCode的关键部分）
            if not position and target_code:
                # 尝试搜索targetCode中的关键词
                keywords = target_code.split()
                for keyword in keywords:
                    if len(keyword) > 3 and keyword.isalnum():  # 过滤掉短词和符号
                        position = analyzer.find_text_position(keyword)
                        if position:
                            break

                # 如果还是找不到，尝试搜索整个targetCode
                if not position:
                    position = analyzer.find_text_position(target_code.strip())

            # 如果找到位置，更新codePosition
            if position:
                enhanced_problem['codePosition'] = position.to_array()
                print(f"[位置增强] 为问题找到精确位置: {position.to_array()}")
            else:
                # 保持原有的codePosition或设置默认值
                if 'codePosition' not in enhanced_problem:
                    enhanced_problem['codePosition'] = [1, 0, 1, 0]  # 默认第一行
                print(f"[位置增强] 未找到精确位置，使用默认值")

            enhanced_problems.append(enhanced_problem)

        except Exception as e:
            print(f"[位置增强] 处理问题时出错: {e}")
            # 出错时保持原问题不变
            enhanced_problems.append(problem)

    return enhanced_problems


def enhance_single_problem_position(problem: Dict, code_content: str) -> Dict:
    """
    为单个问题增强位置信息
    """
    if not code_content:
        return problem

    try:
        analyzer = CodePositionAnalyzer(code_content)
        enhanced_problem = problem.copy()

        target_code = problem.get('targetCode', '')
        problem_desc = problem.get('problem', '')

        # 使用相同的位置分析逻辑
        position = None
        import re

        # 尝试多种方式定位代码位置（复用之前的逻辑）
        if 'throws Exception' in target_code or 'except' in target_code:
            if 'throws Exception' in target_code:
                method_match = re.search(r'(\w+)\s*\([^)]*\)\s*throws\s+Exception', target_code)
                if method_match:
                    method_name = method_match.group(1)
                    position = analyzer.find_exception_declaration_position(method_name)
            elif 'except' in target_code:
                position = analyzer.find_text_position('except')
        elif target_code.startswith('@'):
            annotation_name = target_code.split('(')[0][1:]
            position = analyzer.find_annotation_position(annotation_name)
        elif '.' in target_code and '(' in target_code and not target_code.startswith('def '):
            method_call = target_code.split('(')[0].strip()
            position = analyzer.find_method_call_position(method_call)
        elif 'os.environ' in target_code:
            position = analyzer.find_text_position('os.environ')
        elif problem_desc and ('异常处理' in problem_desc or 'except' in problem_desc.lower()):
            position = analyzer.find_text_position('except') or analyzer.find_text_position('try')
        elif target_code:
            position = analyzer.find_text_position(target_code.strip())

        if position:
            enhanced_problem['codePosition'] = position.to_array()
            print(f"[单个问题位置增强] 找到精确位置: {position.to_array()}")
        else:
            enhanced_problem['codePosition'] = [1, 0, 1, 0]
            print(f"[单个问题位置增强] 未找到精确位置，使用默认值")

        return enhanced_problem

    except Exception as e:
        print(f"[单个问题位置增强] 处理出错: {e}")
        return problem


def build_cr_chain(llm, devmind_service, dataset_ids: List[str], max_retrieval_rounds: int = 2,
                   chain_options: Optional[Dict[str, Any]] = None):
    """
    构建代码审查链路：LLM初判 -> 多轮知识库检索 -> LLM知识增强判别
    :param llm: LLM实例，需有invoke({"prompt": ...})方法
    :param devmind_service: DevmindService实例
    :param dataset_ids: 知识库数据集ID列表
    :param max_retrieval_rounds: 检索轮数，默认2轮
    :param chain_options: 链路配置参数（如max_retrieval_rounds、prompt_type等）
    :return: LCEL链对象
    """
    # 解析链路配置
    if chain_options:
        max_retrieval_rounds = chain_options.get("max_retrieval_rounds", max_retrieval_rounds)

    # 1. LLM初判
    def llm_initial_judge(inputs: Dict[str, Any]) -> Dict[str, Any]:
        # 支持diff内容和上下文
        diff_content = inputs.get("diff_content")
        full_code = inputs.get("full_code")
        upstream_code = inputs.get("upstream_code", {})
        downstream_code = inputs.get("downstream_code", {})

        prompt_type = None
        if chain_options and 'prompt_type' in chain_options:
            prompt_type = chain_options['prompt_type']

        # 处理依赖代码
        if not upstream_code and 'upstream' in inputs:
            # 尝试从upstream列表构建upstream_code
            upstream_list = inputs.get('upstream', [])
            print(f"[llm_initial_judge] 从upstream列表构建upstream_code: {upstream_list}")
            upstream_code = {name: f"// 依赖函数: {name}" for name in upstream_list}

        if not downstream_code and 'downstream' in inputs:
            # 尝试从downstream列表构建downstream_code
            downstream_list = inputs.get('downstream', [])
            print(f"[llm_initial_judge] 从downstream列表构建downstream_code: {downstream_list}")
            downstream_code = {name: f"// 被调用函数: {name}" for name in downstream_list}

        upstream_str = '\n'.join([f'【{k}】{v}' for k, v in upstream_code.items()]) if upstream_code else '无'
        downstream_str = '\n'.join([f'【{k}】{v}' for k, v in downstream_code.items()]) if downstream_code else '无'

        prompt = f'''
## Python 代码审查规则（commonCheck）

### P0（严重问题，必须修复）
- P0-1: 存在语法错误、未定义变量、未导入模块、缩进错误等导致代码无法运行的问题
- P0-2: 存在明显的安全漏洞（如 SQL 注入、命令注入、明文密码、危险 eval/exec 等）
- P0-3: 关键业务流程存在逻辑漏洞或数据丢失风险
- P0-4: 资源未正确关闭（如文件、数据库连接、网络连接等）

### P1（重要问题，建议修复）
- P1-1: 违反 PEP8 规范（如命名不规范、行过长、空格/缩进不一致等）
- P1-2: 魔法数字/字符串未定义为常量
- P1-3: 异常未捕获或捕获过于宽泛（如 except:）
- P1-4: 函数/类/模块缺少必要的 docstring 注释
- P1-5: 代码重复、未封装、未复用
- P1-6: 资源释放不及时，可能导致内存泄漏

### P2（一般问题，建议优化）
- P2-1: 变量/函数/类命名不清晰或不符合语义
- P2-2: 代码结构不清晰，函数过长、嵌套层级过深
- P2-3: 不必要的 print/log 输出未清理
- P2-4: 过度依赖全局变量
- P2-5: 兼容性问题（如 Python2/3 差异、依赖未声明等）
- P2-6: 性能可优化（如不必要的循环、低效的数据结构等）

## 代码审查要求
1. 严格按照上述规则逐项检查，不得遗漏。
2. 仅对 diff 范围内的代码进行审查，未变更部分无需检查。
3. 问题定位需精确到代码行，引用原始代码内容。
4. 每个问题需给出详细描述、风险说明及修改建议。
5. 如无问题，直接说明"未发现问题"。
6. 检查结果需结构化输出，包含问题等级（P0/P1/P2）、问题描述、建议、代码位置、原始代码片段等。

## 输出格式要求
- 严格按照结构化 JSON 输出，不得输出多余文本。
- 每个问题需包含：level, problem, suggestion, codePosition, targetCode。
- diff片段：
{diff_content}
- 完整代码块：
{full_code}
- 正向依赖代码（被本块调用）：
{upstream_str}
- 反向依赖代码（调用本块）：
{downstream_str}
'''
        # 可根据prompt_type扩展不同场景
        if prompt_type == "security":
            prompt = "【安全审查】" + prompt
        elif prompt_type == "performance":
            prompt = "【性能审查】" + prompt
        result = llm.invoke(prompt).content
        print(f"[llm_initial_judge] 初步判断结果长度: {len(result)}")
        return {**inputs, "initial_judge": result, "upstream_code": upstream_code, "downstream_code": downstream_code}

    # 2. 多轮知识库检索 - 异步函数
    async def multi_round_knowledge_retrieval(inputs: Dict[str, Any]) -> Dict[str, Any]:
        initial_judge = inputs["initial_judge"]
        # 解析问题点
        problem_points = []
        for line in initial_judge.splitlines():
            line = line.strip()
            if line and (line[0].isdigit() and line[1:3] in ['. ', '.\t']):
                problem_points.append(line.split('.', 1)[-1].strip())
            elif line and line[0].isdigit() and line[1] == '.':
                problem_points.append(line[2:].strip())
        if not problem_points:
            if initial_judge.strip():
                problem_points = [initial_judge.strip()]
        all_knowledge = []
        # 第一轮：整体问题点检索
        result = await devmind_service.aretrieve_chunks(
            question=initial_judge,
            dataset_ids=dataset_ids,
            page=1,
            page_size=5
        )
        all_knowledge.extend(result.get("data", {}).get("chunks", []))
        # 第二轮：每个问题点单独检索
        if max_retrieval_rounds > 1:
            for point in problem_points:
                if not point or point == "未发现明显问题":
                    continue
                result = await devmind_service.aretrieve_chunks(
                    question=point,
                    dataset_ids=dataset_ids,
                    page=1,
                    page_size=3
                )
                all_knowledge.extend(result.get("data", {}).get("chunks", []))
        # 去重
        seen = set()
        unique_knowledge = []
        for k in all_knowledge:
            content = k.get("content")
            if content and content not in seen:
                unique_knowledge.append(content)
                seen.add(content)
        knowledge_str = "\n".join(unique_knowledge)
        print(knowledge_str)
        return {**inputs, "knowledge": knowledge_str}

    # 创建一个包装器，将异步函数转换为同步函数
    def knowledge_retrieval_wrapper(inputs: Dict[str, Any]) -> Dict[str, Any]:
        import asyncio
        # 在同步环境中运行异步函数
        try:
            # 尝试获取当前事件循环
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # 如果事件循环已经在运行，使用asyncio.run_coroutine_threadsafe
                import concurrent.futures
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = asyncio.run_coroutine_threadsafe(multi_round_knowledge_retrieval(inputs), loop)
                    return future.result()
            else:
                # 如果事件循环未运行，直接使用asyncio.run
                return asyncio.run(multi_round_knowledge_retrieval(inputs))
        except RuntimeError:
            # 如果无法获取事件循环，创建一个新的
            return asyncio.run(multi_round_knowledge_retrieval(inputs))

    # 3. LLM知识增强判别
    def llm_final_judge(inputs: Dict[str, Any]) -> Dict[str, Any]:
        # 支持diff内容和上下文
        diff_content = inputs.get("diff_content")
        full_code = inputs.get("full_code")
        upstream_code = inputs.get("upstream_code", {})
        downstream_code = inputs.get("downstream_code", {})

        # 打印输入信息，用于调试
        print(f"[llm_final_judge] 输入信息:")
        print(f"  diff_content 长度: {len(diff_content) if diff_content else 0}")
        print(f"  full_code 长度: {len(full_code) if full_code else 0}")
        print(f"  upstream_code 数量: {len(upstream_code)}")
        print(f"  downstream_code 数量: {len(downstream_code)}")

        # 处理依赖代码
        if not upstream_code and 'upstream' in inputs:
            # 尝试从upstream列表构建upstream_code
            upstream_list = inputs.get('upstream', [])
            print(f"[llm_final_judge] 从upstream列表构建upstream_code: {upstream_list}")
            upstream_code = {name: f"// 依赖函数: {name}" for name in upstream_list}

        if not downstream_code and 'downstream' in inputs:
            # 尝试从downstream列表构建downstream_code
            downstream_list = inputs.get('downstream', [])
            print(f"[llm_final_judge] 从downstream列表构建downstream_code: {downstream_list}")
            downstream_code = {name: f"// 被调用函数: {name}" for name in downstream_list}

        upstream_str = '\n'.join([f'【{k}】{v}' for k, v in upstream_code.items()]) if upstream_code else '无'
        downstream_str = '\n'.join([f'【{k}】{v}' for k, v in downstream_code.items()]) if downstream_code else '无'
        initial_judge = inputs["initial_judge"]
        knowledge = inputs.get("knowledge", "")
        # 彻底防御
        if isinstance(knowledge, dict):
            knowledge_str = knowledge.get("knowledge", "")
        elif isinstance(knowledge, str):
            if knowledge == "未发现明显问题":
                knowledge_str = ""
            else:
                try:
                    k = json.loads(knowledge)
                    if isinstance(k, dict) and "knowledge" in k:
                        knowledge_str = k["knowledge"]
                    else:
                        knowledge_str = knowledge
                except Exception:
                    knowledge_str = knowledge
        else:
            knowledge_str = str(knowledge)
        prompt_type = None
        if chain_options and 'prompt_type' in chain_options:
            prompt_type = chain_options['prompt_type']

        # 创建结构化输出解析器
        parser = PydanticOutputParser(pydantic_object=CodeReviewProblemsResponse)

        prompt = f"""
你是一名企业级智能代码审查专家。请结合以下五部分信息，给出最终的代码审查结论和建议：
1. diff片段：
{diff_content}
2. 完整代码块：
{full_code}
3. 正向依赖代码（被本块调用）：
{upstream_str}
4. 反向依赖代码（调用本块）：
{downstream_str}
5. 企业知识库相关内容：
{knowledge_str}

分析要求：
- 对每个初步判别中的问题点，结合知识库内容进行详细分析，并给出修复建议
- 如果知识库内容中有额外的风险提示、反面案例或最佳实践，也请补充说明
- 在targetCode中提供精确的代码片段，便于后续精确定位
- codePosition使用4点坐标格式：[startLine, startColumn, endLine, endColumn]

{parser.get_format_instructions()}

如果没有问题，请输出空数组。"""
        if prompt_type == "security":
            prompt = "【安全审查】" + prompt
        elif prompt_type == "performance":
            prompt = "【性能审查】" + prompt
        print("[llm_final_judge] prompt:\n", prompt)
        result = llm.invoke(prompt).content
        print("[llm_final_judge] LLM返回内容:\n", result)

        try:
            # 尝试使用结构化解析器
            structured_response = parser.parse(result)
            structured_problems = structured_response.problems
            print(f"[llm_final_judge] 结构化解析成功，问题数量: {len(structured_problems)}")

            # 转换为字典格式，并进行位置增强
            problems = []
            for problem in structured_problems:
                problem_dict = {
                    "level": problem.level.value.upper(),  # 转换为大写格式
                    "problem": problem.problem,
                    "suggestion": problem.suggestion,
                    "targetCode": problem.targetCode,
                    "codePosition": problem.codePosition
                }
                problems.append(problem_dict)

            # 如果LLM已经返回了坐标格式，检查是否需要进一步增强
            enhanced_problems = []
            for problem in problems:
                if (isinstance(problem.get('codePosition'), list) and
                    len(problem['codePosition']) == 4 and
                    all(isinstance(x, int) for x in problem['codePosition'])):
                    # 已经是正确的坐标格式，直接使用
                    enhanced_problems.append(problem)
                    print(f"[llm_final_judge] 问题已有精确坐标: {problem['codePosition']}")
                else:
                    # 需要位置增强
                    enhanced_problem = enhance_single_problem_position(problem, full_code)
                    enhanced_problems.append(enhanced_problem)

            print(f"[llm_final_judge] 最终问题数量: {len(enhanced_problems)}")

        except Exception as e:
            print(f"[llm_final_judge] 结构化解析失败: {e}，尝试JSON解析")
            try:
                problems = json.loads(result)
                print("[llm_final_judge] JSON解析成功，进行位置增强")
                enhanced_problems = enhance_problems_with_positions(problems, full_code)
            except Exception as e2:
                print(f"[llm_final_judge] JSON解析也失败: {e2}")
                problems = []
                enhanced_problems = []

        return {**inputs, "final_judge": result, "problems": enhanced_problems}

    # LCEL编排（使用正确的方式处理异步函数）
    chain = RunnableSequence(
        RunnableLambda(llm_initial_judge),
        RunnableLambda(knowledge_retrieval_wrapper),  # 使用包装器处理异步函数
        RunnableLambda(llm_final_judge)
    )
    print(f"[build_cr_chain] chain steps: {getattr(chain, 'steps', None)}")
    return chain
