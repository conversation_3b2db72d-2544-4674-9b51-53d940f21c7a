"""
代码解析器工厂模块
"""
from typing import Optional

from .base_parser import BaseCodeParser
from .python_parser import PythonParser
from .java_parser import JavaParser
from .javascript_parser import JSTypeScriptParser


class CodeParserFactory:
    """代码解析器工厂"""

    @staticmethod
    def get_parser(file_path: str) -> Optional[BaseCodeParser]:
        """
        根据文件路径获取对应的解析器
        
        Args:
            file_path: 文件路径
            
        Returns:
            Optional[BaseCodeParser]: 对应的解析器实例，如果不支持则返回None
        """
        if file_path.endswith('.py'):
            return PythonParser()
        elif file_path.endswith('.java'):
            return JavaParser()
        elif file_path.endswith(('.js', '.ts', '.jsx', '.tsx')):
            return JSTypeScriptParser()
        else:
            return None

    @staticmethod
    def get_supported_extensions():
        """
        获取支持的文件扩展名列表
        
        Returns:
            List[str]: 支持的文件扩展名列表
        """
        return ['.py', '.java', '.js', '.ts', '.jsx', '.tsx']

    @staticmethod
    def is_supported(file_path: str) -> bool:
        """
        检查文件是否支持解析
        
        Args:
            file_path: 文件路径
            
        Returns:
            bool: 是否支持解析
        """
        return any(file_path.endswith(ext) for ext in CodeParserFactory.get_supported_extensions())

    @staticmethod
    def get_parser_info():
        """
        获取所有解析器的信息
        
        Returns:
            Dict[str, Dict]: 解析器信息字典
        """
        return {
            'python': {
                'class': 'PythonParser',
                'extensions': ['.py'],
                'description': 'Python代码解析器，支持函数、类、装饰器等解析'
            },
            'java': {
                'class': 'JavaParser', 
                'extensions': ['.java'],
                'description': 'Java代码解析器，支持类、方法、构造函数等解析'
            },
            'javascript': {
                'class': 'JSTypeScriptParser',
                'extensions': ['.js', '.ts', '.jsx', '.tsx'],
                'description': 'JavaScript/TypeScript代码解析器，支持函数、类、箭头函数等解析'
            }
        }