"""
Python代码解析器模块
"""
import ast
import re
from typing import List, Dict, Any

from .base_parser import BaseCodeParser

# 检查parso可用性
try:
    import parso
    PARSO_AVAILABLE = True
except ImportError:
    PARSO_AVAILABLE = False


class PythonParser(BaseCodeParser):
    """Python代码解析器"""

    def parse_chunks(self, content: str, file_path: str, resolve_code: bool = True) -> List[Dict[str, Any]]:
        """解析Python代码块"""
        if not hasattr(self, 'func_index'):
            self.func_index = None

        lines = content.splitlines()
        chunks = [self._create_file_chunk(content, file_path)]

        func_chunks = []
        class_chunks = []

        # 收集import别名映射
        self._collect_import_aliases(lines)

        if PARSO_AVAILABLE:
            # 使用parso解析
            try:
                module = parso.parse(content)
                self._parse_ast_structure(module, chunks, func_chunks, class_chunks, file_path)
            except Exception as e:
                print(f"[PythonParser] parso解析失败: {e}，使用fallback解析器")
                fallback_chunks = self._fallback_python_parse(content, file_path, lines)
                func_chunks.extend(fallback_chunks)
                chunks.extend(fallback_chunks)
        else:
            # 使用fallback解析
            print("[PythonParser] parso不可用，使用fallback解析器")
            fallback_chunks = self._fallback_python_parse(content, file_path, lines)
            func_chunks.extend(fallback_chunks)
            chunks.extend(fallback_chunks)

        # 分析函数调用关系
        if resolve_code:
            self._analyze_function_calls(content, func_chunks, file_path)

        return chunks

    def _collect_import_aliases(self, lines: List[str]):
        """收集import别名映射"""
        self.import_alias_map = {}

        for line in lines:
            line = line.strip()
            if line.startswith('import '):
                # import module as m
                parts = line.split()
                if 'as' in parts:
                    idx = parts.index('as')
                    if idx > 1:
                        self.import_alias_map[parts[idx + 1]] = parts[1]
            elif line.startswith('from '):
                # from module import x as y
                m = re.match(r'from ([\w\.]+) import (.+)', line)
                if m:
                    mod = m.group(1)
                    rest = m.group(2)
                    for part in rest.split(','):
                        part = part.strip()
                        if ' as ' in part:
                            orig, alias = part.split(' as ')
                            self.import_alias_map[alias.strip()] = f"{mod}.{orig.strip()}"
                        else:
                            self.import_alias_map[part] = f"{mod}.{part}"

    def _fallback_python_parse(self, content: str, file_path: str, lines: List[str]) -> List[Dict[str, Any]]:
        """Python fallback解析器（基于正则表达式）"""
        chunks = []

        # 正则表达式模式
        class_pattern = r'^class\s+(\w+)'
        function_pattern = r'^(?:\s*)def\s+(\w+)\s*$'

        current_class = None

        for i, line in enumerate(lines, 1):
            line_stripped = line.strip()

            # 匹配类声明
            class_match = re.search(class_pattern, line_stripped)
            if class_match:
                current_class = class_match.group(1)
                print(f"[Python Fallback] 找到类: {current_class}")
                continue

            # 匹配函数声明
            function_match = re.search(function_pattern, line_stripped)
            if function_match:
                function_name = function_match.group(1)

                # 估算函数结束行
                end_line = self._estimate_python_function_end_line(lines, i - 1)
                function_content = '\n'.join(lines[i-1:end_line])

                unique_name = f"{current_class}.{function_name}" if current_class else function_name

                chunk = self._create_function_chunk(
                    name=unique_name,
                    content=function_content,
                    file_path=file_path,
                    start_line=i,
                    end_line=end_line,
                    raw_func_name=function_name,
                    class_name=current_class
                )
                chunks.append(chunk)
                print(f"[Python Fallback] 找到函数: {unique_name}")

        return chunks

    def _estimate_python_function_end_line(self, lines: List[str], start_idx: int) -> int:
        """估算Python函数结束行（基于缩进）"""
        if start_idx >= len(lines):
            return len(lines)

        # 获取函数定义行的缩进
        def_line = lines[start_idx]
        def_indent = len(def_line) - len(def_line.lstrip())

        # 查找下一个同级或更低级别的代码行
        for i in range(start_idx + 1, len(lines)):
            line = lines[i]
            if line.strip():  # 非空行
                current_indent = len(line) - len(line.lstrip())
                if current_indent <= def_indent:
                    return i

        return len(lines)

    def _parse_ast_structure(self, module, chunks, func_chunks, class_chunks, file_path):
        """解析AST结构"""

        def visit(node, parent_class=None):
            try:
                if node.type == 'classdef':
                    class_name = node.name.value
                    start_line = node.start_pos[0]
                    end_line = start_line + node.get_code().count('\n')

                    class_chunk = self._create_class_chunk(
                        name=class_name,
                        content=node.get_code(),
                        file_path=file_path,
                        start_line=start_line,
                        end_line=end_line
                    )
                    class_chunks.append(class_chunk)
                    chunks.append(class_chunk)

                    for child in getattr(node, 'children', []):
                        visit(child, parent_class=class_name)

                elif node.type == 'funcdef':
                    start_line = node.start_pos[0]
                    end_line = start_line + node.get_code().count('\n')
                    func_name = node.name.value
                    unique_name = f"{parent_class}.{func_name}" if parent_class else func_name

                    chunk = self._create_function_chunk(
                        name=unique_name,
                        content=node.get_code(),
                        file_path=file_path,
                        start_line=start_line,
                        end_line=end_line,
                        raw_func_name=func_name,
                        class_name=parent_class
                    )
                    func_chunks.append(chunk)
                    chunks.append(chunk)
                else:
                    for child in getattr(node, 'children', []):
                        visit(child, parent_class=parent_class)
            except Exception as e:
                print(f"[PythonParser][visit] 解析节点异常: {e}")

        visit(module)

    def _analyze_function_calls(self, content: str, func_chunks: List[Dict], file_path: str):
        """分析函数调用关系"""

        class FuncCallVisitor(ast.NodeVisitor):
            def __init__(self, import_map):
                self.func_calls = {}
                self.current_func = None
                self.class_stack = []
                self.import_map = import_map
                self.decorated_funcs = {}

            def visit_FunctionDef(self, node):
                self._visit_function_def(node)

            def visit_AsyncFunctionDef(self, node):
                self._visit_function_def(node, is_async=True)

            def _visit_function_def(self, node, is_async=False):
                try:
                    func_name = node.name
                    class_name = self.class_stack[-1] if self.class_stack else None
                    unique_name = f"{class_name}.{func_name}" if class_name else func_name
                    self.current_func = unique_name
                    self.func_calls.setdefault(unique_name, set())

                    if is_async:
                        self.func_calls[unique_name].add('ASYNC_FUNCTION')

                    # 处理装饰器
                    self._process_decorators(node, unique_name)
                    self.generic_visit(node)
                except Exception as e:
                    print(f"[FuncCallVisitor] visit_FunctionDef异常: {e}")
                self.current_func = None

            def _process_decorators(self, node, unique_name):
                """处理装饰器"""
                if hasattr(node, 'decorator_list') and node.decorator_list:
                    for decorator in node.decorator_list:
                        try:
                            if isinstance(decorator, ast.Call) and hasattr(decorator, 'func') and isinstance(
                                    decorator.func, ast.Attribute):
                                if getattr(decorator.func, 'attr', None) == 'route':
                                    self.func_calls[unique_name].add('FLASK_ROUTE')
                            elif isinstance(decorator, ast.Name):
                                self.func_calls[unique_name].add(f"DECORATOR_{decorator.id}")
                                self.decorated_funcs[unique_name] = decorator.id
                        except Exception as e:
                            print(f"[FuncCallVisitor] 解析装饰器异常: {e}")

            def visit_ClassDef(self, node):
                try:
                    self.class_stack.append(node.name)
                    self.generic_visit(node)
                    self.class_stack.pop()
                except Exception as e:
                    print(f"[FuncCallVisitor] visit_ClassDef异常: {e}")

            def visit_Call(self, node):
                try:
                    if self.current_func:
                        self._process_call_node(node)
                    self.generic_visit(node)
                except Exception as e:
                    print(f"[FuncCallVisitor] visit_Call异常: {e}")

            def _process_call_node(self, node):
                """处理函数调用节点"""
                try:
                    if isinstance(node.func, ast.Name):
                        # 直接函数调用: func()
                        func_name = node.func.id
                        if func_name in self.import_map:
                            self.func_calls[self.current_func].add(self.import_map[func_name])
                        else:
                            self.func_calls[self.current_func].add(func_name)

                    elif isinstance(node.func, ast.Attribute):
                        # 属性调用: obj.method()
                        if isinstance(node.func.value, ast.Name):
                            obj_name = node.func.value.id
                            method_name = node.func.attr
                            if obj_name in self.import_map:
                                full_name = f"{self.import_map[obj_name]}.{method_name}"
                            else:
                                full_name = f"{obj_name}.{method_name}"
                            self.func_calls[self.current_func].add(full_name)
                        else:
                            self.func_calls[self.current_func].add(node.func.attr)

                except Exception as e:
                    print(f"[FuncCallVisitor] _process_call_node异常: {e}")

        try:
            tree = ast.parse(content)
            visitor = FuncCallVisitor(self.import_alias_map)
            visitor.visit(tree)

            # 构建反向调用关系
            reverse_calls = {}
            for caller, callees in visitor.func_calls.items():
                for callee in callees:
                    reverse_calls.setdefault(callee, set()).add(caller)

            # 创建函数映射
            func_chunk_map = {chunk["name"]: chunk for chunk in func_chunks}

            # 设置依赖关系
            for chunk in func_chunks:
                name = chunk["name"]
                upstream = set()

                # 设置上游依赖
                for callee in visitor.func_calls.get(name, []):
                    if callee in func_chunk_map:
                        upstream.add(callee)
                    elif not callee.startswith(('ASYNC_', 'FLASK_', 'DECORATOR_')):
                        upstream.add(callee)  # 外部依赖

                chunk["upstream"] = list(upstream)

                # 设置下游依赖
                downstream = set()
                raw_name = chunk.get("_raw_func_name", "")

                # 查找调用当前方法的其他方法
                for caller in reverse_calls.get(raw_name, set()):
                    if caller != name:
                        downstream.add(caller)

                # 查找调用完整方法名的其他方法
                for caller in reverse_calls.get(name, set()):
                    if caller != name:
                        downstream.add(caller)

                chunk["downstream"] = list(downstream)

                # 清理临时字段
                chunk.pop("_raw_func_name", None)
                chunk.pop("_class", None)

            print(f"[Python依赖分析] 完成，处理了 {len(func_chunks)} 个函数")

        except Exception as e:
            print(f"[PythonParser] 分析函数调用关系失败: {e}")