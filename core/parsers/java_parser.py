"""
Java代码解析器模块
"""
import re
from typing import List, Dict, Any

from .base_parser import BaseCodeParser

# 检查javalang可用性
try:
    import javalang
    JAVALANG_AVAILABLE = True
except ImportError:
    JAVALANG_AVAILABLE = False


class JavaParser(BaseCodeParser):
    """Java代码解析器"""

    def __init__(self):
        super().__init__()
        self.package_name = ""

    def parse_chunks(self, content: str, file_path: str, resolve_code: bool = True) -> List[Dict[str, Any]]:
        """解析Java代码块"""
        lines = content.splitlines()
        chunks = [self._create_file_chunk(content, file_path)]

        method_chunks = []
        class_chunks = []

        if JAVALANG_AVAILABLE:
            try:
                # 使用javalang解析
                tree = javalang.parse.parse(content)
                self._collect_java_imports(tree)
                visitor = self._create_java_visitor(lines, file_path, chunks, method_chunks, class_chunks)

                for path, node in tree:
                    visitor.visit(node, path)

                if resolve_code:
                    self._analyze_java_dependencies(tree, method_chunks, file_path)

            except Exception as e:
                print(f"[JavaParser] javalang解析失败: {e}，使用fallback解析器")
                fallback_chunks = self._fallback_java_parse(content, file_path, lines, resolve_code)
                chunks.extend(fallback_chunks)
        else:
            print("[JavaParser] javalang不可用，使用fallback解析器")
            fallback_chunks = self._fallback_java_parse(content, file_path, lines, resolve_code)
            chunks.extend(fallback_chunks)

        return chunks

    def _fallback_java_parse(self, content: str, file_path: str, lines: List[str], resolve_code: bool) -> List[Dict[str, Any]]:
        """Java fallback解析器（基于正则表达式）"""
        chunks = []
        method_chunks = []

        # 正则表达式模式
        class_pattern = r'^\s*(?:public|private|protected)?\s*class\s+(\w+)'
        method_pattern = r'^\s*(?:public|private|protected)?\s*(?:static)?\s*(?:\w+\s+)*(\w+)\s*\([^)]*\)\s*\{'
        constructor_pattern = r'^\s*(?:public|private|protected)?\s*(\w+)\s*\([^)]*\)\s*\{'

        current_class = None

        for i, line in enumerate(lines, 1):
            line_stripped = line.strip()

            # 匹配类声明
            class_match = re.search(class_pattern, line_stripped)
            if class_match:
                current_class = class_match.group(1)
                print(f"[Fallback] 找到类: {current_class}")
                continue

            # 匹配方法声明
            method_match = re.search(method_pattern, line_stripped)
            if method_match and current_class:
                method_name = method_match.group(1)

                # 跳过getter/setter等简单方法
                if method_name in ['get', 'set', 'is', 'has']:
                    continue

                # 估算方法结束行
                end_line = self._estimate_method_end_line(lines, i - 1)
                method_content = '\n'.join(lines[i-1:end_line])

                unique_name = f"{current_class}.{method_name}"

                chunk = self._create_method_chunk(
                    name=unique_name,
                    content=method_content,
                    file_path=file_path,
                    start_line=i,
                    end_line=end_line,
                    raw_method_name=method_name,
                    class_name=current_class,
                    method_type="method"
                )
                method_chunks.append(chunk)
                chunks.append(chunk)
                print(f"[Fallback] 找到方法: {unique_name}")

            # 匹配构造函数
            constructor_match = re.search(constructor_pattern, line_stripped)
            if constructor_match and current_class:
                constructor_name = constructor_match.group(1)
                if constructor_name == current_class:  # 确认是构造函数
                    end_line = self._estimate_method_end_line(lines, i - 1)
                    constructor_content = '\n'.join(lines[i-1:end_line])

                    unique_name = f"{current_class}.{constructor_name}"

                    chunk = self._create_method_chunk(
                        name=unique_name,
                        content=constructor_content,
                        file_path=file_path,
                        start_line=i,
                        end_line=end_line,
                        raw_method_name=constructor_name,
                        class_name=current_class,
                        method_type="constructor"
                    )
                    method_chunks.append(chunk)
                    chunks.append(chunk)
                    print(f"[Fallback] 找到构造函数: {unique_name}")

        # 分析依赖关系
        if resolve_code and method_chunks:
            self._fallback_analyze_dependencies(method_chunks, content)

        return chunks

    def _estimate_method_end_line(self, lines: List[str], start_idx: int) -> int:
        """估算方法结束行（基于大括号匹配）"""
        brace_count = 0
        for i in range(start_idx, len(lines)):
            line = lines[i]
            brace_count += line.count('{') - line.count('}')
            if brace_count <= 0 and i > start_idx:
                return i + 1
        return len(lines)

    def _fallback_analyze_dependencies(self, method_chunks: List[Dict], content: str):
        """Fallback依赖分析（基于正则表达式）"""
        print(f"[Fallback依赖分析] 开始分析 {len(method_chunks)} 个方法")

        # 收集方法调用关系
        method_calls = {}

        for chunk in method_chunks:
            chunk_content = chunk['content']
            caller_name = chunk['name']
            calls = set()

            # 查找方法调用模式
            # 1. 直接方法调用: methodName()
            direct_calls = re.findall(r'(\w+)\s*\(', chunk_content)
            for call in direct_calls:
                if call not in ['if', 'for', 'while', 'switch', 'catch', 'return', 'new', 'super', 'this']:  # 排除关键字
                    # 检查是否是同类方法调用
                    class_name = chunk.get('_class')
                    if class_name:
                        calls.add(f"{class_name}.{call}")
                    else:
                        calls.add(call)

            # 2. 对象方法调用: obj.method()
            object_calls = re.findall(r'(\w+)\.(\w+)\s*\(', chunk_content)
            for obj, method in object_calls:
                if obj != 'this' and obj != 'super':
                    calls.add(f"{obj}.{method}")

            # 3. 类实例化: new ClassName()
            new_calls = re.findall(r'new\s+(\w+)\s*\(', chunk_content)
            for class_name in new_calls:
                calls.add(f"{class_name}.__init__")

            # 4. this.method() 调用
            this_calls = re.findall(r'this\.(\w+)\s*\(', chunk_content)
            class_name = chunk.get('_class')
            for method in this_calls:
                if class_name:
                    calls.add(f"{class_name}.{method}")

            # 5. 静态方法调用: ClassName.staticMethod()
            static_calls = re.findall(r'([A-Z]\w+)\.(\w+)\s*\(', chunk_content)
            for class_name, method in static_calls:
                calls.add(f"{class_name}.{method}")

            # 6. super.method() 调用
            super_calls = re.findall(r'super\.(\w+)\s*\(', chunk_content)
            for method in super_calls:
                calls.add(f"super.{method}")

            method_calls[caller_name] = calls

        print(f"[Fallback依赖分析] 收集到的方法调用关系:")
        for caller, callees in method_calls.items():
            if callees:
                print(f"  {caller} -> {list(callees)}")

        # 构建反向调用关系
        reverse_calls = {}
        for caller, callees in method_calls.items():
            for callee in callees:
                reverse_calls.setdefault(callee, set()).add(caller)

        # 创建方法映射
        func_chunk_map = {chunk["name"]: chunk for chunk in method_chunks}

        # 设置依赖关系
        for chunk in method_chunks:
            name = chunk["name"]
            upstream = set()

            # 设置上游依赖
            for callee in method_calls.get(name, []):
                if callee in func_chunk_map:
                    upstream.add(callee)
                else:
                    upstream.add(callee)  # 外部依赖

            chunk["upstream"] = list(upstream)

            # 设置下游依赖
            downstream = set()
            raw_name = chunk["_raw_method_name"]

            # 查找调用当前方法的其他方法
            for caller in reverse_calls.get(raw_name, set()):
                if caller != name:
                    downstream.add(caller)

            # 查找调用完整方法名的其他方法
            for caller in reverse_calls.get(name, set()):
                if caller != name:
                    downstream.add(caller)

            chunk["downstream"] = list(downstream)

            # 清理临时字段
            chunk.pop("_raw_method_name", None)
            chunk.pop("_class", None)

        print(f"[Fallback依赖分析] 完成，处理了 {len(method_chunks)} 个方法")

    def _collect_java_imports(self, tree):
        """收集Java导入信息"""
        self.import_alias_map = {}
        self.package_name = ""

        for path, node in tree:
            if isinstance(node, javalang.tree.PackageDeclaration):
                self.package_name = node.name
            elif isinstance(node, javalang.tree.Import):
                if node.path:
                    parts = node.path.split('.')
                    if node.wildcard:
                        # import com.example.*
                        self.import_alias_map[parts[-2]] = '.'.join(parts[:-1])
                    else:
                        # import com.example.ClassName
                        class_name = parts[-1]
                        self.import_alias_map[class_name] = node.path

    def _create_java_visitor(self, lines, file_path, chunks, method_chunks, class_chunks):
        """创建Java访问器"""
        class JavaVisitor:
            def __init__(self):
                self.method_calls = {}

            def visit(self, node, path):
                if isinstance(node, javalang.tree.ClassDeclaration):
                    self._visit_class(node, path, lines, file_path, chunks, class_chunks, method_chunks)
                elif isinstance(node, javalang.tree.MethodDeclaration):
                    self._visit_method(node, path, lines, file_path, method_chunks)
                elif isinstance(node, javalang.tree.ConstructorDeclaration):
                    self._visit_constructor(node, path, lines, file_path, method_chunks)

            def _visit_class(self, node, path, lines, file_path, chunks, class_chunks, method_chunks):
                """访问类声明"""
                class_name = node.name
                start_line = node.position.line if node.position else 1
                # 估算类结束行
                end_line = len(lines)
                for i in range(start_line, len(lines)):
                    if lines[i-1].strip().endswith('}') and not lines[i-1].strip().startswith('//'):
                        end_line = i
                        break

                class_content = '\n'.join(lines[start_line-1:end_line])
                class_chunk = {
                    "type": "class",
                    "name": class_name,
                    "file": file_path,
                    "content": class_content,
                    "start_line": start_line,
                    "end_line": end_line,
                    "upstream": [],
                    "downstream": []
                }
                class_chunks.append(class_chunk)
                chunks.append(class_chunk)

            def _visit_method(self, node, path, lines, file_path, method_chunks):
                """访问方法声明"""
                method_name = node.name
                class_name = self._get_class_name_from_path(path)
                unique_name = f"{class_name}.{method_name}" if class_name else method_name
                start_line = node.position.line if node.position else 1
                end_line = self._estimate_method_end_from_node(node, lines, start_line)
                method_content = '\n'.join(lines[start_line-1:end_line])
                chunk = {
                    "type": "method",
                    "name": unique_name,
                    "file": file_path,
                    "content": method_content,
                    "start_line": start_line,
                    "end_line": end_line,
                    "_raw_method_name": method_name,
                    "_class": class_name,
                    "upstream": [],
                    "downstream": []
                }
                method_chunks.append(chunk)
                chunks.append(chunk)  # 添加到主chunks列表

            def _visit_constructor(self, node, path, lines, file_path, method_chunks):
                """访问构造函数声明"""
                class_name = self._get_class_name_from_path(path)
                unique_name = f"{class_name}.{class_name}" if class_name else "constructor"
                start_line = node.position.line if node.position else 1
                end_line = self._estimate_method_end_from_node(node, lines, start_line)
                constructor_content = '\n'.join(lines[start_line-1:end_line])
                chunk = {
                    "type": "constructor",
                    "name": unique_name,
                    "file": file_path,
                    "content": constructor_content,
                    "start_line": start_line,
                    "end_line": end_line,
                    "_raw_method_name": class_name,
                    "_class": class_name,
                    "upstream": [],
                    "downstream": []
                }
                method_chunks.append(chunk)
                chunks.append(chunk)  # 添加到主chunks列表

            def _get_class_name_from_path(self, path):
                """从路径中获取类名"""
                for item in reversed(path):
                    if isinstance(item, javalang.tree.ClassDeclaration):
                        return item.name
                return None

            def _estimate_method_end_from_node(self, node, lines, start_line):
                """从节点估算方法结束行"""
                # 简单的大括号匹配
                brace_count = 0
                for i in range(start_line - 1, len(lines)):
                    line = lines[i]
                    brace_count += line.count('{') - line.count('}')
                    if brace_count <= 0 and i > start_line - 1:
                        return i + 1
                return len(lines)

        return JavaVisitor()

    def _analyze_java_dependencies(self, tree, method_chunks, file_path):
        """分析Java依赖关系"""
        print(f"[Java依赖分析] 文件 {file_path} 分析到的方法数量: {len(method_chunks)}")

        # 收集所有方法调用关系
        method_calls = {}
        for path, node in tree:
            if isinstance(node, javalang.tree.ClassDeclaration):
                self._collect_class_method_calls(node, method_calls)

        print(f"[Java依赖分析] 收集到的方法调用关系:")
        for caller, callees in method_calls.items():
            if callees:
                print(f"  {caller} -> {list(callees)}")

        # 构建反向调用关系
        reverse_calls = {}
        for caller, callees in method_calls.items():
            for callee in callees:
                reverse_calls.setdefault(callee, set()).add(caller)

        # 创建方法映射
        func_chunk_map = {chunk["name"]: chunk for chunk in method_chunks}
        name_to_chunks = {}
        for chunk in method_chunks:
            name_to_chunks.setdefault(chunk["name"], []).append(chunk)

        # 设置依赖关系
        for chunk in method_chunks:
            name = chunk["name"]
            upstream = set()

            # 设置上游依赖（当前方法调用的其他方法）
            for callee in method_calls.get(name, []):
                # 先查完整名匹配
                if callee in func_chunk_map:
                    upstream.add(callee)
                # 再查同名函数
                elif callee in name_to_chunks:
                    for c in name_to_chunks[callee]:
                        upstream.add(c["name"])
                # 处理特殊依赖（如构造函数调用）
                elif callee.endswith(".__init__"):
                    upstream.add(callee)
                # 处理导入的类方法调用
                elif "." in callee:
                    class_name = callee.split(".")[0]
                    if class_name in self.import_alias_map:
                        full_callee = f"{self.import_alias_map[class_name]}.{callee.split('.', 1)[1]}"
                        upstream.add(full_callee)
                    else:
                        upstream.add(callee)

            chunk["upstream"] = list(upstream)

            # 设置下游依赖（哪些方法调用了当前方法）
            downstream = set()
            raw_name = chunk["_raw_method_name"]

            # 1. 使用原始方法名查找
            for caller in reverse_calls.get(raw_name, set()):
                if caller != name:
                    downstream.add(caller)

            # 2. 使用完整方法名查找（如果是类方法）
            if chunk.get("_class"):
                full_name = f"{chunk['_class']}.{raw_name}"
                for caller in reverse_calls.get(full_name, set()):
                    if caller != name:
                        downstream.add(caller)

            # 3. 使用当前方法的唯一名查找
            for caller in reverse_calls.get(name, set()):
                if caller != name:
                    downstream.add(caller)

            chunk["downstream"] = list(downstream)

            # 清理临时字段
            chunk.pop("_raw_method_name", None)
            chunk.pop("_class", None)

        print(f"[Java依赖分析] 完成，处理了 {len(method_chunks)} 个方法")

    def _collect_class_method_calls(self, class_node, method_calls):
        """收集类中的方法调用关系"""
        class_name = class_node.name

        for member in class_node.body:
            if isinstance(member, (javalang.tree.MethodDeclaration, javalang.tree.ConstructorDeclaration)):
                if isinstance(member, javalang.tree.ConstructorDeclaration):
                    method_name = f"{class_name}.{class_name}"
                else:
                    method_name = f"{class_name}.{member.name}"

                calls = set()

                # 遍历方法体，查找方法调用
                try:
                    for path, child in member:
                        if isinstance(child, javalang.tree.MethodInvocation):
                            # 直接方法调用: method()
                            method_called = child.member
                            if child.qualifier:
                                # 对象方法调用: obj.method()
                                qualifier_name = self._extract_qualifier_name(child.qualifier)
                                if qualifier_name:
                                    calls.add(f"{qualifier_name}.{method_called}")
                                else:
                                    calls.add(method_called)
                            else:
                                # 同类方法调用
                                calls.add(f"{class_name}.{method_called}")

                        elif isinstance(child, javalang.tree.SuperMethodInvocation):
                            # 父类方法调用: super.method()
                            if class_node.extends:
                                parent_class = class_node.extends.name
                                calls.add(f"{parent_class}.{child.member}")

                        elif isinstance(child, javalang.tree.ClassCreator):
                            # 类实例化: new Class()
                            if hasattr(child.type, 'name'):
                                clazz_name = child.type.name
                                calls.add(f"{clazz_name}.__init__")
                            elif hasattr(child.type, 'sub_type') and hasattr(child.type.sub_type, 'name'):
                                clazz_name = child.type.sub_type.name
                                calls.add(f"{clazz_name}.__init__")

                except Exception as e:
                    print(f"[Java依赖分析] 解析方法 {method_name} 时出错: {e}")

                method_calls[method_name] = calls
                print(f"[Java依赖分析] 方法 {method_name} 调用: {list(calls)}")

    def _extract_qualifier_name(self, qualifier):
        """提取限定符名称"""
        try:
            if hasattr(qualifier, 'member'):
                # 链式调用: obj.field.method()
                return qualifier.member
            elif hasattr(qualifier, 'name'):
                # 简单对象: obj.method()
                return qualifier.name
            elif hasattr(qualifier, 'value'):
                # 字面量
                return str(qualifier.value)
            else:
                return str(qualifier)
        except Exception as e:
            print(f"[Java依赖分析] 提取限定符名称失败: {e}")
            return None