"""
JavaScript/TypeScript代码解析器模块
"""
import os
from typing import List, Dict, Any

from .base_parser import BaseCodeParser
from consts.chunk_consts import TREE_SITTER_LIB_PATH

# 检查tree-sitter可用性
try:
    from tree_sitter import Language, Parser
    TREE_SITTER_AVAILABLE = True
except ImportError:
    TREE_SITTER_AVAILABLE = False


class JSTypeScriptParser(BaseCodeParser):
    """JavaScript/TypeScript代码解析器"""

    def __init__(self):
        super().__init__()
        self.js_language = None
        self.ts_language = None

        if TREE_SITTER_AVAILABLE and os.path.exists(TREE_SITTER_LIB_PATH):
            try:
                self.js_language = Language(TREE_SITTER_LIB_PATH, 'javascript')
                self.ts_language = Language(TREE_SITTER_LIB_PATH, 'typescript')
            except Exception as e:
                print(f"加载tree-sitter语言库失败: {e}")

    def parse_chunks(self, content: str, file_path: str, resolve_code: bool = True) -> List[Dict[str, Any]]:
        """解析JS/TS代码块"""
        lines = content.splitlines()
        chunks = [self._create_file_chunk(content, file_path)]

        language = self.ts_language if file_path.endswith('.ts') else self.js_language

        if language is None:
            print(f"[JSTypeScriptParser] tree-sitter不可用，跳过解析 {file_path}")
            return chunks

        try:
            parser = Parser()
            parser.language = language
            tree = parser.parse(bytes(content, "utf8"))

            func_chunks = []
            self._parse_js_ast(tree.root_node, content, lines, file_path, func_chunks)

            if resolve_code:
                self._analyze_js_dependencies(tree.root_node, content, func_chunks)

            chunks.extend(func_chunks)

        except Exception as e:
            print(f"[JSTypeScriptParser] 解析文件 {file_path} 时出错: {str(e)}")

        return chunks

    def _parse_js_ast(self, node, content, lines, file_path, func_chunks, parent_class=None):
        """解析JS AST"""
        if node.type == 'class_declaration':
            class_name = None
            for c in node.children:
                if c.type == 'identifier':
                    class_name = content[c.start_byte:c.end_byte]
                    break
            
            for child in node.children:
                self._parse_js_ast(child, content, lines, file_path, func_chunks, parent_class=class_name)

        elif node.type in (
            'function_declaration', 
            'method_definition', 
            'generator_function_declaration', 
            'arrow_function'
        ):
            start_line = node.start_point[0] + 1
            end_line = node.end_point[0] + 1
            code = '\n'.join(lines[start_line - 1:end_line])

            name = ''
            for c in node.children:
                if c.type == 'identifier':
                    name = content[c.start_byte:c.end_byte]
                    break

            unique_name = f"{parent_class}.{name}" if parent_class and name else name

            chunk = self._create_function_chunk(
                name=unique_name,
                content=code,
                file_path=file_path,
                start_line=start_line,
                end_line=end_line,
                raw_func_name=name,
                class_name=parent_class
            )
            func_chunks.append(chunk)

        for child in node.children:
            self._parse_js_ast(child, content, lines, file_path, func_chunks, parent_class=parent_class)

    def _analyze_js_dependencies(self, root_node, content, func_chunks):
        """分析JS依赖关系"""
        # 简化的依赖分析实现
        call_map = {chunk["name"]: set() for chunk in func_chunks}

        def find_calls(node, current_func=None):
            if node.type in (
                'function_declaration', 
                'method_definition', 
                'generator_function_declaration', 
                'arrow_function'
            ):
                name = ''
                for c in node.children:
                    if c.type == 'identifier':
                        name = content[c.start_byte:c.end_byte]
                        break
                current_func = name

            if node.type == 'call_expression' and current_func:
                for c in node.children:
                    if c.type == 'identifier':
                        called_func = content[c.start_byte:c.end_byte]
                        if current_func in call_map:
                            call_map[current_func].add(called_func)
                    elif c.type == 'member_expression':
                        # 处理 obj.method() 调用
                        member_name = self._extract_member_expression(c, content)
                        if member_name and current_func in call_map:
                            call_map[current_func].add(member_name)

            for child in node.children:
                find_calls(child, current_func=current_func)

        find_calls(root_node)

        # 设置依赖关系
        reverse_calls = {}
        for caller, callees in call_map.items():
            for callee in callees:
                reverse_calls.setdefault(callee, set()).add(caller)

        for chunk in func_chunks:
            chunk["upstream"] = list(call_map.get(chunk["name"], []))
            
            raw_name = chunk.get("_raw_func_name", "")
            downstream = set()
            
            # 查找调用当前函数的其他函数
            for caller in reverse_calls.get(raw_name, []):
                if caller != chunk["name"]:
                    downstream.add(caller)
            
            # 查找调用完整函数名的其他函数
            for caller in reverse_calls.get(chunk["name"], []):
                if caller != chunk["name"]:
                    downstream.add(caller)
            
            chunk["downstream"] = list(downstream)

            # 清理临时字段
            chunk.pop("_raw_func_name", None)
            chunk.pop("_class", None)

        print(f"[JS依赖分析] 完成，处理了 {len(func_chunks)} 个函数")

    def _extract_member_expression(self, node, content):
        """提取成员表达式的名称"""
        try:
            # 简单处理 obj.method 的情况
            obj_name = ""
            method_name = ""
            
            for child in node.children:
                if child.type == 'identifier':
                    if not obj_name:
                        obj_name = content[child.start_byte:child.end_byte]
                    else:
                        method_name = content[child.start_byte:child.end_byte]
                elif child.type == 'property_identifier':
                    method_name = content[child.start_byte:child.end_byte]
            
            if obj_name and method_name:
                return f"{obj_name}.{method_name}"
            elif method_name:
                return method_name
                
        except Exception as e:
            print(f"[JSTypeScriptParser] 提取成员表达式失败: {e}")
        
        return None