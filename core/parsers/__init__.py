"""
代码解析器模块

提供多种编程语言的代码解析功能，包括：
- Python解析器：支持函数、类、装饰器等解析
- Java解析器：支持类、方法、构造函数等解析
- JavaScript/TypeScript解析器：支持函数、类、箭头函数等解析

使用示例：
    from core.parsers import CodeParserFactory

    parser = CodeParserFactory.get_parser("example.py")
    if parser:
        chunks = parser.parse_chunks(content, file_path)
"""

from .base_parser import BaseCodeParser
from .java_parser import JavaParser
from .javascript_parser import JSTypeScriptParser
from .parser_factory import CodeParserFactory
from .python_parser import PythonParser

__all__ = [
    'BaseCodeParser',
    'PythonParser',
    'JavaParser',
    'JSTypeScriptParser',
    'CodeParserFactory'
]

