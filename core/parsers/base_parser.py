"""
代码解析器基类模块
"""
from abc import ABC, abstractmethod
from typing import List, Dict, Any


class BaseCodeParser(ABC):
    """代码解析器基类"""

    def __init__(self):
        self.import_alias_map = {}

    @abstractmethod
    def parse_chunks(self, content: str, file_path: str, resolve_code: bool = True) -> List[Dict[str, Any]]:
        """
        解析代码块，子类需要实现
        
        Args:
            content: 代码内容
            file_path: 文件路径
            resolve_code: 是否解析代码依赖关系
            
        Returns:
            List[Dict[str, Any]]: 解析后的代码块列表
        """
        raise NotImplementedError("子类必须实现parse_chunks方法")

    def _create_file_chunk(self, content: str, file_path: str) -> Dict[str, Any]:
        """创建文件级别的代码块"""
        lines = content.splitlines()
        return {
            "type": "file",
            "name": file_path,
            "file": file_path,
            "content": content,
            "start_line": 1,
            "end_line": len(lines),
            "upstream": [],
            "downstream": []
        }

    def _create_function_chunk(self, name: str, content: str, file_path: str, 
                              start_line: int, end_line: int, 
                              raw_func_name: str = None, class_name: str = None) -> Dict[str, Any]:
        """创建函数代码块"""
        chunk = {
            "type": "function",
            "name": name,
            "file": file_path,
            "content": content,
            "start_line": start_line,
            "end_line": end_line,
            "upstream": [],
            "downstream": []
        }
        
        if raw_func_name:
            chunk["_raw_func_name"] = raw_func_name
        if class_name:
            chunk["_class"] = class_name
            
        return chunk

    def _create_class_chunk(self, name: str, content: str, file_path: str,
                           start_line: int, end_line: int) -> Dict[str, Any]:
        """创建类代码块"""
        return {
            "type": "class",
            "name": name,
            "file": file_path,
            "content": content,
            "start_line": start_line,
            "end_line": end_line,
            "upstream": [],
            "downstream": []
        }

    def _create_method_chunk(self, name: str, content: str, file_path: str,
                            start_line: int, end_line: int,
                            raw_method_name: str = None, class_name: str = None,
                            method_type: str = "method") -> Dict[str, Any]:
        """创建方法代码块"""
        chunk = {
            "type": method_type,
            "name": name,
            "file": file_path,
            "content": content,
            "start_line": start_line,
            "end_line": end_line,
            "upstream": [],
            "downstream": []
        }
        
        if raw_method_name:
            chunk["_raw_method_name"] = raw_method_name
        if class_name:
            chunk["_class"] = class_name
            
        return chunk