"""
Agent工厂

负责创建和管理各种Agent实例
"""

import logging
from typing import Any, Dict, Optional, Type, List
from .base_agent import BaseAgent
from .llm_review_agent import LLMReviewAgent
from .diff_analysis_agent import DiffAnalysisAgent
from .result_aggregation_agent import ResultAggregationAgent


class AgentFactory:
    """Agent工厂类"""
    
    def __init__(self):
        """初始化Agent工厂"""
        self.logger = logging.getLogger("agent_factory")
        self._agent_registry: Dict[str, Type[BaseAgent]] = {}
        self._agent_instances: Dict[str, BaseAgent] = {}
        
        # 注册内置Agent类型
        self._register_builtin_agents()
    
    def _register_builtin_agents(self):
        """注册内置Agent类型"""
        self._agent_registry.update({
            'llm_review': LLMReviewAgent,
            'diff_analysis': DiffAnalysisAgent,
            'result_aggregation': ResultAggregationAgent
        })
        
        self.logger.info(f"已注册 {len(self._agent_registry)} 个内置Agent类型")
    
    def register_agent_type(self, agent_type: str, agent_class: Type[BaseAgent]):
        """
        注册新的Agent类型
        
        Args:
            agent_type: Agent类型名称
            agent_class: Agent类
        """
        if not issubclass(agent_class, BaseAgent):
            raise ValueError(f"Agent类 {agent_class} 必须继承自 BaseAgent")
        
        self._agent_registry[agent_type] = agent_class
        self.logger.info(f"已注册Agent类型: {agent_type}")
    
    def create_agent(self, 
                    agent_type: str, 
                    agent_id: Optional[str] = None,
                    config: Optional[Dict[str, Any]] = None,
                    **kwargs) -> BaseAgent:
        """
        创建Agent实例
        
        Args:
            agent_type: Agent类型
            agent_id: Agent实例ID，如果不提供则使用agent_type
            config: Agent配置
            **kwargs: 其他参数
            
        Returns:
            BaseAgent: Agent实例
        """
        if agent_type not in self._agent_registry:
            raise ValueError(f"未知的Agent类型: {agent_type}")
        
        agent_class = self._agent_registry[agent_type]
        agent_id = agent_id or agent_type
        
        try:
            # 根据Agent类型传递不同的参数
            if agent_type == 'llm_review':
                agent = agent_class(
                    llm_service=kwargs.get('llm_service'),
                    devmind_service=kwargs.get('devmind_service'),
                    config=config
                )
            elif agent_type == 'diff_analysis':
                agent = agent_class(
                    chunk_service=kwargs.get('chunk_service'),
                    git_service=kwargs.get('git_service'),
                    config=config
                )
            elif agent_type == 'result_aggregation':
                agent = agent_class(
                    llm_service=kwargs.get('llm_service'),
                    config=config
                )
            else:
                # 通用创建方式
                agent = agent_class(config=config, **kwargs)
            
            # 缓存Agent实例
            self._agent_instances[agent_id] = agent
            
            self.logger.info(f"已创建Agent: {agent_type} (ID: {agent_id})")
            return agent
            
        except Exception as e:
            error_msg = f"创建Agent失败: {agent_type}, 错误: {str(e)}"
            self.logger.error(error_msg)
            raise RuntimeError(error_msg)
    
    def get_agent(self, agent_id: str) -> Optional[BaseAgent]:
        """
        获取Agent实例
        
        Args:
            agent_id: Agent实例ID
            
        Returns:
            BaseAgent: Agent实例，如果不存在返回None
        """
        return self._agent_instances.get(agent_id)
    
    def remove_agent(self, agent_id: str) -> bool:
        """
        移除Agent实例
        
        Args:
            agent_id: Agent实例ID
            
        Returns:
            bool: 是否成功移除
        """
        if agent_id in self._agent_instances:
            del self._agent_instances[agent_id]
            self.logger.info(f"已移除Agent: {agent_id}")
            return True
        return False
    
    def list_agent_types(self) -> List[str]:
        """
        列出所有可用的Agent类型
        
        Returns:
            List[str]: Agent类型列表
        """
        return list(self._agent_registry.keys())
    
    def list_agent_instances(self) -> Dict[str, Dict[str, Any]]:
        """
        列出所有Agent实例的状态
        
        Returns:
            Dict: Agent实例状态信息
        """
        return {
            agent_id: agent.get_status()
            for agent_id, agent in self._agent_instances.items()
        }
    
    def reset_all_agents(self):
        """重置所有Agent实例的状态"""
        for agent in self._agent_instances.values():
            agent.reset()
        self.logger.info("已重置所有Agent状态")
    
    def create_cr_agent_set(self, 
                           llm_service,
                           devmind_service,
                           chunk_service,
                           git_service,
                           config: Optional[Dict[str, Any]] = None) -> Dict[str, BaseAgent]:
        """
        创建完整的CR Agent集合
        
        Args:
            llm_service: LLM服务
            devmind_service: DevMind服务
            chunk_service: 代码分块服务
            git_service: Git服务
            config: 配置
            
        Returns:
            Dict[str, BaseAgent]: Agent集合
        """
        config = config or {}
        
        agents = {}
        
        # 创建差异分析Agent
        agents['diff_analysis'] = self.create_agent(
            'diff_analysis',
            'cr_diff_analysis',
            config.get('diff_analysis', {}),
            chunk_service=chunk_service,
            git_service=git_service
        )
        
        # 创建LLM审查Agent
        agents['llm_review'] = self.create_agent(
            'llm_review',
            'cr_llm_review',
            config.get('llm_review', {}),
            llm_service=llm_service,
            devmind_service=devmind_service
        )
        
        # 创建结果聚合Agent
        agents['result_aggregation'] = self.create_agent(
            'result_aggregation',
            'cr_result_aggregation',
            config.get('result_aggregation', {}),
            llm_service=llm_service
        )
        
        self.logger.info("已创建完整的CR Agent集合")
        return agents


# 全局Agent工厂实例
_agent_factory: Optional[AgentFactory] = None


def get_agent_factory() -> AgentFactory:
    """获取全局Agent工厂实例"""
    global _agent_factory
    if _agent_factory is None:
        _agent_factory = AgentFactory()
    return _agent_factory


def create_agent(agent_type: str, 
                agent_id: Optional[str] = None,
                config: Optional[Dict[str, Any]] = None,
                **kwargs) -> BaseAgent:
    """
    便捷函数：创建Agent实例
    
    Args:
        agent_type: Agent类型
        agent_id: Agent实例ID
        config: Agent配置
        **kwargs: 其他参数
        
    Returns:
        BaseAgent: Agent实例
    """
    factory = get_agent_factory()
    return factory.create_agent(agent_type, agent_id, config, **kwargs)
