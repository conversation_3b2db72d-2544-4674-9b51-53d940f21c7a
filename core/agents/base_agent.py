"""
Agent基类

定义所有Agent的统一接口和基础功能
"""

import logging
from abc import ABC, abstractmethod
from typing import Any, Dict, Optional, List
from dataclasses import dataclass
from enum import Enum


class AgentStatus(Enum):
    """Agent状态枚举"""
    IDLE = "idle"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"


@dataclass
class AgentResult:
    """Agent执行结果"""
    success: bool
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'success': self.success,
            'data': self.data,
            'error': self.error,
            'metadata': self.metadata
        }


class BaseAgent(ABC):
    """Agent基类，定义所有Agent的统一接口"""
    
    def __init__(self, name: str, config: Optional[Dict[str, Any]] = None):
        """
        初始化Agent
        
        Args:
            name: Agent名称
            config: Agent配置
        """
        self.name = name
        self.config = config or {}
        self.logger = logging.getLogger(f"agent.{name}")
        self.status = AgentStatus.IDLE
        self._execution_count = 0
        self._error_count = 0
        
    @abstractmethod
    async def execute(self, input_data: Dict[str, Any]) -> AgentResult:
        """
        执行Agent任务
        
        Args:
            input_data: 输入数据
            
        Returns:
            AgentResult: 执行结果
        """
        pass
    
    @abstractmethod
    def validate_input(self, input_data: Dict[str, Any]) -> bool:
        """
        验证输入数据
        
        Args:
            input_data: 输入数据
            
        Returns:
            bool: 验证是否通过
        """
        pass
    
    async def run(self, input_data: Dict[str, Any]) -> AgentResult:
        """
        运行Agent，包含状态管理和错误处理
        
        Args:
            input_data: 输入数据
            
        Returns:
            AgentResult: 执行结果
        """
        try:
            self.status = AgentStatus.RUNNING
            self._execution_count += 1
            
            # 验证输入
            if not self.validate_input(input_data):
                raise ValueError(f"Agent {self.name} 输入数据验证失败")
            
            self.logger.info(f"Agent {self.name} 开始执行，第 {self._execution_count} 次")
            
            # 执行任务
            result = await self.execute(input_data)
            
            if result.success:
                self.status = AgentStatus.COMPLETED
                self.logger.info(f"Agent {self.name} 执行成功")
            else:
                self.status = AgentStatus.FAILED
                self._error_count += 1
                self.logger.error(f"Agent {self.name} 执行失败: {result.error}")
            
            return result
            
        except Exception as e:
            self.status = AgentStatus.FAILED
            self._error_count += 1
            error_msg = f"Agent {self.name} 执行异常: {str(e)}"
            self.logger.error(error_msg)
            
            return AgentResult(
                success=False,
                error=error_msg,
                metadata={
                    'execution_count': self._execution_count,
                    'error_count': self._error_count
                }
            )
    
    def get_status(self) -> Dict[str, Any]:
        """
        获取Agent状态信息
        
        Returns:
            Dict: 状态信息
        """
        return {
            'name': self.name,
            'status': self.status.value,
            'execution_count': self._execution_count,
            'error_count': self._error_count,
            'config': self.config
        }
    
    def reset(self):
        """重置Agent状态"""
        self.status = AgentStatus.IDLE
        self._execution_count = 0
        self._error_count = 0
        self.logger.info(f"Agent {self.name} 状态已重置")
    
    def update_config(self, config: Dict[str, Any]):
        """
        更新Agent配置
        
        Args:
            config: 新的配置
        """
        self.config.update(config)
        self.logger.info(f"Agent {self.name} 配置已更新")
