"""
LLM审查Agent

负责使用LLM进行代码审查
"""

import time
from typing import Any, Dict, Optional, List

from .base_agent import BaseAgent, AgentResult

# 延迟导入LangChain相关模块，避免启动时的依赖问题
try:
    from langchain.prompts import ChatPromptTemplate
    from langchain.schema import SystemMessage, HumanMessage
    LANGCHAIN_AVAILABLE = True
except ImportError:
    LANGCHAIN_AVAILABLE = False
    ChatPromptTemplate = None
    SystemMessage = None
    HumanMessage = None

try:
    from core.chains.intelligent_cr_chain import build_intelligent_cr_chain
    CR_CHAIN_AVAILABLE = True
except ImportError:
    CR_CHAIN_AVAILABLE = False
    build_intelligent_cr_chain = None


class LLMReviewAgent(BaseAgent):
    """LLM代码审查Agent"""
    
    def __init__(self, 
                 llm_service,
                 devmind_service,
                 config: Optional[Dict[str, Any]] = None):
        """
        初始化LLM审查Agent
        
        Args:
            llm_service: LLM服务实例
            devmind_service: DevMind服务实例
            config: Agent配置
        """
        super().__init__("llm_review_agent", config)
        self.llm_service = llm_service
        self.devmind_service = devmind_service
        
        # 默认配置
        self.default_config = {
            'cr_mode': 'standard',  # fast, standard, deep
            'enable_performance_monitoring': True,
            'dataset_ids': ['default_dataset'],
            'max_retry_count': 3,
            'timeout_seconds': 300
        }
        
        # 合并配置
        self.config = {**self.default_config, **self.config}
        
    def validate_input(self, input_data: Dict[str, Any]) -> bool:
        """
        验证输入数据
        
        Args:
            input_data: 输入数据
            
        Returns:
            bool: 验证是否通过
        """
        required_fields = ['content', 'codePosition']
        
        for field in required_fields:
            if field not in input_data:
                self.logger.error(f"缺少必需字段: {field}")
                return False
                
        if not input_data['content'].strip():
            self.logger.error("代码内容不能为空")
            return False
            
        return True
    
    async def execute(self, input_data: Dict[str, Any]) -> AgentResult:
        """
        执行LLM代码审查

        Args:
            input_data: 输入数据，包含代码内容、位置等信息

        Returns:
            AgentResult: 审查结果
        """
        try:
            # 检查依赖是否可用
            if not LANGCHAIN_AVAILABLE:
                return AgentResult(
                    success=False,
                    error="LangChain依赖不可用，请安装: pip install langchain"
                )

            if not CR_CHAIN_AVAILABLE:
                return AgentResult(
                    success=False,
                    error="CR链模块不可用，请检查core.chains.intelligent_cr_chain模块"
                )

            # 获取配置
            cr_mode = self.config.get('cr_mode', 'standard')
            dataset_ids = self.config.get('dataset_ids', ['default_dataset'])
            enable_monitoring = self.config.get('enable_performance_monitoring', True)

            # 获取LLM实例
            llm = self.llm_service.get_llm()

            # 准备链输入
            chain_input = self._prepare_chain_input(input_data)

            # 构建CR链
            cr_chain = build_intelligent_cr_chain(
                llm,
                self.devmind_service,
                dataset_ids,
                cr_mode
            )

            self.logger.info(f"使用 {cr_mode} 模式进行代码审查")

            # 性能监控
            start_time = time.time() if enable_monitoring else None

            # 执行审查
            chain_result = await cr_chain.ainvoke(chain_input)

            # 记录性能
            if enable_monitoring and start_time:
                elapsed = time.time() - start_time
                self.logger.info(f"LLM审查耗时: {elapsed:.2f}s, 模式: {cr_mode}")

            # 处理结果
            result_data = self._process_chain_result(chain_result, input_data)

            return AgentResult(
                success=True,
                data=result_data,
                metadata={
                    'cr_mode': cr_mode,
                    'execution_time': elapsed if enable_monitoring and start_time else None,
                    'dataset_ids': dataset_ids
                }
            )

        except Exception as e:
            error_msg = f"LLM审查执行失败: {str(e)}"
            self.logger.error(error_msg)
            return AgentResult(
                success=False,
                error=error_msg
            )
    
    def _prepare_chain_input(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        准备链输入数据
        
        Args:
            input_data: 原始输入数据
            
        Returns:
            Dict: 链输入数据
        """
        chain_input = dict(input_data)
        
        # 确保依赖信息被正确传递
        if 'upstream' not in chain_input and 'upstream_code' in input_data:
            chain_input['upstream'] = list(input_data['upstream_code'].keys())
        if 'downstream' not in chain_input and 'downstream_code' in input_data:
            chain_input['downstream'] = list(input_data['downstream_code'].keys())
        
        # 确保diff_content和full_code存在
        if 'diff_content' not in chain_input and 'content' in input_data:
            chain_input['diff_content'] = input_data['content']
        if 'full_code' not in chain_input and 'content' in input_data:
            chain_input['full_code'] = input_data['content']
        
        return chain_input
    
    def _process_chain_result(self, 
                            chain_result: Dict[str, Any], 
                            input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理链执行结果
        
        Args:
            chain_result: 链执行结果
            input_data: 原始输入数据
            
        Returns:
            Dict: 处理后的结果
        """
        return {
            "codePosition": input_data.get("codePosition"),
            "codePositionArray": input_data.get("codePositionArray", [1, 0, 1, 0]),
            "cr_suggestion": chain_result.get("final_judge"),
            "knowledge": chain_result.get("knowledge"),
            "problems": chain_result.get("problems"),
            "upstream": input_data.get("upstream", []),
            "downstream": input_data.get("downstream", []),
            "upstream_code": input_data.get("upstream_code", {}),
            "downstream_code": input_data.get("downstream_code", {})
        }
    
    def set_cr_mode(self, mode: str):
        """
        设置CR模式
        
        Args:
            mode: CR模式 (fast, standard, deep)
        """
        valid_modes = ["fast", "standard", "deep"]
        if mode not in valid_modes:
            raise ValueError(f"无效的CR模式: {mode}，支持的模式: {valid_modes}")
        
        self.config['cr_mode'] = mode
        self.logger.info(f"CR模式已设置为: {mode}")
    
    def get_cr_mode(self) -> str:
        """获取当前CR模式"""
        return self.config.get('cr_mode', 'standard')
