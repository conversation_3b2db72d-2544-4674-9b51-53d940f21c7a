"""
代码差异分析Agent

负责分析代码差异，提取关键信息和上下文
"""

from typing import Any, Dict, Optional, List
from .base_agent import BaseAgent, AgentResult


class DiffAnalysisAgent(BaseAgent):
    """代码差异分析Agent"""
    
    def __init__(self, 
                 chunk_service,
                 git_service,
                 config: Optional[Dict[str, Any]] = None):
        """
        初始化差异分析Agent
        
        Args:
            chunk_service: 代码分块服务
            git_service: Git服务
            config: Agent配置
        """
        super().__init__("diff_analysis_agent", config)
        self.chunk_service = chunk_service
        self.git_service = git_service
        
        # 默认配置
        self.default_config = {
            'max_diff_lines': 400,
            'enable_context_enrichment': True,
            'include_dependencies': True,
            'chunk_size': 50
        }
        
        # 合并配置
        self.config = {**self.default_config, **self.config}

    def update_chunk_service(self, chunk_service):
        """
        更新chunk_service

        Args:
            chunk_service: 新的代码分块服务实例
        """
        self.chunk_service = chunk_service
        self.logger.info("已更新DiffAnalysisAgent的chunk_service")
    
    def validate_input(self, input_data: Dict[str, Any]) -> bool:
        """
        验证输入数据
        
        Args:
            input_data: 输入数据
            
        Returns:
            bool: 验证是否通过
        """
        if 'code_diff' not in input_data:
            self.logger.error("缺少必需字段: code_diff")
            return False
            
        if not input_data['code_diff'].strip():
            self.logger.error("代码差异内容不能为空")
            return False
            
        return True
    
    async def execute(self, input_data: Dict[str, Any]) -> AgentResult:
        """
        执行代码差异分析
        
        Args:
            input_data: 输入数据，包含代码差异和Git信息
            
        Returns:
            AgentResult: 分析结果
        """
        try:
            code_diff = input_data['code_diff']
            options = input_data.get('options', {})
            
            # 提取Git信息
            project = options.get('project')
            repo = options.get('repo')
            from_branch = options.get('fromBranch')
            to_branch = options.get('toBranch')
            
            self.logger.info(f"开始分析代码差异: {project}/{repo} {from_branch}...{to_branch}")
            
            # 分析差异并分块
            if self.config.get('enable_context_enrichment', True):
                # 使用上下文增强的分析
                segments = await self._analyze_with_context(
                    code_diff, project, repo, from_branch, to_branch
                )
            else:
                # 基础分析
                segments = await self._basic_analysis(code_diff, options)
            
            if not segments:
                raise ValueError("未能分析出有效的代码片段")
            
            self.logger.info(f"代码差异分析完成，共生成 {len(segments)} 个片段")
            
            return AgentResult(
                success=True,
                data={
                    'segments': segments,
                    'total_segments': len(segments),
                    'analysis_metadata': {
                        'project': project,
                        'repo': repo,
                        'from_branch': from_branch,
                        'to_branch': to_branch,
                        'context_enriched': self.config.get('enable_context_enrichment', True)
                    }
                }
            )
            
        except Exception as e:
            error_msg = f"代码差异分析失败: {str(e)}"
            self.logger.error(error_msg)
            return AgentResult(
                success=False,
                error=error_msg
            )
    
    async def _analyze_with_context(self, 
                                  code_diff: str, 
                                  project: str, 
                                  repo: str, 
                                  from_branch: str,
                                  to_branch: str) -> List[Dict[str, Any]]:
        """
        使用上下文增强进行代码差异分析
        
        Args:
            code_diff: 代码差异
            project: 项目名
            repo: 仓库名
            from_branch: 源分支
            to_branch: 目标分支
            
        Returns:
            List[Dict]: 分析后的代码片段
        """
        try:
            # 检查chunk_service是否可用
            if not self.chunk_service:
                self.logger.warning("ChunkService不可用，降级到基础分析")
                return await self._basic_analysis(code_diff, {
                    'project': project,
                    'repo': repo,
                    'fromBranch': from_branch,
                    'toBranch': to_branch
                })

            # 使用ChunkService进行上下文增强分析
            segments = self.chunk_service.enrich_diff_segments_with_context(code_diff)
            
            # 转换为标准格式
            standardized_segments = []
            for segment in segments:
                # 获取文件信息
                file_path = segment.get('file', segment.get('file_path', 'unknown'))
                start_line = segment.get('start_line', 1)
                end_line = segment.get('end_line', 1)

                # 构建codePosition
                code_position = segment.get('codePosition', f"{file_path}:{start_line}-{end_line}")

                standardized_segment = {
                    'content': segment.get('content', ''),
                    'codePosition': code_position,
                    'codePositionArray': [start_line, 0, end_line, 0],
                    'upstream': segment.get('upstream', []),
                    'downstream': segment.get('downstream', []),
                    'upstream_code': segment.get('upstream_code', {}),
                    'downstream_code': segment.get('downstream_code', {}),
                    'file': file_path,  # 统一使用file字段
                    'file_path': file_path,  # 保持兼容性
                    'type': segment.get('type', 'unknown'),  # 添加文件类型
                    'start_line': start_line,
                    'end_line': end_line
                }
                standardized_segments.append(standardized_segment)
            
            return standardized_segments
            
        except Exception as e:
            self.logger.error(f"上下文增强分析失败: {str(e)}")
            # 降级到基础分析
            return await self._basic_analysis(code_diff, {
                'project': project,
                'repo': repo,
                'fromBranch': from_branch,
                'toBranch': to_branch
            })
    
    async def _basic_analysis(self, 
                            code_diff: str, 
                            options: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        基础代码差异分析
        
        Args:
            code_diff: 代码差异
            options: 选项参数
            
        Returns:
            List[Dict]: 分析后的代码片段
        """
        try:
            # 简单按行分割差异
            lines = code_diff.split('\n')
            max_lines = self.config.get('max_diff_lines', 400)
            
            segments = []
            current_segment = []
            current_file = None
            line_count = 0
            
            for line in lines:
                if line.startswith('diff --git') or line.startswith('+++'):
                    # 新文件开始
                    if current_segment and current_file:
                        segments.append(self._create_segment(
                            '\n'.join(current_segment),
                            current_file,
                            line_count
                        ))
                        current_segment = []
                        line_count = 0

                    if line.startswith('diff --git'):
                        # 从 diff --git a/file b/file 中提取文件名
                        import re
                        match = re.search(r'diff --git a/(.+?) b/(.+)', line)
                        if match:
                            current_file = match.group(2)  # 使用b/后面的文件名（新文件）
                        else:
                            current_file = "unknown"
                    elif line.startswith('+++'):
                        # 从 +++ b/file 中提取文件名
                        current_file = self._extract_file_from_plus_line(line)
                
                current_segment.append(line)
                line_count += 1
                
                # 如果达到最大行数，创建一个片段
                if line_count >= max_lines:
                    if current_file:
                        segments.append(self._create_segment(
                            '\n'.join(current_segment), 
                            current_file, 
                            line_count
                        ))
                    current_segment = []
                    line_count = 0
            
            # 处理最后一个片段
            if current_segment and current_file:
                segments.append(self._create_segment(
                    '\n'.join(current_segment), 
                    current_file, 
                    line_count
                ))
            
            return segments
            
        except Exception as e:
            self.logger.error(f"基础分析失败: {str(e)}")
            # 如果基础分析也失败，返回整个diff作为单个片段
            return [self._create_segment(code_diff, "unknown_file", len(code_diff.split('\n')))]
    
    def _create_segment(self, content: str, file_path: str, line_count: int) -> Dict[str, Any]:
        """
        创建代码片段

        Args:
            content: 代码内容
            file_path: 文件路径
            line_count: 行数

        Returns:
            Dict: 代码片段
        """
        # 清理文件路径，移除diff前缀
        clean_file_path = self._clean_file_path(file_path)

        # 检测文件类型
        file_type = self._detect_file_type(clean_file_path)

        return {
            'content': content,
            'codePosition': f"{clean_file_path}:1-{line_count}",
            'codePositionArray': [1, 0, line_count, 0],
            'upstream': [],
            'downstream': [],
            'upstream_code': {},
            'downstream_code': {},
            'file': clean_file_path,  # 统一使用file字段
            'file_path': clean_file_path,  # 保持兼容性
            'type': file_type,  # 添加文件类型
            'start_line': 1,
            'end_line': line_count
        }

    def _clean_file_path(self, file_path: str) -> str:
        """
        清理文件路径，移除diff相关的前缀

        Args:
            file_path: 原始文件路径

        Returns:
            str: 清理后的文件路径
        """
        if not file_path or file_path == "unknown_file":
            return "unknown"

        # 移除常见的diff前缀
        if file_path.startswith('b/'):
            file_path = file_path[2:]
        elif file_path.startswith('a/'):
            file_path = file_path[2:]
        elif file_path.startswith('+++'):
            # 处理 +++ b/filename 格式
            parts = file_path.split()
            if len(parts) > 1 and parts[1].startswith('b/'):
                file_path = parts[1][2:]
            elif len(parts) > 1:
                file_path = parts[1]
            else:
                file_path = "unknown"
        elif file_path.startswith('---'):
            # 处理 --- a/filename 格式
            parts = file_path.split()
            if len(parts) > 1 and parts[1].startswith('a/'):
                file_path = parts[1][2:]
            elif len(parts) > 1:
                file_path = parts[1]
            else:
                file_path = "unknown"

        # 移除路径中的空格和特殊字符
        file_path = file_path.strip()

        return file_path if file_path else "unknown"

    def _detect_file_type(self, file_path: str) -> str:
        """
        根据文件扩展名检测文件类型

        Args:
            file_path: 文件路径

        Returns:
            str: 文件类型
        """
        if not file_path or file_path == "unknown":
            return "unknown"

        # 获取文件扩展名
        if '.' not in file_path:
            return "unknown"

        ext = file_path.split('.')[-1].lower()

        # 文件类型映射
        type_mapping = {
            'py': 'python',
            'js': 'javascript',
            'jsx': 'javascript',
            'ts': 'typescript',
            'tsx': 'typescript',
            'java': 'java',
            'cpp': 'cpp',
            'cc': 'cpp',
            'cxx': 'cpp',
            'c': 'c',
            'h': 'c',
            'hpp': 'cpp',
            'go': 'go',
            'rs': 'rust',
            'php': 'php',
            'rb': 'ruby',
            'swift': 'swift',
            'kt': 'kotlin',
            'scala': 'scala',
            'sh': 'shell',
            'bash': 'shell',
            'zsh': 'shell',
            'fish': 'shell',
            'sql': 'sql',
            'html': 'html',
            'htm': 'html',
            'css': 'css',
            'scss': 'css',
            'sass': 'css',
            'less': 'css',
            'xml': 'xml',
            'json': 'json',
            'yaml': 'yaml',
            'yml': 'yaml',
            'toml': 'toml',
            'ini': 'config',
            'conf': 'config',
            'cfg': 'config',
            'env': 'config',
            'md': 'markdown',
            'txt': 'text',
            'log': 'text',
            'dockerfile': 'docker',
            'makefile': 'makefile'
        }

        return type_mapping.get(ext, 'unknown')

    def _extract_file_from_plus_line(self, line: str) -> str:
        """
        从+++ b/filename格式的行中提取文件名

        Args:
            line: diff行内容

        Returns:
            str: 提取的文件名
        """
        if line.startswith('+++'):
            # 移除+++前缀
            line = line[3:].strip()

            # 处理 b/filename 格式
            if line.startswith('b/'):
                return line[2:]
            elif line.startswith('a/'):
                return line[2:]
            else:
                # 直接返回文件名
                return line if line else "unknown"

        return "unknown"
