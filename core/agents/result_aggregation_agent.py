
"""
结果聚合Agent

负责聚合多个代码审查结果，生成最终报告
"""

import json
from typing import Any, Dict, Optional, List

from .base_agent import BaseAgent, AgentResult

# 延迟导入LangChain相关模块，避免启动时的依赖问题
try:
    from langchain.output_parsers import StructuredOutputParser, OutputFixingParser
    from langchain.prompts import ChatPromptTemplate
    from langchain.schema import SystemMessage, HumanMessage
    from langchain_core.output_parsers.json import JsonOutputParser
    LANGCHAIN_AVAILABLE = True
except ImportError:
    LANGCHAIN_AVAILABLE = False
    StructuredOutputParser = None
    OutputFixingParser = None
    ChatPromptTemplate = None
    SystemMessage = None
    HumanMessage = None
    JsonOutputParser = None

try:
    from pydantic import BaseModel, Field
    PYDANTIC_AVAILABLE = True
except ImportError:
    PYDANTIC_AVAILABLE = False
    BaseModel = None
    Field = None


# 条件定义Pydantic模型
if PYDANTIC_AVAILABLE:
    class ProblemItem(BaseModel):
        """问题项模型"""
        scene: str = Field(description="检查类别，分为'commonCheck'、'customCheck'两类")
        num: str = Field(description="问题数量")
        checkResult: str = Field(description="通过或不通过,存在P0问题则为不通过")

    class MergedResult(BaseModel):
        """合并结果模型"""
        checkBranch: str = Field(description="分支名")
        sumCheckResult: str = Field(description="通过或不通过,存在P0问题则为不通过")
        resultDesc: str = Field(description="问题类别及简要描述")
        totalProblem: str = Field(description="问题总数")
        problemList: List[ProblemItem] = Field(description="问题列表")
else:
    # 如果Pydantic不可用，定义简单的类
    class ProblemItem:
        def __init__(self, scene="", num="", checkResult=""):
            self.scene = scene
            self.num = num
            self.checkResult = checkResult

    class MergedResult:
        def __init__(self, checkBranch="", sumCheckResult="", resultDesc="", totalProblem="", problemList=None):
            self.checkBranch = checkBranch
            self.sumCheckResult = sumCheckResult
            self.resultDesc = resultDesc
            self.totalProblem = totalProblem
            self.problemList = problemList or []


class ResultAggregationAgent(BaseAgent):
    """结果聚合Agent"""

    def __init__(self,
                 llm_service,
                 config: Optional[Dict[str, Any]] = None):
        """
        初始化结果聚合Agent
        Args:
            llm_service: LLM服务实例
            config: Agent配置
        """
        super().__init__("result_aggregation_agent", config)
        self.llm_service = llm_service
        # 默认配置
        self.default_config = {
            'preserve_details': True,
            'enable_validation': True,
            'merge_strategy': 'intelligent'  # simple, intelligent
        }
        # 合并配置
        self.config = {**self.default_config, **self.config}
    def validate_input(self, input_data: Dict[str, Any]) -> bool:
        """
        验证输入数据
        Args:
            input_data: 输入数据
        Returns:
            bool: 验证是否通过
        """
        if 'results' not in input_data:
            self.logger.error("缺少必需字段: results")
            return False
        results = input_data['results']
        if not isinstance(results, list) or len(results) == 0:
            self.logger.error("results必须是非空列表")
            return False
        return True
    async def execute(self, input_data: Dict[str, Any]) -> AgentResult:
        """
        执行结果聚合和增强

        Args:
            input_data: 输入数据，包含:
                - results: 审查结果列表
                - options: 可选参数
                - segments: 代码片段信息
                - metadata: 元数据信息

        Returns:
            AgentResult: 聚合和增强后的结果
        """
        try:
            results = input_data['results']
            options = input_data.get('options', {})
            segments = input_data.get('segments', [])
            metadata = input_data.get('metadata', {})

            self.logger.info(f"开始聚合和增强 {len(results)} 个审查结果")

            # 第一步：从外部参数确定正确的checkBranch
            correct_check_branch = self._determine_check_branch_from_external(options, metadata)
            self.logger.info(f"从外部参数确定checkBranch: {correct_check_branch}")

            # 第二步：预处理所有结果，统一设置正确的checkBranch
            preprocessed_results = self._preprocess_results_with_correct_branch(results, correct_check_branch)

            # 第三步：基础聚合
            if len(preprocessed_results) == 1:
                merged_result = preprocessed_results[0]
                aggregation_type = 'single_result'
            else:
                # 选择聚合策略
                merge_strategy = self.config.get('merge_strategy', 'intelligent')

                # 如果LangChain不可用，强制使用简单合并
                if not LANGCHAIN_AVAILABLE and merge_strategy == 'intelligent':
                    self.logger.warning("LangChain不可用，使用简单合并策略")
                    merge_strategy = 'simple'

                if merge_strategy == 'intelligent' and LANGCHAIN_AVAILABLE:
                    merged_result = await self._intelligent_merge(preprocessed_results)
                else:
                    merged_result = await self._simple_merge(preprocessed_results)

                aggregation_type = merge_strategy

            # 确保合并结果中的checkBranch是正确的
            merged_result['checkBranch'] = correct_check_branch

            self.logger.info("基础聚合完成")

            # 第二步：结果增强
            enhanced_result = await self._enhance_result(merged_result, preprocessed_results, segments, metadata, options)

            self.logger.info("结果聚合和增强完成")

            return AgentResult(
                success=True,
                data=enhanced_result,
                metadata={
                    'aggregation_type': aggregation_type,
                    'input_count': len(results),
                    'enhancement_applied': True,
                    'segments_count': len(segments)
                }
            )

        except Exception as e:
            error_msg = f"结果聚合和增强失败: {str(e)}"
            self.logger.error(error_msg)

            # 如果聚合失败，尝试返回基础格式的结果
            try:
                results = input_data.get('results', [])
                if results:
                    fallback_result = await self._create_fallback_result(results, input_data)
                    self.logger.warning("聚合失败，返回降级结果")
                    return AgentResult(
                        success=True,
                        data=fallback_result,
                        metadata={'aggregation_type': 'fallback_result'}
                    )
            except Exception as fallback_error:
                self.logger.error(f"降级结果创建也失败: {str(fallback_error)}")

            return AgentResult(
                success=False,
                error=error_msg
            )
    async def _intelligent_merge(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        智能合并结果，使用LLM进行合并

        Args:
            results: 审查结果列表

        Returns:
            Dict: 合并后的结果
        """
        try:
            # 提取并保存所有detail信息，按scene分类
            details_by_scene = {}
            results_without_details = []

            for result in results:
                result_copy = result.copy()

                # 保存detail信息并按scene分类
                if 'problemList' in result_copy and isinstance(result_copy['problemList'], list):
                    for problem in result_copy['problemList']:
                        if 'scene' in problem and 'detail' in problem and isinstance(problem['detail'], list):
                            if problem['scene'] not in details_by_scene:
                                details_by_scene[problem['scene']] = []

                            details_by_scene[problem['scene']].extend(problem['detail'])

                            # 对每个场景下的detail按level排序
                            details_by_scene[problem['scene']].sort(key=lambda x: {
                                'p0': 0, 'p1': 1, 'p2': 2
                            }.get(x.get('level', '').lower(), 999))

                    # 移除detail信息，保留其他字段
                    result_copy['problemList'] = [
                        {k: v for k, v in problem.items() if k != 'detail'}
                        for problem in result_copy['problemList']
                    ]

                results_without_details.append(result_copy)
            # 使用LLM合并结果
            output_parser = JsonOutputParser(pydantic_object=MergedResult)
            # 创建系统提示
            system_message = SystemMessage("""
你是一个JSON处理专家，用户会给你一个json数组，请按照以下要求进行处理:

## 重要：你必须只返回纯JSON格式，不要包含任何解释文字或代码块标记。

## 数据处理要求：
1. checkBranch合并逻辑：如果输入数据中没有checkBranch字段或为空，则设置为"未知分支"；如果有多个不同的checkBranch值，选择第一个有效的值
2. resultDesc合并逻辑: 按照问题等级(P0,P1,P2)进行分类累加,格式必须为"P0:X个,P1:X个,P2:X个"，如果某个等级为0则不显示
3. totalProblem合并逻辑: 对每个数据的totalProblem转换为数字后进行累加,保证计算正确
4. problemList合并逻辑: 每项的scene保持原始值，num相加，checkResult存在不通过的值则为不通过
5. sumCheckResult合并逻辑: 如果存在P0问题，则sumCheckResult为不通过，否则为通过

## 输出格式：
- 只返回JSON对象，不要包含```json```标记
- 不要添加任何解释文字
- 确保JSON格式正确且完整
            """)
            # 创建人类消息
            human_message = HumanMessage(
                f"以下是多个代码片段的review结果，请帮我合并: {json.dumps(results_without_details)}"
            )
            # 创建提示模板
            prompt = ChatPromptTemplate.from_messages([system_message, human_message])
            # 获取LLM实例
            llm = self.llm_service.get_llm()
            # 创建包含错误处理的管道
            try:
                # 首先尝试直接解析
                chain = prompt | llm | output_parser
                merged_result = chain.invoke({})
                self.logger.info(f"LLM合并结果: {json.dumps(merged_result)}")
            except Exception as parse_error:
                self.logger.warning(f"直接解析失败: {str(parse_error)}")

                # 尝试使用修复解析器
                try:
                    parser_with_fix = OutputFixingParser.from_llm(llm, output_parser)
                    chain = prompt | llm | parser_with_fix
                    merged_result = chain.invoke({})
                    self.logger.info(f"修复解析成功: {json.dumps(merged_result)}")
                except Exception as fix_error:
                    self.logger.warning(f"修复解析也失败: {str(fix_error)}")

                    # 尝试手动提取JSON
                    try:
                        raw_response = (prompt | llm).invoke({})
                        merged_result = self._extract_json_from_response(raw_response.content if hasattr(raw_response, 'content') else str(raw_response))
                        self.logger.info(f"手动提取JSON成功: {json.dumps(merged_result)}")
                    except Exception as manual_error:
                        self.logger.error(f"手动提取JSON失败: {str(manual_error)}")
                        raise manual_error
            # 将detail信息添加回结果
            if self.config.get('preserve_details', True):
                if 'problemList' in merged_result and isinstance(merged_result['problemList'], list):
                    merged_result['problemList'] = [
                        {**problem, 'detail': details_by_scene.get(problem['scene'], [])}
                        for problem in merged_result['problemList']
                    ]

            return merged_result
        except Exception as e:
            self.logger.error(f"智能合并失败: {str(e)}")
            # 降级到简单合并
            return await self._simple_merge(results)
    async def _simple_merge(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        简单合并结果

        Args:
            results: 审查结果列表

        Returns:
            Dict: 合并后的结果
        """
        # 简单的合并逻辑：取第一个结果作为基础，累加问题数量
        base_result = results[0].copy()

        total_problems = 0
        all_problems = []

        for result in results:
            if 'totalProblem' in result:
                try:
                    total_problems += int(result['totalProblem'])
                except (ValueError, TypeError):
                    pass

            if 'problemList' in result and isinstance(result['problemList'], list):
                all_problems.extend(result['problemList'])

        # 更新合并结果
        base_result['totalProblem'] = str(total_problems)
        base_result['problemList'] = all_problems

        # 检查是否有P0问题
        has_p0_problem = any(
            problem.get('checkResult') == '不通过'
            for problem in all_problems
        )
        base_result['sumCheckResult'] = '不通过' if has_p0_problem else '通过'

        return base_result

    def _extract_json_from_response(self, response_text: str) -> Dict[str, Any]:
        """
        从LLM响应中手动提取JSON

        Args:
            response_text: LLM的原始响应文本

        Returns:
            Dict: 提取的JSON对象
        """
        import re

        try:
            # 尝试直接解析整个响应
            return json.loads(response_text)
        except json.JSONDecodeError:
            pass

        # 尝试提取JSON代码块
        json_patterns = [
            r'```json\s*(\{.*?\})\s*```',  # ```json {...} ```
            r'```\s*(\{.*?\})\s*```',      # ``` {...} ```
            r'(\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\})',  # 匹配完整的JSON对象
        ]

        for pattern in json_patterns:
            matches = re.findall(pattern, response_text, re.DOTALL)
            for match in matches:
                try:
                    return json.loads(match)
                except json.JSONDecodeError:
                    continue

        # 尝试查找最大的JSON对象
        start_idx = response_text.find('{')
        if start_idx != -1:
            brace_count = 0
            for i, char in enumerate(response_text[start_idx:], start_idx):
                if char == '{':
                    brace_count += 1
                elif char == '}':
                    brace_count -= 1
                    if brace_count == 0:
                        try:
                            json_str = response_text[start_idx:i+1]
                            return json.loads(json_str)
                        except json.JSONDecodeError:
                            break

        # 如果所有方法都失败，抛出异常
        raise ValueError(f"无法从响应中提取有效的JSON: {response_text[:200]}...")

    async def _enhance_result(self,
                            merged_result: Dict[str, Any],
                            original_results: List[Dict[str, Any]],
                            segments: List[Dict[str, Any]],
                            metadata: Dict[str, Any],
                            options: Dict[str, Any]) -> Dict[str, Any]:
        """
        增强聚合结果，添加UI展示所需的完整信息

        Args:
            merged_result: 基础聚合结果
            original_results: 原始审查结果列表
            segments: 代码片段信息
            metadata: 元数据信息
            options: 选项参数

        Returns:
            Dict: 增强后的完整结果
        """
        try:
            # checkBranch已经在预处理阶段设置好了，直接使用
            self.logger.info(f"使用预处理设置的checkBranch: {merged_result.get('checkBranch')}")

            # 尝试使用CR结果增强器
            from utils.cr_result_enhancer import CRResultEnhancer

            enhancer = CRResultEnhancer()

            # 构建分支信息传递给增强器
            branch_info = self._build_branch_info(options)

            enhanced_result = enhancer.enhance_cr_result(
                merged_result,
                reviewer="AI代码审查系统",
                branch_info=branch_info
            )
            complete_result = enhancer.to_complete_format(enhanced_result)

            # 添加额外的元数据信息
            complete_result['originalReviewResults'] = original_results
            complete_result['segmentsInfo'] = {
                'count': len(segments),
                'segments': segments
            }
            complete_result['metadata'] = metadata
            complete_result['options'] = options

            # 更新statistics中的计数信息
            if 'statistics' in complete_result:
                complete_result['statistics']['segmentsCount'] = len(segments)
                complete_result['statistics']['reviewResultsCount'] = len(original_results)

            self.logger.info("使用CR结果增强器成功增强结果")
            return complete_result

        except Exception as e:
            self.logger.warning(f"CR结果增强器不可用或失败: {str(e)}，使用内置增强逻辑")
            return await self._builtin_enhance_result(merged_result, original_results, segments, metadata, options)


    async def _builtin_enhance_result(self,
                                    merged_result: Dict[str, Any],
                                    original_results: List[Dict[str, Any]],
                                    segments: List[Dict[str, Any]],
                                    metadata: Dict[str, Any],
                                    options: Dict[str, Any]) -> Dict[str, Any]:
        """
        内置结果增强逻辑（当CR结果增强器不可用时的降级方案）

        Args:
            merged_result: 基础聚合结果
            original_results: 原始审查结果列表
            segments: 代码片段信息
            metadata: 元数据信息
            options: 选项参数

        Returns:
            Dict: 增强后的结果
            :param merged_result:
            :param original_results:
            :param segments:
            :param metadata:
            :param options:
            :return:
        """

        # 统计问题信息
        total_problems = 0
        critical_count = 0
        warning_count = 0
        moderate_count = 0
        minor_count = 0
        all_problems = []

        self.logger.debug(f"处理合并结果: {merged_result}")

        # 从合并结果中提取问题信息
        if 'problemList' in merged_result:
            for problem_group in merged_result['problemList']:
                if 'detail' in problem_group and isinstance(problem_group['detail'], list):
                    for problem in problem_group['detail']:
                        all_problems.append(problem)
                        level = problem.get('level', '').lower()
                        if level == 'p0':
                            critical_count += 1
                        elif level == 'p1':
                            warning_count += 1
                        elif level == 'p2':
                            moderate_count += 1
                        else:
                            minor_count += 1

        total_problems = critical_count + warning_count + moderate_count + minor_count

        # 计算整体评分 - 统一评分标准
        overall_score = max(0, 100 - (critical_count * 25 + warning_count * 15 + moderate_count * 10 + minor_count * 5))

        # 确定总体结果 - 确保数据一致性
        if critical_count > 0:
            overall_result = "不通过"  # 有严重问题必须不通过
            self.logger.info(f"存在{critical_count}个严重问题(P0)，结果为不通过")
        elif overall_score < 80:
            overall_result = "不通过"  # 评分低于80分不通过
            self.logger.info(f"评分{overall_score}分低于80分，结果为不通过")
        elif total_problems == 0:
            overall_result = "通过"  # 无问题通过
            self.logger.info("无问题发现，结果为通过")
        else:
            overall_result = "通过"  # 有问题但评分达标，通过
            self.logger.info(f"有{total_problems}个问题但评分{overall_score}分达标，结果为通过")

        # 生成结果描述 - 确保与实际问题一致
        if total_problems == 0:
            result_description = "代码质量良好，未发现问题"
        else:
            desc_parts = []
            if critical_count > 0:
                desc_parts.append(f"P0:{critical_count}个")
            if warning_count > 0:
                desc_parts.append(f"P1:{warning_count}个")
            if moderate_count > 0:
                desc_parts.append(f"P2:{moderate_count}个")
            if minor_count > 0:
                desc_parts.append(f"P3+:{minor_count}个")
            result_description = ",".join(desc_parts)

        # 生成全局修改建议 - 这是唯一需要LLM汇总的部分
        recommendations = await self._generate_global_recommendations(
            all_problems, critical_count, warning_count, moderate_count, minor_count, total_problems
        )

        # checkBranch已经在预处理阶段设置好了，直接使用
        self.logger.info(f"内置增强使用预处理的checkBranch: {merged_result.get('checkBranch')}")

        # 构建增强结果
        enhanced_result = {
            # 总结信息 - 基于统计计算，保证精确度
            'summary': self._generate_precise_summary(
                merged_result, original_results, segments, metadata,
                total_problems, critical_count, warning_count, moderate_count, minor_count,
                overall_result, result_description, overall_score
            ),

            # 评分信息
            'scoring': {
                'overallScore': overall_score,
                'maxScore': 100,
                'dimensions': {
                    'criticalIssues': {
                        'score': max(0, 40 - critical_count * 20),
                        'maxScore': 40,
                        'description': f"严重问题扣分 ({critical_count}个)"
                    },
                    'warningIssues': {
                        'score': max(0, 30 - warning_count * 10),
                        'maxScore': 30,
                        'description': f"警告问题扣分 ({warning_count}个)"
                    },
                    'moderateIssues': {
                        'score': max(0, 20 - moderate_count * 5),
                        'maxScore': 20,
                        'description': f"中等问题扣分 ({moderate_count}个)"
                    },
                    'minorIssues': {
                        'score': max(0, 10 - minor_count * 2),
                        'maxScore': 10,
                        'description': f"轻微问题扣分 ({minor_count}个)"
                    }
                },
                'qualityGrade': 'A' if overall_score >= 90 else 'B' if overall_score >= 80 else 'C' if overall_score >= 70 else 'D',
                'passThreshold': 80,
                'isPassed': overall_score >= 80 and critical_count == 0
            },

            # 问题列表
            'problems': [
                {
                    'level': problem.get('level', ''),
                    'problem': problem.get('problem', ''),
                    'suggestion': problem.get('suggestion', ''),
                    'targetCode': problem.get('targetCode', ''),
                    'codePosition': problem.get('codePosition', [])
                }
                for problem in all_problems
            ],

            # 统计信息
            'statistics': {
                'totalProblems': total_problems,
                'criticalCount': critical_count,
                'warningCount': warning_count,
                'moderateCount': moderate_count,
                'minorCount': minor_count,
                'segmentsCount': len(segments),
                'reviewResultsCount': len(original_results),
                'problemDistribution': {
                    'P0': critical_count,
                    'P1': warning_count,
                    'P2': moderate_count,
                    'P3+': minor_count
                }
            },

            # 审查指标
            'reviewMetrics': {
                'qualityScore': overall_score,
                'riskLevel': '高' if critical_count > 0 else '中' if warning_count > 2 else '低',
                'totalProblems': total_problems,
                'problemDensity': f"{total_problems}/文件",
                'coverageRate': '100%',  # 假设全覆盖
                'reviewEfficiency': 'high' if metadata.get('parallel_processing') else 'standard'
            },

            # 改进建议
            'recommendations': recommendations,

            # 保留原始数据
            'originalResult': merged_result,
            'originalReviewResults': original_results,
            'segmentsInfo': {
                'count': len(segments),
                'segments': segments
            },
            'metadata': metadata,
            'options': options
        }

        return enhanced_result

    def _generate_precise_summary(self,
                                merged_result: Dict[str, Any],
                                original_results: List[Dict[str, Any]],
                                segments: List[Dict[str, Any]],
                                metadata: Dict[str, Any],
                                total_problems: int,
                                critical_count: int,
                                warning_count: int,
                                moderate_count: int,
                                minor_count: int,
                                overall_result: str,
                                result_description: str,
                                overall_score: int) -> Dict[str, Any]:
        """
        生成精确的summary，基于统计计算保证准确性

        Args:
            merged_result: 合并后的结果
            original_results: 原始结果列表
            segments: 代码片段列表
            metadata: 元数据
            total_problems: 总问题数
            critical_count: 严重问题数
            warning_count: 警告问题数
            moderate_count: 中等问题数
            minor_count: 轻微问题数
            overall_result: 总体结果
            result_description: 结果描述
            overall_score: 总体评分

        Returns:
            Dict: 精确的summary字典
        """
        from datetime import datetime

        # 基础信息 - 直接从数据中获取
        summary = {
            # 基础审查信息
            'checkBranch': merged_result.get('checkBranch', 'unknown'),
            'reviewTime': datetime.now().strftime('%H:%M:%S'),  # 只显示时间，不显示日期
            'reviewer': 'AI代码审查系统',

            # 审查结果 - 基于统计计算
            'overallResult': overall_result,
            'resultDescription': result_description,
            'totalProblems': total_problems,
        }

        # 任务执行统计 - 基于实际数据计算
        task_execution_summary = self._calculate_task_execution_summary(original_results)
        summary['taskExecutionSummary'] = task_execution_summary

        # 质量门禁统计 - 基于问题严重程度计算
        quality_gates_summary = self._calculate_quality_gates_summary(
            total_problems, critical_count, warning_count, moderate_count, minor_count
        )
        summary['qualityGatesSummary'] = quality_gates_summary

        # 代码覆盖统计 - 基于实际分析范围计算
        coverage_summary = self._calculate_coverage_summary(segments)
        summary['coverageSummary'] = coverage_summary

        # 问题分布统计 - 精确计算
        if total_problems > 0:
            summary['problemDistribution'] = {
                'critical': critical_count,
                'warning': warning_count,
                'moderate': moderate_count,
                'minor': minor_count,
                'criticalRate': round(critical_count / total_problems * 100, 1),
                'warningRate': round(warning_count / total_problems * 100, 1)
            }

        # 审查效率统计 - 基于实际数据计算
        efficiency_summary = self._calculate_efficiency_summary(
            original_results, segments, metadata, total_problems
        )
        summary['efficiencySummary'] = efficiency_summary

        return summary

    def _calculate_task_execution_summary(self, original_results: List[Dict[str, Any]]) -> str:
        """计算任务执行统计"""
        total_tasks = len(original_results)
        successful_tasks = 0
        tasks_with_problems = 0

        for result in original_results:
            # 判断任务是否成功执行 - 修复判断逻辑
            # 只要结果不为空且包含有效数据，就认为任务成功执行
            is_successful = False

            # 检查是否有sumCheckResult字段且不为空
            sum_check_result = result.get('sumCheckResult')
            if sum_check_result and sum_check_result.strip():
                is_successful = True

            # 检查是否有totalProblem字段且不为None
            total_problem = result.get('totalProblem')
            if total_problem is not None:
                is_successful = True

            # 检查是否有problemList字段且不为空
            problem_list = result.get('problemList')
            if problem_list and isinstance(problem_list, list) and len(problem_list) > 0:
                is_successful = True

            # 如果以上都没有，但result本身不为空，也认为是成功的
            if not is_successful and result and isinstance(result, dict) and len(result) > 0:
                is_successful = True

            if is_successful:
                successful_tasks += 1

                # 判断是否发现问题 - 改进问题检测逻辑
                has_problems = False

                # 方法1：通过totalProblem字段判断
                try:
                    if total_problem is not None:
                        problem_count = int(total_problem)
                        if problem_count > 0:
                            has_problems = True
                except (ValueError, TypeError):
                    pass

                # 方法2：通过problemList字段判断
                if not has_problems and problem_list:
                    for problem_category in problem_list:
                        if isinstance(problem_category, dict):
                            # 检查detail字段
                            details = problem_category.get('detail', [])
                            if details and isinstance(details, list) and len(details) > 0:
                                has_problems = True
                                break

                            # 检查num字段
                            try:
                                num = problem_category.get('num', '0')
                                if int(num) > 0:
                                    has_problems = True
                                    break
                            except (ValueError, TypeError):
                                pass

                # 方法3：通过sumCheckResult判断
                if not has_problems and sum_check_result:
                    if sum_check_result.strip() == '不通过':
                        has_problems = True

                if has_problems:
                    tasks_with_problems += 1

        return f"执行{total_tasks}个任务，{successful_tasks}个成功，{tasks_with_problems}个发现问题"

    def _calculate_quality_gates_summary(self,
                                       total_problems: int,
                                       critical_count: int,
                                       warning_count: int,
                                       moderate_count: int,
                                       minor_count: int) -> str:
        """计算质量门禁统计"""
        # 质量门禁规则
        gates = {
            'critical_gate': critical_count == 0,  # 严重问题门禁：不允许有P0问题
            'warning_gate': warning_count <= 5,    # 警告问题门禁：P1问题不超过5个
            'total_gate': total_problems <= 20     # 总问题门禁：总问题不超过20个
        }

        passed_gates = sum(gates.values())
        total_gates = len(gates)
        pass_rate = round(passed_gates / total_gates * 100, 1)

        gate_status = "通过" if passed_gates == total_gates else "未通过"

        return f"质量门禁{gate_status}，通过率{pass_rate}%"

    def _calculate_coverage_summary(self, segments: List[Dict[str, Any]]) -> str:
        """计算代码覆盖统计"""
        total_segments = len(segments)
        analyzed_segments = len([seg for seg in segments if seg.get('content')])

        # 计算文件覆盖
        unique_files = set()
        for seg in segments:
            file_name = seg.get('file', 'unknown')
            if file_name != 'unknown':
                unique_files.add(file_name)

        files_count = len(unique_files)

        # 计算代码行数
        total_lines = sum(seg.get('lines_count', 0) for seg in segments if isinstance(seg.get('lines_count'), int))

        return f"分析{files_count}个文件，{total_segments}个代码片段，共{total_lines}行代码"

    def _calculate_efficiency_summary(self,
                                    original_results: List[Dict[str, Any]],
                                    segments: List[Dict[str, Any]],
                                    metadata: Dict[str, Any],
                                    total_problems: int) -> str:
        """计算审查效率统计"""
        # 处理模式
        processing_mode = "并行" if metadata.get('parallel_processing') else "串行"

        # 问题发现率
        if len(segments) > 0:
            problem_density = round(total_problems / len(segments), 2)
            return f"{processing_mode}处理，问题密度{problem_density}个/片段"
        else:
            return f"{processing_mode}处理，无代码片段分析"

    async def _generate_global_recommendations(self,
                                             all_problems: List[Dict[str, Any]],
                                             critical_count: int,
                                             warning_count: int,
                                             moderate_count: int,
                                             minor_count: int,
                                             total_problems: int) -> List[str]:
        """
        生成全局修改建议 - 使用LLM汇总分点输出

        Args:
            all_problems: 所有问题列表
            critical_count: 严重问题数
            warning_count: 警告问题数
            moderate_count: 中等问题数
            minor_count: 轻微问题数
            total_problems: 总问题数

        Returns:
            List[str]: 全局修改建议列表
        """
        try:
            # 如果没有问题，返回简单建议
            if total_problems == 0:
                return ["代码质量良好，保持当前的开发标准"]

            # 如果LLM不可用，使用基础规则生成建议
            if not LANGCHAIN_AVAILABLE:
                return self._generate_basic_recommendations(
                    critical_count, warning_count, moderate_count, minor_count, total_problems
                )

            # 使用LLM生成全局建议
            return await self._generate_llm_recommendations(
                all_problems, critical_count, warning_count, moderate_count, minor_count, total_problems
            )

        except Exception as e:
            self.logger.warning(f"生成全局建议失败: {str(e)}，使用基础建议")
            return self._generate_basic_recommendations(
                critical_count, warning_count, moderate_count, minor_count, total_problems
            )

    def _generate_basic_recommendations(self,
                                      critical_count: int,
                                      warning_count: int,
                                      moderate_count: int,
                                      minor_count: int,
                                      total_problems: int) -> List[str]:
        """生成基础修改建议（不依赖LLM）"""
        recommendations = []

        # 基于问题严重程度的建议
        if critical_count > 0:
            recommendations.append(f"🚨 优先修复{critical_count}个严重问题，这些问题可能导致系统故障")

        if warning_count > 0:
            recommendations.append(f"⚠️ 关注{warning_count}个警告级别问题的修复，提高代码质量")

        if moderate_count > 5:
            recommendations.append(f"🔧 建议进行代码重构以减少{moderate_count}个中等级别问题")

        if minor_count > 10:
            recommendations.append(f"📝 逐步修复{minor_count}个轻微问题，提升代码规范性")

        # 基于总体情况的建议
        if total_problems > 20:
            recommendations.append("🏗️ 代码质量需要整体改进，建议进行全面重构")
        elif total_problems > 10:
            recommendations.append("📊 建议制定问题修复计划，分阶段改进代码质量")
        elif total_problems > 0:
            recommendations.append("✨ 问题数量较少，建议及时修复以保持代码质量")

        return recommendations

    async def _generate_llm_recommendations(self,
                                          all_problems: List[Dict[str, Any]],
                                          critical_count: int,
                                          warning_count: int,
                                          moderate_count: int,
                                          minor_count: int,
                                          total_problems: int) -> List[str]:
        """使用LLM生成全局修改建议"""
        try:
            # 构建问题摘要
            problem_summary = []
            for problem in all_problems[:10]:  # 只取前10个问题作为示例
                problem_summary.append({
                    'level': problem.get('level', ''),
                    'problem': problem.get('problem', ''),
                    'suggestion': problem.get('suggestion', '')
                })

            # 构建LLM提示
            prompt = f"""
基于以下代码审查结果，生成全局修改建议：

问题统计：
- 严重问题(P0): {critical_count}个
- 警告问题(P1): {warning_count}个
- 中等问题(P2): {moderate_count}个
- 轻微问题(P3+): {minor_count}个
- 总计: {total_problems}个

主要问题示例：
{problem_summary}

请生成3-5条全局修改建议，要求：
1. 分点输出，每条建议独立成行
2. 优先级从高到低排列
3. 具体可执行，避免空泛建议
4. 考虑问题的整体分布和严重程度
5. 包含具体的改进方向和方法

输出格式：
- 建议1
- 建议2
- 建议3
"""

            # 调用LLM
            llm = self.llm_service.get_llm()
            response = await llm.ainvoke(prompt)

            # 解析LLM响应
            recommendations = []
            lines = response.content.strip().split('\n')
            for line in lines:
                line = line.strip()
                if line.startswith('-') or line.startswith('•'):
                    # 移除列表标记
                    recommendation = line[1:].strip()
                    if recommendation:
                        recommendations.append(recommendation)

            # 如果LLM返回的建议太少，补充基础建议
            if len(recommendations) < 2:
                basic_recommendations = self._generate_basic_recommendations(
                    critical_count, warning_count, moderate_count, minor_count, total_problems
                )
                recommendations.extend(basic_recommendations[:3])

            return recommendations[:5]  # 最多返回5条建议

        except Exception as e:
            self.logger.warning(f"LLM生成建议失败: {str(e)}")
            return self._generate_basic_recommendations(
                critical_count, warning_count, moderate_count, minor_count, total_problems
            )

    async def _create_fallback_result(self,
                                    results: List[Dict[str, Any]],
                                    input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        创建降级结果（当所有聚合和增强都失败时的最后方案）

        Args:
            results: 原始审查结果列表
            input_data: 输入数据

        Returns:
            Dict: 降级结果
        """
        from datetime import datetime

        # 取第一个有效结果作为基础
        base_result = None
        for result in results:
            if result and isinstance(result, dict):
                base_result = result
                break

        if not base_result:
            # 如果没有有效结果，创建空结果
            return {
                'summary': {
                    'checkBranch': 'unknown',
                    'reviewTime': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'reviewer': 'AI代码审查系统',
                    'overallResult': '通过',
                    'resultDescription': '无问题发现',
                    'totalProblems': 0
                },
                'scoring': {
                    'overallScore': 100,
                    'maxScore': 100,
                    'qualityGrade': 'A',
                    'passThreshold': 80,
                    'isPassed': True
                },
                'problems': [],
                'statistics': {
                    'totalProblems': 0,
                    'criticalCount': 0,
                    'warningCount': 0,
                    'moderateCount': 0,
                    'minorCount': 0
                },
                'reviewMetrics': {
                    'qualityScore': 100,
                    'riskLevel': '低',
                    'totalProblems': 0
                },
                'recommendations': ['代码质量良好'],
                'originalResult': {},
                'fallbackMode': True
            }

        # 基于第一个结果创建简化的降级结果
        total_problem = 0
        try:
            total_problem = int(base_result.get('totalProblem', 0))
        except (ValueError, TypeError):
            pass

        overall_score = max(0, 100 - total_problem * 10)
        overall_result = base_result.get('sumCheckResult', '通过')

        return {
            'summary': {
                'checkBranch': base_result.get('checkBranch', 'unknown'),
                'reviewTime': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'reviewer': 'AI代码审查系统',
                'overallResult': overall_result,
                'resultDescription': base_result.get('resultDesc', '代码质量良好，未发现问题'),
                'totalProblems': total_problem
            },
            'scoring': {
                'overallScore': overall_score,
                'maxScore': 100,
                'qualityGrade': 'A' if overall_score >= 90 else 'B' if overall_score >= 80 else 'C' if overall_score >= 70 else 'D',
                'passThreshold': 80,
                'isPassed': overall_result == '通过' and overall_score >= 80
            },
            'problems': [],  # 降级模式下不解析具体问题
            'statistics': {
                'totalProblems': total_problem,
                'criticalCount': 0,
                'warningCount': 0,
                'moderateCount': 0,
                'minorCount': 0
            },
            'reviewMetrics': {
                'qualityScore': overall_score,
                'riskLevel': '低' if total_problem == 0 else '中' if total_problem < 5 else '高',
                'totalProblems': total_problem
            },
            'recommendations': [
                '建议查看详细审查结果' if total_problem > 0 else '代码质量良好'
            ],
            'originalResult': base_result,
            'originalReviewResults': results,
            'fallbackMode': True
        }

    def _extract_correct_branch_info(self, options: Dict[str, Any], metadata: Dict[str, Any]) -> Optional[str]:
        """
        从options和metadata中提取正确的分支信息

        Args:
            options: 选项参数
            metadata: 元数据信息

        Returns:
            str: 正确的分支信息，如果无法提取则返回None
        """
        try:
            # 优先从options中获取分支信息
            from_branch = options.get('fromBranch') or options.get('from_branch')
            to_branch = options.get('toBranch') or options.get('to_branch')
            project = options.get('project')
            repo = options.get('repo')

            # 如果options中没有，尝试从metadata中获取
            if not from_branch:
                from_branch = metadata.get('fromBranch') or metadata.get('from_branch')
            if not to_branch:
                to_branch = metadata.get('toBranch') or metadata.get('to_branch')
            if not project:
                project = metadata.get('project')
            if not repo:
                repo = metadata.get('repo')

            # 构建分支信息字符串
            if from_branch:
                if project and repo:
                    return f"{project}/{repo}:{from_branch}"
                else:
                    return from_branch

            return None

        except Exception as e:
            self.logger.warning(f"提取分支信息失败: {str(e)}")
            return None

    def _build_branch_info(self, options: Dict[str, Any]) -> Optional[Dict[str, str]]:
        """
        构建分支信息字典

        Args:
            options: 选项参数

        Returns:
            Dict: 分支信息字典
        """
        try:
            branch_info = {}

            # 提取各个字段
            if 'project' in options:
                branch_info['project'] = str(options['project'])
            if 'repo' in options:
                branch_info['repo'] = str(options['repo'])
            if 'fromBranch' in options or 'from_branch' in options:
                branch_info['fromBranch'] = str(options.get('fromBranch') or options.get('from_branch'))
            if 'toBranch' in options or 'to_branch' in options:
                branch_info['toBranch'] = str(options.get('toBranch') or options.get('to_branch'))

            return branch_info if branch_info else None

        except Exception as e:
            self.logger.warning(f"构建分支信息失败: {str(e)}")
            return None

    def _determine_check_branch_from_external(self, options: Dict[str, Any], metadata: Dict[str, Any]) -> str:
        """
        从外部参数确定正确的checkBranch，不依赖模型处理

        Args:
            options: 选项参数
            metadata: 元数据信息

        Returns:
            str: 正确的checkBranch值
        """
        try:
            # 优先从options中获取分支信息
            from_branch = options.get('fromBranch') or options.get('from_branch')
            project = options.get('project')
            repo = options.get('repo')

            # 如果options中没有，尝试从metadata中获取
            if not from_branch:
                from_branch = metadata.get('fromBranch') or metadata.get('from_branch')
            if not project:
                project = metadata.get('project')
            if not repo:
                repo = metadata.get('repo')

            # 构建checkBranch值
            if from_branch:
                if project and repo:
                    check_branch = f"{project}/{repo}:{from_branch}"
                else:
                    check_branch = from_branch

                self.logger.info(f"从外部参数构建checkBranch: {check_branch}")
                return check_branch
            else:
                self.logger.warning("外部参数中未找到fromBranch信息，使用默认值")
                return "未知分支"

        except Exception as e:
            self.logger.error(f"从外部参数确定checkBranch失败: {str(e)}")
            return "未知分支"

    def _preprocess_results_with_correct_branch(self, results: List[Dict[str, Any]], correct_check_branch: str) -> List[Dict[str, Any]]:
        """
        预处理结果，统一设置正确的checkBranch

        Args:
            results: 原始结果列表
            correct_check_branch: 正确的checkBranch值

        Returns:
            List[Dict]: 预处理后的结果列表
        """
        preprocessed_results = []

        for result in results:
            # 复制结果避免修改原始数据
            processed_result = result.copy()

            # 记录原始的checkBranch（如果存在且错误）
            original_check_branch = processed_result.get('checkBranch')
            if original_check_branch and original_check_branch != correct_check_branch:
                # 检查是否为文件路径格式
                if isinstance(original_check_branch, str) and (':' in original_check_branch and any(char.isdigit() for char in original_check_branch.split(':')[-1])):
                    self.logger.warning(f"替换错误的checkBranch格式: {original_check_branch} → {correct_check_branch}")
                else:
                    self.logger.info(f"替换checkBranch: {original_check_branch} → {correct_check_branch}")

            # 设置正确的checkBranch
            processed_result['checkBranch'] = correct_check_branch

            preprocessed_results.append(processed_result)

        return preprocessed_results
