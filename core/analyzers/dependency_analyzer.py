"""
依赖分析器模块
"""
from typing import Dict, List, Set, Any
from utils.chunk_utils import safe_get_from_func_index, find_unique_key_for_dep
from consts.chunk_consts import DEFAULT_MAX_DEP_DEPTH, SPECIAL_MARKERS


class DependencyAnalyzer:
    """依赖分析器"""

    def __init__(self, func_index: Dict, func_code_cache: Dict = None, cross_module_analyzer=None):
        self.func_index = func_index
        self.func_code_cache = func_code_cache or {}
        self.func_code_fail_log_cache = set()
        self.cross_module_analyzer = cross_module_analyzer

    def get_func_code_by_name(self, callee: str, file_path: str) -> str:
        """根据函数名获取函数代码"""
        # 特殊标记处理（提前返回）
        if any(callee.startswith(marker) for marker in SPECIAL_MARKERS):
            return f"// 特殊标记: {callee}"

        if not self.func_index:
            print(f"函数索引为空，无法查找: {callee}")
            return None

        # 缓存查找结果，避免重复搜索
        cache_key = f"{file_path}:{callee}"
        if cache_key in self.func_code_cache:
            return self.func_code_cache[cache_key]

        # 尝试多种匹配策略
        code = self._try_multiple_match_strategies(callee, file_path)

        if code:
            self.func_code_cache[cache_key] = code
            return code

        # 日志优化：第一次查找失败只缓存，第二次及以后才打印详细日志
        if cache_key not in self.func_code_fail_log_cache:
            self.func_code_fail_log_cache.add(cache_key)
        else:
            print(f"所有策略均未找到匹配: {callee}")

        self.func_code_cache[cache_key] = None
        return None

    def _try_multiple_match_strategies(self, callee: str, file_path: str) -> str:
        """尝试多种匹配策略"""
        strategies = [
            self._exact_match_strategy,
            self._class_method_match_strategy,
            self._global_index_match_strategy,
            self._cross_file_match_strategy,
            self._fuzzy_match_strategy
        ]

        for strategy in strategies:
            code = strategy(callee, file_path)
            if code:
                return code

        return None

    def _exact_match_strategy(self, callee: str, file_path: str) -> str:
        """精确匹配策略"""
        # 如果callee已经是全路径或全局名，直接查
        if ':' in callee:
            key = callee.strip()
            code = safe_get_from_func_index(self.func_index, key, 'content')
            if code:
                return code[0] if isinstance(code, list) else code

            # 如果是global:开头，也尝试去掉global:再查一次
            if callee.startswith('global:'):
                key2 = callee[len('global:'):].strip()
                code = safe_get_from_func_index(self.func_index, key2, 'content')
                if code:
                    return code[0] if isinstance(code, list) else code

        # 精确匹配：当前文件中的完整函数名
        key = f"{file_path}:{callee}".strip()
        code = safe_get_from_func_index(self.func_index, key, 'content')
        if code:
            return code[0] if isinstance(code, list) else code

        return None

    def _class_method_match_strategy(self, callee: str, file_path: str) -> str:
        """类方法匹配策略"""
        if '.' not in callee or ':' in callee or callee.startswith('global:'):
            return None

        # 类方法唯一名匹配
        key = f"{file_path}:{callee}".strip()
        code = safe_get_from_func_index(self.func_index, key, 'content')
        if code:
            return code[0] if isinstance(code, list) else code

        return None

    def _global_index_match_strategy(self, callee: str, file_path: str) -> str:
        """全局索引匹配策略"""
        if '.' not in callee or callee.startswith('global:') or ':' in callee:
            return None

        global_key = f"global:{callee}".strip()
        code = safe_get_from_func_index(self.func_index, global_key, 'content')
        if code:
            return code[0] if isinstance(code, list) else code

        return None

    def _cross_file_match_strategy(self, callee: str, file_path: str) -> str:
        """跨文件匹配策略"""
        # 跨文件所有 Class.method 结尾的 key 查找
        if '.' in callee:
            for k in self.func_index:
                if k.endswith(f".{callee.split('.')[-1]}") or k.endswith(f":{callee}"):
                    code = safe_get_from_func_index(self.func_index, k, 'content')
                    if code:
                        return code[0] if isinstance(code, list) else code

            # 跨文件所有 :method 结尾的 key 查找
            method_name = callee.split('.')[-1]
            for k in self.func_index:
                if k.endswith(f":{method_name}"):
                    code = safe_get_from_func_index(self.func_index, k, 'content')
                    if code:
                        return code[0] if isinstance(code, list) else code

        # 只用方法名查找（无类名）
        if '.' not in callee and ':' not in callee and not callee.startswith('global:'):
            for k in self.func_index:
                if k.endswith(f":{callee}"):
                    code = safe_get_from_func_index(self.func_index, k, 'content')
                    if code:
                        return code[0] if isinstance(code, list) else code

        return None

    def _fuzzy_match_strategy(self, callee: str, file_path: str) -> str:
        """模糊匹配策略"""
        best_match = None
        best_score = 0

        for k in self.func_index:
            if ':' in k:
                func_part = k.split(':', 1)[1]
                if callee in func_part or func_part in callee:
                    score = len(set(callee) & set(func_part)) / max(len(callee), len(func_part))
                    if score > 0.7 and score > best_score:
                        best_score = score
                        code = safe_get_from_func_index(self.func_index, k, 'content')
                        if code:
                            best_match = code[0] if isinstance(code, list) else code

        return best_match

    def collect_multi_level_deps(self, chunk: Dict, direction: str = 'upstream',
                                 max_depth: int = DEFAULT_MAX_DEP_DEPTH) -> Dict[str, str]:
        """收集多层级依赖"""
        result = {}
        visited = set()

        self.logger.info(f"🔗 开始收集 {chunk.get('name', 'unknown')} 的 {direction} 依赖")
        self.logger.info(f"📋 chunk中的{direction}: {chunk.get(direction, [])}")
        print(f"[依赖补全] 开始收集 {chunk.get('name', 'unknown')} 的 {direction} 依赖")
        print(f"[依赖补全] chunk中的{direction}: {chunk.get(direction, [])}")

        def helper(name: str, depth: int):
            if depth > max_depth or name in visited:
                return
            visited.add(name)

            # 使用智能键查找
            matched_keys = find_unique_key_for_dep(self.func_index, name)
            if not matched_keys:
                # 尝试跨模块依赖解析
                if self.cross_module_analyzer:
                    cross_module_info = self.cross_module_analyzer.resolve_cross_module_dependency(
                        name, chunk.get('file', ''))
                    if cross_module_info:
                        print(f"[依赖补全] 通过跨模块分析找到 {name}: {cross_module_info}")
                        # 创建虚拟依赖代码
                        virtual_code = self._create_virtual_dependency_code(name, cross_module_info)
                        if virtual_code:
                            result[name] = virtual_code
                            print(f"[依赖补全] 成功创建虚拟依赖 {name}")
                        return

                print(f"[依赖补全] {name} 不在func_index中，跳过递归")
                return

            # 使用第一个匹配的键
            actual_key = matched_keys[0]
            codes = safe_get_from_func_index(self.func_index, actual_key, 'content')
            if codes:
                # 验证：确保不会将当前chunk的代码作为依赖
                current_chunk_content = chunk.get('content', '')
                if codes == current_chunk_content:
                    print(f"[依赖补全] 警告：跳过自引用依赖 {name}")
                    return

                if isinstance(codes, list):
                    for idx, code in enumerate(codes):
                        if code and code != current_chunk_content:
                            result_key = f"{name}#{idx}"
                            result[result_key] = code
                else:
                    result[name] = codes
                print(f"[依赖补全] 成功获取 {name} 的代码")

            # 递归查找依赖
            deps = safe_get_from_func_index(self.func_index, actual_key, direction)
            if deps:
                print(f"[依赖补全] {name} 的 {direction} 依赖: {deps}")
                self._process_deps_recursively(deps, depth, helper)

        deps = chunk.get(direction, [])
        print(f"[依赖补全] 开始处理 {len(deps)} 个直接依赖")
        for dep in deps:
            helper(dep, 1)

        self.logger.info(f"✅ 最终收集到 {len(result)} 个 {direction} 依赖代码")
        print(f"[依赖补全] 最终收集到 {len(result)} 个依赖代码")

        # 输出依赖详情
        if result:
            self.logger.info(f"📝 {direction} 依赖详情:")
            for dep_name, dep_code in list(result.items())[:5]:  # 最多显示5个
                code_preview = dep_code[:100] + "..." if len(dep_code) > 100 else dep_code
                self.logger.info(f"   - {dep_name}: {code_preview}")

            if len(result) > 5:
                self.logger.info(f"   ... 还有 {len(result) - 5} 个依赖")

        return result

    def _process_deps_recursively(self, deps: Any, depth: int, helper_func):
        """递归处理依赖"""
        if isinstance(deps, list):
            for dep_list in deps:
                if isinstance(dep_list, list):
                    for dep in dep_list:
                        helper_func(dep, depth + 1)
                elif dep_list:
                    helper_func(dep_list, depth + 1)
        elif isinstance(deps, str):
            helper_func(deps, depth + 1)
        else:
            # deps可能是其他类型，尝试转换为列表处理
            try:
                for dep in deps:
                    helper_func(dep, depth + 1)
            except TypeError:
                print(f"[依赖补全] 警告：无法处理依赖类型 {type(deps)}: {deps}")

    def _create_virtual_dependency_code(self, dep_name: str, cross_module_info: Dict) -> str:
        """创建虚拟依赖代码"""
        try:
            if cross_module_info.get('type') == 'spring_service':
                # Spring服务
                service_class = cross_module_info.get('service_class', 'UnknownService')
                module = cross_module_info.get('module', 'unknown-module')
                return f"""
// 跨模块依赖: {dep_name}
// 来源: {module}
@Service
public class {service_class} {{
    // 实际实现在模块: {module}
    // 文件路径: {cross_module_info.get('file_path', 'unknown')}
    public void {dep_name.split('.')[-1]}() {{
        // 跨模块服务调用
    }}
}}"""

            elif cross_module_info.get('app_key'):
                # Thrift服务
                app_key = cross_module_info.get('app_key')
                impl_method = cross_module_info.get('impl_method', dep_name)
                return f"""
// 跨模块Thrift依赖: {dep_name}
// 服务应用: {app_key}
// 实现方法: {impl_method}
@ThriftService
public class ThriftServiceImpl {{
    public Object {dep_name.split('.')[-1]}() {{
        // Thrift服务实现
        // 应用Key: {app_key}
        return null;
    }}
}}"""

            else:
                # 通用跨模块依赖
                module = cross_module_info.get('module', 'unknown-module')
                return f"""
// 跨模块依赖: {dep_name}
// 来源模块: {module}
public class CrossModuleDependency {{
    public void {dep_name.split('.')[-1]}() {{
        // 跨模块方法调用
        // 文件: {cross_module_info.get('file_path', 'unknown')}
    }}
}}"""

        except Exception as e:
            print(f"[依赖补全] 创建虚拟依赖代码失败: {e}")
            return f"// 跨模块依赖: {dep_name} (解析失败)"


    def resolve_chunk_code(self, chunk: Dict):
        """解析chunk的依赖代码内容"""
        chunk["upstream_code"] = {}
        for callee in chunk.get("upstream", []):
            code = self.get_func_code_by_name(callee, chunk["file"])
            if code:
                chunk["upstream_code"][callee] = code

        chunk["downstream_code"] = {}
        for caller in chunk.get("downstream", []):
            if caller != chunk["name"]:
                code = self.get_func_code_by_name(caller, chunk["file"])
                if code:
                    chunk["downstream_code"][caller] = code
