"""
代码位置分析器
用于精确定位代码问题的位置，支持行号和列号定位
"""

import re
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass


@dataclass
class CodePosition:
    """代码位置信息"""
    start_line: int
    end_line: int
    start_column: int = 0
    end_column: int = 0

    def to_array(self) -> List[int]:
        """转换为数组格式: [startLine, startColumn, endLine, endColumn]"""
        return [self.start_line, self.start_column, self.end_line, self.end_column]

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "startLine": self.start_line,
            "endLine": self.end_line,
            "startColumn": self.start_column,
            "endColumn": self.end_column
        }


class CodePositionAnalyzer:
    """代码位置分析器"""
    
    def __init__(self, content: str):
        self.content = content
        self.lines = content.splitlines()
    
    def find_method_signature_position(self, method_name: str, class_name: str = None) -> Optional[CodePosition]:
        """查找方法签名的位置"""
        # 构建方法签名的正则表达式
        if class_name:
            # 在特定类中查找方法
            class_pattern = rf'class\s+{re.escape(class_name)}'
            method_pattern = rf'(?:public|private|protected)?\s*(?:static)?\s*(?:final)?\s*\w+(?:<[^>]*>)?\s+{re.escape(method_name)}\s*\([^)]*\)(?:\s*throws\s+[^{{]+)?'
        else:
            # 全局查找方法
            method_pattern = rf'(?:public|private|protected)?\s*(?:static)?\s*(?:final)?\s*\w+(?:<[^>]*>)?\s+{re.escape(method_name)}\s*\([^)]*\)(?:\s*throws\s+[^{{]+)?'
        
        for i, line in enumerate(self.lines, 1):
            match = re.search(method_pattern, line)
            if match:
                start_column = match.start()
                end_column = match.end()
                return CodePosition(
                    start_line=i,
                    end_line=i,
                    start_column=start_column,
                    end_column=end_column
                )
        
        return None
    
    def find_exception_declaration_position(self, method_name: str) -> Optional[CodePosition]:
        """查找方法中throws Exception声明的位置"""
        method_pattern = rf'(?:public|private|protected)?\s*(?:static)?\s*(?:final)?\s*\w+(?:<[^>]*>)?\s+{re.escape(method_name)}\s*\([^)]*\)\s*(throws\s+Exception)'
        
        for i, line in enumerate(self.lines, 1):
            match = re.search(method_pattern, line)
            if match:
                throws_match = match.group(1)
                start_column = match.start(1)
                end_column = match.end(1)
                return CodePosition(
                    start_line=i,
                    end_line=i,
                    start_column=start_column,
                    end_column=end_column
                )
        
        return None
    
    def find_annotation_position(self, annotation_name: str, target_line: int = None) -> Optional[CodePosition]:
        """查找注解的位置"""
        annotation_pattern = rf'@{re.escape(annotation_name)}(?:\([^)]*\))?'
        
        search_lines = [(target_line, self.lines[target_line - 1])] if target_line else enumerate(self.lines, 1)
        
        for i, line in search_lines:
            match = re.search(annotation_pattern, line)
            if match:
                start_column = match.start()
                end_column = match.end()
                return CodePosition(
                    start_line=i,
                    end_line=i,
                    start_column=start_column,
                    end_column=end_column
                )
        
        return None
    
    def find_import_position(self, import_class: str) -> Optional[CodePosition]:
        """查找import语句的位置"""
        import_pattern = rf'import\s+(?:static\s+)?{re.escape(import_class)}\s*;'
        
        for i, line in enumerate(self.lines, 1):
            match = re.search(import_pattern, line)
            if match:
                start_column = match.start()
                end_column = match.end()
                return CodePosition(
                    start_line=i,
                    end_line=i,
                    start_column=start_column,
                    end_column=end_column
                )
        
        return None
    
    def find_variable_declaration_position(self, variable_name: str, variable_type: str = None) -> Optional[CodePosition]:
        """查找变量声明的位置"""
        if variable_type:
            var_pattern = rf'(?:private|public|protected)?\s*(?:static)?\s*(?:final)?\s*{re.escape(variable_type)}\s+{re.escape(variable_name)}'
        else:
            var_pattern = rf'(?:private|public|protected)?\s*(?:static)?\s*(?:final)?\s*\w+\s+{re.escape(variable_name)}'
        
        for i, line in enumerate(self.lines, 1):
            match = re.search(var_pattern, line)
            if match:
                start_column = match.start()
                end_column = match.end()
                return CodePosition(
                    start_line=i,
                    end_line=i,
                    start_column=start_column,
                    end_column=end_column
                )
        
        return None
    
    def find_method_call_position(self, method_call: str, line_number: int = None) -> Optional[CodePosition]:
        """查找方法调用的位置"""
        # 提取对象和方法名
        if '.' in method_call:
            obj_name, method_name = method_call.rsplit('.', 1)
            call_pattern = rf'{re.escape(obj_name)}\.{re.escape(method_name)}\s*\('
        else:
            call_pattern = rf'{re.escape(method_call)}\s*\('
        
        search_lines = [(line_number, self.lines[line_number - 1])] if line_number else enumerate(self.lines, 1)
        
        for i, line in search_lines:
            match = re.search(call_pattern, line)
            if match:
                start_column = match.start()
                end_column = match.end() - 1  # 不包含左括号
                return CodePosition(
                    start_line=i,
                    end_line=i,
                    start_column=start_column,
                    end_column=end_column
                )
        
        return None
    
    def find_class_declaration_position(self, class_name: str) -> Optional[CodePosition]:
        """查找类声明的位置"""
        class_pattern = rf'(?:public|private|protected)?\s*(?:static)?\s*(?:final)?\s*(?:abstract)?\s*class\s+{re.escape(class_name)}'
        
        for i, line in enumerate(self.lines, 1):
            match = re.search(class_pattern, line)
            if match:
                start_column = match.start()
                end_column = match.end()
                return CodePosition(
                    start_line=i,
                    end_line=i,
                    start_column=start_column,
                    end_column=end_column
                )
        
        return None
    
    def find_text_position(self, text: str, line_number: int = None) -> Optional[CodePosition]:
        """查找任意文本的位置"""
        search_lines = [(line_number, self.lines[line_number - 1])] if line_number else enumerate(self.lines, 1)
        
        for i, line in search_lines:
            index = line.find(text)
            if index != -1:
                start_column = index
                end_column = index + len(text)
                return CodePosition(
                    start_line=i,
                    end_line=i,
                    start_column=start_column,
                    end_column=end_column
                )
        
        return None
    
    def find_multiline_position(self, start_text: str, end_text: str) -> Optional[CodePosition]:
        """查找跨多行的代码位置"""
        start_pos = None
        end_pos = None
        
        for i, line in enumerate(self.lines, 1):
            if start_text in line and not start_pos:
                start_index = line.find(start_text)
                start_pos = (i, start_index)
            
            if end_text in line and start_pos:
                end_index = line.find(end_text) + len(end_text)
                end_pos = (i, end_index)
                break
        
        if start_pos and end_pos:
            return CodePosition(
                start_line=start_pos[0],
                end_line=end_pos[0],
                start_column=start_pos[1],
                end_column=end_pos[1]
            )
        
        return None


def create_positioned_issue(issue_type: str, level: str, problem: str, suggestion: str,
                          position: CodePosition, target_code: str = None) -> Dict[str, Any]:
    """创建带位置信息的问题报告"""
    issue = {
        "level": level,
        "type": issue_type,
        "problem": problem,
        "suggestion": suggestion,
        "codePosition": position.to_array()  # 使用简单的数组格式: [startLine, startColumn, endLine, endColumn]
    }

    if target_code:
        issue["targetCode"] = target_code

    return issue
