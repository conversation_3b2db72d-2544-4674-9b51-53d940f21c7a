"""
跨模块依赖分析器
专门处理Thrift服务、微服务接口等跨模块依赖
"""

import os
import re
import json
from typing import Dict, List, Set, Any, Optional
from dataclasses import dataclass
from pathlib import Path


@dataclass
class ServiceMapping:
    """服务映射信息"""
    service_name: str
    interface_class: str
    implementation_class: str
    module_path: str
    app_key: str = None
    timeout: int = None


@dataclass
class ThriftServiceInfo:
    """Thrift服务信息"""
    service_interface: str
    client_proxy: str
    implementation: str
    app_key: str
    timeout: int
    methods: List[str]


class CrossModuleAnalyzer:
    """跨模块依赖分析器"""
    
    def __init__(self, project_root: str):
        self.project_root = project_root
        self.service_registry = {}  # 服务注册表
        self.thrift_services = {}   # Thrift服务映射
        self.interface_implementations = {}  # 接口-实现映射
        self.module_index = {}      # 模块索引
        
        # 初始化分析
        self._scan_project_structure()
        self._build_service_registry()
        self._build_thrift_mappings()
    
    def _scan_project_structure(self):
        """扫描项目结构"""
        print("[跨模块分析] 开始扫描项目结构...")
        
        java_files = []
        for root, dirs, files in os.walk(self.project_root):
            # 跳过常见的非源码目录
            dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['target', 'build', 'node_modules']]
            
            for file in files:
                if file.endswith('.java'):
                    file_path = os.path.join(root, file)
                    rel_path = os.path.relpath(file_path, self.project_root)
                    java_files.append(rel_path)
        
        print(f"[跨模块分析] 发现 {len(java_files)} 个Java文件")
        
        # 构建模块索引
        for file_path in java_files:
            module_name = self._extract_module_name(file_path)
            if module_name not in self.module_index:
                self.module_index[module_name] = []
            self.module_index[module_name].append(file_path)
    
    def _extract_module_name(self, file_path: str) -> str:
        """从文件路径提取模块名"""
        # 例如: state_subsidies_audit-infrastructure/src/main/java/... -> state_subsidies_audit-infrastructure
        parts = file_path.split('/')
        if len(parts) > 0:
            return parts[0]
        return "unknown"
    
    def _build_service_registry(self):
        """构建服务注册表"""
        print("[跨模块分析] 构建服务注册表...")
        
        for module, files in self.module_index.items():
            for file_path in files:
                try:
                    self._analyze_service_file(file_path, module)
                except Exception as e:
                    # 静默处理文件读取错误
                    pass
    
    def _analyze_service_file(self, file_path: str, module: str):
        """分析服务文件"""
        full_path = os.path.join(self.project_root, file_path)
        if not os.path.exists(full_path):
            return
            
        try:
            with open(full_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except:
            return
        
        # 分析Thrift客户端代理
        if '@ThriftClientProxy' in content:
            self._extract_thrift_client_info(content, file_path, module)
        
        # 分析Thrift服务实现
        if 'implements' in content and 'ThriftService' in content:
            self._extract_thrift_service_impl(content, file_path, module)
        
        # 分析Spring服务
        if '@Service' in content or '@Component' in content:
            self._extract_spring_service_info(content, file_path, module)
    
    def _extract_thrift_client_info(self, content: str, file_path: str, module: str):
        """提取Thrift客户端信息"""
        # 提取类名
        class_match = re.search(r'public\s+class\s+(\w+)', content)
        if not class_match:
            return
        
        class_name = class_match.group(1)
        
        # 提取ThriftClientProxy注解信息
        proxy_pattern = r'@ThriftClientProxy\s*\(\s*remoteAppKey\s*=\s*"([^"]+)"(?:\s*,\s*timeout\s*=\s*(\d+))?\s*\)'
        proxy_match = re.search(proxy_pattern, content)
        
        if proxy_match:
            app_key = proxy_match.group(1)
            timeout = int(proxy_match.group(2)) if proxy_match.group(2) else 5000
            
            # 提取服务接口类型
            service_pattern = r'private\s+(\w+)\s+(\w+);'
            service_match = re.search(service_pattern, content)
            
            if service_match:
                interface_class = service_match.group(1)
                service_field = service_match.group(2)
                
                # 提取方法列表
                methods = self._extract_public_methods(content)
                
                service_info = ThriftServiceInfo(
                    service_interface=interface_class,
                    client_proxy=class_name,
                    implementation=f"{app_key}.{interface_class}Impl",  # 推测实现类名
                    app_key=app_key,
                    timeout=timeout,
                    methods=methods
                )
                
                self.thrift_services[service_field] = service_info
                
                # 建立方法映射
                for method in methods:
                    client_method = f"{class_name}.{method}"
                    service_method = f"{service_field}.{method}"
                    impl_method = f"{interface_class}Impl.{method}"

                    self.service_registry[service_method] = {
                        'client_method': client_method,
                        'impl_method': impl_method,
                        'app_key': app_key,
                        'module': module,
                        'file_path': file_path
                    }

                    # 同时建立接口方法映射
                    interface_method = f"{interface_class}.{method}"
                    self.service_registry[interface_method] = {
                        'client_method': client_method,
                        'impl_method': impl_method,
                        'app_key': app_key,
                        'module': module,
                        'file_path': file_path
                    }

                print(f"[跨模块分析] 为 {class_name} 建立了 {len(methods)} 个方法映射")
    
    def _extract_thrift_service_impl(self, content: str, file_path: str, module: str):
        """提取Thrift服务实现信息"""
        # 提取实现类名
        impl_pattern = r'public\s+class\s+(\w+)\s+implements\s+(\w+)'
        impl_match = re.search(impl_pattern, content)
        
        if impl_match:
            impl_class = impl_match.group(1)
            interface_class = impl_match.group(2)
            
            methods = self._extract_public_methods(content)
            
            for method in methods:
                impl_method = f"{impl_class}.{method}"
                interface_method = f"{interface_class}.{method}"
                
                self.interface_implementations[interface_method] = {
                    'impl_method': impl_method,
                    'impl_class': impl_class,
                    'module': module,
                    'file_path': file_path
                }
    
    def _extract_spring_service_info(self, content: str, file_path: str, module: str):
        """提取Spring服务信息"""
        # 提取类名
        class_match = re.search(r'public\s+class\s+(\w+)', content)
        if not class_match:
            return
        
        class_name = class_match.group(1)
        methods = self._extract_public_methods(content)
        
        for method in methods:
            service_method = f"{class_name}.{method}"
            self.service_registry[service_method] = {
                'service_class': class_name,
                'module': module,
                'file_path': file_path,
                'type': 'spring_service'
            }
    
    def _extract_public_methods(self, content: str) -> List[str]:
        """提取公共方法名"""
        # 更精确的方法匹配模式
        method_pattern = r'public\s+(?:(?:static|final|synchronized)\s+)*(?:\w+(?:<[^>]*>)?(?:\[\])?\s+)+(\w+)\s*\([^)]*\)\s*\{'
        methods = re.findall(method_pattern, content, re.MULTILINE)

        # 过滤掉构造函数和常见的getter/setter
        filtered_methods = []
        for method in methods:
            if (not method[0].isupper() and
                not method.startswith('get') and
                not method.startswith('set') and
                not method.startswith('is') and
                method not in ['equals', 'hashCode', 'toString']):
                filtered_methods.append(method)

        return filtered_methods
    
    def _build_thrift_mappings(self):
        """构建Thrift服务映射"""
        print(f"[跨模块分析] 构建完成，发现 {len(self.thrift_services)} 个Thrift服务")
        print(f"[跨模块分析] 服务注册表包含 {len(self.service_registry)} 个方法映射")
    
    def resolve_cross_module_dependency(self, dep_name: str, current_module: str) -> Optional[Dict]:
        """解析跨模块依赖"""
        # 直接查找服务注册表
        if dep_name in self.service_registry:
            return self.service_registry[dep_name]
        
        # 尝试模糊匹配
        for service_key, service_info in self.service_registry.items():
            if dep_name in service_key or service_key.endswith(f".{dep_name.split('.')[-1]}"):
                return service_info
        
        # 查找接口实现
        if dep_name in self.interface_implementations:
            return self.interface_implementations[dep_name]
        
        return None
    
    def get_thrift_service_info(self, service_field: str) -> Optional[ThriftServiceInfo]:
        """获取Thrift服务信息"""
        return self.thrift_services.get(service_field)
    
    def find_service_implementations(self, interface_name: str) -> List[Dict]:
        """查找服务实现"""
        implementations = []
        
        for key, impl_info in self.interface_implementations.items():
            if interface_name in key:
                implementations.append(impl_info)
        
        return implementations
    
    def get_cross_module_suggestions(self, dep_name: str) -> List[str]:
        """获取跨模块依赖建议"""
        suggestions = []
        
        # 基于方法名的建议
        method_name = dep_name.split('.')[-1] if '.' in dep_name else dep_name
        
        for service_key in self.service_registry.keys():
            if method_name in service_key:
                suggestions.append(service_key)
        
        return suggestions[:5]  # 返回前5个建议
