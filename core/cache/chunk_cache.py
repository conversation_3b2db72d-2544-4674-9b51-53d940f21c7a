"""
代码分块缓存管理
"""
import json
import os
import time
from typing import Dict, Any, Optional
from consts.chunk_consts import DEFAULT_CACHE_TTL


class ChunkCache:
    """代码分块缓存管理器"""
    
    def __init__(self, cache_dir: str = "/tmp"):
        self.cache_dir = cache_dir
        
    def _get_cache_file_path(self, cache_key: str) -> str:
        """获取缓存文件路径"""
        return os.path.join(self.cache_dir, f"func_index_{hash(cache_key)}.json")
    
    def get_cache_key(self, project: str, repo: str, branch: str, suffixes: list) -> str:
        """生成缓存键"""
        return f"{project}_{repo}_{branch}_{'_'.join(sorted(suffixes))}"
    
    def load_cache(self, cache_key: str, ttl: int = DEFAULT_CACHE_TTL) -> Optional[Dict]:
        """加载缓存数据"""
        cache_file = self._get_cache_file_path(cache_key)
        
        if not os.path.exists(cache_file):
            return None
            
        cache_mtime = os.path.getmtime(cache_file)
        if time.time() - cache_mtime >= ttl:
            return None
            
        try:
            with open(cache_file, 'r', encoding='utf-8') as f:
                cache_data = json.load(f)
                print(f"读取缓存文件成功")
                
                # 兼容旧缓存格式处理
                cache_data = self._normalize_cache_data(cache_data)
                
                print(f"从缓存加载函数索引，共 {len(cache_data)} 个函数")
                return cache_data
                
        except Exception as e:
            print(f"读取缓存失败: {str(e)}，将重新构建索引")
            return None
    
    def save_cache(self, cache_key: str, data: Dict) -> bool:
        """保存缓存数据"""
        cache_file = self._get_cache_file_path(cache_key)
        
        try:
            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"缓存索引失败: {str(e)}")
            return False
    
    def _normalize_cache_data(self, cache_data: Any) -> Dict:
        """标准化缓存数据格式"""
        # 兼容旧缓存格式：如果是list，转换为dict
        if isinstance(cache_data, list):
            func_index = {}
            for chunk in cache_data:
                if isinstance(chunk, dict) and 'name' in chunk and 'file' in chunk:
                    unique_name = f"{chunk['file']}:{chunk['name']}"
                    func_index[unique_name] = chunk
            cache_data = func_index
        
        # 兼容旧缓存格式：如果是dict但value为list，转换为dict
        if isinstance(cache_data, dict):
            for k, v in list(cache_data.items()):
                if isinstance(v, list):
                    # 只保留第一个元素（假设为chunk）
                    if v and isinstance(v[0], dict):
                        cache_data[k] = v[0]
        
        # 兼容旧缓存格式：补全缺失字段
        if isinstance(cache_data, dict):
            for k, v in list(cache_data.items()):
                if isinstance(v, dict) and ('start_line' not in v or 'end_line' not in v):
                    v.setdefault('start_line', 1)
                    v.setdefault('end_line', 1)
        
        return cache_data