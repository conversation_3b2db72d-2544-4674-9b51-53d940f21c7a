"""
Shangou AI CR 应用启动文件

按照第一阶段架构重构要求，集成新的服务架构
"""

import asyncio
import logging
import os
import signal
import sys
import time
import traceback
import threading
from pathlib import Path

from werkzeug.serving import run_simple

# 导入新架构组件
from config.base_config import BaseConfig
from core.logging import init_logging
from services.service_manager import ServiceManager, init_service_manager, set_service_manager
from services.kms_service import KmsService
from services.org_service import OrgService
from services.git_service import GitService

# 可选依赖的服务
try:
    from services.daxiang_service import DaXiangService
except ImportError:
    logging.warning("DaXiangService不可用，将跳过相关功能")
    DaXiangService = None

try:
    from services.devtools_service import DevtoolsService
except ImportError:
    logging.warning("DevtoolsService不可用，将跳过相关功能")
    DevtoolsService = None

try:
    from services.km_service import KmService
except ImportError:
    logging.warning("KmService不可用，将跳过相关功能")
    KmService = None
from core.database import init_database
from core.service_registry import register_service, set_initialization_in_progress

# 导入Flask应用（可选）
try:
    from api.apps import app
except ImportError:
    logging.warning("Flask应用不可用，将跳过Web服务功能")
    app = None

# 全局变量
stop_event = threading.Event()
service_manager: ServiceManager = None


def signal_handler(sig, frame):
    """信号处理器"""
    logging.info("Received interrupt signal, shutting down...")
    stop_event.set()
    
    # 停止所有服务
    if service_manager:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(service_manager.stop_all_services())
        finally:
            loop.close()
    
    time.sleep(1)
    sys.exit(0)


async def initialize_services(config: BaseConfig) -> ServiceManager:
    """初始化所有服务"""
    logging.info("开始初始化服务...")

    # 设置初始化状态
    set_initialization_in_progress(True)

    # 初始化数据库
    try:
        db_manager = init_database(config.database)
        logging.info("数据库初始化成功")
    except Exception as e:
        logging.error(f"数据库初始化失败: {str(e)}")
        # 数据库初始化失败不应该阻止应用启动

    # 使用统一的服务管理器创建函数
    from services.service_manager import create_service_manager
    manager = create_service_manager(config)

    # 设置全局服务管理器
    set_service_manager(manager)

    # 创建基础服务并注册到全局注册表（用于兼容性）
    logging.info("注册服务到全局注册表...")

    # 从管理器获取服务并注册到全局注册表
    for service_name in manager.list_services():
        service = manager.get_service(service_name)
        if service:
            register_service(service_name, service)

    # 手动创建一些可能缺失的服务
    try:
        # 确保基础服务存在
        if not manager.get_service("kms_service"):
            kms_service = KmsService(config.get_service_config("kms"))
            manager.register_service(kms_service)
            register_service("kms_service", kms_service)

        if not manager.get_service("org_service"):
            org_service = OrgService(config.get_service_config("org"))
            manager.register_service(org_service)
            register_service("org_service", org_service)

        if not manager.get_service("git_service"):
            git_service = GitService(config.get_service_config("git"))
            manager.register_service(git_service)
            register_service("git_service", git_service)

        # 创建依赖服务
        kms_svc = manager.get_service("kms_service")
        org_svc = manager.get_service("org_service")

        if kms_svc and DaXiangService and not manager.get_service("daxiang_service"):
            daxiang_service = DaXiangService(kms_svc, config.get_service_config("daxiang"))
            manager.register_service(daxiang_service)
            register_service("daxiang_service", daxiang_service)

        if kms_svc and DevtoolsService and not manager.get_service("devtools_service"):
            devtools_service = DevtoolsService(kms_svc, config.get_service_config("devtools"))
            manager.register_service(devtools_service)
            register_service("devtools_service", devtools_service)

        if kms_svc and org_svc and KmService and not manager.get_service("km_service"):
            daxiang_svc = manager.get_service("daxiang_service")
            if daxiang_svc:
                km_service = KmService(kms_svc, daxiang_svc, org_svc, config.get_service_config("km"))
                manager.register_service(km_service)
                register_service("km_service", km_service)

    except Exception as e:
        logging.warning(f"创建补充服务时出错: {str(e)}")

    logging.info("服务注册完成")

    # 启动所有服务
    results = await manager.start_all_services()

    # 检查启动结果
    failed_services = [name for name, success in results.items() if not success]
    if failed_services:
        logging.warning(f"以下服务启动失败: {failed_services}")

    successful_services = [name for name, success in results.items() if success]
    logging.info(f"成功启动服务: {successful_services}")

    # 预热服务注册表，确保服务可以正确获取
    await _warmup_service_registry()

    # 完成初始化
    set_initialization_in_progress(False)
    logging.info("服务初始化完成")

    return manager


async def _warmup_service_registry():
    """预热服务注册表，确保所有服务都可以正确获取"""
    try:
        logging.info("预热服务注册表...")
        from core.service_registry import (
            get_kms_service, get_org_service, get_git_service,
            get_daxiang_service, get_devtools_service, get_km_service,
            get_llm_service
        )

        # 触发服务获取，确保它们都在缓存中
        services = [
            get_kms_service(),
            get_org_service(),
            get_git_service(),
            get_daxiang_service(),
            get_devtools_service(),
            get_km_service()
        ]

        # 添加LLM服务（如果可用）
        try:
            llm_svc = get_llm_service()
            if llm_svc:
                services.append(llm_svc)
        except Exception as e:
            logging.debug(f"LLM服务不可用: {str(e)}")

        # 检查所有服务的健康状态
        for service in services:
            if hasattr(service, 'health_check'):
                health = await service.health_check()
                logging.debug(f"服务 {service.name} 健康状态: {health.get('status', 'unknown')}")

        logging.info("服务注册表预热完成")

    except Exception as e:
        logging.error(f"服务注册表预热失败: {str(e)}")


def show_banner():
    """显示启动横幅"""
    banner = r"""
         ____  _   _    _    _   _  ____  ___  _   _ 
        / ___|| | | |  / \  | \ | |/ ___|/ _ \| | | |
        \___ \| |_| | / _ \ |  \| | |  _| | | | | | |
         ___) |  _  |/ ___ \| |\  | |_| | |_| | |_| |
        |____/|_| |_/_/   \_\_| \_|\____|\___/ \___/
        
        AI Code Review System - Phase 1 Architecture
    """
    logging.info(banner)


def show_config_info(config: BaseConfig):
    """显示配置信息"""
    logging.info("=== 配置信息 ===")
    logging.info(f"应用名称: {config.app_name}")
    logging.info(f"版本: {config.version}")
    logging.info(f"调试模式: {config.debug}")
    logging.info(f"API端口: {config.api.port}")
    logging.info(f"数据库类型: {config.database.type}")
    logging.info(f"MCP启用: {config.mcp.enabled}")
    logging.info(f"项目根目录: {Path(__file__).parent}")
    logging.info("================")


def main():
    """主函数"""
    try:
        # 解析命令行参数
        import argparse
        parser = argparse.ArgumentParser(description="Shangou AI CR Server")
        parser.add_argument("--config", default=None, help="配置文件路径")
        parser.add_argument("--debug", action="store_true", help="调试模式")
        parser.add_argument("--version", action="store_true", help="显示版本信息")
        parser.add_argument("--port", type=int, default=None, help="HTTP端口")
        parser.add_argument("--host", default=None, help="HTTP主机")
        args = parser.parse_args()
        
        if args.version:
            print("Shangou AI CR v1.0.0 - Phase 1 Architecture")
            return
        
        # 加载配置
        if args.config:
            config = BaseConfig.load_from_file(args.config)
        else:
            config = BaseConfig.load_from_env()
        
        # 应用命令行参数
        if args.debug:
            config.debug = True
            config.api.debug = True
            config.monitoring.log_level = "DEBUG"
        
        if args.port:
            config.api.port = args.port
        
        if args.host:
            config.api.host = args.host
        
        # 初始化日志系统
        init_logging(config.monitoring, config.app_name)
        
        # 显示启动信息
        show_banner()
        show_config_info(config)
        
        # 初始化服务
        global service_manager
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            service_manager = loop.run_until_complete(initialize_services(config))
        except Exception as e:
            logging.error(f"服务初始化失败: {str(e)}")
            raise
        finally:
            # 确保所有待处理的任务完成
            pending_tasks = asyncio.all_tasks(loop)
            if pending_tasks:
                logging.info(f"等待 {len(pending_tasks)} 个待处理任务完成...")
                loop.run_until_complete(asyncio.gather(*pending_tasks, return_exceptions=True))
            loop.close()
        
        # 设置信号处理
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        # 启动HTTP服务器
        if app:
            logging.info(f"启动HTTP服务器: {config.api.host}:{config.api.port}")
            run_simple(
                hostname=config.api.host,
                port=config.api.port,
                application=app,
                threaded=True,
                use_reloader=config.api.debug,
                use_debugger=config.api.debug,
            )
        else:
            logging.warning("Flask应用不可用，跳过HTTP服务器启动")
            logging.info("服务初始化完成，但无法启动Web服务")
        
    except KeyboardInterrupt:
        logging.info("收到中断信号，正在关闭...")
    except Exception as e:
        logging.error(f"应用启动失败: {str(e)}")
        traceback.print_exc()
    finally:
        # 清理资源
        if service_manager:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                loop.run_until_complete(service_manager.stop_all_services())
                # 确保所有待处理的任务完成
                pending_tasks = asyncio.all_tasks(loop)
                if pending_tasks:
                    logging.info(f"等待 {len(pending_tasks)} 个待处理任务完成...")
                    loop.run_until_complete(asyncio.gather(*pending_tasks, return_exceptions=True))
            except Exception as e:
                logging.error(f"服务关闭失败: {str(e)}")
            finally:
                loop.close()


if __name__ == "__main__":
    main()
