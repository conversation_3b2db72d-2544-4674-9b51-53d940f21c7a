# 统一数据库配置文件
standard:
# Zebra代理配置（公司内部MySQL组件）
zebra:
  app_key: 'com.sankuai.shangou.ai.cr'  # 公司应用Key
  ref_keys:
    default: 'sgai_cr_test'  # 默认数据库引用Key
    # 可以添加其他环境的引用key
    # prod: 'sgai_rag_flow_prod'
    # staging: 'sgai_rag_flow_staging'
  
  # 连接池配置
  pool_size: 100  # 最大连接数
  pool_timeout: 30  # 连接超时时间（秒）
  pool_recycle: 3600  # 连接回收时间（秒）

# 全局配置
global:
  # 默认使用哪种连接方式: standard 或 zebra
  default_connection: 'standard' 