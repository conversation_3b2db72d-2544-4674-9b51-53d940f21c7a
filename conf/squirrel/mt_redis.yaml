# 美团Redis配置
# 集群名称
cluster_name: redis-sg-fission-activity_qa

# 环境：test, prod
env: test

# Proxy地址（valkey直连方式使用）
proxy_address: *************:80

# 是否使用Squirrel SDK连接Redis（true使用SDK，false使用valkey直连）
use_squirrel_sdk: false

# Squirrel SDK配置文件路径（SDK方式使用）
# proxy_config_path: /path/to/squirrel-proxy.conf

# 应用标识（SDK方式使用）
appkey: com.sankuai.sg.ai

# Category配置
category_config:
  category: sg_ai
  duration: "-1"  # -1表示不过期
  indexTemplate: "c{0}"
  version: 0 