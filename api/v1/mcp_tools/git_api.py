"""
Git操作服务API

提供Git仓库操作相关的RESTful API接口
"""

from flask import request, jsonify
from typing import Dict, List, Any, Optional
from .base import MCPToolsAPI, handle_api_errors, validate_json_request, tool_registry
from api.service.git_service import GitService

class GitAPI(MCPToolsAPI):
    """Git操作服务API类"""
    
    def __init__(self):
        super().__init__("git")
        self.git_service = GitService()
        
        # 注册MCP工具
        self._register_mcp_tools()
    
    def _register_mcp_tools(self):
        """注册MCP工具定义"""
        
        # 获取代码差异工具
        tool_registry.register_tool(
            name="git_get_diff",
            description="获取Git仓库两个引用之间的代码差异",
            input_schema={
                "type": "object",
                "properties": {
                    "project": {"type": "string", "description": "项目名称"},
                    "repo": {"type": "string", "description": "仓库名称"},
                    "from_ref": {"type": "string", "description": "起始分支/commit"},
                    "to_ref": {"type": "string", "description": "目标分支/commit"},
                    "options": {
                        "type": "object",
                        "properties": {
                            "context_lines": {"type": "integer", "default": 0},
                            "filter": {"type": "string", "default": "AM"},
                            "exclude_patterns": {
                                "type": "array",
                                "items": {"type": "string"},
                                "default": ["*.lock", "node_modules"]
                            }
                        }
                    }
                },
                "required": ["project", "repo", "from_ref", "to_ref"]
            },
            api_endpoint="/api/v1/mcp-tools/git/diff",
            http_method="POST"
        )
        
        # 获取文件列表工具
        tool_registry.register_tool(
            name="git_list_files",
            description="获取Git仓库的文件列表",
            input_schema={
                "type": "object",
                "properties": {
                    "project": {"type": "string", "description": "项目名称"},
                    "repo": {"type": "string", "description": "仓库名称"},
                    "branch": {"type": "string", "description": "分支名称"},
                    "suffixes": {
                        "type": "array",
                        "items": {"type": "string"},
                        "description": "文件后缀过滤"
                    },
                    "limit": {"type": "integer", "description": "返回文件数量限制", "default": 1000}
                },
                "required": ["project", "repo"]
            },
            api_endpoint="/api/v1/mcp-tools/git/files",
            http_method="GET"
        )
        
        # 读取文件内容工具
        tool_registry.register_tool(
            name="git_read_file",
            description="读取Git仓库中指定文件的内容",
            input_schema={
                "type": "object",
                "properties": {
                    "project": {"type": "string", "description": "项目名称"},
                    "repo": {"type": "string", "description": "仓库名称"},
                    "file_path": {"type": "string", "description": "文件路径"},
                    "branch": {"type": "string", "description": "分支名称"}
                },
                "required": ["project", "repo", "file_path"]
            },
            api_endpoint="/api/v1/mcp-tools/git/files/content",
            http_method="GET"
        )
    
    def register_routes(self, blueprint):
        """注册路由"""
        
        @blueprint.route('/git/diff', methods=['POST'])
        @handle_api_errors
        @validate_json_request(['project', 'repo', 'from_ref', 'to_ref'])
        def get_diff():
            """获取代码差异"""
            data = request.get_json()
            project = data['project']
            repo = data['repo']
            from_ref = data['from_ref']
            to_ref = data['to_ref']
            options = data.get('options', {})
            
            try:
                diff_content = self.git_service.get_diff(project, repo, from_ref, to_ref)
                
                if diff_content is None:
                    return jsonify(self.success_response({
                        "diff_content": "",
                        "statistics": {
                            "files_changed": 0,
                            "insertions": 0,
                            "deletions": 0
                        },
                        "files": []
                    }))
                
                # 分析diff统计信息
                stats = self._analyze_diff_stats(diff_content)
                
                return jsonify(self.success_response({
                    "diff_content": diff_content,
                    "statistics": stats,
                    "files": self._extract_changed_files(diff_content),
                    "refs": {
                        "from": from_ref,
                        "to": to_ref
                    }
                }))
                
            except Exception as e:
                return self.handle_service_error(e)
        
        @blueprint.route('/git/files', methods=['GET'])
        @handle_api_errors
        def list_files():
            """获取文件列表"""
            project = request.args.get('project')
            repo = request.args.get('repo')
            branch = request.args.get('branch')
            suffixes = request.args.getlist('suffixes')
            limit = int(request.args.get('limit', 1000))
            
            if not project or not repo:
                return jsonify(self.success_response({
                    "error": "project and repo parameters are required"
                }))
            
            try:
                files = self.git_service.list_repo_files(
                    project, repo, branch, suffixes if suffixes else None
                )
                
                # 应用限制
                total_count = len(files)
                if limit and len(files) > limit:
                    files = files[:limit]
                
                # 生成文件信息
                file_info = []
                for file_path in files:
                    file_info.append({
                        "path": file_path,
                        "type": "file",
                        "language": self._detect_language(file_path)
                    })
                
                return jsonify(self.success_response({
                    "files": file_info,
                    "total_count": total_count,
                    "filtered_count": len(file_info),
                    "filters": {
                        "branch": branch,
                        "suffixes": suffixes,
                        "limit": limit
                    }
                }))
                
            except Exception as e:
                return self.handle_service_error(e)
        
        @blueprint.route('/git/files/content', methods=['GET'])
        @handle_api_errors
        def read_file_content():
            """读取文件内容"""
            project = request.args.get('project')
            repo = request.args.get('repo')
            file_path = request.args.get('file_path')
            branch = request.args.get('branch')
            
            if not project or not repo or not file_path:
                return jsonify(self.success_response({
                    "error": "project, repo, and file_path parameters are required"
                }))
            
            try:
                content = self.git_service.read_file(project, repo, file_path)
                
                # 文件信息
                file_info = {
                    "path": file_path,
                    "size": len(content.encode('utf-8')) if content else 0,
                    "language": self._detect_language(file_path),
                    "line_count": len(content.splitlines()) if content else 0
                }
                
                return jsonify(self.success_response({
                    "content": content,
                    "encoding": "utf-8",
                    "file_info": file_info,
                    "branch": branch
                }))
                
            except Exception as e:
                return self.handle_service_error(e)
        
        @blueprint.route('/git/repository/info', methods=['GET'])
        @handle_api_errors
        def get_repository_info():
            """获取仓库信息"""
            project = request.args.get('project')
            repo = request.args.get('repo')
            
            if not project or not repo:
                return jsonify(self.success_response({
                    "error": "project and repo parameters are required"
                }))
            
            try:
                # 获取基本信息
                ssh_url = self.git_service.get_ssh_url({'project': project, 'repo': repo})
                repo_path = self.git_service.get_repo_path(project, repo)
                
                return jsonify(self.success_response({
                    "project": project,
                    "repo": repo,
                    "ssh_url": ssh_url,
                    "local_path": repo_path,
                    "status": "available"
                }))
                
            except Exception as e:
                return self.handle_service_error(e)
        
        @blueprint.route('/git/tools', methods=['GET'])
        @handle_api_errors
        def list_git_tools():
            """列出Git相关的MCP工具"""
            tools = [tool for name, tool in tool_registry.list_tools().items() 
                    if name.startswith('git_')]
            
            return jsonify(self.success_response({
                "tools": tools,
                "count": len(tools)
            }))
    
    def _analyze_diff_stats(self, diff_content: str) -> Dict[str, int]:
        """分析diff统计信息"""
        if not diff_content:
            return {"files_changed": 0, "insertions": 0, "deletions": 0}
        
        lines = diff_content.split('\n')
        files_changed = set()
        insertions = 0
        deletions = 0
        
        for line in lines:
            if line.startswith('+++') or line.startswith('---'):
                # 提取文件名
                if line.startswith('+++') and not line.endswith('/dev/null'):
                    file_path = line[4:].strip()
                    if file_path.startswith('b/'):
                        file_path = file_path[2:]
                    files_changed.add(file_path)
            elif line.startswith('+') and not line.startswith('+++'):
                insertions += 1
            elif line.startswith('-') and not line.startswith('---'):
                deletions += 1
        
        return {
            "files_changed": len(files_changed),
            "insertions": insertions,
            "deletions": deletions
        }
    
    def _extract_changed_files(self, diff_content: str) -> List[Dict[str, Any]]:
        """提取变更文件列表"""
        if not diff_content:
            return []
        
        lines = diff_content.split('\n')
        files = []
        current_file = None
        
        for line in lines:
            if line.startswith('+++') and not line.endswith('/dev/null'):
                file_path = line[4:].strip()
                if file_path.startswith('b/'):
                    file_path = file_path[2:]
                
                current_file = {
                    "path": file_path,
                    "status": "modified",  # 简化处理，实际可以更精确
                    "changes": 0
                }
                files.append(current_file)
            elif current_file and (line.startswith('+') or line.startswith('-')):
                if not line.startswith('+++') and not line.startswith('---'):
                    current_file["changes"] += 1
        
        return files
    
    def _detect_language(self, file_path: str) -> Optional[str]:
        """检测文件语言"""
        if file_path.endswith('.py'):
            return 'python'
        elif file_path.endswith(('.js', '.jsx')):
            return 'javascript'
        elif file_path.endswith(('.ts', '.tsx')):
            return 'typescript'
        elif file_path.endswith('.java'):
            return 'java'
        elif file_path.endswith(('.cpp', '.cc', '.cxx')):
            return 'cpp'
        elif file_path.endswith('.c'):
            return 'c'
        elif file_path.endswith('.go'):
            return 'go'
        elif file_path.endswith('.rs'):
            return 'rust'
        elif file_path.endswith(('.html', '.htm')):
            return 'html'
        elif file_path.endswith('.css'):
            return 'css'
        elif file_path.endswith(('.yml', '.yaml')):
            return 'yaml'
        elif file_path.endswith('.json'):
            return 'json'
        elif file_path.endswith('.xml'):
            return 'xml'
        elif file_path.endswith('.md'):
            return 'markdown'
        else:
            return 'unknown'
