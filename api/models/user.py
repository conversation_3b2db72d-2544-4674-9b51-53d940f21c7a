"""
用户模型 - 内存存储版本
不依赖数据库，使用内存存储用户信息
"""

from flask_login import UserMixin
from typing import Dict, Optional
import uuid
from datetime import datetime


class User(UserMixin):
    """用户模型类 - 内存存储版本"""
    
    # 内存存储用户数据
    _users: Dict[str, 'User'] = {}
    
    def __init__(self, user_id: str, email: str, nickname: str, 
                 access_token: str = None, login_channel: str = "sso",
                 avatar: str = None, **kwargs):
        self.id = user_id
        self.email = email
        self.nickname = nickname
        self.access_token = access_token or str(uuid.uuid4())
        self.login_channel = login_channel
        self.avatar = avatar
        self.last_login_time = datetime.now()
        self.is_authenticated = True
        self.is_active = True
        self.is_anonymous = False
        self.create_time = datetime.now()
        
        # 存储到内存中
        User._users[self.id] = self
    
    def get_id(self):
        """Flask-Login要求的方法"""
        return self.id
    
    def to_json(self) -> Dict:
        """转换为JSON格式"""
        return {
            "id": self.id,
            "email": self.email,
            "nickname": self.nickname,
            "access_token": self.access_token,
            "login_channel": self.login_channel,
            "avatar": self.avatar,
            "last_login_time": self.last_login_time.isoformat() if self.last_login_time else None,
            "is_authenticated": self.is_authenticated,
            "is_active": self.is_active,
            "is_anonymous": self.is_anonymous,
            "create_time": self.create_time.isoformat() if self.create_time else None
        }
    
    @classmethod
    def get_by_id(cls, user_id: str) -> Optional['User']:
        """根据ID获取用户"""
        return cls._users.get(user_id)
    
    @classmethod
    def get_by_email(cls, email: str) -> Optional['User']:
        """根据邮箱获取用户"""
        for user in cls._users.values():
            if user.email == email:
                return user
        return None
    
    @classmethod
    def create_user(cls, user_id: str, email: str, nickname: str, **kwargs) -> 'User':
        """创建新用户"""
        return cls(user_id=user_id, email=email, nickname=nickname, **kwargs)
    
    @classmethod
    def get_all_users(cls) -> Dict[str, 'User']:
        """获取所有用户（调试用）"""
        return cls._users.copy()
    
    def update_access_token(self, access_token: str = None):
        """更新访问令牌"""
        if access_token:
            self.access_token = access_token
        else:
            self.access_token = str(uuid.uuid4())
        self.last_login_time = datetime.now()

    def update_last_login(self):
        """更新最后登录时间"""
        self.last_login_time = datetime.now()

    def save(self):
        """保存用户信息（内存版本中无需实际操作）"""
        # 在内存版本中，数据已经在内存中，无需额外保存操作
        # 这个方法保持兼容性
        pass

    def __repr__(self):
        return f"<User {self.nickname}({self.email})>"