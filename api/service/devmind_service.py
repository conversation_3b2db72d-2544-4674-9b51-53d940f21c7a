import os

import dotenv
import requests
from typing import Optional, Dict, Any

dotenv.load_dotenv()


class DevmindService:
    """
    与 Devmind 聊天助手交互的服务类
    """

    def __init__(self, base_url: Optional[str] = None, api_key: Optional[str] = None):
        self.base_url = base_url.rstrip('/') if base_url else os.environ["DM_BASE_URL"]
        self.api_key = api_key or os.environ["DM_API_KEY"]

    def converse_with_assistant(
            self,
            chat_id: str,
            question: str,
            stream: bool = True
    ) -> Dict[str, Any]:
        """
        向指定聊天助手提问，开启 AI 对话

        :param chat_id: 聊天助手ID
        :param question: 提问内容
        :param stream: 是否流式输出
        :return: devmind API 响应
        """
        url = f"{self.base_url}/api/v1/chats/{chat_id}/completions"
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }
        payload = {
            "question": question,
            "stream": stream
        }

        response = requests.post(url, headers=headers, json=payload, timeout=30)
        response.raise_for_status()
        return response.json()
