import os
import requests
from typing import Optional, Dict, Any
import httpx


class DevmindService:
    """
    与 Devmind 聊天助手交互的服务类
    """

    def __init__(self, base_url: Optional[str] = None, api_key: Optional[str] = None):
        self.base_url = base_url.rstrip('/') or os.environ['DEVMIND_BASE_URL']
        self.api_key = api_key or os.environ['DEVMIND_API_KEY']

    def converse_with_assistant(
            self,
            chat_id: str,
            question: str,
            stream: bool = True,
            session_id: Optional[str] = None,
            user_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        向指定聊天助手提问，开启 AI 对话

        :param chat_id: 聊天助手ID
        :param question: 提问内容
        :param stream: 是否流式输出
        :param session_id: 会话ID（可选）
        :param user_id: 用户ID（可选）
        :return: devmind API 响应
        """
        url = f"{self.base_url}/api/v1/chats/{chat_id}/completions"
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }
        payload = {
            "question": question,
            "stream": stream
        }
        if session_id:
            payload["session_id"] = session_id
        if user_id:
            payload["user_id"] = user_id

        response = requests.post(url, headers=headers, json=payload, timeout=30)
        response.raise_for_status()
        return response.json()

    def retrieve_chunks(
            self,
            question: str,
            dataset_ids: Optional[list] = None,
            document_ids: Optional[list] = None,
            page: int = 1,
            page_size: int = 30,
            similarity_threshold: float = 0.2,
            vector_similarity_weight: float = 0.3,
            top_k: int = 1024,
            rerank_id: Optional[str] = None,
            keyword: bool = False,
            highlight: bool = False
    ) -> Dict[str, Any]:
        """
        检索chunks，调用Devmind /api/v1/retrieval接口
        :param question: 检索问题
        :param dataset_ids: 数据集ID列表
        :param document_ids: 文档ID列表
        :param page: 页码
        :param page_size: 每页数量
        :param similarity_threshold: 最小相似度
        :param vector_similarity_weight: 向量相似度权重
        :param top_k: 参与向量计算的chunk数
        :param rerank_id: rerank模型ID
        :param keyword: 是否启用关键词匹配
        :param highlight: 是否高亮
        :return: API响应
        """
        url = f"{self.base_url}/api/v1/retrieval"
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }
        payload = {
            "question": question,
            "page": page,
            "page_size": page_size,
            "similarity_threshold": similarity_threshold,
            "vector_similarity_weight": vector_similarity_weight,
            "top_k": top_k,
            "keyword": keyword,
            "highlight": highlight,
            "dataset_ids": []
        }
        if dataset_ids is not None:
            payload["dataset_ids"] = dataset_ids
        if document_ids is not None:
            payload["document_ids"] = document_ids
        if rerank_id is not None:
            payload["rerank_id"] = rerank_id
        response = requests.post(url, headers=headers, json=payload, timeout=30)
        response.raise_for_status()
        return response.json()

    async def aretrieve_chunks(
            self,
            question: str,
            dataset_ids: Optional[list] = None,
            document_ids: Optional[list] = None,
            page: int = 1,
            page_size: int = 30,
            similarity_threshold: float = 0.2,
            vector_similarity_weight: float = 0.3,
            top_k: int = 1024,
            rerank_id: Optional[str] = None,
            keyword: bool = False,
            highlight: bool = False
    ) -> Dict[str, Any]:
        """
        异步检索chunks，调用Devmind /api/v1/retrieval接口
        """
        url = f"{self.base_url}/api/v1/retrieval"
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }
        payload = {
            "question": question,
            "page": page,
            "page_size": page_size,
            "similarity_threshold": similarity_threshold,
            "vector_similarity_weight": vector_similarity_weight,
            "top_k": top_k,
            "keyword": keyword,
            "highlight": highlight,
            "dataset_ids": []
        }
        if dataset_ids is not None:
            payload["dataset_ids"] = dataset_ids
        if document_ids is not None:
            payload["document_ids"] = document_ids
        if rerank_id is not None:
            payload["rerank_id"] = rerank_id
        async with httpx.AsyncClient() as client:
            response = await client.post(url, headers=headers, json=payload, timeout=30)
        response.raise_for_status()
        return response.json()
