# 学城服务，负责学城文档的创建与管理
# 对接学城开放平台API
import logging
import os
from pathlib import Path

import octo_rpc
import yaml
from typing import Any, Dict

from octo_rpc import NonMeshClient, load

from api.service.daxiang_service import DaXiangService
from api.service.kms_service import KmsService
from api.service.org_service import OrgService
from services.km_service import CreateDocParams

octo_rpc.log.setLevel(logging.CRITICAL)
ROOT_DIR = Path(__file__).resolve().parent.parent.parent.__str__()

class KmService:
    """
    学城服务，负责学城文档的创建与管理
    """

    def __init__(self):
        """初始化学城服务"""
        self.env = os.environ.get('INF_BOM_ENV', 'test')
        self.swimlane = os.environ.get('INF_BOM_SWIMLANE', None)
        self.km_config = self._load_km_config()
        self.kms_service = KmsService()
        self.dx_service = DaXiangService(self.kms_service)  # 这里应该传入KmsService实例
        self.org_service = OrgService()
        try:
            self.open_km_service = load(ROOT_DIR+"/infra/thrift/km/OpenKmService.thrift")
            self.thrift_client = NonMeshClient(
                appkey=self.km_config.get('localAppKey'),
                env=self.env,
                service=self.open_km_service.XmOpenKmServiceI,
                service_name="com.sankuai.xm.openplatform.api.service.open.XmOpenKmServiceI",
                remote_appkey=self.km_config.get('remoteAppKey'),
                timeout=30000,
            )
        except Exception as e:
            print(f"初始化学城服务失败: {str(e)}")

    def _load_km_config(self) -> Dict[str, Any]:
        """加载学城配置"""
        try:
            with open('config/legacy/km_conf.yaml', 'r') as f:
                config = yaml.safe_load(f)
                return config.get(self.env, {})
        except Exception as e:
            print(f"加载学城配置失败: {str(e)}")
            return {}

    async def create_doc(self, request_context: Any, params: CreateDocParams) -> str:
        """
        创建学城2.0文档
        
        Args:
            request_context: 请求上下文
            params: 创建文档的参数
            
        Returns:
            创建后的文档URL
        """
        try:
            # 获取访问令牌
            access_token = self.dx_service.get_access_token(request_context)

            # 如果没有操作者员工ID，从组织架构服务获取
            if not params.operatorEmpId:
                emp_info = self.org_service.get_emp_info(
                    getattr(request_context, 'headers', {}),
                    getattr(request_context, 'args', {})
                )
                params.operatorEmpId = int(emp_info['empId']) if emp_info['empId'] else None
            # 调用Thrift服务创建文档
            print(request_context.json.get("spaceId"))
            params.spaceId = request_context.json.get("spaceId")
            res = self.thrift_client.addCollaborationContent(
                request=self.open_km_service.CollaborationContentReq(**params.to_dict()),
                accessToken=access_token
            )

            # 解析响应
            status = getattr(res, 'status', None)
            info = getattr(res, 'info', None)

            if not status or status.code != 0:
                raise ValueError(f"[yunzhuan:km:createDoc] {status}\n")

            # 返回文档URL
            return f"{self.km_config.get('domain')}/collabpage/{info}"

        except Exception as e:
            print(f"创建文档失败: {str(e)}")
            raise ValueError(f"创建文档失败: {str(e)}")
