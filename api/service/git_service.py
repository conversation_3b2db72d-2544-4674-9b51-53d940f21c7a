# Git服务，负责仓库操作与diff获取
# 需对接Python的Git操作库（如GitPython）
import os
import shutil
from pathlib import Path
from typing import Any, Dict, Optional, Union

import git  # pip install gitpython

BASE_PATH = Path(__file__).resolve().parent.parent.parent.__str__()


class GitService:
    """
    Git服务，负责仓库操作与diff获取
    """

    def __init__(self):
        self.base_dir = os.path.join(BASE_PATH, 'temp-repos')
        # 创建base目录（如不存在）
        if not os.path.exists(self.base_dir):
            os.makedirs(self.base_dir, exist_ok=True)

    @staticmethod
    def init_repo(repo_path: str) -> git.Repo:
        """初始化仓库，返回git.Repo对象"""
        return git.Repo(repo_path)

    @staticmethod
    def get_ssh_url(ref: Union[Dict[str, Any], Any]) -> str:
        """
        获取SSH地址，支持 PRRef 结构和 project/repo 结构
        PRRef: {'repository': {'links': {'clone': [{'href': ..., 'name': ...}, ...]}}}
        普通: {'project': ..., 'repo': ...}
        """
        if isinstance(ref, dict) and 'repository' in ref:
            # PRRef 情况
            clone_links = ref['repository'].get('links', {}).get('clone', [])
            for link in clone_links:
                if link.get('name') == 'ssh':
                    return link.get('href', '')
            return ''
        # 普通 project/repo 情况
        return f"*******************:{ref['project']}/{ref['repo']}.git"

    def get_repo_path(self, project: str, repo: str) -> str:
        """获取本地仓库路径"""
        return os.path.join(self.base_dir, f"{project}_{repo}")

    def get_diff(
            self,
            project: str,
            repo: str,
            from_ref: Union[Dict[str, Any], str],
            to_ref: Union[Dict[str, Any], str]
    ) -> Optional[str]:
        """
        获取 diff 内容，仿照 Node.js 逻辑，支持自动 clone、fetch、merge-base、diff 过滤
        Args:
            project: 项目名
            repo: 仓库名
            from_ref: 起始分支/commit/PRRef
            to_ref: 目标分支/commit/PRRef
        Returns:
            过滤后的 diff 字符串
        """
        repo_path = self.get_repo_path(project, repo)
        ssh_url = self.get_ssh_url({'project': project, 'repo': repo})

        # 兼容 PRRef 结构
        from_branch = from_ref['displayId'] if isinstance(from_ref, dict) and 'displayId' in from_ref else from_ref
        to_branch = to_ref['displayId'] if isinstance(to_ref, dict) and 'displayId' in to_ref else to_ref

        try:
            # 这里可对接 SSH 初始化（如有 DevtoolsService.initSSH() 需求，可在此处实现）

            # 如果本地仓库不存在，先 clone
            if not os.path.exists(repo_path):
                if os.path.exists(repo_path):
                    shutil.rmtree(repo_path)
                git.Repo.clone_from(ssh_url, repo_path)
            repo_obj = git.Repo(repo_path)

            # fetch 最新
            repo_obj.git.fetch('--all')

            # 确保分支都 fetch 下来
            repo_obj.git.fetch('origin', from_branch)
            repo_obj.git.fetch('origin', to_branch)

            # 计算 merge-base
            merge_base = repo_obj.git.merge_base(
                f'refs/remotes/origin/{to_branch}',
                f'refs/remotes/origin/{from_branch}'
            )
            if isinstance(merge_base, list):
                merge_base = merge_base[0]
            merge_base_sha = str(merge_base).strip()

            # 生成 diff
            diff = repo_obj.git.diff(
                '--diff-filter=AM',
                '-U0',
                merge_base_sha,
                f'refs/remotes/origin/{from_branch}',
                '--',
                '.',
                ':(exclude)package-lock.json',
                ':(exclude)*.lock',
                ':(exclude)pnpm-lock.yaml',
                ':(exclude).pnpm',
                ':(exclude)node_modules',
                ':(exclude)*.log'
            )

            # 过滤掉删除的行，只保留新增的行
            filtered_diff = '\n'.join([line for line in diff.split('\n') if not line.startswith('-')])

            return filtered_diff

        except Exception as e:
            print(f'Git operation failed: {e}')
            raise RuntimeError('Failed to get diff') from e

    def list_repo_files(self, project: str, repo: str, branch: Optional[str] = None, suffixes=None) -> list:
        """
        获取本地仓库指定分支所有文件（可选后缀过滤），如本地无仓库则自动clone
        """
        import git
        repo_path = self.get_repo_path(project, repo)
        file_list = []
        ssh_url = self.get_ssh_url({'project': project, 'repo': repo})

        # 如果本地仓库不存在或损坏，自动clone
        if not os.path.exists(repo_path) or not os.path.exists(os.path.join(repo_path, '.git')):
            if os.path.exists(repo_path):
                import shutil
                shutil.rmtree(repo_path)
            git.Repo.clone_from(ssh_url, repo_path)
        repo_obj = git.Repo(repo_path)

        # 如果指定分支，自动fetch并checkout
        if branch:
            repo_obj.git.fetch('origin', branch)
            # 检查本地分支是否存在
            if branch not in repo_obj.heads:
                repo_obj.git.checkout('-b', branch, f'origin/{branch}')
            else:
                repo_obj.git.checkout(branch)

        for root, dirs, files in os.walk(repo_path):
            for file in files:
                if not suffixes or any(file.endswith(suf) for suf in suffixes):
                    rel_path = os.path.relpath(os.path.join(root, file), repo_path)
                    file_list.append(rel_path)
        return file_list

    def read_file(self, project: str, repo: str, file_path: str) -> str:
        """
        读取本地仓库指定文件内容
        """
        repo_path = self.get_repo_path(project, repo)
        abs_path = os.path.join(repo_path, file_path)
        with open(abs_path, 'r', encoding='utf-8') as f:
            return f.read()
