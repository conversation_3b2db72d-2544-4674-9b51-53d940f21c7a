import base64
import hashlib
import hmac
import time

import requests
import json
import subprocess
from typing import Dict, Any, Optional, Union

from kms_sdk.kms import Kms
from sts_sdk.model.enums import AuthAction
from sts_sdk.model.subject import STSRequest, SignParam
from sts_sdk.service.signature_service_factory import STSServiceFactory

from api.service.kms_service import KmsService


class DevtoolsService:
    """
    DevTools服务，负责与开发工具网关交互
    """

    def __init__(self):
        self.kms_service = KmsService()
        # 设置客户端 appkey
        self.CLIENT_APPKEY = 'com.sankuai.yunzhuan.devhelper'
        # 设置服务端的appkey
        self.SERVER_APPKEY = 'com.sankuai.devtools.gateway.service'
        # 域名
        self.DOMAIN = 'https://dev-api.vip.sankuai.com/mcode'

    def oceanus_auth(self) -> str:
        """
        获取Oceanus认证Token
        """
        # 实际实现需要对接Oceanus SDK
        # 这里是模拟实现
        try:
            # token默认从kms读取，如有跨环境调用，需要指定token
            s1_json = {
                "algo": "0001",
                "type": "patriot",
                "time": str(round(time.time() * 1000))
            }
            s2_json = {
                "ns": self.CLIENT_APPKEY,
                "name": self.SERVER_APPKEY
            }
            s1 = base64.b64encode(json.dumps(s1_json).encode()).decode()
            s2 = base64.b64encode(json.dumps(s2_json).encode()).decode()
            # token默认从kms读取，如有跨环境调用，需要指定token
            token = json.loads(Kms.get_by_name(self.CLIENT_APPKEY, "auth_client_" + self.SERVER_APPKEY))[0]
            secret = hmac.new(token.encode(), s2.encode(), hashlib.sha1).hexdigest().upper()
            return f'{s1}.{s2}.{secret}'
        except Exception as e:
            print(f"Oceanus认证失败: {str(e)}")
            return ""

    def get_token_by_sso(self, mis: str, ssoid: str, online: bool = True) -> str:
        """
        获取SSO生成的STS鉴权
        """
        try:
            sts_sign_service = STSServiceFactory.create(
                STSRequest(self.SERVER_APPKEY, "SSOTicket", "online" if online else None))
            sign_param = SignParam(
                client_id=mis,
                aud="",
                scp="",
                roles="",
                ext_map={'ssoid': ssoid}
            )
            auth_token = sts_sign_service.sign(sign_param)
            return auth_token.at
        except Exception as e:
            print(f"SSO认证失败: {str(e)}")
            return ""

    def get_token_by_iam(self, iamname: str, iampwd: str, online: bool = True) -> str:
        """
        获取IAM生成的STS鉴权
        """
        try:
            sts_sign_service = STSServiceFactory.create(
                STSRequest(self.SERVER_APPKEY, "IAMTicket", AuthAction.SIGN, "online" if online else None))
            sign_param = SignParam(client_id=iamname, aud="", scp="", roles="", ext_map={'iampwd': iampwd})
            auth_token = sts_sign_service.sign(sign_param)
            return auth_token.at
        except Exception as e:
            print(f"IAM认证失败: {str(e)}")
            return ""

    def get_token(self, options: Optional[Dict[str, str]] = None) -> Dict[str, str]:
        """
        获取认证Token
        """
        token = ""
        mis = options.get("mis") if options else None
        ssoid = options.get("ssoid") if options else None
        if mis and ssoid:
            token = self.get_token_by_sso(mis, ssoid, True)
            print(f"SSO认证成功: {token}")
        else:
            password_result = self.kms_service.get_key(self.CLIENT_APPKEY, "git_yunzhuan_password")
            if password_result.get("success"):
                password = password_result.get("data")
                token = self.get_token_by_iam('git_yunzhuan', password, True)
        oceanus_token = self.oceanus_auth()

        return {
            "STS-TOKEN": token,
            "oceanus-remote-appkey": self.SERVER_APPKEY,
            "oceanus-auth": oceanus_token,
            "Cache-Control": "max-age=300",
        }

    def get_pr_info(self, url: str, params: Dict[str, Any], options: Optional[Dict[str, str]] = None) -> Dict[str, Any]:
        """
        获取PR信息（GET）
        """
        header_info = self.get_token(options)
        url_with_params = f"{self.DOMAIN}{url}"
        try:
            response = requests.get(
                url_with_params,
                headers=header_info,
                params=params
            )
            response.raise_for_status()
            return response.json()
        except Exception as e:
            raise e

    def set_comment(self, url: str, comments: str, options: Optional[Dict[str, str]] = None) -> Dict[str, Any]:
        """
        设置评论（POST）
        """
        header_info = self.get_token(options)
        url_with_params = f"{self.DOMAIN}{url}"
        try:
            response = requests.post(
                url_with_params,
                headers=header_info,
                json={"text": comments}
            )
            response.raise_for_status()
            return response.json()
        except Exception as e:
            raise e

    def init_ssh(self) -> Dict[str, Any]:
        """
        初始化SSH
        """
        try:
            public_key_result = self.kms_service.get_key(self.CLIENT_APPKEY, "code_id_ed25519_pub")
            private_key_result = self.kms_service.get_key(self.CLIENT_APPKEY, "code_id_ed25519")
            if not public_key_result.get("success") or not private_key_result.get("success"):
                raise Exception("获取SSH密钥失败")
            public_key = public_key_result.get("data")
            private_key = private_key_result.get("data")
            command = f'bash init_ssh.sh "{private_key}" "{public_key}"'
            process = subprocess.Popen(
                command,
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            stdout, stderr = process.communicate()
            return {
                "stdout": stdout,
                "stderr": stderr
            }
        except Exception as e:
            print(f"SSH初始化失败: {str(e)}")
            return {"error": str(e)}

    def create_pr(self, url: str, params: Dict[str, Any], options: Optional[Dict[str, str]] = None) -> Union[str, int]:
        """
        创建PR（POST）
        """
        header_info = self.get_token(options)
        url_with_params = f"{self.DOMAIN}{url}"
        try:
            response = requests.post(
                url_with_params,
                headers=header_info,
                json=params
            )
            response.raise_for_status()
            return response.json().get("id")
        except requests.exceptions.HTTPError as e:
            if e.response.status_code == 409:
                errors = e.response.json().get("errors", [])
                if errors and errors[0].get("existingPullRequest", {}).get("id"):
                    return errors[0]["existingPullRequest"]["id"]
                raise Exception(f"PR创建失败: {errors[0].get('message')}")
            raise e
        except Exception as e:
            raise e

    def fetch_pr_info(self, project: str, repo: str, pr_number: str, options: Optional[Dict[str, str]] = None) -> Dict[
        str, str]:
        """
        获取PR信息（真实调用prInfoHandler）
        """
        try:
            options = options if options else {}
            options["project"] = project
            options["repo"] = repo
            options["id"] = pr_number
            pr_info_response = self.pr_info_handler(options)

            from_branch = pr_info_response.get("fromRef", {}).get("displayId")
            to_branch = pr_info_response.get("toRef", {}).get("displayId")
            return {
                "project": project,
                "fromBranch": from_branch,
                "toBranch": to_branch
            }
        except Exception as e:
            print(f"获取PR信息失败: {str(e)}")
            return {
                "project": project,
                "fromBranch": None,
                "toBranch": None
            }

    def pr_info_handler(self, ctx) -> Dict[str, Any]:
        """
        处理PR信息请求（真实接口调用）
        """
        try:
            if hasattr(ctx, 'args'):
                project = ctx.args.get('project')
                repo = ctx.args.get('repo')
                pr_id = ctx.args.get('id')
            elif hasattr(ctx, 'params'):
                project = ctx.params.get('project')
                repo = ctx.params.get('repo')
                pr_id = ctx.params.get('id')
            else:
                project = ctx.get('project')
                repo = ctx.get('repo')
                pr_id = ctx.get('id')

            # 真实API请求
            pr_url = f"{self.DOMAIN}/rest/api/2.0/projects/{project}/repos/{repo}/pull-requests/{pr_id}"
            headers = self.get_token(ctx)

            pr_resp = requests.get(pr_url, headers=headers)
            pr_resp.raise_for_status()
            pr_info = pr_resp.json()
            return pr_info
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }

    def get_pr_comments(self, project, repo, pr_id, options: dict = None):
        """
        获取PR评论接口返回的json数据
        :param project: 项目名（如 wangqichen02）
        :param repo: 仓库名（如 shangou_ai_cr）
        :param pr_id: PR编号（如 5）
        :param options: 认证相关参数（如 mis/ssoid/token）
        :return: comments_json
        """
        import requests
        url = f"https://dev.sankuai.com/rest/api/5.0/projects/{project}/repos/{repo}/pull-requests/{pr_id}/assignments"
        params = {'states': 'open'}
        # 优先使用 options 里的 token，否则自动获取
        header_info = self.get_token(options)
        resp = requests.get(url, params=params, headers=header_info)
        resp.raise_for_status()
        return resp.json()

    def get_all_pr_diffs(self, project: str, repo: str, pr_id: str, options: Optional[Dict[str, str]] = None) -> Dict[str, Any]:
        """
        获取指定PR的所有变更文件的diff内容
        :param project: 项目名
        :param repo: 仓库名
        :param pr_id: PR编号
        :param options: 认证相关参数
        :return: {file_path: diff_json, ...}
        """
        try:
            # 1. 获取变更文件列表，处理分页
            changes_url = f"/rest/api/2.0/projects/{project}/repos/{repo}/pull-requests/{pr_id}/changes"
            all_files = []
            start = 0
            while True:
                params = {"start": start}
                header_info = self.get_token(options)
                print(header_info)
                url_with_params = f"{self.DOMAIN}{changes_url}"
                resp = requests.get(url_with_params, headers=header_info, params=params)
                resp.raise_for_status()
                data = resp.json()
                values = data.get("values", [])
                all_files.extend(values)
                if data.get("lastPage", True):
                    break
                start = data.get("nextPageStart", 0)
                if not values or start == 0:
                    break
            # 2. 针对每个文件，获取diff内容
            diffs = {}
            for file_info in all_files:
                file_path = file_info.get("path", {}).get("toString")
                if not file_path:
                    continue
                diff_url = f"/rest/api/2.0/projects/{project}/repos/{repo}/pull-requests/{pr_id}/diff/{file_path}"
                url_with_params = f"{self.DOMAIN}{diff_url}"
                try:
                    diff_resp = requests.get(url_with_params, headers=header_info)
                    diff_resp.raise_for_status()
                    diffs[file_path] = diff_resp.json()
                except Exception as e:
                    diffs[file_path] = {"error": str(e)}
            return diffs
        except Exception as e:
            return {"success": False, "error": str(e)}
