import base64
import hashlib
import hmac
import time

import requests
import json
import subprocess
from typing import Dict, Any, Optional, Union

from kms_sdk.kms import Kms
from sts_sdk.model.enums import AuthAction
from sts_sdk.model.subject import STSRequest, SignParam
from sts_sdk.service.signature_service_factory import STSServiceFactory

from api.service.kms_service import KmsService


class DevtoolsService:
    """
    DevTools服务，负责与开发工具网关交互
    """

    def __init__(self):
        self.kms_service = KmsService()
        # 设置客户端 appkey
        self.CLIENT_APPKEY = 'com.sankuai.yunzhuan.devhelper'
        # 设置服务端的appkey
        self.SERVER_APPKEY = 'com.sankuai.devtools.gateway.service'
        # 域名
        self.DOMAIN = 'https://dev-api.vip.sankuai.com/mcode'

    def oceanus_auth(self) -> str:
        """
        获取Oceanus认证Token
        """
        # 实际实现需要对接Oceanus SDK
        # 这里是模拟实现
        try:
            # token默认从kms读取，如有跨环境调用，需要指定token
            # auth_token = json.loads(Kms.get_by_name(self.CLIENT_APPKEY, "auth_client_" + self.SERVER_APPKEY))[0]
            s1_json = {
                "algo": "0001",
                "type": "patriot",
                "time": str(round(time.time() * 1000))
            }
            s2_json = {
                "ns": self.CLIENT_APPKEY,
                "name": self.SERVER_APPKEY
            }
            s1 = base64.b64encode(json.dumps(s1_json).encode()).decode()
            s2 = base64.b64encode(json.dumps(s2_json).encode()).decode()
            # token默认从kms读取，如有跨环境调用，需要指定token
            token = json.loads(Kms.get_by_name(self.CLIENT_APPKEY, "auth_client_" + self.SERVER_APPKEY))[0]
            secret = hmac.new(token.encode(), s2.encode(), hashlib.sha1).hexdigest().upper()
            return f'{s1}.{s2}.{secret}'
        except Exception as e:
            print(f"Oceanus认证失败: {str(e)}")
            return ""

    def get_token_by_sso(self, mis: str, ssoid: str, online: bool = True) -> str:
        """
        获取SSO生成的STS鉴权
        """
        # 实际实现需要对接STS SDK
        # 这里是模拟实现
        try:
            # 模拟STS.init和STS.sign的实现
            # 实际应该使用STS SDK
            print(mis + " " + ssoid)
            sts_sign_service = STSServiceFactory.create(STSRequest(self.SERVER_APPKEY,
                                                                   "SSOTicket",
                                                                   "online" if online else None))
            sign_param = SignParam(
                client_id=mis,
                aud="",
                scp="",
                roles="",
                ext_map={'ssoid': ssoid}
            )
            auth_token = sts_sign_service.sign(sign_param)

            return auth_token.at
        except Exception as e:
            print(f"SSO认证失败: {str(e)}")
            return ""

    def get_token_by_iam(self, iamname: str, iampwd: str, online: bool = True) -> str:
        """
        获取IAM生成的STS鉴权
        """
        # 实际实现需要对接STS SDK
        # 这里是模拟实现
        try:
            # 模拟STS.init和STS.sign的实现
            # 实际应该使用STS SDK
            sts_sign_service = STSServiceFactory.create(STSRequest(self.SERVER_APPKEY,  # Token的Appkey
                                                                   "IAMTicket",  # Token的KeyName
                                                                   AuthAction.SIGN,
                                                                   "online" if online else None))  # 指定STSService模式，这里是签发模式
            sign_param = SignParam(client_id=iamname,
                                   aud="",
                                   scp="",
                                   roles="",
                                   ext_map={'iampwd': iampwd})  # 扩展字段以map形式存放，仅支持str，str
            auth_token = sts_sign_service.sign(sign_param)
            return auth_token.at
        except Exception as e:
            print(f"IAM认证失败: {str(e)}")
            return ""

    def get_token(self, options: Optional[Dict[str, str]] = None) -> Dict[str, str]:
        """
        获取认证Token
        """
        token = ""
        mis = options.get("mis") if options else None
        ssoid = options.get("ssoid") if options else None

        if mis and ssoid:
            token = self.get_token_by_sso(mis, ssoid, True)
        else:
            # 从kms获取
            password_result = self.kms_service.get_key(self.CLIENT_APPKEY, "git_yunzhuan_password")
            if password_result.get("success"):
                password = password_result.get("data")
                token = self.get_token_by_iam('git_yunzhuan', password, True)
        oceanus_token = self.oceanus_auth()

        return {
            "STS-TOKEN": token,
            "oceanus-remote-appkey": self.SERVER_APPKEY,
            "oceanus-auth": oceanus_token,
            "Cache-Control": "max-age=300",
        }

    def get_pr_info(self, url: str, params: Dict[str, Any], options: Optional[Dict[str, str]] = None) -> Dict[str, Any]:
        """
        获取PR信息
        """
        header_info = self.get_token(options)
        url_with_params = f"{self.DOMAIN}{url}"

        try:
            response = requests.get(
                url_with_params,
                headers=header_info,
                params=params
            )
            response.raise_for_status()
            return response.json()
        except Exception as e:
            raise e

    def set_comment(self, url: str, comments: str, options: Optional[Dict[str, str]] = None) -> Dict[str, Any]:
        """
        设置评论
        """
        header_info = self.get_token(options)
        url_with_params = f"{self.DOMAIN}{url}"

        try:
            response = requests.post(
                url_with_params,
                headers=header_info,
                json={"text": comments}
            )
            response.raise_for_status()
            return response.json()
        except Exception as e:
            raise e

    def init_ssh(self) -> Dict[str, Any]:
        """
        初始化SSH
        """
        try:
            public_key_result = self.kms_service.get_key(self.CLIENT_APPKEY, "code_id_ed25519_pub")
            private_key_result = self.kms_service.get_key(self.CLIENT_APPKEY, "code_id_ed25519")

            if not public_key_result.get("success") or not private_key_result.get("success"):
                raise Exception("获取SSH密钥失败")

            public_key = public_key_result.get("data")
            private_key = private_key_result.get("data")

            # 执行初始化SSH的脚本
            command = f'bash init_ssh.sh "{private_key}" "{public_key}"'
            process = subprocess.Popen(
                command,
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            stdout, stderr = process.communicate()

            return {
                "stdout": stdout,
                "stderr": stderr
            }
        except Exception as e:
            print(f"SSH初始化失败: {str(e)}")
            return {"error": str(e)}

    def create_pr(self, url: str, params: Dict[str, Any], options: Optional[Dict[str, str]] = None) -> Union[str, int]:
        """
        创建PR
        """
        header_info = self.get_token(options)
        print(f"header_info: {header_info}")
        url_with_params = f"{self.DOMAIN}{url}"
        print(f"{url_with_params}, createPr urlWithParams")
        try:
            response = requests.post(
                url_with_params,
                headers=header_info,
                json=params
            )
            print(f"response: {response.json()}")
            response.raise_for_status()
            return response.json().get("id")
        except requests.exceptions.HTTPError as e:
            if e.response.status_code == 409:
                errors = e.response.json().get("errors", [])
                if errors and errors[0].get("existingPullRequest", {}).get("id"):
                    return errors[0]["existingPullRequest"]["id"]
                raise Exception(f"PR创建失败: {errors[0].get('message')}")
            raise e
        except Exception as e:
            raise e

    def fetch_pr_info(self, project: str, repo: str, pr_number: str) -> Dict[str, str]:
        """
        获取PR信息
        """
        # 这里应该调用devtools_app.py中的pr_info_handler函数
        # 但由于我们没有完整的上下文，这里模拟实现
        try:
            from api.apps.devtools_app import pr_info_handler
            from flask import Request

            # 创建一个模拟的请求对象
            class MockRequest:
                def __init__(self, args):
                    self.args = args

                def get_json(self):
                    return {}

            # 模拟Flask的request对象
            request = MockRequest({
                "project": project,
                "repo": repo,
                "id": pr_number
            })

            # 调用pr_info_handler函数
            # 注意：这里假设pr_info_handler函数能够接受request参数
            # 实际实现可能需要调整
            pr_info_response = pr_info_handler(request)

            # 从响应中提取信息
            pr_info = pr_info_response.get("prInfo", {})
            from_branch = pr_info.get("fromRef", {}).get("displayId")
            to_branch = pr_info.get("toRef", {}).get("displayId")

            return {
                "project": project,
                "fromBranch": from_branch,
                "toBranch": to_branch
            }
        except Exception as e:
            print(f"获取PR信息失败: {str(e)}")
            # 返回一个模拟的结果
            return {
                "project": project,
                "fromBranch": "feature/mock",
                "toBranch": "master"
            }
