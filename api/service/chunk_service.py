"""
重构后的代码分块服务
"""
import json
import os
from typing import List, Dict, Any, Optional

from consts.chunk_consts import (
    CONFIG_SUFFIXES, SUPPORTED_CODE_SUFFIXES, DEFAULT_CACHE_TTL,
    DEFAULT_MAX_LINES, DEFAULT_MAX_DEP_DEPTH, DEFAULT_CONTEXT_LINES
)
from utils.chunk_utils import (
    is_import_only_block, detect_source_roots, split_diff_by_file,
    extract_new_code_from_diff, split_diff_by_continuous_lines,
    find_best_chunk_for_comment, find_unique_key_for_dep
)
from core.cache.chunk_cache import ChunkCache
from core.parsers import CodeParserFactory
from core.analyzers.dependency_analyzer import DependencyAnalyzer
from core.analyzers.cross_module_analyzer import CrossModuleAnalyzer


class ChunkService:
    """重构后的代码分块服务"""
    
    def __init__(self, git_service, project: Optional[str], repo: Optional[str], branch: Optional[str] = None):
        self.git_service = git_service
        self.project = project
        self.repo = repo
        self.branch = branch or "master"
        
        # 初始化组件
        self.cache = ChunkCache()
        self.func_index = {}
        self.dependency_analyzer = None
        self.cross_module_analyzer = None

        # 初始化跨模块分析器
        try:
            # 假设项目根目录可以从git_service获取
            project_root = getattr(git_service, 'project_root', '/tmp/project')
            self.cross_module_analyzer = CrossModuleAnalyzer(project_root)
            print(f"[ChunkService] 跨模块分析器初始化成功")
        except Exception as e:
            print(f"[ChunkService] 跨模块分析器初始化失败: {e}")
            self.cross_module_analyzer = None

        # 构建函数索引
        self.func_index = self.build_project_func_index()
        self.dependency_analyzer = DependencyAnalyzer(
            self.func_index,
            cross_module_analyzer=self.cross_module_analyzer
        )
    
    def get_core_code_files(self, suffixes: List[str]) -> List[str]:
        """获取核心代码文件列表"""
        return self.git_service.list_repo_files(self.project, self.repo, branch=self.branch, suffixes=suffixes)
    
    def chunk_code_file(self, file_path: str) -> List[Dict[str, Any]]:
        """对单个代码文件进行分块"""
        content = self.git_service.read_file(self.project, self.repo, file_path)
        if not content or not content.strip():
            return []
        
        # 获取对应的解析器
        parser = CodeParserFactory.get_parser(file_path)
        if not parser:
            return []
        
        # 解析代码块
        chunks = parser.parse_chunks(content, file_path)
        
        # 标准化依赖关系
        self._standardize_dependencies(chunks)
        
        # 过滤纯导入依赖块
        return [c for c in chunks if not is_import_only_block(c['content'])]
    
    def chunk_all_files(self, suffixes: List[str], resolve_code: bool = True) -> List[Dict[str, Any]]:
        """对所有文件进行分块"""
        try:
            code_files = self.get_core_code_files(suffixes)
            all_chunks = []
            
            for file_path in code_files:
                parser = CodeParserFactory.get_parser(file_path)
                if not parser:
                    continue
                
                content = self.git_service.read_file(self.project, self.repo, file_path)
                if not content:
                    continue
                
                chunks = parser.parse_chunks(content, file_path, resolve_code=resolve_code)
                if chunks:
                    all_chunks.extend(chunks)
            
            return all_chunks
        except Exception as e:
            print(f"chunk_all_files 异常: {e}")
            return []
    
    def build_project_func_index(self, suffixes=None, use_cache=True, cache_ttl=DEFAULT_CACHE_TTL):
        """构建项目函数索引"""
        if not suffixes:
            suffixes = SUPPORTED_CODE_SUFFIXES
        
        # 尝试从缓存加载
        cache_key = self.cache.get_cache_key(self.project, self.repo, self.branch, suffixes)
        if use_cache:
            cached_data = self.cache.load_cache(cache_key, cache_ttl)
            if cached_data:
                self.func_index = cached_data
                return self.func_index
        
        print(f"开始构建函数索引，支持的文件类型: {suffixes}")
        
        # 获取所有文件并检测源码根目录
        all_files = self.git_service.list_repo_files(self.project, self.repo, branch=self.branch, suffixes=suffixes)
        print(f"找到 {len(all_files)} 个匹配的文件")
        
        roots = detect_source_roots(all_files)
        print(f"检测到源码根目录: {roots}")
        
        # 过滤源码文件
        src_files = self._filter_source_files(all_files, roots)
        print(f"最终使用 {len(src_files)} 个源码文件构建索引")
        
        # 分块处理
        all_chunks = self._process_source_files(src_files)
        print(f"获取到 {len(all_chunks)} 个分块")
        
        # 构建函数索引
        func_index = self._build_func_index_from_chunks(all_chunks)
        
        # 补全跨文件依赖
        self._complete_cross_file_dependencies(func_index)
        
        # 缓存结果
        if use_cache:
            self.cache.save_cache(cache_key, func_index)
        
        self.func_index = func_index
        print(f"函数索引构建完成，共 {len(func_index)} 个唯一名（含多重索引）")
        
        return func_index
    
    def chunk_diff_file(self, diff_content: Optional[str], from_branch: Optional[str],
                        to_branch: Optional[str]) -> List[Dict[str, Any]]:
        """对diff内容进行切块"""
        # 参数校验
        if not diff_content and (not from_branch or not to_branch):
            raise ValueError("Either diff_content or from_branch and to_branch must be provided")
        
        if not diff_content:
            diff_content = self.git_service.get_diff(self.project, self.repo, from_branch, to_branch)
        
        print(f"开始处理diff内容，长度: {len(diff_content)}")
        file_blocks = split_diff_by_file(diff_content)
        print(f"分割出 {len(file_blocks)} 个文件块")
        
        all_chunks = []
        for file_path, diff_lines in file_blocks:
            # 跳过配置文件
            if file_path.endswith(CONFIG_SUFFIXES):
                print(f"配置文件 {file_path} 跳过CR分块")
                continue
            
            # 检查文件类型
            parser = CodeParserFactory.get_parser(file_path)
            if not parser:
                print(f"文件 {file_path} 不是支持的语言类型，跳过")
                continue
            
            # 处理diff文件
            chunks = self._process_diff_file(file_path, diff_lines, parser)
            all_chunks.extend(chunks)
        
        print(f"总共生成 {len(all_chunks)} 个块")
        return all_chunks
    
    def enrich_diff_segments_with_context(self, diff_content: str, max_dep_depth: int = DEFAULT_MAX_DEP_DEPTH) -> List[Dict]:
        """基于diff内容，返回每个片段的所有覆盖代码块、递归正向依赖、反向依赖"""
        # 确保函数索引已构建
        if not self.func_index:
            print("函数索引为空，开始构建...")
            self.func_index = self.build_project_func_index()
        
        print(f"函数索引构建完成，共 {len(self.func_index)} 个函数")
        
        # 分析diff文件
        file_blocks = split_diff_by_file(diff_content)
        print(f"分割出 {len(file_blocks)} 个文件块")
        
        # 收集所有覆盖的代码块
        all_covered_chunks, diff_segment_infos = self._collect_covered_chunks(file_blocks)
        
        # 去重并生成CR任务
        unique_chunks = self._deduplicate_chunks(all_covered_chunks)
        segments = self._generate_cr_tasks(unique_chunks, diff_segment_infos, max_dep_depth)
        
        # 处理未命中任何块的diff
        segments.extend(self._handle_unmatched_diffs(diff_segment_infos))
        
        return segments
    
    def build_qa_pairs_with_merged_comments(self, comments_json: Dict, chunks: List[Dict], 
                                          diff_file_root: str = None) -> List[Dict]:
        """合并同一chunk下的多条评论为一个问答对"""
        chunk_comment_map = {}
        chunk_obj_map = {}
        unmatched_comments = []
        
        # 处理评论
        for file_comments in comments_json['result']['values']:
            file_path = file_comments['path']
            for comment_item in file_comments['comments']:
                anchor = comment_item['commentAnchor']
                start_line = anchor['lineRange']['start']['right']['line']
                end_line = anchor['lineRange']['end']['right']['line']
                comment_text = comment_item['comment']['text']
                author = comment_item['comment']['author']['displayName']
                
                chunk = find_best_chunk_for_comment(chunks, file_path, start_line, end_line)
                if chunk:
                    chunk_id = f"{chunk['file']}:{chunk['start_line']}-{chunk['end_line']}"
                    if chunk_id not in chunk_comment_map:
                        chunk_comment_map[chunk_id] = []
                        chunk_obj_map[chunk_id] = chunk
                    chunk_comment_map[chunk_id].append({
                        'text': comment_text,
                        'author': author
                    })
                else:
                    unmatched_comments.append({
                        'file_path': file_path,
                        'start_line': start_line,
                        'end_line': end_line,
                        'text': comment_text,
                        'author': author
                    })
        
        # 生成问答对
        qa_pairs = []
        
        # 处理匹配到chunk的评论
        for chunk_id, comments in chunk_comment_map.items():
            chunk = chunk_obj_map[chunk_id]
            question = (
                f"文件：{chunk['file']}，第{chunk['start_line']}-{chunk['end_line']}行代码（{chunk['type']}）:\n"
                f"{chunk['content']}"
            )
            answer = ""
            for idx, c in enumerate(comments, 1):
                answer += f"{idx}. {c['author']}：{c['text']}\n"
            qa_pairs.append({'question': question, 'answer': answer.strip()})
        
        # 处理未匹配chunk的评论
        for uc in unmatched_comments:
            code_snippet = self._get_code_snippet_for_comment(uc, diff_file_root)
            question = f"文件：{uc['file_path']}，第{uc['start_line']}-{uc['end_line']}行代码：\n{code_snippet}"
            answer = f"{uc['author']}：{uc['text']}"
            qa_pairs.append({'question': question, 'answer': answer})
        
        return qa_pairs
    
    def split_code_diff_into_segments(self, diff_content: str, max_lines: int = DEFAULT_MAX_LINES) -> List[Dict]:
        """按文件和最大行数将diff内容切分为多个片段"""
        file_blocks = split_diff_by_file(diff_content)
        segments = []
        
        for file_path, diff_lines in file_blocks:
            code = extract_new_code_from_diff(diff_lines)
            if not code.strip():
                continue
            
            code_lines = code.splitlines()
            for i in range(0, len(code_lines), max_lines):
                segment_lines = code_lines[i:i + max_lines]
                segment_content = '\n'.join(segment_lines)
                code_position = f"{file_path}:{i + 1}-{i + len(segment_lines)}"
                segments.append({
                    "codePosition": code_position,
                    "content": segment_content
                })
        
        return segments
    
    @staticmethod
    def save_chunks_to_json(chunks: List[Dict], output_path: str):
        """将分块结果以json格式存入文件"""
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(chunks, f, ensure_ascii=False, indent=2)
    
    # ==================== 私有方法 ====================
    
    def _standardize_dependencies(self, chunks: List[Dict]):
        """标准化依赖关系为唯一名"""
        for chunk in chunks:
            for dep_type in ['upstream', 'downstream']:
                if dep_type in chunk:
                    new_deps = []
                    for dep in chunk[dep_type]:
                        unique_keys = find_unique_key_for_dep(self.func_index or {}, dep)
                        if unique_keys:
                            new_deps.extend(unique_keys)
                        else:
                            new_deps.append(dep)
                    chunk[dep_type] = new_deps
    
    def _filter_source_files(self, all_files: List[str], roots: List[str]) -> List[str]:
        """过滤源码文件"""
        src_files = []
        for f in all_files:
            for root in roots:
                if f.startswith(root + os.sep) or f == root:
                    src_files.append(f)
                    break
        
        # 如果过滤后文件太少，使用所有文件
        if len(src_files) < len(all_files) * 0.5:
            print(f"警告：根目录过滤掉了太多文件，使用所有文件进行索引构建")
            src_files = all_files
        
        return src_files
    
    def _process_source_files(self, src_files: List[str]) -> List[Dict]:
        """处理源码文件，进行分块"""
        all_chunks = []
        for file_path in src_files:
            try:
                parser = CodeParserFactory.get_parser(file_path)
                if not parser:
                    continue
                
                file_content = self.git_service.read_file(self.project, self.repo, file_path)
                if not file_content:
                    continue
                
                chunks = parser.parse_chunks(file_content, file_path, resolve_code=False)
                if chunks:
                    all_chunks.extend(chunks)
            except Exception as e:
                print(f"处理文件 {file_path} 时出错: {e}")
                continue
        
        return all_chunks
    
    def _build_func_index_from_chunks(self, all_chunks: List[Dict]) -> Dict:
        """从分块构建函数索引"""
        func_index = {}
        
        def add_to_index(index, key, chunk):
            if key in index:
                if isinstance(index[key], list):
                    index[key].append(chunk)
                else:
                    index[key] = [index[key], chunk]
            else:
                index[key] = chunk
        
        for chunk in all_chunks:
            if chunk['type'] in ('function', 'method', 'constructor', 'arrow', 'lambda'):
                file_path = chunk['file']
                name = chunk['name']
                unique_name = f"{file_path}:{name}"
                simple_name = f"{file_path}:{name.split('.')[-1]}"
                global_name = f"global:{name}"

                add_to_index(func_index, unique_name, chunk)
                add_to_index(func_index, global_name, chunk)
                add_to_index(func_index, simple_name, chunk)

                # Java特殊处理：为类方法添加更多索引键
                if "." in name and file_path.endswith('.java'):
                    class_name, method_name = name.rsplit(".", 1)

                    # 添加方法名索引
                    method_key = f"global:{method_name}"
                    add_to_index(func_index, method_key, chunk)

                    # 添加类名.方法名索引（不带文件路径）
                    class_method_key = f"global:{class_name}.{method_name}"
                    add_to_index(func_index, class_method_key, chunk)

                    # 添加简化的类名.方法名索引
                    simple_class_name = class_name.split(".")[-1]  # 取最后一部分类名
                    simple_class_method_key = f"global:{simple_class_name}.{method_name}"
                    add_to_index(func_index, simple_class_method_key, chunk)
        
        return func_index
    
    def _complete_cross_file_dependencies(self, func_index: Dict):
        """补全跨文件依赖"""
        print("开始补全跨文件依赖...")
        for key, chunk in func_index.items():
            chunk_list = chunk if isinstance(chunk, list) else [chunk]
            for c in chunk_list:
                if not isinstance(c, dict):
                    continue
                
                new_upstream = []
                for dep in c.get('upstream', []):
                    unique_keys = find_unique_key_for_dep(func_index, dep)
                    if unique_keys:
                        for unique_key in unique_keys:
                            target = func_index[unique_key]
                            target_list = target if isinstance(target, list) else [target]
                            for t in target_list:
                                if not isinstance(t, dict):
                                    continue
                                if 'downstream' not in t:
                                    t['downstream'] = []
                                if key not in t['downstream']:
                                    t['downstream'].append(key)
                        new_upstream.extend(unique_keys)
                    else:
                        new_upstream.append(dep)
                c['upstream'] = new_upstream
        print("跨文件依赖补全完成。")
    
    def _process_diff_file(self, file_path: str, diff_lines: List[str], parser) -> List[Dict]:
        """处理单个diff文件"""
        print(f"处理文件: {file_path}")
        
        code = extract_new_code_from_diff(diff_lines)
        if not code.strip():
            print(f"文件 {file_path} 提取的代码为空，跳过")
            return []
        
        print(f"提取的代码长度: {len(code)}")
        
        # 尝试获取完整文件内容进行分析
        try:
            full_content = self.git_service.read_file(self.project, self.repo, file_path)
            if full_content and code.strip() in full_content:
                print(f"找到完整文件内容，使用完整文件进行分析")
                chunks = parser.parse_chunks(full_content, file_path)
                # 标记包含diff代码的块
                for c in chunks:
                    c['from_diff'] = code.strip() in c['content']
            else:
                # 使用diff代码进行分析
                chunks = self._analyze_diff_with_context(file_path, code, full_content, parser)
        except Exception as e:
            print(f"读取完整文件失败: {str(e)}，使用diff代码进行分析")
            chunks = parser.parse_chunks(code, file_path)
            for c in chunks:
                c['from_diff'] = True
        
        # 过滤纯导入依赖块
        filtered_chunks = [c for c in chunks if not is_import_only_block(c['content'])]
        
        return filtered_chunks
    
    def _analyze_diff_with_context(self, file_path: str, code: str, full_content: str, parser) -> List[Dict]:
        """使用上下文分析diff代码"""
        if not full_content:
            chunks = parser.parse_chunks(code, file_path)
            for c in chunks:
                c['from_diff'] = True
            return chunks
        
        # 尝试在完整文件中找到diff位置并补全上下文
        full_file_lines = full_content.splitlines()
        code_lines = [l for l in code.splitlines() if l.strip()]
        
        diff_start_idx = None
        for i in range(len(full_file_lines)):
            if full_file_lines[i:i+len(code_lines)] == code_lines:
                diff_start_idx = i
                break
        
        if diff_start_idx is not None:
            # 自动补全上下文
            ctx_start = max(0, diff_start_idx - DEFAULT_CONTEXT_LINES)
            ctx_end = min(len(full_file_lines), diff_start_idx + len(code_lines) + DEFAULT_CONTEXT_LINES)
            context_code = '\n'.join(full_file_lines[ctx_start:ctx_end])
            chunks = parser.parse_chunks(context_code, file_path)
        else:
            chunks = parser.parse_chunks(code, file_path)
        
        for c in chunks:
            c['from_diff'] = True
        
        return chunks
    
    def _collect_covered_chunks(self, file_blocks: List[tuple]) -> tuple:
        """收集所有被diff覆盖的代码块"""
        all_covered_chunks = []
        diff_segment_infos = []
        
        for file_path, diff_lines in file_blocks:
            if file_path.endswith(CONFIG_SUFFIXES):
                print(f"配置文件 {file_path} 跳过上下文补全")
                continue
            
            print(f"处理文件: {file_path}")
            code = extract_new_code_from_diff(diff_lines)
            if not code.strip():
                print(f"文件 {file_path} 提取的代码为空，跳过")
                continue
            
            # 获取完整文件内容进行分块
            all_chunks = self._get_file_chunks_for_context(file_path)
            print(f"文件 {file_path} 分块结果: {len(all_chunks)} 个块")
            
            # diff切块
            file_diff_segments = split_diff_by_continuous_lines(diff_lines)
            print(f"文件 {file_path} diff切分为 {len(file_diff_segments)} 个片段")
            
            # 找到覆盖的代码块
            for seg in file_diff_segments:
                covering_chunks = self._find_covering_chunks(seg, all_chunks)
                diff_segment_infos.append({
                    'file_path': file_path,
                    'seg_start': seg['start_line'],
                    'seg_end': seg['end_line'],
                    'seg_code': '\n'.join([l for l in seg['lines'] if l.strip()]),
                    'covering_chunks': covering_chunks
                })
                all_covered_chunks.extend(covering_chunks)
        
        return all_covered_chunks, diff_segment_infos
    
    def _get_file_chunks_for_context(self, file_path: str) -> List[Dict]:
        """获取文件的代码块用于上下文分析"""
        try:
            full_file_content = self.git_service.read_file(self.project, self.repo, file_path)
            if full_file_content:
                parser = CodeParserFactory.get_parser(file_path)
                if parser:
                    return parser.parse_chunks(full_file_content, file_path, resolve_code=True)
        except Exception as e:
            print(f"读取或分析文件 {file_path} 失败: {str(e)}")
        
        return self.chunk_code_file(file_path)
    
    def _find_covering_chunks(self, seg: Dict, all_chunks: List[Dict]) -> List[Dict]:
        """找到覆盖指定片段的代码块"""
        covering_chunks = []
        seg_start = seg['start_line']
        seg_end = seg['end_line']
        
        for chunk in all_chunks:
            if 'start_line' in chunk and 'end_line' in chunk:
                if not (seg_end < chunk['start_line'] or seg_start > chunk['end_line']):
                    covering_chunks.append(chunk)
        
        return covering_chunks
    
    def _deduplicate_chunks(self, all_covered_chunks: List[Dict]) -> List[Dict]:
        """去重代码块"""
        unique_chunk_keys = set()
        unique_chunks = []
        
        for c in all_covered_chunks:
            sig = (c['file'], c['start_line'], c['end_line'], c['content'])
            if sig not in unique_chunk_keys:
                unique_chunks.append(c)
                unique_chunk_keys.add(sig)
        
        return unique_chunks
    
    def _generate_cr_tasks(self, unique_chunks: List[Dict], diff_segment_infos: List[Dict], 
                          max_dep_depth: int) -> List[Dict]:
        """生成CR任务"""
        segments = []
        
        for chunk in unique_chunks:
            # 找到相关的diff片段
            related_diffs = []
            for info in diff_segment_infos:
                if chunk in info['covering_chunks']:
                    related_diffs.append(info['seg_code'])
            
            merged_diff_content = '\n'.join(related_diffs)
            
            # 确定完整代码
            if chunk['type'] in ('function', 'method', 'constructor', 'arrow', 'lambda'):
                full_code = chunk['content']
            else:
                full_code = merged_diff_content
            
            # 依赖补全
            if self.dependency_analyzer:
                merged_upstream = self.dependency_analyzer.collect_multi_level_deps(
                    chunk, 'upstream', max_dep_depth)
                merged_downstream = self.dependency_analyzer.collect_multi_level_deps(
                    chunk, 'downstream', max_dep_depth)
            else:
                merged_upstream = {}
                merged_downstream = {}
            
            code_position = f"{chunk['file']}:{chunk['start_line']}-{chunk['end_line']}"
            print(f"生成CR任务: {code_position}，diff片段数: {len(related_diffs)}，"
                  f"upstream: {len(merged_upstream)}，downstream: {len(merged_downstream)}")
            
            segments.append({
                "codePosition": code_position,
                "diff_content": merged_diff_content,
                "full_code": full_code,
                "upstream_code": merged_upstream,
                "downstream_code": merged_downstream
            })
        
        return segments
    
    def _handle_unmatched_diffs(self, diff_segment_infos: List[Dict]) -> List[Dict]:
        """处理未匹配任何块的diff"""
        segments = []
        
        for info in diff_segment_infos:
            if not info['covering_chunks']:
                code_position = f"{info['file_path']}:{info['seg_start']}-{info['seg_end']}"
                print(f"diff未命中任何块，单独生成任务: {code_position}")
                segments.append({
                    "codePosition": code_position,
                    "diff_content": info['seg_code'],
                    "full_code": info['seg_code'],
                    "upstream_code": {},
                    "downstream_code": {}
                })
        
        return segments
    
    def _get_code_snippet_for_comment(self, comment: Dict, diff_file_root: str = None) -> str:
        """获取评论对应的代码片段"""
        if diff_file_root:
            try:
                with open(f"{diff_file_root}/{comment['file_path']}", 'r') as f:
                    lines = f.readlines()
                return ''.join(lines[comment['start_line'] - 1:comment['end_line']])
            except Exception:
                pass
        
        return f"(未能匹配chunk，建议人工校验，锚点行：{comment['start_line']}-{comment['end_line']})"