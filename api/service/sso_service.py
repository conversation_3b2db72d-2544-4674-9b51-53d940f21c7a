# SSO服务，负责用户认证和信息获取
# 对接公司SSO服务和组织架构API
import requests
from typing import Any, Dict, Optional


class SsoService:
    """
    SSO服务，负责用户认证和信息获取
    """
    
    def __init__(self):
        """初始化SSO服务"""
        self.org_domain = 'http://org2.gateway.it.test.sankuai.com'
    
    def get_user_info(self, request_context: Any) -> Dict[str, Any]:
        """
        获取用户信息
        
        Args:
            request_context: 请求上下文，包含SDK和用户信息
            
        Returns:
            用户信息结果
        """
        try:
            # 在实际环境中，应该从request_context.sdk.sso.userInfo获取用户信息
            # 这里简单模拟
            user_info = getattr(request_context, 'user_info', {})
            if not user_info and hasattr(request_context, 'sdk') and hasattr(request_context.sdk, 'sso'):
                user_info = getattr(request_context.sdk.sso, 'userInfo', {})
            
            return {'success': True, 'data': user_info}
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def get_access_token(self, request_context: Any) -> Dict[str, Any]:
        """
        获取访问令牌
        
        Args:
            request_context: 请求上下文，包含SDK和访问令牌
            
        Returns:
            访问令牌结果
        """
        try:
            # 在实际环境中，应该从request_context.sdk.sso.accessToken获取访问令牌
            # 这里简单模拟
            access_token = None
            if hasattr(request_context, 'sdk') and hasattr(request_context.sdk, 'sso'):
                access_token = getattr(request_context.sdk.sso, 'accessToken', None)
            
            return {'success': True, 'data': access_token}
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def get_emp_id(self, request_headers: Dict[str, str]) -> str:
        """
        获取员工ID
        
        Args:
            request_headers: 请求头
            
        Returns:
            员工ID
        """
        try:
            mis_id = request_headers.get('mis-id')
            emp_id = self.get_emp_id_by_mis_id(mis_id)
            return emp_id
        except Exception as e:
            print(f"[yunzhuan:sso] 获取 empId 失败: {str(e)}")
            return ''
    
    def get_emp_id_by_mis_id(self, mis_id: Optional[str] = None) -> str:
        """
        根据misId获取员工ID
        
        Args:
            mis_id: 员工MIS ID
            
        Returns:
            员工ID
        """
        try:
            if not mis_id:
                raise ValueError("misId is empty")
            
            # 构建请求URL
            url = f"{self.org_domain}/api/org2/emps?mises={mis_id}"
            
            # 发送请求
            response = requests.get(url)
            response.raise_for_status()  # 如果响应状态码不是200，抛出异常
            
            # 解析响应
            data = response.json().get('data', {})
            return data.get('empId', '')
            
        except Exception as e:
            raise ValueError(f"获取员工ID失败: {str(e)}")