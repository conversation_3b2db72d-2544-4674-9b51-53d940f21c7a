import asyncio
import json
from typing import Any, Dict, Optional, List, Union

from langchain.output_parsers import StructuredOutputParser, OutputFixingParser
from langchain.prompts import ChatPromptTemplate
from langchain.schema import SystemMessage, HumanMessage
from langchain_core.output_parsers.json import JsonOutputParser
from pydantic import Field

from utils import singleton
from .llm_service import LLMService, OpenAIConfig
from api.service.chunk_service import ChunkService
from core.chains.cr_chain import build_cr_chain
from core.chains.fast_cr_chain import build_fast_cr_chain
from core.chains.async_fast_cr_chain import build_async_fast_cr_chain
from core.chains.intelligent_cr_chain import build_intelligent_cr_chain

# 最大diff行数常量
MAX_DIFF_LINES = 400


@singleton
class CRService:
    """
    代码审查主服务，负责代码diff分析、LLM调用、结果汇总等。
    """

    def __init__(self, horn_service, kms_service, daxiang_service, git_service, dm_service):
        self.horn_service = horn_service
        self.kms_service = kms_service
        self.dx_service = daxiang_service
        self.git_service = git_service
        self.devmind_service = dm_service
        self.llm_service = None
        self.api_key = None
        self.config = None

        # CR链配置
        self.cr_mode = "fast"  # 默认使用快速模式: "fast" | "async_fast" | "standard" | "deep"
        self.enable_performance_monitoring = True

        self.init_inf()

    def init_inf(self):
        """初始化LLM服务和API Key，拉取配置和密钥"""
        llm_horn_config = self.horn_service.fetch_config("CR_CHECKER").get('crLlmConfig', {})
        llm_app_key = self.kms_service.get_key("com.sankuai.yunzhuan.devhelper", "modal_app_id")
        self.api_key = llm_app_key.get('data', 'mock_api_key')
        print(f"当前LLM应用ID为: {self.api_key}")

        # 使用配置初始化LLM服务
        self.llm_service = LLMService(OpenAIConfig(
            api_key=self.api_key,
            **(llm_horn_config.get('crLlmConfig', {}) or {})
        ))
        self.config = llm_horn_config or {}

        # 从配置中读取CR模式
        cr_config = self.config.get('crConfig', {})
        self.cr_mode = cr_config.get('mode', 'fast')
        self.enable_performance_monitoring = cr_config.get('enablePerformanceMonitoring', True)

        print(f"[CRService] CR配置: 模式={self.cr_mode}, 性能监控={self.enable_performance_monitoring}")

    def set_cr_mode(self, mode: str):
        """
        动态设置CR模式
        Args:
            mode: CR模式 ("fast" | "standard" | "deep" | "async_fast")
        """
        valid_modes = ["fast", "standard", "deep", "async_fast"]
        if mode not in valid_modes:
            raise ValueError(f"无效的CR模式: {mode}，支持的模式: {valid_modes}")

        self.cr_mode = mode
        print(f"[CRService] CR模式已切换为: {mode}")

        # 输出模式说明
        mode_descriptions = {
            "fast": "纯LLM能力，不检索知识库，快速响应",
            "standard": "LLM智能决策是否检索知识库，平衡思考链路",
            "deep": "深度思考链路 + 自检复审，全面分析",
            "async_fast": "异步快速模式，兼容旧版本"
        }
        print(f"[CRService] 模式说明: {mode_descriptions.get(mode, '未知模式')}")

    def get_cr_mode(self) -> str:
        """获取当前CR模式"""
        return self.cr_mode

    def enable_performance_monitor(self, enable: bool = True):
        """启用/禁用性能监控"""
        self.enable_performance_monitoring = enable
        print(f"[CRService] 性能监控已{'启用' if enable else '禁用'}")

    def process_cr_request(self, ctx: Any, code_diff: str, params: Optional[Dict[str, Any]] = None):
        """
        处理代码评审请求，串联diff、LLM、配置等业务

        Args:
            ctx: 上下文信息
            code_diff: Git diff格式的代码差异字符串
            params: 可选参数，包含PR链接和任务Git信息等

        Returns:
            代码审查结果
        """
        try:
            project = params.get('project') or getattr(ctx.params, 'project', None)
            repo = params.get('repo') or getattr(ctx.params, 'repo', None)
            from_branch = params.get('fromBranch') or getattr(ctx.params, 'fromBranch', None)
            to_branch = params.get('toBranch') or getattr(ctx.params, 'toBranch', None)
            print(f"开始CR: {project}, {repo}, {from_branch}, {to_branch}")

            if not code_diff:
                raise ValueError('code_diff不能为空')

            return self.process_code_diff(code_diff, params, async_mode=False)
        except Exception as error:
            print(f"处理代码评审请求失败: {error}")
            raise error

    def split_code_diff_into_segments(self, code_diff: str, options: Optional[Dict[str, Any]] = None) -> List[
        Dict[str, Any]]:
        """
        使用ChunkService对diff内容进行分块，自动补全正反向依赖和依赖代码内容。
        Args:
            code_diff: Git diff格式的代码差异字符串
            options: 可选参数，包含project/repo/fromBranch/toBranch等
        Returns:
            分割后的任务列表，每个任务包含索引和代码片段列表
        """
        # 从options获取git信息
        project = options.get('project') if options else None
        repo = options.get('repo') if options else None
        from_branch = options.get('fromBranch') if options else None
        to_branch = options.get('toBranch') if options else None
        # 实例化ChunkService
        chunk_service = ChunkService(self.git_service, project, repo, from_branch)
        # 用chunk_diff_file分块
        chunks = chunk_service.chunk_diff_file(code_diff, from_branch, to_branch)
        # 组装为原有结构
        tasks = []
        for idx, chunk in enumerate(chunks):
            tasks.append({
                "taskIndex": idx,
                "taskList": [{
                    "codePosition": f"{chunk['file']}:{chunk['start_line']}-{chunk['end_line']}",
                    "codePositionArray": [chunk['start_line'], 0, chunk['end_line'], 0],  # 添加数组格式
                    "content": chunk['content'],
                    "upstream": chunk.get('upstream', []),
                    "downstream": chunk.get('downstream', []),
                    "upstream_code": chunk.get('upstream_code', {}),
                    "downstream_code": chunk.get('downstream_code', {})
                }]
            })
        return tasks

    def process_with_langchain(
            self,
            code_diffs: Union[Dict, List[Dict], Dict[str, List[Dict]]],
            options: Optional[Dict[str, Any]] = None,
            chain_options: Optional[Dict[str, Any]] = None,
            output_mode: str = "detailed"
    ):
        """
        同步入口，自动调用异步批量处理并返回结果
        """
        return asyncio.run(self.async_process_with_langchain(code_diffs, options, chain_options, output_mode))

    async def async_process_with_langchain(
            self,
            code_diffs: Union[Dict, List[Dict], Dict[str, List[Dict]]],
            options: Optional[Dict[str, Any]] = None,
            chain_options: Optional[Dict[str, Any]] = None,
            output_mode: str = "detailed"
    ):
        """
        支持多入口、批量并发、链路配置和多种输出结构的异步处理方法
        """
        print(f"[async_process_with_langchain] options={options}, chain_options={chain_options}")
        tasks = self._normalize_code_diffs(code_diffs)
        print(f"[async_process_with_langchain] tasks count={len(tasks)}")
        coros = [self._process_single_diff(seg, options, chain_options, output_mode) for seg in tasks]
        results = await asyncio.gather(*coros)
        return self._aggregate_results(results, output_mode)

    def _normalize_code_diffs(self, code_diffs: Union[Dict, List[Dict], Dict[str, List[Dict]]]) -> List[Dict]:
        """
        统一入口，支持单diff、diff列表、dict of list
        """
        if isinstance(code_diffs, dict):
            # 可能是单diff或dict of list
            if 'content' in code_diffs:
                return [code_diffs]
            # dict of list
            result = []
            for v in code_diffs.values():
                if isinstance(v, list):
                    result.extend(v)
            return result
        elif isinstance(code_diffs, list):
            return code_diffs
        else:
            raise ValueError("不支持的code_diffs类型")

    async def _process_single_diff(self, seg, options, chain_options, output_mode: str):
        dataset_ids = options.get('dataset_ids') if options and options.get('dataset_ids') in options else [
            "your_default_dataset_id"]
        llm = self.llm_service.get_llm()
        print(f"[_process_single_diff] llm={llm}")
        devmind_service = self.devmind_service

        # 直接用seg的所有字段作为输入
        chain_input = dict(seg)

        # 确保依赖信息被正确传递
        if 'upstream' not in chain_input and 'upstream_code' in seg:
            chain_input['upstream'] = list(seg['upstream_code'].keys())
        if 'downstream' not in chain_input and 'downstream_code' in seg:
            chain_input['downstream'] = list(seg['downstream_code'].keys())

        # 确保diff_content和full_code存在
        if 'diff_content' not in chain_input and 'content' in seg:
            chain_input['diff_content'] = seg['content']
        if 'full_code' not in chain_input and 'content' in seg:
            chain_input['full_code'] = seg['content']

        if chain_options and 'prompt_type' in chain_options:
            chain_input['prompt_type'] = chain_options['prompt_type']

        # 根据配置选择CR链类型
        if self.cr_mode == "fast":
            cr_chain = build_intelligent_cr_chain(llm, devmind_service, dataset_ids, "fast")
            print(f"[_process_single_diff] 使用智能Fast模式（纯LLM）")
        elif self.cr_mode == "standard":
            cr_chain = build_intelligent_cr_chain(llm, devmind_service, dataset_ids, "standard")
            print(f"[_process_single_diff] 使用智能Standard模式（LLM决策知识库）")
        elif self.cr_mode == "deep":
            cr_chain = build_intelligent_cr_chain(llm, devmind_service, dataset_ids, "deep")
            print(f"[_process_single_diff] 使用智能Deep模式（深度思考+自检）")
        elif self.cr_mode == "async_fast":
            cr_chain = build_async_fast_cr_chain(llm, devmind_service, dataset_ids)
            print(f"[_process_single_diff] 使用异步快速CR链")
        else:
            # 兼容旧版本，默认使用标准智能模式
            cr_chain = build_intelligent_cr_chain(llm, devmind_service, dataset_ids, "standard")
            print(f"[_process_single_diff] 使用默认智能Standard模式")

        print(f"[_process_single_diff] cr_chain={cr_chain}, chain_input={chain_input}")

        # 性能监控
        import time
        start_time = time.time() if self.enable_performance_monitoring else None

        chain_result = await cr_chain.ainvoke(chain_input)

        if self.enable_performance_monitoring and start_time:
            elapsed = time.time() - start_time
            print(f"[性能监控] CR链执行耗时: {elapsed:.2f}s, 模式: {self.cr_mode}")
        print(f"[_process_single_diff] chain_result keys: {list(chain_result.keys())}")

        if output_mode == "detailed":
            return {
                "codePosition": seg.get("codePosition"),
                "cr_suggestion": chain_result.get("final_judge"),
                "knowledge": chain_result.get("knowledge"),
                "problems": chain_result.get("problems"),
                "upstream": seg.get("upstream", []),
                "downstream": seg.get("downstream", []),
                "upstream_code": seg.get("upstream_code", {}),
                "downstream_code": seg.get("downstream_code", {})
            }
        else:
            return {
                "codePosition": seg.get("codePosition"),
                "upstream": seg.get("upstream", []),
                "downstream": seg.get("downstream", []),
                "cr_suggestion": chain_result.get("final_judge")
            }

    def _aggregate_results(self, results: List[Dict], output_mode: str):
        """
        聚合批量处理结果，支持详细/简要模式
        """
        if output_mode == "detailed":
            return results
        else:
            # 简要模式只返回结论
            return [{"codePosition": r["codePosition"], "cr_suggestion": r["cr_suggestion"]} for r in results]

    def merge_code_review_results(self, results: List[Any]):
        """
        合并多个代码review结果，使用历史消息优化合并过程
        Args:
            results: 各片段的代码review结果
        Returns:
            合并后的结果
        """
        try:
            if not results or len(results) == 0:
                raise ValueError("没有可合并的结果")

            if len(results) == 1:
                return results[0]

            # 提取并保存所有detail信息，按scene分类
            details_by_scene = {}

            # 创建一个不包含detail的结果数组，用于LLM处理
            results_without_details = []

            for result in results:
                result_copy = result.copy()

                # 保存detail信息并按scene分类
                if 'problemList' in result_copy and isinstance(result_copy['problemList'], list):
                    for problem in result_copy['problemList']:
                        if 'scene' in problem and 'detail' in problem and isinstance(problem['detail'], list):
                            if problem['scene'] not in details_by_scene:
                                details_by_scene[problem['scene']] = []

                            details_by_scene[problem['scene']].extend(problem['detail'])

                            # 对每个场景下的detail按level排序
                            details_by_scene[problem['scene']].sort(key=lambda x: {
                                'p0': 0, 'p1': 1, 'p2': 2
                            }.get(x.get('level', '').lower(), 999))

                    # 移除detail信息，保留其他字段
                    result_copy['problemList'] = [
                        {k: v for k, v in problem.items() if k != 'detail'}
                        for problem in result_copy['problemList']
                    ]

                results_without_details.append(result_copy)

            # 使用LLM合并结果
            from pydantic import BaseModel, Field
            from typing import List

            class ProblemItem(BaseModel):
                scene: str = Field(description="检查类别，分为'commonCheck'、'customCheck'两类，严格按照规则定义进行分类")
                num: str = Field(description="问题数量")
                checkResult: str = Field(description="通过或不通过,存在P0问题则为不通过")

            class MergedResult(BaseModel):
                checkBranch: str = Field(description="分支名")
                sumCheckResult: str = Field(description="通过或不通过,存在P0问题则为不通过")
                resultDesc: str = Field(description="问题类别及简要描述")
                totalProblem: str = Field(description="问题总数")
                problemList: List[ProblemItem] = Field(description="问题列表")

            # 创建结构化输出解析器
            output_parser = JsonOutputParser(pydantic_object=MergedResult)


            # 创建系统提示
            system_message = SystemMessage("""
你是一个JSON处理专家，用户会给你一个json数组，请按照以下要求进行处理:
## 非常重要：你必须确保不丢失任何数据，保留输入的原始值不做任何加工处理。
## 具体每个字段合并逻辑如下:
1. checkBranch合并逻辑：保留原始值不做变更
2. resultDesc合并逻辑: 按照问题等级(P0,P1,P2)进行分类累加,保证计算正确
3. totalProblem合并逻辑: 对每个数据的totalProblem转换为数字后进行累加,保证计算正确
4. problemList合并逻辑: 每项的scene保持原始值，num相加，checkResult存在不通过的值则为不通过
5. sumCheckResult合并逻辑: 如果存在P0问题，则sumCheckResult为不通过，否则为通过
## 当你完成合并时，请执行以下验证步骤进行正确性检查:
1. 确认所有原始数据没有被二次加工
2. 确认totalProblem的数量与resultDesc中所有的P0,P1,P2三个数量相加的值相等
3. 确认每个场景的num值等于该场景在原始数据中num值的总和
            """)

            # 创建人类消息
            human_message = HumanMessage(
                f"以下是多个代码片段的review结果，请帮我合并: {json.dumps(results_without_details)}")

            # 创建提示模板
            prompt = ChatPromptTemplate.from_messages([system_message, human_message])

            # 获取LLM实例
            llm = self.llm_service.get_llm()

            # 创建包含错误处理的管道
            parser_with_fix = OutputFixingParser.from_llm(llm, output_parser)
            chain = prompt | llm | parser_with_fix

            # 调用链
            merged_result = chain.invoke({})
            print(f"合并结果: {json.dumps(merged_result)}")

            # 将detail信息添加回结果
            if 'problemList' in merged_result and isinstance(merged_result['problemList'], list):
                merged_result['problemList'] = [
                    {**problem, 'detail': details_by_scene.get(problem['scene'], [])}
                    for problem in merged_result['problemList']
                ]

            return merged_result

        except Exception as e:
            print(f"合并代码评审结果失败: {str(e)}")
            # 如果合并失败，返回第一个有效结果
            for result in results:
                if result:
                    return result
            raise ValueError("无法合并结果，且没有可用的单个结果")

    async def summary_review2km_json(
            self,
            initial_result: str,
            pr_link: Optional[str] = None,
            task_git_info: Optional[Dict[str, Any]] = None
    ):
        """
        生成学城文档JSON结构，便于后续对接KM
        Args:
            initial_result: LLM返回的初始结果
            pr_link: PR链接
            task_git_info: 任务Git信息
        Returns:
            学城文档JSON结构
        """
        # 转义 initialResult 中的模板字符串特殊字符
        escaped_result = json.loads(initial_result)
        print(escaped_result)
        # 创建结构化输出解析器
        from typing import List
        from pydantic import BaseModel

        class DetailItem(BaseModel):
            targetCode: str = Field(description="未通过的问题代码")
            problem: str = Field(description="未通过的检查项")
            suggestion: str = Field(description="修改建议")
            codePosition: str = Field(description="代码的位置")
            codePositionArray: List[int] = Field(description="代码位置数组格式 [startLine, startColumn, endLine, endColumn]", default=[1, 0, 1, 0])
            level: str = Field(description="p0|p1|p2")

        class ProblemItem(BaseModel):
            scene: str = Field(description="检查类别，分为'commonCheck'、'customCheck'两类，严格按照规则定义进行分类")
            num: str = Field(description="问题数量")
            checkResult: str = Field(description="通过或不通过,存在P0问题则为不通过")
            detail: List[DetailItem] = Field(description="问题详情")

        class ReviewResult(BaseModel):
            checkBranch: str = Field(description="分支名")
            sumCheckResult: str = Field(description="通过或不通过,存在P0问题则为不通过")
            resultDesc: str = Field(description="问题类别及简要描述")
            totalProblem: str = Field(description="问题总数")
            problemList: List[ProblemItem] = Field(description="问题列表")

        output_parser = JsonOutputParser(pydantic_object=ReviewResult)

        # 获取格式指令
        format_instructions = (output_parser.get_format_instructions()
                               .replace("{", "\\{")
                               .replace("}", "\\}")
                               .replace("${", "\\${"))

        # 创建系统消息
        system_message = SystemMessage(f"""你是一位JSON格式专家，需要将代码review结果总结为标准JSON数据:
1. 每个检查项，如果该项下存在P0级问题，checkResult必须设置为\"不通过\"
2. problemList下的每一项detail中,targetCode必须保留问题代码原始内容，不进行任何加工
3. 必须返回有效的JSON数据（不要添加任何代码块标记，直接返回裸 JSON）,不带有任何附加文本,并严格确保符合以下格式规范:
{format_instructions}
        """)

        # 创建人类消息
        human_message = HumanMessage(f"请将以下review结果严格按照要求转换为有效的JSON格式:\n{escaped_result}")

        # 创建提示模板
        prompt = ChatPromptTemplate.from_messages([system_message, human_message])
        # 创建专用于总结的LLM服务
        summary_llm_service = LLMService(OpenAIConfig(
            api_key=self.api_key,
            model_name="anthropic.claude-3.5-sonnet-v2",
        ))
        llm = summary_llm_service.get_llm()

        # 创建包含错误处理的管道
        parser_with_fix = OutputFixingParser.from_llm(llm, output_parser)

        try:
            # 首先尝试使用结构化解析器
            chain = prompt | llm | parser_with_fix
            structured_result = chain.invoke({
                # 'format_instructions': format_instructions,
                # 'escaped_result': escaped_result
            })

            print(f"结构化结果: {json.dumps(structured_result)}")

            # 添加分支信息
            if task_git_info:
                repo = task_git_info.get('repo', '')
                project = task_git_info.get('project', '')
                from_branch = task_git_info.get('fromBranch', '')
                to_branch = task_git_info.get('toBranch', '')

                if isinstance(repo, list) and repo:
                    repo = repo[0]
                if isinstance(project, list) and project:
                    project = project[0]
                if isinstance(from_branch, list) and from_branch:
                    from_branch = from_branch[0]
                if isinstance(to_branch, list) and to_branch:
                    to_branch = to_branch[0]

                structured_result['checkBranch'] = f"{repo}/{project}:{from_branch}...{to_branch}"

            # 添加prLink前缀到每个codePosition
            if pr_link and 'problemList' in structured_result:
                for problem in structured_result['problemList']:
                    if 'detail' in problem:
                        for item in problem['detail']:
                            if 'codePosition' in item:
                                import urllib.parse
                                item[
                                    'codePosition'] = f"{pr_link}/diff?currentPath={urllib.parse.quote(item['codePosition'].strip())}"

            # 成功得到结构化数据
            return structured_result

        except Exception as e:
            print(f"结构化解析失败: {str(e)}")
            raise ValueError(f"报告总结失败: {str(e)}")

    @staticmethod
    def generate_cr_tpl(params):
        """
        渲染CR模板，使用Jinja2渲染

        Args:
            params: 模板参数

        Returns:
            渲染后的模板
        """
        import jinja2
        import os

        # 读取模板文件
        template_path = os.path.join(os.path.dirname(__file__), '../../km_doc_template/cr-tpl.jinja2')
        if os.path.exists(template_path):
            with open(template_path, 'r', encoding='utf-8') as f:
                template_content = f.read()
        else:
            # 如果模板文件不存在，使用默认模板
            template_content = """
# 代码审查报告
分支: {{ checkBranch }}
结果: {{ sumCheckResult }}
问题总数: {{ totalProblem }}

{% for problem in problemList %}
## {{ problem.scene }} ({{ problem.num }}个问题)
检查结果: {{ problem.checkResult }}

{% for detail in problem.detail %}
### 问题 {{ loop.index }}: {{ detail.level }}
- 代码位置: {{ detail.codePosition }}
- 问题代码: 
- 问题代码: 
- 问题代码: 
- 问题代码: 
- 问题代码: 
- 问题代码: 
- 问题代码: 
- 问题代码: 
\`\`\`
{{ detail.targetCode }}
\`\`\`
- 问题描述: {{ detail.problem }}
- 修改建议: {{ detail.suggestion }}

{% endfor %}
{% endfor %}
            """

        # 创建Jinja2环境
        env = jinja2.Environment()

        # 添加辅助函数
        def gen_node_id():
            import hashlib
            import uuid
            return hashlib.md5(str(uuid.uuid4()).encode()).hexdigest()

        def escape_helper(value):
            if not value:
                return ""
            return value.replace('"', '\\"').replace('\n', '\\n')

        def md_helper(value=""):
            if not value:
                return ""
            return value.replace('\`\`\`', '\`\`\`').replace('"', '\\"').replace('\n', '\\n')

        env.filters['genNodeId'] = gen_node_id
        env.filters['escapeHelper'] = escape_helper
        env.filters['mdHelper'] = md_helper

        # 渲染模板
        template = env.from_string(template_content)
        return template.render(**params)

    def extract_cr_info_from_dx_chain(self, text: str):
        """
        从大象链提取CR信息，解析AI返回的结构化内容

        Args:
            text: 大象消息文本

        Returns:
            解析后的CR信息
        """
        # 创建结构化输出解析器
        from pydantic import BaseModel, Field

        class CrInfo(BaseModel):
            repo: str = Field(description="code仓库名称")
            project: str = Field(description="code仓库组")
            fromBranch: str = Field(description="cr分支名")
            toBranch: str = Field(description="对比分支名")
            parentId: str = Field(description="学城父文档id")

        output_parser = StructuredOutputParser.from_pydantic_model(CrInfo)
        format_instructions = (output_parser.get_format_instructions()
                               .replace("{", "\\{")
                               .replace("}", "\\}")
                               .replace("${", "\\${"))

        # 创建系统提示
        system_prompt = SystemMessage(f"""
用户发送了信息给你，你需要在用户发送的信息中获取到代码审查的相关信息,并总结成json格式:
1. 例如用户发送"请帮我cr下saas_crm_react的dev分支,并在123456下生成报告",则你提取到的信息为：
  {{
    "repo": "saas_crm_react",
    "project": "set", //如用户没提及则默认是set;当用户提及mtfe/saas_crm时,则project为"mtfe",repo为"saas_crm"
    "fromBranch": "dev",
    "toBranch": "master", //如用户没提及则默认是master
    "parentId": "123456"
  }}
2. 仅输出有效的JSON数据，不含任何其他内容
3. 确保符合以下格式规范
{format_instructions}
        """)

        # 创建人类消息
        human_message = HumanMessage(
            f"请将用户发送的命令提取后转换为JSON格式:\n{text},如用户信息不足则提示用户输入完整信息")

        # 创建提示模板
        prompt = ChatPromptTemplate.from_messages([system_prompt, human_message])

        # 创建专用于意图提取的LLM服务
        intention_llm_service = LLMService(OpenAIConfig(
            api_key=self.api_key,
            model_name="LongCat-Medium-32K-Chat"
        ))
        llm = intention_llm_service.get_llm()

        # 创建链
        chain = prompt | llm | output_parser

        # 调用链
        return chain.invoke({})

    def get_cr_config_by_project(self, task_git_info: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        获取项目对应的CR配置

        Args:
            task_git_info: 任务Git信息，包含repo等信息

        Returns:
            项目CR配置
        """
        # 获取所有项目配置
        project_configs = (self.horn_service.fetch_config("CR_CHECKER")).get("projectConfig", [])
        print(f"项目配置: {project_configs}")
        # 默认配置
        default_config = {
            "lang": "python",
            "crConfigKey": "pythonBaseCrRules",
            "customCrConfigKey": ""
        }

        # 如果没有任务Git信息或没有repo信息，返回默认配置
        if not task_git_info or not task_git_info.get('repo'):
            return default_config

        # 查找匹配的项目配置
        repo = task_git_info['repo']
        for project_config in project_configs:
            if project_config.get('project') == repo:
                return project_config
        # 如果没有找到匹配的项目配置，返回默认配置
        return default_config

    def generate_cr_system_prompt(self, task_git_info: Optional[Dict[str, Any]] = None):
        """
        生成CR系统提示词，结合项目配置和业务规则

        Args:
            task_git_info: 任务Git信息，包含repo等信息

        Returns:
            系统提示词
        """
        # 获取项目配置
        project_cr_config = self.get_cr_config_by_project(task_git_info)

        # 打印项目信息
        repo = None
        if task_git_info and 'repo' in task_git_info:
            repo = task_git_info['repo']
            print(f'项目配置为: {repo}')

        # 获取通用代码检查规则
        cr_config_key = project_cr_config.get('crConfigKey', 'default')
        print(f'cr规则使用的规则key为: {cr_config_key}')

        cr_system_rules = self.horn_service.fetch_config("CR_CHECKER").get(cr_config_key, '')

        # 获取项目定制代码检查规则
        custom_cr_rules = None
        custom_cr_config_key = project_cr_config.get('customCrConfigKey')
        if custom_cr_config_key:
            custom_cr_rules = self.horn_service.fetch_config("CR_CHECKER").get(custom_cr_config_key, '')

        # 构建模板
        lang = project_cr_config.get('lang', 'Python')
        template = f"""## 你是一个代码研发专家，专精{lang}语言，请严格按照以下要求及提供的规则对代码进行review，并给出review结果。

## code review要求
1. 严格按照代码检查规则一项一项进行检查，不要有任何遗漏
2. 严格检查用户提供的代码范围内检查，如果代码信息不足，不要进行推理，直接按没问题处理
3. 严格按照代码检查规则的规则范围进行检查，不要进行其他不在检查规则中的检查
4. 代码内容中包含了文件位置标记，格式为[FILE_POSITION:文件路径]，报告问题时请严格使用这些标记来准确引用代码位置
5. 问题代码必须是准确的原始代码内容，不要进行任何加工或总结
6. 每条规则开头为问题等级信息(P0,P1,P2)，请严格按照该信息进行等级判定
7. 请给出对应的问题代码的原始检查规则（通用代码检查(commonCheck)规则或项目定制代码检查(customCheck)规则）,不要加工省略
8. 在检查规则范围内，请给出对应的问题代码的推荐代码改法
9. 没问题的代码请不要给任何输出
## 通用代码检查(commonCheck)规则
{cr_system_rules}
## 项目定制代码检查(customCheck)规则
{custom_cr_rules or '无项目定制代码检查规则'}
"""
        return template.replace("{", "{{").replace("}", "}}")

    @staticmethod
    def generate_code_prompt(code_diff: str, options: Optional[Dict[str, Any]] = None):
        """
        生成代码提示词，结合diff内容和上下文

        Args:
            code_diff: 代码差异字符串
            options: 可选参数，包含分段索引、总分段数等

        Returns:
            代码提示词
        """
        # 构建分段信息
        segment_info = ""
        if options and 'segmentIndex' in options and 'totalSegments' in options:
            segment_info = f"(这是第 {options['segmentIndex']} 段代码，共 {options['totalSegments']} 段)"

        # 构建PR链接信息
        pr_info = ""
        if options and 'prLink' in options and options['prLink']:
            pr_info = f"PR链接: {options['prLink']}"

        # 构建Git信息
        git_info = ""
        if options and 'taskGitInfo' in options and options['taskGitInfo']:
            git_info_dict = options['taskGitInfo']
            repo = git_info_dict.get('repo', '')
            project = git_info_dict.get('project', '')
            from_branch = git_info_dict.get('fromBranch', '')
            to_branch = git_info_dict.get('toBranch', '')

            if isinstance(repo, list) and repo:
                repo = repo[0]
            if isinstance(project, list) and project:
                project = project[0]
            if isinstance(from_branch, list) and from_branch:
                from_branch = from_branch[0]
            if isinstance(to_branch, list) and to_branch:
                to_branch = to_branch[0]

            git_info = f"""
仓库信息:
- 项目: {project}
- 仓库: {repo}
- 源分支: {from_branch}
- 目标分支: {to_branch}
"""

        # 构建完整提示词
        prompt = f"""请审查以下代码差异 {segment_info}

{pr_info}
{git_info}
代码差异内容:
```diff
{code_diff}
```
请按照系统提示中的规则进行代码审查，并给出详细的问题列表和改进建议。
"""

        return prompt.replace("{", "{{").replace("}", "}}")

    def process_code_diff(
            self,
            code_diff: str,
            options: Optional[Dict[str, Any]] = None,
            async_mode: bool = True
    ):
        """
        处理代码差异，支持大型代码差异的分段并行处理
        Args:
            code_diff: Git diff格式的代码差异字符串
            options: 可选参数，包含PR链接和任务Git信息等
            async_mode: 是否异步并发处理，便于调试
        Returns:
            代码审查结果
        """
        try:
            # 获取git信息
            project = options.get('project') if options else None
            repo = options.get('repo') if options else None
            from_branch = options.get('fromBranch') if options else None
            # enrich diff segments with context
            segments = ChunkService(self.git_service, project, repo, from_branch).enrich_diff_segments_with_context(
                code_diff)

            if not segments:
                raise ValueError("未能分割出有效代码片段")
            if async_mode:
                async def batch_cr():
                    coros = [self._process_single_diff(seg, options, None, "detailed") for seg in segments]
                    return await asyncio.gather(*coros)

                results = asyncio.run(batch_cr())
                return self.merge_code_review_results(results)
            else:
                results = []
                for seg in segments:
                    result = asyncio.run(self._process_single_diff(seg, options, None, "detailed"))
                    results.append(result)
                return self.merge_code_review_results(results)
        except Exception as e:
            error_msg = f"处理大型代码评审失败: {str(e)}"
            print(error_msg)
            raise ValueError(error_msg)

    def extract_pr_info_from_dx_chain(self, text: str):
        """
        从大象链提取PR信息，解析AI返回的PR相关内容

        Args:
            text: 大象消息文本

        Returns:
            解析后的PR信息
        """
        # 创建结构化输出解析器
        from pydantic import BaseModel, Field

        class PrInfo(BaseModel):
            project: str = Field(description="code仓库组")
            repo: str = Field(description="code仓库名称")
            prNumber: str = Field(description="PR编号")
            parentId: str = Field(description="学城父文档id")

        output_parser = StructuredOutputParser.from_pydantic_model(PrInfo)
        format_instructions = (output_parser.get_format_instructions()
                               .replace("{", "\\{")
                               .replace("}", "\\}")
                               .replace("${", "\\${"))

        # 创建系统提示
        system_prompt = SystemMessage(f"""
用户发送了信息给你，你需要在用户发送的信息中获取到PR的相关信息,并总结成json格式:
1. 例如用户发送"请帮我cr下，https://dev.sankuai.com/code/repo-detail/set/empower-order-mrn/pr/195/overview，并在123456下创建学城文档",则你提取到的信息为：
  {{
    "project": "set",
    "repo": "empower-order-mrn",
    "prNumber": "195",
    "parentId": "123456"
  }}
2. 仅输出有效的JSON数据，不含任何其他内容
3. 确保符合以下格式规范
4. 缺少信息时尽量提取出可以提取的信息，没有的信息返回空字符
{format_instructions}
        """)

        # 创建人类消息
        human_message = HumanMessage(
            f"请将用户发送的命令提取后转换为JSON格式:\n{text},如用户信息不足则提示用户输入完整信息")

        # 创建提示模板
        prompt = ChatPromptTemplate.from_messages([system_prompt, human_message])

        # 创建专用于意图提取的LLM服务
        intention_llm_service = LLMService(OpenAIConfig(
            api_key=self.api_key,
            model_name="deepseek-v3-friday"
        ))
        llm = intention_llm_service.get_llm()

        # 创建链
        chain = prompt.pipe(llm).pipe(output_parser)

        # 调用链
        return chain.invoke({})
