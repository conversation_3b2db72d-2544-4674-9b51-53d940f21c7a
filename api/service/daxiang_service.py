# 大象服务，负责与大象平台相关的API交互
import json
import logging
import os
from pathlib import Path
from typing import Any, Dict, List, Optional

import dotenv
import octo_rpc
from octo_rpc import NonMeshClient, load

from api.service.kms_service import KmsService
from api.service.org_service import OrgService
from utils import singleton

dotenv.load_dotenv()
octo_rpc.log.setLevel(logging.ERROR)

ROOT_DIR = Path(__file__).resolve().parent.parent.parent.__str__()


@singleton
class DaXiangService:
    """
    大象服务，负责与大象平台相关的API交互
    """

    def __init__(self, kms_service: KmsService):
        self.org_service = OrgService()
        self.kms_service = kms_service
        # 根据环境变量选择配置
        self.CLIENT_APP_KEY = os.environ['CLIENT_APP_KEY']

        if os.environ['INF_BOM_ENV'] == 'test':
            self.DX_APP_ID = os.environ['DX_APP_ID_DEV']
        else:
            self.DX_APP_ID = os.environ['DX_APP_ID_PROD']
        if not self.DX_APP_ID:
            raise ValueError('APP_ID 必须设置。请在 .env 文件中设置这些变量，或者在运行时设置环境变量。')
        # 环境变量
        self.env = os.environ.get('INF_BOM_ENV')
        # 初始化大象开放平台鉴权服务
        self.DX_APP_KEY = os.environ["DX_APP_KEY"]
        print(ROOT_DIR + "/infra/thrift/daxiang/AuthEntity.thrift")
        open_auth_service = load(ROOT_DIR + "/infra/thrift/daxiang/AuthService.thrift")
        self.dx_auth_client = NonMeshClient(
            appkey=self.CLIENT_APP_KEY,
            timeout=5000,
            env=self.env,
            service_name="com.sankuai.xm.openplatform.auth.service.XmAuthServiceI",
            remote_appkey=self.DX_APP_KEY,
            service=open_auth_service.XmAuthServiceI
        )

        open_invoke_service = load(ROOT_DIR + "/infra/thrift/daxiang/InvokeApi.thrift")
        self.dx_invoke_client = NonMeshClient(
            appkey=self.CLIENT_APP_KEY,
            remote_appkey=self.DX_APP_KEY,
            timeout=5000,
            service_name="com.sankuai.xm.openplatform.api.service.open.OpenServiceI",
            service=open_invoke_service.OpenServiceI
        )

        open_message_service = load(ROOT_DIR + "/infra/thrift/daxiang/OpenMessageService.thrift")
        self.dx_messages_client = NonMeshClient(
            appkey=self.CLIENT_APP_KEY,
            timeout=5000,
            service_name="com.sankuai.xm.openplatform.api.service.open.XmOpenMessageServiceI",
            remote_appkey=self.DX_APP_KEY,
            service=open_message_service.XmOpenMessageServiceI
        )

    def get_access_token(self, ctx: Any) -> Optional[str]:
        """获取办公开放平台accessToken"""
        # API 文档请参考: https://km.sankuai.com/collabpage/1464276162

        # 获取办公开放平台APP_SECRET

        kms_service_res = self.kms_service.get_key(self.CLIENT_APP_KEY, "dxopen_sk")
        app_secret = kms_service_res.get('data')

        try:

            auth_entity = load(ROOT_DIR + "/infra/thrift/daxiang/AuthEntity.thrift")
            print("app_id:", self.DX_APP_ID, "secret:", app_secret)
            data = self.dx_auth_client.accessToken(
                appAuthInfo=auth_entity.AppAuthInfo(appkey=self.DX_APP_ID, appSecret=app_secret))
            code = data.status.code
            msg = data.status.msg
            print('获取token结果', data.__dict__)
            if code != 0:
                raise Exception(f"token 获取失败: {msg}")

            return data.accessToken.token
        except Exception as error:
            print('获取token失败', error)
            return None

    def convert_emp_id_to_user_id(self, emp_ids: List[str], ctx: Any):
        """将empId转换为userId"""

        token = self.get_access_token(ctx)
        json_data = json.dumps({
            'empIdList': emp_ids
        })

        try:
            invoke_entity = load(ROOT_DIR + "/infra/thrift/daxiang/AuthEntity.thrift")
            data = self.dx_invoke_client.invoke(token,
                                                invoke_entity.InvokeReq(path='/open-apis/dx/queryUidByEmpIdList',
                                                                        jsonData=json_data))

            if data.status.code == 0:
                uid_map = json.loads(data.data).get('uidMap', {})
                uid_list = list(uid_map.values())
                return uid_list

            return data
        except Exception as error:
            print('empId转换为userId失败', error)
            return None

    def convert_userid_to_emp_id(self, uids: List[str], ctx: Any):
        """将uid转换为empId"""
        token = self.get_access_token(ctx)
        json_data = json.dumps({
            'uidList': uids
        })

        try:
            invoke_entity = load(ROOT_DIR + "/infra/thrift/daxiang/AuthEntity.thrift")
            data = self.dx_invoke_client.invoke(token,
                                                invoke_entity.InvokeReq(
                                                    path='/open-apis/dx/queryUidByEmpIdList',
                                                    jsonData=json_data))
            print('uid转换为empId结果', data)

            if data.status.code == 0:
                emp_id_map = json.loads(data.data).get('empIdMap', {})
                emp_list = list(emp_id_map.values())
                print('uid转换为empId结果', emp_list)
                return emp_list

            return data
        except Exception as error:
            print('uid转换为empId失败', error)
            return None

    async def send_chat_msg_by_robot(self, ctx: Any, params: Optional[Dict[str, Any]] = None) -> Any:
        """以机器人身份发送单聊消息"""
        if params is None:
            params = {}

        # 获取办公开放平台accessToken
        token = self.get_access_token(ctx)

        receiver_ids = None
        if params.get('uid'):
            receiver_ids = [params['uid']]
        else:
            emp_id = params.get('empId') or ctx.sdk.sso.userInfo.id if hasattr(ctx, 'sdk') else None
            if emp_id:
                receiver_ids = self.convert_emp_id_to_user_id([emp_id], ctx)
            else:
                emp_info = self.org_service.get_emp_info(
                    getattr(ctx, 'headers', {}),
                    getattr(ctx, 'args', {})
                )
                receiver_ids = [int(emp_info.get('empId'))] if emp_info.get('empId') else None

        print('发送消息reqParams', token, receiver_ids, params.get('text'))

        try:
            # 移除不需要的参数
            body_params = {k: v for k, v in params.items() if k not in ['uid', 'empId', 'type']}

            messages_entity = load(ROOT_DIR + "/infra/thrift/daxiang/MessageEntity.thrift")
            data = self.dx_messages_client.sendChatMsgByRobot(token,
                                                              messages_entity.SendChatMsgByRobotReq(
                                                                  receiverIds=receiver_ids,
                                                                  sendMsgInfo=messages_entity.SendMsgInfo(
                                                                      type=params.get('type', 'text'),
                                                                      body=json.dumps(body_params),
                                                                      isDynamicMsg=False,
                                                                  )
                                                              ))

            code = data.status.code
            msg = data.status.msg

            if code != 0:
                raise Exception(f"发送消息失败: {msg}")

            print('发送消息成功', data)
            return data
        except Exception as error:
            print('发送消息失败', error)
            return None
