import os
from typing import Any, Dict, Optional


class HornService:
    """
    Horn服务，负责远程配置的获取，可对接配置中心或本地mock。
    """

    def __init__(self):
        # 可扩展为初始化远程配置SDK
        self.is_dev = os.environ.get('INF_BOM_ENV', 'prod') != 'prod'

    @staticmethod
    def fetch_config(horn_key: str, params: Optional[Dict[str, Any]] = None, default_value: Any = None):
        """
        获取远程配置，当前为本地mock，后续可对接实际配置中心（如Apollo、Consul等）。
        :param horn_key: 配置key
        :param params: 额外参数
        :param default_value: 默认值
        :return: 配置字典
        """
        # TODO: 对接实际配置中心API，替换为真实拉取逻辑
        # 示例：
        # config = remote_config_sdk.fetch(horn_key, params)
        # if config: return config
        # return default_value
        if horn_key == 'CR_CHECKER':
            return {
                'projectConfig': [
                    {
                        "project": "shangou_ai_cr",
                        "lang": "python",
                        "crConfigKey": "pythonBaseCrRules",
                        "customCrConfigKey": ""
                    }],
                'crLlmConfig': {
                    "modelName": "anthropic.claude-3.7-sonnet",
                    "maxTokens": 8192,
                    "temperature": 0.2,
                    "maxDiffLines": 600
                },
                'pythonBaseCrRules': """
## Python 代码审查规则（commonCheck）

### P0（严重问题，必须修复）
- P0-1: 存在语法错误、未定义变量、未导入模块、缩进错误等导致代码无法运行的问题
- P0-2: 存在明显的安全漏洞（如 SQL 注入、命令注入、明文密码、危险 eval/exec 等）
- P0-3: 关键业务流程存在逻辑漏洞或数据丢失风险
- P0-4: 资源未正确关闭（如文件、数据库连接、网络连接等）

### P1（重要问题，建议修复）
- P1-1: 违反 PEP8 规范（如命名不规范、行过长、空格/缩进不一致等）
- P1-2: 魔法数字/字符串未定义为常量
- P1-3: 异常未捕获或捕获过于宽泛（如 except:）
- P1-4: 函数/类/模块缺少必要的 docstring 注释
- P1-5: 代码重复、未封装、未复用
- P1-6: 资源释放不及时，可能导致内存泄漏

### P2（一般问题，建议优化）
- P2-1: 变量/函数/类命名不清晰或不符合语义
- P2-2: 代码结构不清晰，函数过长、嵌套层级过深
- P2-3: 不必要的 print/log 输出未清理
- P2-4: 过度依赖全局变量
- P2-5: 兼容性问题（如 Python2/3 差异、依赖未声明等）
- P2-6: 性能可优化（如不必要的循环、低效的数据结构等）

## 代码审查要求
1. 严格按照上述规则逐项检查，不得遗漏。
2. 仅对 diff 范围内的代码进行审查，未变更部分无需检查。
3. 问题定位需精确到代码行，引用原始代码内容。
4. 每个问题需给出详细描述、风险说明及修改建议。
5. 如无问题，直接说明“未发现问题”。
6. 检查结果需结构化输出，包含问题等级（P0/P1/P2）、问题描述、建议、代码位置、原始代码片段等。

## 输出格式要求
- 严格按照结构化 JSON 输出，不得输出多余文本。
- 每个问题需包含：level, problem, suggestion, codePosition, targetCode。

"""
            }
        return default_value or {}
