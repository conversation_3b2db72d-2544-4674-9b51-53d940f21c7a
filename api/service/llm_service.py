# LLM服务，负责大模型实例的初始化与配置
from typing import Optional, Dict, Any
from langchain_openai import ChatOpenAI


class OpenAIConfig:
    def __init__(self,
                 api_key: str,
                 base_url: str = "https://aigc.sankuai.com/v1/openai/native",
                 temperature: float = 0.1,
                 model_name: str = "anthropic.claude-3.5-sonnet",
                 max_tokens: int = 8192,
                 max_completion_tokens: int = 8002):
        self.api_key = api_key
        self.base_url = base_url
        self.temperature = temperature
        self.model_name = model_name
        self.max_tokens = max_tokens
        self.max_completion_tokens = max_completion_tokens

    def __str__(self):
        return str(self.__dict__)


class LLMService:
    """
    LLM服务，负责大模型实例的初始化与配置
    """

    def __init__(self, config: OpenAIConfig):
        self.config = config
        self.llm = None  # 这里可对接实际的LLM实例，如openai.ChatCompletion等
        self.init_llm()

    def init_llm(self):
        """初始化LLM实例，需对接实际Python LLM库"""
        try:
            # 使用langchain库初始化ChatModel实例
            self.llm = ChatOpenAI(
                api_key=self.config.api_key,
                base_url=self.config.base_url,
                model=self.config.model_name,
                temperature=self.config.temperature,
                max_completion_tokens=self.config.max_completion_tokens,
                max_tokens=self.config.max_tokens
            )
        except Exception as e:
            # 捕获初始化过程中的异常并记录
            raise RuntimeError(f"LLM实例初始化失败: {e}")

    def update_config(self, new_config: Dict[str, Any]):
        """更新配置并重新初始化LLM实例"""
        for k, v in new_config.items():
            if hasattr(self.config, k):
                setattr(self.config, k, v)
        self.init_llm()
        return self

    def get_llm(self):
        """获取LLM实例"""
        return self.llm

    def get_max_tokens(self) -> int:
        """获取最大token数"""
        return getattr(self.config, 'max_tokens', 8192)

    def print_config(self):
        print(self.config)
