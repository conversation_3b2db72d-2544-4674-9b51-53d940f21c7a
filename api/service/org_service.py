# 组织架构服务，负责员工信息获取
# 对接公司组织架构API
import os
import hmac
import base64
import hashlib
from pathlib import Path

import requests
import yaml
from typing import Any, Dict, Optional

from utils import singleton

ROOT_DIR = Path(__file__).resolve().parent.parent.parent.__str__()


@singleton
class OrgService:
    """
    组织架构服务，负责员工信息获取
    """

    def __init__(self):
        """初始化组织架构服务，加载配置"""
        self.env = os.environ.get('INF_BOM_ENV', 'test')
        self.org_config = self._load_org_config()

    def _load_org_config(self) -> Dict[str, Any]:
        """加载组织架构配置"""
        try:
            with open(ROOT_DIR + '/conf/org_conf.yaml', 'r') as f:
                config = yaml.safe_load(f)
                return config.get(self.env, {})
        except Exception as e:
            print(f"加载组织架构配置失败: {str(e)}")
            return {}

    @staticmethod
    def create_signature(params: Dict[str, Any]) -> str:
        """
        创建ORG签名
        
        Args:
            params: 签名参数，包含accessKey, secretKey, httpMethod, path, signDate
            
        Returns:
            签名字符串
        """
        access_key = params.get('accessKey', '')
        secret_key = params.get('secretKey', '')
        http_method = params.get('httpMethod', 'GET')
        path = params.get('path', '')
        sign_date = params.get('signDate', '')

        # 构建待签名字符串
        string_to_sign = f"{http_method} {path}\n{sign_date}"

        # 使用HMAC-SHA1计算签名
        h = hmac.new(secret_key.encode('utf-8'), string_to_sign.encode('utf-8'), hashlib.sha1)
        signature = base64.b64encode(h.digest()).decode('utf-8')

        return f"MWS {access_key}:{signature}"

    def get_emp_info(self, request_headers, query_params: Dict[str, str]) -> Dict[str, Any]:
        """
        获取员工信息
        
        Args:
            request_headers: 请求头
            query_params: 查询参数
            
        Returns:
            员工信息
        """
        try:
            # 从请求头或查询参数中获取misId
            mis_id_friday = request_headers.get('appfactory-context-user-id')
            mis_id = request_headers.get('mis') or query_params.get('mis')

            # 使用misId获取员工信息
            emp_info = self.get_emp_info_by_mis_id(mis_id_friday or mis_id)
            print("emp_info:", emp_info)
            return emp_info
        except Exception as e:
            raise ValueError(f"获取员工信息失败: {str(e)}")

    def get_emp_info_by_mis_id(self, mis_id: Optional[str] = None) -> Dict[str, Any]:
        """
        根据misId获取员工信息
        
        Args:
            mis_id: 员工MIS ID
            
        Returns:
            员工信息
        """
        try:
            if not mis_id:
                raise ValueError("misId is empty")

            print(f"misId: {mis_id}")

            # 构建请求URL和头信息
            url = f"{self.org_config.get('domain')}/api/org2/emps/_batch?mises={mis_id}"
            headers = {
                'Authorization': f"MWS {self.org_config.get('accessKey')}:{self.org_config.get('signature')}",
                'Date': self.org_config.get('signDate'),
                'data-scope': 'tenantId=1;source=MT'
            }

            # 发送请求
            response = requests.get(url, headers=headers)
            response.raise_for_status()  # 如果响应状态码不是200，抛出异常

            # 解析响应
            data = response.json().get('data', [])
            return data[0] if data else {}

        except Exception as e:
            print(f"[yunzhuan:sso] 获取 empId 失败: {str(e)}")
            raise ValueError(f"获取员工信息失败: {str(e)}")
