import asyncio

from flask import request, jsonify

from api.service.devmind_service import DevmindService
from common.devtools import pr_info_handler
from api.service.cr_lc_service import CrLCService
from api.service.daxiang_service import DaXiangService
from api.service.devtools_service import DevtoolsService
from api.service.git_service import GitService
from api.service.horn_service import HornService
from api.service.kms_service import KmsService
from utils.report2raptor_utils import report2raptor

# 初始化服务（实际应按需依赖注入）
horn_service = HornService()
kms_service = KmsService()
dx_service = DaXiangService(kms_service)
git_service = GitService()
devmind_service = DevmindService()
cr_lc_service = CrLCService(horn_service, kms_service, dx_service, git_service, devmind_service)
devtools_service = DevtoolsService()


@manager.route('/test_summary_review', methods=['POST'])
def test_summary_review():
    try:
        input_data = request.get_json()
        review_result = input_data.get('reviewResult')
        pr_link = input_data.get('prLink', 'https://code.sankuai.com/pr/123')
        task_git_info = input_data.get('gitInfo', {
            'repo': 'test-repo',
            'project': 'test-project',
            'fromBranch': 'feature/test',
            'toBranch': 'master'
        })
        # 业务调用（此处为mock，需补全真实实现）
        loop = asyncio.get_event_loop()
        asyncio.set_event_loop(loop)
        result = loop.run_until_complete(cr_lc_service.summary_review2km_json(
            review_result, pr_link, task_git_info
        ))
        return jsonify({'success': True, 'data': result})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@manager.route('/pr_info', methods=['GET'])
async def pr_info_handler_route():
    try:
        project = request.args.get('project')
        repo = request.args.get('repo')
        pr_id = request.args.get('id')

        # 使用controller中的pr_info_handler
        result = pr_info_handler(request)
        return jsonify(result)
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@manager.route('/create_pr', methods=['POST'])
async def create_pr():
    try:
        input_data = request.get_json()
        project = input_data.get('project')
        repo = input_data.get('repo')
        from_branch = input_data.get('fromBranch')
        to_branch = input_data.get('toBranch')
        title = input_data.get('title', '云篆自动创建PR')

        # 构建URL和参数
        url = f"/api/v1/projects/{project}/repos/{repo}/pull-requests"
        params = {
            "title": title,
            "fromRef": {
                "id": from_branch,
                "repository": {
                    "slug": repo,
                    "project": {
                        "key": project
                    }
                }
            },
            "toRef": {
                "id": to_branch,
                "repository": {
                    "slug": repo,
                    "project": {
                        "key": project
                    }
                }
            }
        }

        # 调用DevtoolsService创建PR
        pr_id = devtools_service.create_pr(url, params)
        pr_url = f'https://dev.sankuai.com/code/repo-detail/{project}/{repo}/pr/{pr_id}'
        return jsonify({'url': pr_url})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@manager.route('/set_pr_comment', methods=['POST'])
async def set_pr_comment():
    try:
        input_data = request.get_json()
        project = input_data.get('project')
        repo = input_data.get('repo')
        pr_id = input_data.get('id')
        comments = input_data.get('comments')

        # 构建URL
        url = f"/api/v1/projects/{project}/repos/{repo}/pull-requests/{pr_id}/comments"

        # 调用DevtoolsService设置评论
        result = devtools_service.set_comment(url, comments)
        return jsonify({'result': result})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@manager.route('/get_branch_diff', methods=['GET'])
async def get_branch_diff():
    try:
        project = request.args.get('project')
        repo = request.args.get('repo')
        from_branch = request.args.get('fromBranch')
        to_branch = request.args.get('toBranch')

        diff = git_service.get_diff(project, repo, from_branch, to_branch)
        diff_lines = len(diff.split('\n')) if diff else 0

        report2raptor(
            name='get-branch-diff',
            value=1,
            tags={
                'project': project,
                'repo': repo,
                'fromBranch': from_branch,
                'toBranch': to_branch,
                'diffLines': diff_lines
            },
            msg='获取分支差异成功'
        )

        return jsonify({'diff': diff})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@manager.route('/init_ssh', methods=['POST'])
async def init_ssh():
    try:
        # 调用DevtoolsService初始化SSH
        result = await devtools_service.init_ssh()

        if 'error' in result:
            return jsonify({'success': False, 'error': result['error']}), 500

        return jsonify({
            'success': True,
            'data': result,
            'message': 'SSH keys initialized successfully'
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@manager.route('/fetch_pr_info', methods=['GET'])
async def fetch_pr_info_route():
    try:
        project = request.args.get('project')
        repo = request.args.get('repo')
        pr_number = request.args.get('prNumber')

        # 调用DevtoolsService获取PR信息
        result = await devtools_service.fetch_pr_info(project, repo, pr_number)

        return jsonify(result)
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500
