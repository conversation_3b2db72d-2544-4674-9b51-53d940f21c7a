"""
SSO认证蓝图
提供SSO登录、回调、登出等功能
"""

import logging
import urllib.parse
from functools import wraps

from flask import request, redirect, session, jsonify
from flask_login import login_user, logout_user, login_required, current_user

# 导入自定义模块
from api.models.user import User
from utils.api_utils import (
    # 参数验证和安全防护
    validate_request, not_allowed_parameters,
    # 错误处理
    construct_error_response,  # 响应构建
    construct_json_result, auth_error_response,  # 重试机制
    get_exponential_backoff_interval,
    # 返回码
    RetCode
)
from utils.sso_utils import get_ssoid, get_sso_user_info, build_sso_login_url


def enhanced_sso_request(func, *args, max_retries=3, **kwargs):
    """
    增强的SSO请求函数，支持指数退避重试机制

    Args:
        func: 要执行的SSO函数
        *args: 函数参数
        max_retries: 最大重试次数
        **kwargs: 函数关键字参数

    Returns:
        SSO函数的返回结果
    """
    import time

    for retry in range(max_retries + 1):
        try:
            result = func(*args, **kwargs)
            if result:  # 如果成功获取到结果
                return result

            if retry < max_retries:
                # 计算退避时间
                wait_time = get_exponential_backoff_interval(retry, full_jitter=True)
                logging.warning(f"SSO请求失败，{wait_time}秒后重试 (第{retry + 1}次)")
                time.sleep(wait_time)

        except Exception as e:
            if retry < max_retries:
                wait_time = get_exponential_backoff_interval(retry, full_jitter=True)
                logging.warning(f"SSO请求异常: {str(e)}，{wait_time}秒后重试 (第{retry + 1}次)")
                time.sleep(wait_time)
            else:
                logging.error(f"SSO请求最终失败: {str(e)}")
                raise e

    return None


def require_valid_origin(f):
    """
    装饰器：验证请求来源的有效性
    """
    @wraps(f)
    def wrapper(*args, **kwargs):
        origin = request.headers.get('Origin')
        referer = request.headers.get('Referer')

        # 在生产环境中，应该验证origin是否在允许的域名列表中
        if not origin and not referer:
            logging.warning("请求缺少Origin和Referer头部")
            return auth_error_response("无效的请求来源")

        return f(*args, **kwargs)
    return wrapper


@manager.route("/sso_login", methods=["GET"])
@require_valid_origin
@not_allowed_parameters('token', 'session_id')  # 防止恶意参数注入
def sso_login():
    """
    SSO登录入口
    重定向到SSO登录页面
    """
    try:
        # 构建回调URL - 应该指向后端接口，不是前端
        backend_url = request.host_url.rstrip('/')
        redirect_uri = urllib.parse.quote(f"{backend_url}/yunzhuan/api/v1/auth/sso_callback")

        # 保存前端URL到session，用于回调后重定向
        frontend_url = request.headers.get('Origin') or request.headers.get('Referer')
        if frontend_url:
            # 提取前端的基础URL（去掉路径部分）
            from urllib.parse import urlparse
            parsed = urlparse(frontend_url)
            frontend_base_url = f"{parsed.scheme}://{parsed.netloc}"
            session['frontend_url'] = frontend_base_url
            logging.info(f"保存前端URL到session: {frontend_base_url}")
        else:
            # 如果无法获取前端URL，使用默认值
            session['frontend_url'] = backend_url
            logging.warning("无法获取前端URL，使用后端URL作为默认值")

        # 使用增强的SSO请求构建登录URL
        sso_url = enhanced_sso_request(build_sso_login_url, redirect_uri)

        if not sso_url:
            logging.error("构建SSO登录URL失败")
            return construct_json_result(
                code=RetCode.SERVER_ERROR,
                message="SSO服务暂时不可用，请稍后重试"
            )

        logging.info(f"重定向到SSO登录: {sso_url}")
        return redirect(sso_url)

    except Exception as e:
        logging.exception(f"SSO登录失败: {str(e)}")
        return construct_error_response(e)


@manager.route("/sso_callback", methods=["GET"])
@validate_request('code')  # 验证必需的code参数
@not_allowed_parameters('token', 'session_id', 'user_id')  # 防止参数污染
def sso_callback():
    """
    SSO回调处理
    ---
    tags:
      - OAuth
    parameters:
      - in: query
        name: code
        type: string
        required: true
        description: SSO授权码
    responses:
      302:
        description: 登录成功后重定向到首页
      200:
        description: 登录失败信息
    """
    try:
        # 检查SSO配置
        from utils.sso_utils import get_sso_config
        sso_config = get_sso_config()
        if not sso_config:
            return construct_json_result(
                code=RetCode.SERVER_ERROR,
                message="SSO配置未设置"
            )

        code = request.args.get('code')
        if not code:
            return construct_json_result(
                code=RetCode.AUTHENTICATION_ERROR,
                message="SSO验证失败：未获取到授权码"
            )

        logging.info(f"收到SSO授权码: {code}")

        # 使用增强的SSO请求获取token
        token_data = enhanced_sso_request(get_ssoid, code)
        if not token_data or not token_data.get('accessToken'):
            return construct_json_result(
                code=RetCode.AUTHENTICATION_ERROR,
                message="SSO验证失败：授权码无效"
            )

        # 使用增强的SSO请求获取用户信息
        ssoid = token_data['accessToken']
        user_info = enhanced_sso_request(get_sso_user_info, ssoid)
        if not user_info:
            logging.error("SSO验证失败：无法获取用户信息")
            return construct_json_result(
                code=RetCode.AUTHENTICATION_ERROR,
                message="SSO验证失败：无法获取用户信息"
            )

        # 记录用户信息日志
        logging.warning(f"SSO获取到的用户信息: {user_info}")

        # 检查用户邮箱
        email = user_info.get('email')
        if not email:
            return construct_json_result(
                code=RetCode.AUTHENTICATION_ERROR,
                message="SSO验证失败：用户邮箱为空"
            )

        user_id = user_info.get('uid')
        if not user_id:
            return construct_json_result(
                code=RetCode.AUTHENTICATION_ERROR,
                message="SSO验证失败：用户ID为空"
            )

        # 查找现有用户
        user = User.get_by_email(email)

        if not user:
            # 用户未注册，创建新用户
            try:
                # 尝试多种可能的字段名获取用户名
                nickname = user_info.get('loginName') or user_info.get('name') or email

                # 处理头像
                avatar = ""
                try:
                    if user_info.get("avatar_url"):
                        from utils.file_utils import download_img
                        avatar = download_img(user_info["avatar_url"])
                except Exception as e:
                    logging.exception(f"下载头像失败: {e}")
                    avatar = ""

                # 创建新用户
                user = User.create_user(
                    user_id=user_id,
                    email=email,
                    nickname=nickname,
                    avatar=avatar,
                    access_token=ssoid,
                    login_channel="sso"
                )

                if not user:
                    raise Exception(f"注册失败：{email}")

                logging.warning(f"SSO新用户注册成功: {email}, user_id: {user.get_id()}")

            except Exception as e:
                logging.exception(f"SSO用户注册失败: {e}")
                return construct_json_result(
                    code=RetCode.EXCEPTION_ERROR,
                    message=f"SSO用户注册失败：{str(e)}"
                )
        else:
            # 用户已注册，更新信息
            try:
                # user.update_access_token(ssoid)
                # user.update_last_login()
                logging.warning(f"SSO用户信息更新成功: {email}, user_id: {user_id}")
            except Exception as e:
                logging.exception(f"更新用户信息失败: {e}")

        # 使用Flask-Login登录用户
        login_user(user)

        # 设置session信息
        session['user_id'] = user.get_id()
        session['user_email'] = user.email
        session['user_nickname'] = user.nickname
        session["access_token"] = ssoid
        session["access_token_from"] = "sso"

        # 重定向到前端页面，带上认证信息
        # 从session中获取之前保存的前端URL，如果没有则使用默认路径
        frontend_url = session.get('frontend_url')
        if frontend_url:
            redirect_url = f"{frontend_url}/?auth={user.get_id()}"
        else:
            # 使用默认的前端路径
            backend_url = request.host_url.rstrip('/')
            redirect_url = f"{backend_url}/index.html#/?auth={user.get_id()}"

        logging.warning(f"SSO用户登录成功: {email}, user_id: {user.get_id()}")
        logging.info(f"SSO登录成功，重定向到: {redirect_url}")

        return redirect(redirect_url)

    except Exception as e:
        logging.exception(f"SSO回调处理失败: {str(e)}")
        return construct_error_response(e)


@manager.route("/logout", methods=["POST", "GET"])
@login_required
@not_allowed_parameters('user_id', 'token')  # 防止恶意参数
def logout():
    """
    用户登出
    清除用户会话信息
    """
    try:
        user_info = current_user.to_json() if current_user.is_authenticated else None

        # 使用Flask-Login登出用户
        logout_user()

        # 清除session
        session.clear()

        logging.info(f"用户登出成功: {user_info}")
        return construct_json_result(
            data=True,
            message="登出成功"
        )

    except Exception as e:
        logging.exception(f"用户登出失败: {str(e)}")
        return construct_error_response(e)


@manager.route("/user_info", methods=["GET"])
@login_required
@not_allowed_parameters('user_id', 'token', 'password')  # 防止恶意参数
def get_user_info():
    """
    获取当前用户信息
    需要用户已登录
    """
    try:
        if current_user.is_authenticated:
            user_data = current_user.to_json()
            return construct_json_result(
                data=user_data,
                message="获取用户信息成功"
            )
        else:
            return construct_json_result(
                code=RetCode.AUTHENTICATION_ERROR,
                message="用户未登录"
            )

    except Exception as e:
        logging.exception(f"获取用户信息失败: {str(e)}")
        return construct_error_response(e)


@manager.route("/check_login", methods=["GET"])
@not_allowed_parameters('user_id', 'token', 'password')  # 防止恶意参数
def check_login():
    """
    检查用户登录状态
    不需要登录即可访问
    """
    try:
        if current_user.is_authenticated:
            return construct_json_result(
                data={
                    "is_logged_in": True,
                    "user": current_user.to_json()
                },
                message="用户已登录"
            )
        else:
            return construct_json_result(
                data={
                    "is_logged_in": False,
                    "user": None
                },
                message="用户未登录",
                status=401
            )

    except Exception as e:
        logging.exception(f"检查登录状态失败: {str(e)}")
        return construct_error_response(e)


@manager.route("/debug/users", methods=["GET"])
@not_allowed_parameters('user_id', 'token', 'password', 'email')  # 防止恶意参数
def debug_users():
    """
    调试接口：查看所有用户
    仅用于开发调试
    """
    try:
        # 在生产环境中应该添加管理员权限检查
        import os
        if os.getenv('INF_BOM_ENV', 'test') == 'prod':
            return construct_json_result(
                code=RetCode.AUTHORIZATION_ERROR,
                message="生产环境不允许访问此接口"
            )

        users = User.get_all_users()
        users_data = {user_id: user.to_json() for user_id, user in users.items()}

        return construct_json_result(
            data={
                "total": len(users),
                "users": users_data
            },
            message="获取用户列表成功"
        )

    except Exception as e:
        logging.exception(f"获取用户列表失败: {str(e)}")
        return construct_error_response(e)


@manager.route("/sso_config", methods=["GET"])
@not_allowed_parameters('secret', 'client_secret', 'password')  # 防止敏感信息泄露
def get_sso_config():
    """
    获取SSO配置信息（公开部分）
    用于前端配置SSO相关功能
    """
    try:
        from utils.sso_utils import get_sso_config

        config = get_sso_config()

        # 只返回公开的配置信息，隐藏敏感信息
        public_config = {
            "env": config.get("env", "test"),
            "client_id": config.get("client_id"),
            "sso_host": config.get("sso_host"),
            "login_uri": config.get("login_uri"),
            "logout_uri": config.get("logout_uri")
        }

        return construct_json_result(
            data=public_config,
            message="获取SSO配置成功"
        )

    except Exception as e:
        logging.exception(f"获取SSO配置失败: {str(e)}")
        return construct_error_response(e)


@manager.route("/refresh_token", methods=["POST"])
@login_required
@not_allowed_parameters('user_id', 'password')  # 防止恶意参数
def refresh_token():
    """
    刷新用户访问令牌
    需要用户已登录
    """
    try:
        if not current_user.is_authenticated:
            return construct_json_result(
                code=RetCode.AUTHENTICATION_ERROR,
                message="用户未登录"
            )

        # 更新用户的访问令牌
        current_user.update_access_token()

        # 更新session信息
        session['user_id'] = current_user.id
        session['user_email'] = current_user.email
        session['user_nickname'] = current_user.nickname

        logging.info(f"用户令牌刷新成功: {current_user.email}")

        return construct_json_result(
            data={
                "user": current_user.to_json(),
                "refreshed_at": current_user.updated_at.isoformat() if hasattr(current_user, 'updated_at') else None
            },
            message="令牌刷新成功"
        )

    except Exception as e:
        logging.exception(f"刷新令牌失败: {str(e)}")
        return construct_error_response(e)

