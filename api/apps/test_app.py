import asyncio
import os
from typing import Dict, Optional, Union, List

from flask import request, jsonify, Blueprint

from api.service.cr_lc_service import CrLCService
from api.service.daxiang_service import DaXiangService
from api.service.devmind_service import DevmindService
from api.service.git_service import GitService
from api.service.horn_service import HornService
from api.service.kms_service import KmsService

# 创建蓝图（这个变量会被__init__.py中的代码使用）
# manager = Blueprint('test', __name__)

# 初始化服务
horn_service = HornService()
kms_service = KmsService()
dx_service = DaXiangService(kms_service)
git_service = GitService()
devmind_service = DevmindService()
cr_lc_service = CrLCService(horn_service, kms_service, dx_service, git_service, devmind_service)


class TestSummaryInput:
    """测试总结输入参数类"""
    def __init__(
        self,
        review_result: Optional[str] = None,  # 可选的代码审查结果
        pr_link: Optional[str] = None,        # 可选的PR链接
        git_info: Optional[Dict[str, Union[str, List[str]]]] = None,  # 可选的Git信息
        use_default_md: bool = False          # 是否使用默认的no.md文件
    ):
        self.review_result = review_result
        self.pr_link = pr_link
        self.git_info = git_info
        self.use_default_md = use_default_md


@manager.route('/test_summary_review', methods=['POST'])
def test_summary_review():
    """
    测试代码审查总结功能

    ---
    tags:
      - Test
    parameters:
      - name: body
        in: body
        required: true
        schema:
          type: object
          properties:
            reviewResult:
              type: string
              description: 代码审查结果
            prLink:
              type: string
              description: PR链接
            gitInfo:
              type: object
              properties:
                repo:
                  type: string
                  description: 仓库名
                project:
                  type: string
                  description: 项目名
                fromBranch:
                  type: string
                  description: 源分支
                toBranch:
                  type: string
                  description: 目标分支
            useDefaultMd:
              type: boolean
              description: 是否使用默认的no.md文件
    responses:
      200:
        description: 成功
        schema:
          type: object
          properties:
            success:
              type: boolean
            data:
              type: object
      500:
        description: 失败
        schema:
          type: object
          properties:
            success:
              type: boolean
            error:
              type: string
    """
    try:
        # 获取请求体
        input_data = request.get_json()

        # 创建输入对象
        input_obj = TestSummaryInput(
            review_result=input_data.get('reviewResult'),
            pr_link=input_data.get('prLink'),
            git_info=input_data.get('gitInfo'),
            use_default_md=input_data.get('useDefaultMd', False)
        )

        # 获取代码审查结果
        review_result = None
        if input_obj.use_default_md:
            # 使用默认的no.md文件
            with open(os.path.join(os.path.dirname(__file__), '../service/no.md'), 'r', encoding='utf-8') as f:
                review_result = f.read()
        elif input_obj.review_result:
            # 使用请求中传入的reviewResult
            review_result = input_obj.review_result
        else:
            raise ValueError('必须提供reviewResult或设置useDefaultMd为true')

        # 获取PR链接
        pr_link = input_obj.pr_link or "https://code.sankuai.com/pr/123"

        # 获取Git信息
        task_git_info = input_obj.git_info or {
            "repo": "test-repo",
            "project": "test-project",
            "fromBranch": "feature/test",
            "toBranch": "master"
        }

        # 初始化CrLCService
        cr_lc_service.init_inf()

        # 创建事件循环并运行异步方法
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        result = loop.run_until_complete(cr_lc_service.summary_review2km_json(
            review_result,
            pr_link,
            task_git_info
        ))
        loop.close()

        return jsonify({
            "success": True,
            "data": result
        })
    except Exception as error:
        print(f'Test failed: {str(error)}')
        return jsonify({
            "success": False,
            "error": str(error) or 'Test failed'
        }), 500
