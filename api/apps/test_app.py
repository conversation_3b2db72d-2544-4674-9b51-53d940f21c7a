import asyncio
import os
from typing import Dict, Optional, Union, List


from flask import request, jsonify, Blueprint

# 使用新的服务架构
from services.cr_service import CRService
from core.service_registry import (
    get_service, register_service,
    get_git_service, get_kms_service
)

# 兼容性导入 - 如果需要使用旧服务
try:
    from api.service.daxiang_service import DaXiangService
    from api.service.devmind_service import DevmindService
    from api.service.horn_service import HornService
    LEGACY_SERVICES_AVAILABLE = True
except ImportError:
    LEGACY_SERVICES_AVAILABLE = False
    DaXiangService = None
    DevmindService = None
    HornService = None

try:
    import dotenv
    dotenv.load_dotenv()
    DOTENV_AVAILABLE = True
except ImportError:
    DOTENV_AVAILABLE = False

# 初始化服务 - 使用新架构
def initialize_services():
    """初始化服务实例"""
    global cr_service, horn_service, kms_service, dx_service, git_service, devmind_service

    # 使用新的服务注册表获取服务
    cr_service = get_service('cr_service')
    if not cr_service:
        # 如果没有找到，创建新的CR服务实例
        cr_service = CRService()
        register_service('cr_service', cr_service)

    # 获取其他服务
    git_service = get_git_service()
    kms_service = get_kms_service()

    # 如果需要兼容旧服务，创建它们
    if LEGACY_SERVICES_AVAILABLE:
        horn_service = HornService()
        dx_service = DaXiangService(kms_service) if kms_service else None

        # DevMind服务需要环境变量
        dm_base_url = os.environ.get("DM_BASE_URL")
        dm_api_key = os.environ.get("DM_API_KEY")
        if dm_base_url and dm_api_key:
            devmind_service = DevmindService(base_url=dm_base_url, api_key=dm_api_key)
        else:
            devmind_service = None
    else:
        horn_service = None
        dx_service = None
        devmind_service = None

# 初始化服务
initialize_services()


class TestSummaryInput:
    """测试总结输入参数类"""
    def __init__(
        self,
        review_result: Optional[str] = None,  # 可选的代码审查结果
        pr_link: Optional[str] = None,        # 可选的PR链接
        git_info: Optional[Dict[str, Union[str, List[str]]]] = None,  # 可选的Git信息
        use_default_md: bool = False          # 是否使用默认的no.md文件
    ):
        self.review_result = review_result
        self.pr_link = pr_link
        self.git_info = git_info
        self.use_default_md = use_default_md


@manager.route('/test_summary_review', methods=['POST'])
def test_summary_review():
    """
    测试代码审查总结功能

    ---
    tags:
      - Test
    parameters:
      - name: body
        in: body
        required: true
        schema:
          type: object
          properties:
            reviewResult:
              type: string
              description: 代码审查结果
            prLink:
              type: string
              description: PR链接
            gitInfo:
              type: object
              properties:
                repo:
                  type: string
                  description: 仓库名
                project:
                  type: string
                  description: 项目名
                fromBranch:
                  type: string
                  description: 源分支
                toBranch:
                  type: string
                  description: 目标分支
            useDefaultMd:
              type: boolean
              description: 是否使用默认的no.md文件
    responses:
      200:
        description: 成功
        schema:
          type: object
          properties:
            success:
              type: boolean
            data:
              type: object
      500:
        description: 失败
        schema:
          type: object
          properties:
            success:
              type: boolean
            error:
              type: string
    """
    try:
        # 获取请求体
        input_data = request.get_json()

        # 创建输入对象
        input_obj = TestSummaryInput(
            review_result=input_data.get('reviewResult'),
            pr_link=input_data.get('prLink'),
            git_info=input_data.get('gitInfo'),
            use_default_md=input_data.get('useDefaultMd', False)
        )

        # 获取代码审查结果
        review_result = None
        if input_obj.use_default_md:
            # 使用默认的no.md文件
            with open(os.path.join(os.path.dirname(__file__), '../service/no.md'), 'r', encoding='utf-8') as f:
                review_result = f.read()
        elif input_obj.review_result:
            # 使用请求中传入的reviewResult
            review_result = input_obj.review_result
        else:
            raise ValueError('必须提供reviewResult或设置useDefaultMd为true')

        # 获取PR链接
        pr_link = input_obj.pr_link or "https://code.sankuai.com/pr/123"

        # 获取Git信息
        task_git_info = input_obj.git_info or {
            "repo": "test-repo",
            "project": "test-project",
            "fromBranch": "feature/test",
            "toBranch": "master"
        }

        # 初始化CRService（新架构）
        if hasattr(cr_service, 'init_inf'):
            cr_service.init_inf()
        elif hasattr(cr_service, 'initialize'):
            # 使用新架构的初始化方法
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(cr_service.initialize())
            loop.close()

        # 调用CR服务方法
        if hasattr(cr_service, 'summary_review2km_json'):
            # 使用旧方法
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            result = loop.run_until_complete(cr_service.summary_review2km_json(
                review_result,
                pr_link,
                task_git_info
            ))
            loop.close()
        else:
            # 新架构暂时不支持此方法，返回模拟结果
            result = {
                "success": True,
                "message": "新架构CR服务已创建，但summary_review2km_json方法需要在后续版本中实现",
                "data": {
                    "review_result": review_result[:100] + "..." if len(review_result) > 100 else review_result,
                    "pr_link": pr_link,
                    "git_info": task_git_info,
                    "service_stats": cr_service.get_service_stats() if hasattr(cr_service, 'get_service_stats') else {}
                }
            }

        return jsonify({
            "success": True,
            "data": result
        })
    except Exception as error:
        print(f'Test failed: {str(error)}')
        return jsonify({
            "success": False,
            "error": str(error) or 'Test failed'
        }), 500
