"""
MCP工具集API

按照第一阶段架构重构要求和现有apps目录结构实现
提供统一的RESTful API接口，支持MCP工具集成
"""

import json
import uuid
import asyncio
from datetime import datetime
from typing import Any, Dict, List, Optional
from flask import request, jsonify, g

# 导入配置和服务
from config.base_config import BaseConfig, MCPConfig
from services.mcp_service import MCPService
from services.external.mcp_adapter import MCPAdapter, service_registry

# 导入现有服务
from api.service.daxiang_service import DaXiangService
from api.service.chunk_service import ChunkService
from api.service.git_service import GitService
from api.service.km_service import KmService
from api.service.devtools_service import DevtoolsService
from api.service.kms_service import KmsService

# 设置页面名称，用于URL前缀
page_name = "mcp-tools"

# 全局配置和服务实例
config = BaseConfig.load_from_env()
mcp_service = None
mcp_adapter = None

# 初始化现有服务
kms_service = KmsService()
daxiang_service = DaXiangService(kms_service)
git_service = GitService()
km_service = KmService()
devtools_service = DevtoolsService()


def init_mcp_services():
    """初始化MCP服务"""
    global mcp_service, mcp_adapter
    
    try:
        mcp_service = MCPService(config.mcp)
        mcp_adapter = MCPAdapter(mcp_service, service_registry.get_all_mappings())
        
        # 异步初始化MCP服务
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(mcp_service.start())
        finally:
            loop.close()
            
    except Exception as e:
        print(f"Failed to initialize MCP services: {e}")


# 初始化MCP服务
init_mcp_services()


def success_response(data: Any = None, service: str = "mcp-tools") -> Dict:
    """统一成功响应格式"""
    return {
        "success": True,
        "data": data,
        "error": None,
        "metadata": {
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "request_id": str(uuid.uuid4()),
            "service": service,
            "version": "1.0.0"
        }
    }


def error_response(code: str, message: str, details: Optional[Dict] = None, 
                  service: str = "mcp-tools", status_code: int = 400) -> tuple:
    """统一错误响应格式"""
    response = {
        "success": False,
        "data": None,
        "error": {
            "code": code,
            "message": message,
            "details": details or {}
        },
        "metadata": {
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "request_id": str(uuid.uuid4()),
            "service": service,
            "version": "1.0.0"
        }
    }
    return jsonify(response), status_code


def validate_required_fields(data: Dict, required_fields: List[str]) -> Optional[tuple]:
    """验证必需字段"""
    missing_fields = [field for field in required_fields if field not in data or data[field] is None]
    if missing_fields:
        return error_response(
            code="MISSING_REQUIRED_FIELDS",
            message=f"Missing required fields: {', '.join(missing_fields)}",
            details={"missing_fields": missing_fields}
        )
    return None


def async_route(f):
    """异步路由装饰器"""
    def wrapper(*args, **kwargs):
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            return loop.run_until_complete(f(*args, **kwargs))
        finally:
            loop.close()
    wrapper.__name__ = f.__name__
    return wrapper


# ==================== MCP服务管理API ====================

@manager.route('/mcp/status', methods=['GET'])
@async_route
async def mcp_status():
    """获取MCP服务状态"""
    try:
        if not mcp_service:
            return jsonify(success_response({
                "status": "not_initialized",
                "message": "MCP service not initialized"
            }, "mcp"))
        
        health_info = await mcp_service.health_check()
        service_info = mcp_service.get_service_info()
        
        return jsonify(success_response({
            "service_info": service_info,
            "health_check": health_info,
            "available_tools": mcp_service.get_available_tools(),
            "tool_mappings": service_registry.get_all_mappings()
        }, "mcp"))
        
    except Exception as e:
        return error_response("INTERNAL_ERROR", str(e), service="mcp", status_code=500)


@manager.route('/mcp/tools', methods=['GET'])
def mcp_list_tools():
    """列出所有可用的MCP工具"""
    try:
        if not mcp_service:
            return jsonify(success_response({
                "tools": [],
                "message": "MCP service not initialized"
            }, "mcp"))
        
        available_tools = mcp_service.get_available_tools()
        tool_details = []
        
        for tool_name in available_tools:
            tool_info = mcp_service.get_tool_info(tool_name)
            if tool_info:
                tool_details.append(tool_info)
        
        return jsonify(success_response({
            "tools": tool_details,
            "count": len(tool_details),
            "service_mappings": service_registry.list_services()
        }, "mcp"))
        
    except Exception as e:
        return error_response("INTERNAL_ERROR", str(e), service="mcp", status_code=500)


@manager.route('/mcp/call', methods=['POST'])
@async_route
async def mcp_call_tool():
    """直接调用MCP工具"""
    try:
        data = request.get_json()
        if not data:
            return error_response("INVALID_JSON", "Request body must be valid JSON")
        
        # 验证必需字段
        validation_error = validate_required_fields(data, ['tool_name'])
        if validation_error:
            return validation_error
        
        tool_name = data['tool_name']
        arguments = data.get('arguments', {})
        
        if not mcp_service:
            return error_response("SERVICE_NOT_AVAILABLE", "MCP service not initialized")
        
        result = await mcp_service.call_tool(tool_name, arguments)
        
        return jsonify(success_response(result, "mcp"))
        
    except Exception as e:
        return error_response("INTERNAL_ERROR", str(e), service="mcp", status_code=500)


# ==================== 大象通知服务API ====================

@manager.route('/daxiang/auth/token', methods=['POST'])
def daxiang_get_access_token():
    """获取大象开放平台访问令牌"""
    try:
        data = request.get_json() or {}
        context = data.get('context', {})
        
        access_token = daxiang_service.get_access_token(context)
        
        if access_token:
            return jsonify(success_response({
                "access_token": access_token,
                "token_type": "Bearer",
                "expires_in": 7200
            }, "daxiang"))
        else:
            return jsonify(success_response({
                "access_token": None,
                "error": "Failed to obtain access token"
            }, "daxiang"))
            
    except Exception as e:
        return error_response("INTERNAL_ERROR", str(e), service="daxiang", status_code=500)


@manager.route('/daxiang/users/convert', methods=['POST'])
def daxiang_convert_user_ids():
    """用户ID转换"""
    try:
        data = request.get_json()
        if not data:
            return error_response("INVALID_JSON", "Request body must be valid JSON")
        
        # 验证必需字段
        validation_error = validate_required_fields(data, ['conversion_type', 'ids'])
        if validation_error:
            return validation_error
        
        conversion_type = data['conversion_type']
        ids = data['ids']
        context = data.get('context', {})
        
        if conversion_type == "emp_to_user":
            result = daxiang_service.convert_emp_id_to_user_id(ids, context)
        elif conversion_type == "user_to_emp":
            result = daxiang_service.convert_userid_to_emp_id(ids, context)
        else:
            return error_response("INVALID_CONVERSION_TYPE", f"Invalid conversion_type: {conversion_type}")
        
        if result:
            # 处理转换结果
            if isinstance(result, list):
                conversions = {ids[i]: result[i] for i in range(min(len(ids), len(result)))}
                failed = ids[len(result):] if len(ids) > len(result) else []
            else:
                conversions = result if isinstance(result, dict) else {}
                failed = [id for id in ids if id not in conversions]
            
            return jsonify(success_response({
                "conversions": conversions,
                "failed": failed,
                "conversion_type": conversion_type
            }, "daxiang"))
        else:
            return jsonify(success_response({
                "conversions": {},
                "failed": ids,
                "error": "Conversion failed"
            }, "daxiang"))
            
    except Exception as e:
        return error_response("INTERNAL_ERROR", str(e), service="daxiang", status_code=500)


# ==================== 代码分块服务API ====================

@manager.route('/chunk/file', methods=['POST'])
def chunk_file():
    """单文件代码分块"""
    try:
        data = request.get_json()
        if not data:
            return error_response("INVALID_JSON", "Request body must be valid JSON")
        
        # 验证必需字段
        validation_error = validate_required_fields(data, ['project', 'repo', 'file_path'])
        if validation_error:
            return validation_error
        
        project = data['project']
        repo = data['repo']
        file_path = data['file_path']
        branch = data.get('branch', 'main')
        
        chunk_service = ChunkService(git_service, project, repo, branch)
        chunks = chunk_service.chunk_code_file(file_path)
        
        # 处理文件信息
        file_info = {
            "path": file_path,
            "language": detect_language(file_path),
            "total_lines": sum(chunk.get('end_line', 0) - chunk.get('start_line', 0) + 1 for chunk in chunks)
        }
        
        return jsonify(success_response({
            "chunks": chunks,
            "file_info": file_info,
            "chunk_count": len(chunks)
        }, "chunk"))
        
    except Exception as e:
        return error_response("INTERNAL_ERROR", str(e), service="chunk", status_code=500)


@manager.route('/chunk/batch', methods=['POST'])
def chunk_batch_files():
    """批量文件分块"""
    try:
        data = request.get_json()
        if not data:
            return error_response("INVALID_JSON", "Request body must be valid JSON")

        # 验证必需字段
        validation_error = validate_required_fields(data, ['project', 'repo'])
        if validation_error:
            return validation_error

        project = data['project']
        repo = data['repo']
        branch = data.get('branch', 'main')
        file_patterns = data.get('file_patterns', ['*.py'])
        options = data.get('options', {})

        chunk_service = ChunkService(git_service, project, repo, branch)

        # 转换文件模式为后缀列表
        suffixes = []
        for pattern in file_patterns:
            if pattern.startswith('*.'):
                suffixes.append(pattern[1:])  # 移除 '*'
            else:
                suffixes.append(pattern)

        resolve_code = options.get('resolve_code', True)
        chunks = chunk_service.chunk_all_files(suffixes, resolve_code)

        # 限制返回的文件数量
        max_files = options.get('max_files', 100)
        if len(chunks) > max_files:
            chunks = chunks[:max_files]

        # 统计信息
        file_stats = {}
        for chunk in chunks:
            file_path = chunk.get('file_path', 'unknown')
            if file_path not in file_stats:
                file_stats[file_path] = 0
            file_stats[file_path] += 1

        return jsonify(success_response({
            "chunks": chunks,
            "statistics": {
                "total_chunks": len(chunks),
                "total_files": len(file_stats),
                "files_processed": list(file_stats.keys())[:10],
                "chunks_per_file": file_stats
            }
        }, "chunk"))

    except Exception as e:
        return error_response("INTERNAL_ERROR", str(e), service="chunk", status_code=500)


# ==================== Git操作服务API ====================

@manager.route('/git/diff', methods=['POST'])
def git_get_diff():
    """获取代码差异"""
    try:
        data = request.get_json()
        if not data:
            return error_response("INVALID_JSON", "Request body must be valid JSON")

        # 验证必需字段
        validation_error = validate_required_fields(data, ['project', 'repo', 'from_ref', 'to_ref'])
        if validation_error:
            return validation_error

        project = data['project']
        repo = data['repo']
        from_ref = data['from_ref']
        to_ref = data['to_ref']

        diff_content = git_service.get_diff(project, repo, from_ref, to_ref)

        if diff_content is None:
            return jsonify(success_response({
                "diff_content": "",
                "statistics": {"files_changed": 0, "insertions": 0, "deletions": 0},
                "files": []
            }, "git"))

        # 分析diff统计信息
        stats = analyze_diff_stats(diff_content)

        return jsonify(success_response({
            "diff_content": diff_content,
            "statistics": stats,
            "files": extract_changed_files(diff_content),
            "refs": {"from": from_ref, "to": to_ref}
        }, "git"))

    except Exception as e:
        return error_response("INTERNAL_ERROR", str(e), service="git", status_code=500)


@manager.route('/git/files', methods=['GET'])
def git_list_files():
    """获取文件列表"""
    try:
        project = request.args.get('project')
        repo = request.args.get('repo')
        branch = request.args.get('branch')
        suffixes = request.args.getlist('suffixes')
        limit = int(request.args.get('limit', 1000))

        if not project or not repo:
            return error_response("MISSING_PARAMETERS", "project and repo parameters are required")

        files = git_service.list_repo_files(
            project, repo, branch, suffixes if suffixes else None
        )

        # 应用限制
        total_count = len(files)
        if limit and len(files) > limit:
            files = files[:limit]

        # 生成文件信息
        file_info = []
        for file_path in files:
            file_info.append({
                "path": file_path,
                "type": "file",
                "language": detect_language(file_path)
            })

        return jsonify(success_response({
            "files": file_info,
            "total_count": total_count,
            "filtered_count": len(file_info),
            "filters": {"branch": branch, "suffixes": suffixes, "limit": limit}
        }, "git"))

    except Exception as e:
        return error_response("INTERNAL_ERROR", str(e), service="git", status_code=500)


# ==================== 健康检查和工具清单API ====================

@manager.route('/health', methods=['GET'])
@async_route
async def health_check():
    """健康检查"""
    try:
        services_status = {
            "daxiang": "available" if daxiang_service else "unavailable",
            "git": "available" if git_service else "unavailable",
            "km": "available" if km_service else "unavailable",
            "devtools": "available" if devtools_service else "unavailable",
            "kms": "available" if kms_service else "unavailable"
        }

        # 检查MCP服务状态
        if mcp_service:
            mcp_health = await mcp_service.health_check()
            services_status["mcp"] = mcp_health.get("status", "unknown")
        else:
            services_status["mcp"] = "not_initialized"

        overall_status = "healthy" if all(status in ["available", "healthy"] for status in services_status.values()) else "degraded"

        return jsonify(success_response({
            "status": overall_status,
            "services": services_status,
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "config": {
                "mcp_enabled": config.mcp.enabled,
                "api_version": "1.0.0"
            }
        }))

    except Exception as e:
        return error_response("INTERNAL_ERROR", str(e), status_code=500)


@manager.route('/tools/manifest', methods=['GET'])
def get_tools_manifest():
    """获取MCP工具清单"""
    try:
        tools = [
            {
                "name": "daxiang_get_access_token",
                "description": "获取大象开放平台访问令牌",
                "category": "notification",
                "endpoint": "/daxiang/auth/token",
                "method": "POST",
                "input_schema": {
                    "type": "object",
                    "properties": {
                        "context": {"type": "object", "description": "请求上下文"}
                    }
                }
            },
            {
                "name": "daxiang_convert_user_ids",
                "description": "在员工ID和用户ID之间进行转换",
                "category": "notification",
                "endpoint": "/daxiang/users/convert",
                "method": "POST",
                "input_schema": {
                    "type": "object",
                    "properties": {
                        "conversion_type": {"type": "string", "enum": ["emp_to_user", "user_to_emp"]},
                        "ids": {"type": "array", "items": {"type": "string"}},
                        "context": {"type": "object"}
                    },
                    "required": ["conversion_type", "ids"]
                }
            },
            {
                "name": "chunk_file",
                "description": "对单个代码文件进行分块分析",
                "category": "code_analysis",
                "endpoint": "/chunk/file",
                "method": "POST",
                "input_schema": {
                    "type": "object",
                    "properties": {
                        "project": {"type": "string"},
                        "repo": {"type": "string"},
                        "file_path": {"type": "string"},
                        "branch": {"type": "string", "default": "main"}
                    },
                    "required": ["project", "repo", "file_path"]
                }
            },
            {
                "name": "chunk_batch_files",
                "description": "对多个文件进行批量分块分析",
                "category": "code_analysis",
                "endpoint": "/chunk/batch",
                "method": "POST",
                "input_schema": {
                    "type": "object",
                    "properties": {
                        "project": {"type": "string"},
                        "repo": {"type": "string"},
                        "branch": {"type": "string", "default": "main"},
                        "file_patterns": {"type": "array", "items": {"type": "string"}},
                        "options": {"type": "object"}
                    },
                    "required": ["project", "repo"]
                }
            },
            {
                "name": "git_get_diff",
                "description": "获取Git仓库两个引用之间的代码差异",
                "category": "git",
                "endpoint": "/git/diff",
                "method": "POST",
                "input_schema": {
                    "type": "object",
                    "properties": {
                        "project": {"type": "string"},
                        "repo": {"type": "string"},
                        "from_ref": {"type": "string"},
                        "to_ref": {"type": "string"}
                    },
                    "required": ["project", "repo", "from_ref", "to_ref"]
                }
            },
            {
                "name": "git_list_files",
                "description": "获取Git仓库的文件列表",
                "category": "git",
                "endpoint": "/git/files",
                "method": "GET",
                "input_schema": {
                    "type": "object",
                    "properties": {
                        "project": {"type": "string"},
                        "repo": {"type": "string"},
                        "branch": {"type": "string"},
                        "suffixes": {"type": "array", "items": {"type": "string"}},
                        "limit": {"type": "integer", "default": 1000}
                    },
                    "required": ["project", "repo"]
                }
            }
        ]

        # 按类别分组
        categories = {}
        for tool in tools:
            category = tool['category']
            if category not in categories:
                categories[category] = []
            categories[category].append(tool)

        return jsonify(success_response({
            "tools": tools,
            "categories": categories,
            "total_count": len(tools),
            "version": "1.0.0",
            "description": "Shangou AI CR MCP Tools"
        }))

    except Exception as e:
        return error_response("INTERNAL_ERROR", str(e), status_code=500)


# ==================== 工具函数 ====================

def detect_language(file_path: str) -> Optional[str]:
    """检测文件语言"""
    if file_path.endswith('.py'):
        return 'python'
    elif file_path.endswith(('.js', '.jsx')):
        return 'javascript'
    elif file_path.endswith(('.ts', '.tsx')):
        return 'typescript'
    elif file_path.endswith('.java'):
        return 'java'
    elif file_path.endswith(('.cpp', '.cc', '.cxx')):
        return 'cpp'
    elif file_path.endswith('.c'):
        return 'c'
    elif file_path.endswith('.go'):
        return 'go'
    elif file_path.endswith('.rs'):
        return 'rust'
    else:
        return 'unknown'


def analyze_diff_stats(diff_content: str) -> Dict[str, int]:
    """分析diff统计信息"""
    if not diff_content:
        return {"files_changed": 0, "insertions": 0, "deletions": 0}

    lines = diff_content.split('\n')
    files_changed = set()
    insertions = 0
    deletions = 0

    for line in lines:
        if line.startswith('+++') or line.startswith('---'):
            if line.startswith('+++') and not line.endswith('/dev/null'):
                file_path = line[4:].strip()
                if file_path.startswith('b/'):
                    file_path = file_path[2:]
                files_changed.add(file_path)
        elif line.startswith('+') and not line.startswith('+++'):
            insertions += 1
        elif line.startswith('-') and not line.startswith('---'):
            deletions += 1

    return {
        "files_changed": len(files_changed),
        "insertions": insertions,
        "deletions": deletions
    }


def extract_changed_files(diff_content: str) -> List[Dict[str, Any]]:
    """提取变更文件列表"""
    if not diff_content:
        return []

    lines = diff_content.split('\n')
    files = []
    current_file = None

    for line in lines:
        if line.startswith('+++') and not line.endswith('/dev/null'):
            file_path = line[4:].strip()
            if file_path.startswith('b/'):
                file_path = file_path[2:]

            current_file = {
                "path": file_path,
                "status": "modified",
                "changes": 0
            }
            files.append(current_file)
        elif current_file and (line.startswith('+') or line.startswith('-')):
            if not line.startswith('+++') and not line.startswith('---'):
                current_file["changes"] += 1

    return files
