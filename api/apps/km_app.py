import asyncio
from flask import request, jsonify

# 使用新的服务注册表
from core.service_registry import get_km_service

# 导入CreateDocParams
try:
    from services.km_service import CreateDocParams
except ImportError:
    # 如果导入失败，定义基础的CreateDocParams类
    class CreateDocParams:
        def __init__(self, **kwargs):
            for key, value in kwargs.items():
                setattr(self, key, value)

        def to_dict(self):
            return {k: v for k, v in self.__dict__.items() if not k.startswith('_')}

def normalize_params(params):
    content = params.get('content')
    copy_from_content_id = params.get('copyFromContentId')
    operator_emp_id = params.get('operatorEmpId')
    parent_id = params.get('parentId')
    space_id = params.get('spaceId')
    template_id = params.get('templateId')
    title = params.get('title', f'标题-{int(__import__("time").time())}')
    if not (content or copy_from_content_id or template_id):
        raise ValueError('content, copyFromContentId, templateId 不能都为空')
    return {
        'content': content,
        'copyFromContentId': copy_from_content_id,
        'operatorEmpId': operator_emp_id,
        'parentId': parent_id,
        'spaceId': space_id,
        'templateId': template_id,
        'title': title,
    }

# Flask风格API
@manager.route('/create_doc', methods=['POST'])
def create_doc():
    try:
        params = normalize_params(request.get_json())

        # 创建CreateDocParams对象
        doc_params = CreateDocParams(**params)

        # 获取KM服务实例
        km_service = get_km_service()

        # 调用km_service.create_doc实现真实业务
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            doc_url = loop.run_until_complete(km_service.create_doc(request, doc_params))
            return jsonify({'code': 0, 'message': 'success', 'data': doc_url})
        finally:
            loop.close()

    except Exception as err:
        return jsonify({'code': 1, 'message': str(err), 'data': None}), 500