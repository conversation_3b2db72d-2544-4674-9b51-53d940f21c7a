from flask import request, jsonify
# from api.service.open_km_service import KmService

# open_km_service = KmService()

def normalize_params(params):
    content = params.get('content')
    copy_from_content_id = params.get('copyFromContentId')
    operator_emp_id = params.get('operatorEmpId')
    parent_id = params.get('parentId')
    space_id = params.get('spaceId')
    template_id = params.get('templateId')
    title = params.get('title', f'标题-{int(__import__("time").time())}')
    if not (content or copy_from_content_id or template_id):
        raise ValueError('content, copyFromContentId, templateId 不能都为空')
    return {
        'content': content,
        'copyFromContentId': copy_from_content_id,
        'operatorEmpId': operator_emp_id,
        'parentId': parent_id,
        'spaceId': space_id,
        'templateId': template_id,
        'title': title,
    }

# Flask风格API
@manager.route('/create_doc', methods=['POST'])
def create_doc():
    try:
        params = normalize_params(request.get_json())
        # TODO: 调用km_service.create_doc实现真实业务
        doc_url = f'https://km.sankuai.com/collabpage/mock_id'  # mock
        return jsonify({'code': 0, 'message': 'success', 'data': doc_url})
    except Exception as err:
        return jsonify({'code': 1, 'message': str(err), 'data': None}), 500 