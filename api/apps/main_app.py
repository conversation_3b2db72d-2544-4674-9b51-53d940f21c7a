import asyncio
import json
import math
import urllib.parse
from datetime import datetime

from flask import request, jsonify

from api.service.cr_lc_service import CrLCService
from api.service.daxiang_service import DaXiangService
from api.service.devmind_service import DevmindService
from api.service.devtools_service import DevtoolsService
from api.service.git_service import GitService
from api.service.horn_service import HornService
from api.service.km_service import KmService
from api.service.kms_service import KmsService
from api.service.org_service import OrgService
from api.service.sso_service import SsoService
from services.km_service import CreateDocParams
from utils.report2raptor_utils import report2raptor

kms_service = KmsService()
sso_service = SsoService()
horn_service = HornService()
dx_service = DaXiangService(kms_service)
git_service = GitService()
devmind_service = DevmindService()
cr_lc_service = CrLCService(horn_service, kms_service, dx_service, git_service, devmind_service)
org_service = OrgService()
km_service = KmService()
devtools_service = DevtoolsService()


# open_km_service = KmService()
# org_service = OrgService()
@manager.route('/sso', methods=['GET'])
def sso_handler():
    try:
        # 获取SSO信息
        result = sso_service.get_user_info(request)
        return jsonify(result)
    except Exception as error:
        return jsonify({'success': False, 'error': str(error)})


@manager.route('/emp_info', methods=['GET'])
def get_emp_info():
    try:
        # 获取员工信息
        result = org_service.get_emp_info(request.headers.__dict__, request.args)
        return jsonify({'code': 0, 'data': result, 'message': 'success'})
    except Exception as error:
        return jsonify({'code': -1, 'data': None, 'message': str(error)})


@manager.route('/base_inf_demo', methods=['GET'])
def base_inf_demo_handler():
    # 示例：调用kms_service、horn_service、sso_service
    kms_demo = kms_service.get_key('com.sankuai.yunzhuan.devhelper', 'modal_app_id')
    horn_demo = horn_service.fetch_config('CR_CHECKER')
    sso_demo = sso_service.get_user_info(request)
    access_token_result = sso_service.get_access_token(request)
    access_token = access_token_result.get('data', 'mock_token')
    result = {
        'kmsDemo': kms_demo.get('data'),
        'hornDemo': horn_demo,
        'ssoDemo': sso_demo,
        'accessToken': access_token
    }
    return jsonify(result)


@manager.route('/horn_config', methods=['GET'])
def get_horn_handler():
    horn_data = horn_service.fetch_config('CR_CHECKER')
    return jsonify(horn_data)


def create_pr(req, cr_params, header_info):
    """创建PR并返回PR链接"""
    try:
        project = cr_params.get('project') or req.args.get('project')
        repo = cr_params.get('repo') or req.args.get('repo')
        from_branch = urllib.parse.unquote(cr_params.get('fromBranch') or req.args.get('fromBranch'))
        to_branch = urllib.parse.unquote(cr_params.get('toBranch') or req.args.get('toBranch'))
        title = cr_params.get('title', '云篆自动创建PR')

        # 构建URL和参数
        url = f"/rest/api/2.0/projects/{project}/repos/{repo}/pull-requests"
        params = {
            "title": title,
            "fromRef": {
                "id": f"refs/heads/{from_branch}",
                "displayId": from_branch,
                "repository": {
                    "slug": repo,
                    "project": {
                        "key": project
                    }
                }
            },
            "toRef": {
                "id": f"refs/heads/{to_branch}",
                "displayId": to_branch,
                "repository": {
                    "slug": repo,
                    "project": {
                        "key": project
                    }
                }
            }
        }

        # 调用DevtoolsService创建PR
        pr_id = devtools_service.create_pr(url, params, header_info)
        pr_url = f'https://dev.sankuai.com/code/repo-detail/{project}/{repo}/pr/{pr_id}'

        return pr_url
    except Exception as e:
        print(f"创建PR失败: {str(e)}")
        raise e


def get_branch_diff(request, cr_params):
    """获取分支差异"""
    try:
        project = cr_params.get('project') or request.args.get('project')
        repo = cr_params.get('repo') or request.args.get('repo')
        from_branch = cr_params.get('fromBranch') or request.args.get('fromBranch')
        to_branch = cr_params.get('toBranch') or request.args.get('toBranch')
        print(f"获取分支差异: {project}, {repo}, {from_branch}, {to_branch}")
        diff = git_service.get_diff(project, repo, from_branch, to_branch)
        return diff
    except Exception as e:
        print(f"获取分支差异失败: {str(e)}")
        raise e


@manager.route('/cr_lc', methods=['POST'])
def cr_lc_handler():
    cr_params = request.get_json()
    # header_info = {"mis": request.headers.get("mis"), "ssoid": request.headers.get("ssoid")}
    header_info = {}
    try:
        # 初始化服务
        cr_lc_service.init_inf()

        # 创建PR
        pr_id = cr_params.get('pr_id') or request.args.get('pr_id')
        pr_project = cr_params.get('project') or request.args.get('project')
        pr_repo = request.args.get('repo') or cr_params.get('repo')
        pr_from_branch = request.args.get('fromBranch') or cr_params.get('fromBranch')
        pr_to_branch = request.args.get('toBranch') or cr_params.get('toBranch')
        pr_link = f'https://dev.sankuai.com/code/repo-detail/{pr_project}/{pr_repo}/pr/{pr_id}' if pr_id else create_pr(request, cr_params,  header_info)
        print(f"prLink: {pr_link}")
        # 创建 taskGitInfo
        task_git_info = {
            'project': pr_project,
            'repo': pr_repo,
            'fromBranch': pr_from_branch,
            'toBranch': pr_to_branch
        }

        # 发送PR链接消息
        # loop.run_until_complete(dx_service.send_chat_msg_by_robot(request, {
        #     'text': f"pr地址为：{pr_link}",
        #     'uid': cr_params.get('uid'),
        #     'empId': cr_params.get('empId')
        # }))
        # 获取代码差异
        code_diff = get_branch_diff(request, cr_params)
        print(f"codeDiff: {code_diff}")
        diff_lines = len(code_diff.split('\n')) if code_diff else 0

        # 发送获取差异成功消息
        # loop.run_until_complete(dx_service.send_chat_msg_by_robot(request, {
        #     'text': f"已成功获取到code diff内容，开始进行代码审查，预计需要{math.ceil(diff_lines / 400) * 0.2}分钟...",
        #     'uid': cr_params.get('uid'),
        #     'empId': cr_params.get('empId')
        # }))
        # 使用LangChain处理
        cr_params_with_pr_link = {
            **cr_params,
            'prLink': pr_link,
            'taskGitInfo': task_git_info
        }
        # 处理代码评审请求
        cr_result = cr_lc_service.process_cr_request(request, code_diff, cr_params_with_pr_link)
        print(f"crResult: {json.dumps(cr_result)}")

        # 发送生成文档消息
        # loop.run_until_complete(dx_service.send_chat_msg_by_robot(request, {
        #     'text': "正在总结提炼cr结果，开始生成学城文档...",
        #     'uid': cr_params.get('uid'),
        #     'empId': cr_params.get('empId')
        # }))
        # 上报指标
        report2raptor(
            name="gen-cr-doc",
            value=1,
            tags={
                'totalProblem': cr_result.get('totalProblem'),
                'sumCheckResult': cr_result.get('sumCheckResult'),
                'checkBranch': cr_result.get('checkBranch')
            },
            msg="生成cr报告成功"
        )
        # 生成评审模板
        tpl_result = cr_lc_service.generate_cr_tpl(cr_result)
        # 创建学城文档
        doc_params = CreateDocParams(
            title=request.json.get('kmTitle', f"代码审查报告-{datetime.now().strftime('%Y-%m-%d')}"),
            content=json.dumps(tpl_result),
            operator_emp_id=cr_params.get('empId') or request.headers.get('appfactory-context-user-id'),
            parent_id=cr_params.get('parentId') or request.args.get('parentId') or request.json.get('parentId')
        )

        loop = asyncio.new_event_loop()

        # 创建文档
        doc_url = loop.run_until_complete(km_service.create_doc(request, doc_params))
        print(f"学成文档创建成功: {doc_url}")

        # 发送文档创建成功消息
        loop.run_until_complete(dx_service.send_chat_msg_by_robot(request, {
            'text': f"代码审查结果文档创建成功，请查看：{doc_url}，如需反馈请点击[此处|https://km.sankuai.com/collabpage/2706453728]",
            'uid': cr_params.get('uid'),
            'empId': cr_params.get('empId')
        }))

        # 关闭事件循环
        loop.close()
        # 上报指标
        report2raptor(
            name="summary-cr",
            value=1,
            tags={
                'docUrl': doc_url,
                'totalProblem': cr_result.get('totalProblem'),
                'misId': cr_params.get('empId') or request.headers.get('appfactory-context-user-id')
            },
            msg="生成cr报告成功"
        )
        return jsonify({'crResult': cr_result, 'docUrl': doc_url})
    except Exception as error:
        # 处理特定错误
        if hasattr(error, 'code') and error.code == 409:
            from_branch = cr_params.get('fromBranch') or request.args.get('fromBranch') or request.json.get('fromBranch')
            to_branch = cr_params.get('toBranch') or request.args.get('toBranch') or request.json.get('toBranch')
            from_branch_decoded = from_branch  # 在Python中不需要解码
            to_branch_decoded = to_branch  # 在Python中不需要解码

            # 发送分支已合并消息
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(dx_service.send_chat_msg_by_robot(request, {
                'text': f"流程终止，源分支{from_branch_decoded}已经合并至目标分支{to_branch_decoded}，请重新选择分支",
                'uid': cr_params.get('uid'),
                'empId': cr_params.get('empId')
            }))
            loop.close()
        else:
            # 发送处理失败消息
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(dx_service.send_chat_msg_by_robot(request, {
                'text': f"处理失败，{str(error)}，点此[反馈|https://km.sankuai.com/collabpage/2706453728]",
                'uid': cr_params.get('uid'),
                'empId': cr_params.get('empId')
            }))
            loop.close()
        # 记录错误日志
        print(f'LangChain处理失败: {str(error)}')

        # 上报错误指标
        report2raptor(
            name='gen-cr-doc',
            value=0,
            tags={
                'errorDetail': str(error)
            },
            msg='cr失败'
        )

        return jsonify({
            'success': False,
            'message': '处理请求时发生错误',
            'error': str(error)
        }), 500


@manager.route('/error', methods=['GET'])
def error_handler():
    return '404', 404


@manager.route('/daxiang_msg', methods=['POST'])
def daxiang_handler():
    # TODO: 调用dx_service相关方法，需补全send_chat_msg_by_robot等
    return jsonify({'status': 'ok'})


@manager.route('/convert_uid2emp', methods=['GET'])
def convert_uid2emp_handler():
    # TODO: 调用dx_service.convert_userid_to_emp_id，需补全实现
    return jsonify({'empId': 'mock_empid'})


@manager.route('/db', methods=['GET'])
def db_handler():
    # TODO: 数据库相关，建议实现数据库查询/健康检查等
    return jsonify({'db': 'mock_db'})


@manager.route('/open_callback', methods=['POST'])
def open_callback_service_handler():
    # TODO: 回调服务，建议实现回调参数校验与业务处理
    return jsonify({'status': 'ok'})


@manager.route('/pr_link', methods=['GET'])
def pr_link_handler():
    # TODO: PR链接处理，建议实现PR信息查询与跳转
    return jsonify({'prLink': 'mock_pr_link'})
