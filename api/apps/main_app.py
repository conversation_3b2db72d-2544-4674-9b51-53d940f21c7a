import asyncio
import json
import urllib.parse
from datetime import datetime

from flask import request, jsonify
import os
import dotenv

from utils import api_utils

dotenv.load_dotenv()

# 使用新的服务注册表
from core.service_registry import (
    get_git_service, get_devtools_service, get_daxiang_service,
    get_kms_service, get_org_service, get_km_service
)

# 保留需要的旧服务导入
from services.cr_service import CRService
from api.service.devmind_service import DevmindService
from api.service.horn_service import HornService
from api.service.sso_service import SsoService



# 使用服务注册表获取服务实例
def get_services():
    """获取所有需要的服务实例"""
    kms_service = get_kms_service()
    sso_service = SsoService()
    horn_service = HornService()
    dx_service = get_daxiang_service()
    git_service = get_git_service()
    devmind_service = DevmindService(
        base_url=os.environ.get("DM_BASE_URL", ""),
        api_key=os.environ.get("DM_API_KEY", "")
    )

    # 使用新的CR服务架构 - 传递服务实例和配置
    cr_service = CRService(
        horn_service=horn_service,
        kms_service=kms_service,
        dx_service=dx_service,
        git_service=git_service,
        devmind_service=devmind_service,
        config={
            'cr_mode': 'standard',
            'enable_parallel_processing': True,
            'max_concurrent_tasks': 5,
            'enable_performance_monitoring': True
        }
    )

    org_service = get_org_service()
    km_service = get_km_service()
    devtools_service = get_devtools_service()

    return {
        'kms_service': kms_service,
        'sso_service': sso_service,
        'horn_service': horn_service,
        'dx_service': dx_service,
        'git_service': git_service,
        'devmind_service': devmind_service,
        'cr_service': cr_service,
        'org_service': org_service,
        'km_service': km_service,
        'devtools_service': devtools_service
    }


# open_km_service = KmService()
# org_service = OrgService()
@manager.route('/sso', methods=['GET'])
def sso_handler():
    try:
        # 获取SSO信息
        services = get_services()
        result = services['sso_service'].get_user_info(request)
        return jsonify(result)
    except Exception as error:
        return jsonify({'success': False, 'error': str(error)})


@manager.route('/emp_info', methods=['GET'])
def get_emp_info():
    try:
        # 获取员工信息
        services = get_services()
        result = services['org_service'].get_emp_info(request.headers.__dict__, request.args)
        return jsonify({'code': 0, 'data': result, 'message': 'success'})
    except Exception as error:
        return jsonify({'code': -1, 'data': None, 'message': str(error)})


@manager.route('/base_inf_demo', methods=['GET'])
def base_inf_demo_handler():
    # 示例：调用kms_service、horn_service、sso_service
    services = get_services()
    kms_demo = services['kms_service'].get_key('com.sankuai.yunzhuan.devhelper', 'modal_app_id')
    horn_demo = services['horn_service'].fetch_config('CR_CHECKER')
    sso_demo = services['sso_service'].get_user_info(request)
    access_token_result = services['sso_service'].get_access_token(request)
    access_token = access_token_result.get('data', 'mock_token')
    result = {
        'kmsDemo': kms_demo.get('data'),
        'hornDemo': horn_demo,
        'ssoDemo': sso_demo,
        'accessToken': access_token
    }
    return jsonify(result)


@manager.route('/horn_config', methods=['GET'])
def get_horn_handler():
    services = get_services()
    horn_data = services['horn_service'].fetch_config('CR_CHECKER')
    return jsonify(horn_data)


@manager.route('/cr_config', methods=['GET', 'POST'])
def cr_config_handler():
    """CR配置管理接口"""
    try:
        services = get_services()
        cr_service = services['cr_service']

        if request.method == 'GET':
            # 获取当前CR配置
            config = {
                'mode': cr_service.get_cr_mode(),
                'performanceMonitoring': cr_service.enable_performance_monitoring,
                'availableModes': ['fast', 'standard', 'deep', 'async_fast'],
                'modeDescriptions': {
                    'fast': 'Fast模式 - 纯LLM能力，不检索知识库，<0.5秒',
                    'standard': 'Standard模式 - LLM智能决策是否检索知识库，平衡思考，1-2秒',
                    'deep': 'Deep模式 - 深度思考链路+自检复审，全面分析，2-4秒',
                    'async_fast': '异步快速模式 - 兼容旧版本，异步知识检索，<1秒'
                },
                'modeDetails': {
                    'fast': {
                        'description': '纯LLM专业能力进行代码审查',
                        'features': ['快速响应', '基于LLM专业知识', '无外部依赖'],
                        'useCase': 'CI/CD流水线、快速反馈'
                    },
                    'standard': {
                        'description': 'LLM智能决策是否需要知识库支持',
                        'features': ['智能决策', '按需知识检索', '平衡思考链路'],
                        'useCase': '日常代码审查、功能开发'
                    },
                    'deep': {
                        'description': '深度分析+知识库+自检复审的全面审查',
                        'features': ['深度思考链路', '全面知识检索', '自检复审机制'],
                        'useCase': '关键代码、安全审计、架构审查'
                    },
                    'async_fast': {
                        'description': '异步快速模式，兼容旧版本',
                        'features': ['异步处理', '并发支持', '向后兼容'],
                        'useCase': '高并发场景、批量处理'
                    }
                }
            }
            return jsonify({'code': 0, 'data': config, 'message': 'success'})

        elif request.method == 'POST':
            # 更新CR配置
            config_data = request.get_json()

            # 更新CR模式
            if 'mode' in config_data:
                cr_service.set_cr_mode(config_data['mode'])

            # 更新性能监控
            if 'performanceMonitoring' in config_data:
                cr_service.enable_performance_monitor(config_data['performanceMonitoring'])

            return jsonify({
                'code': 0,
                'data': {
                    'mode': cr_service.get_cr_mode(),
                    'performanceMonitoring': cr_service.enable_performance_monitoring
                },
                'message': 'CR配置更新成功'
            })
        else:
            return jsonify({'code': 1, 'data': None, 'message': 'Invalid request method'})

    except Exception as error:
        return jsonify({'code': -1, 'data': None, 'message': str(error)})


def create_pr(req, cr_params, header_info):
    """创建PR并返回PR链接"""
    try:
        project = cr_params.get('project') or req.args.get('project')
        repo = cr_params.get('repo') or req.args.get('repo')
        from_branch = urllib.parse.unquote(cr_params.get('fromBranch') or req.args.get('fromBranch'))
        to_branch = urllib.parse.unquote(cr_params.get('toBranch') or req.args.get('toBranch'))
        title = cr_params.get('title', '云篆自动创建PR')

        # 构建URL和参数
        url = f"/rest/api/2.0/projects/{project}/repos/{repo}/pull-requests"
        params = {
            "title": title,
            "fromRef": {
                "id": f"refs/heads/{from_branch}",
                "displayId": from_branch,
                "repository": {
                    "slug": repo,
                    "project": {
                        "key": project
                    }
                }
            },
            "toRef": {
                "id": f"refs/heads/{to_branch}",
                "displayId": to_branch,
                "repository": {
                    "slug": repo,
                    "project": {
                        "key": project
                    }
                }
            }
        }

        # 调用DevtoolsService创建PR
        devtools_service = get_devtools_service()
        pr_id = devtools_service.create_pr(url, params, header_info)
        pr_url = f'https://dev.sankuai.com/code/repo-detail/{project}/{repo}/pr/{pr_id}'

        return pr_url
    except Exception as e:
        print(f"创建PR失败: {str(e)}")
        raise e


def get_branch_diff(request, cr_params):
    """获取分支差异"""
    try:
        project = cr_params.get('project') or request.args.get('project')
        repo = cr_params.get('repo') or request.args.get('repo')
        from_branch = cr_params.get('fromBranch') or request.args.get('fromBranch')
        to_branch = cr_params.get('toBranch') or request.args.get('toBranch')
        print(f"获取分支差异: {project}, {repo}, {from_branch}, {to_branch}")
        git_service = get_git_service()
        diff = git_service.get_diff(project, repo, from_branch, to_branch)
        return diff
    except Exception as e:
        print(f"获取分支差异失败: {str(e)}")
        raise e


@manager.route('/cr_lc', methods=['POST'])
def cr_lc_handler():
    cr_params = request.get_json()
    # header_info = {"mis": request.headers.get("mis"), "ssoid": request.headers.get("ssoid")}
    header_info = {}
    # 获取服务实例
    services = get_services()
    cr_service = services['cr_service']
    try:


        # 设置CR模式（如果请求中指定了）
        cr_mode = cr_params.get('crMode') or request.args.get('crMode')
        if cr_mode:
            cr_service.set_cr_mode(cr_mode)
            print(f"[cr_lc_handler] 使用指定的CR模式: {cr_mode}")
        else:
            print(f"[cr_lc_handler] 使用默认CR模式: {cr_service.get_cr_mode()}")

        # 初始化服务（新架构兼容）
        if hasattr(cr_service, 'initialize'):
            # 使用新架构的初始化方法
            asyncio.run(cr_service.initialize())
            print(f"[cr_lc_handler] 新架构CR服务初始化完成，当前模式: {cr_service.get_cr_mode()}")
        elif hasattr(cr_service, 'init_inf'):
            # 兼容旧版本方法
            cr_service.init_inf()
            print(f"[cr_lc_handler] 旧版本CR服务初始化完成，当前模式: {cr_service.get_cr_mode()}")
        else:
            print(f"[cr_lc_handler] CR服务无需初始化，当前模式: {cr_service.get_cr_mode()}")

        # 创建PR
        pr_id = cr_params.get('pr_id') or request.args.get('pr_id')
        pr_project = cr_params.get('project') or request.args.get('project')
        pr_repo = request.args.get('repo') or cr_params.get('repo')
        pr_from_branch = request.args.get('fromBranch') or cr_params.get('fromBranch')
        pr_to_branch = request.args.get('toBranch') or cr_params.get('toBranch')
        pr_link = f'https://dev.sankuai.com/code/repo-detail/{pr_project}/{pr_repo}/pr/{pr_id}' if pr_id else create_pr(request, cr_params,  header_info)
        print(f"prLink: {pr_link}")
        # 创建 taskGitInfo
        task_git_info = {
            'project': pr_project,
            'repo': pr_repo,
            'fromBranch': pr_from_branch,
            'toBranch': pr_to_branch
        }

        # 获取代码差异
        code_diff = get_branch_diff(request, cr_params)
        print(f"codeDiff: {code_diff}")

        # 使用LangChain处理
        cr_params_with_pr_link = {
            **cr_params,
            'prLink': pr_link,
            'taskGitInfo': task_git_info,
            'datasets_ids': [].extend(os.environ['DM_CR_RULE_KB_ID'].split(','))
        }
        # 处理代码评审请求
        cr_result = asyncio.run(cr_service.process_cr_request(request, code_diff, cr_params_with_pr_link))

        # 检查是否需要兼容性字段（通过请求头或参数判断）
        compatibility_mode = request.headers.get('X-CR-Compatibility-Mode') == 'legacy' or cr_params_with_pr_link.get('compatibility_mode') == 'legacy'

        if compatibility_mode:
            # 添加兼容性字段，避免重复但保持向后兼容
            if 'summary' in cr_result and not any(key in cr_result for key in ['checkBranch', 'sumCheckResult', 'totalProblem', 'resultDesc']):
                summary = cr_result['summary']
                cr_result['checkBranch'] = summary.get('checkBranch', 'unknown')
                cr_result['sumCheckResult'] = summary.get('overallResult', '通过')
                cr_result['totalProblem'] = str(summary.get('totalProblems', 0))
                cr_result['resultDesc'] = summary.get('resultDescription', '')
                print("添加兼容性字段以支持旧版本API调用者")

        print(f"crResult: {json.dumps(cr_result,  ensure_ascii=False,  indent=2)}")

        # 输出详细的CR结果日志
        try:
            from utils.cr_log_utils import CRLogFormatter
            import logging

            logger = logging.getLogger(__name__)
            CRLogFormatter.format_cr_result(cr_result, logger)

        except Exception as e:
            print(f"输出详细CR日志失败: {str(e)}")

        # 构建CR任务结果（真正反映每个任务的产出）


        # 使用Flask的make_response确保正确的Content-Type和中文编码
        from flask import make_response
        import json as json_module  # 使用别名避免潜在的作用域问题

        # 使用ensure_ascii=False避免中文被转义
        response_data = json_module.dumps(cr_result, ensure_ascii=False, indent=2)
        response = make_response(response_data)
        response.headers['Content-Type'] = 'application/json; charset=utf-8'
        return response
    except Exception as error:
        # 记录错误日志
        print(f'LangChain处理失败: {str(error)}')

        return api_utils.error_response(500, '处理请求时发生错误'+str(error))


@manager.route('/error', methods=['GET'])
def error_handler():
    return '404', 404


@manager.route('/daxiang_msg', methods=['POST'])
def daxiang_handler():
    # TODO: 调用dx_service相关方法，需补全send_chat_msg_by_robot等
    return jsonify({'status': 'ok'})


@manager.route('/convert_uid2emp', methods=['GET'])
def convert_uid2emp_handler():
    # TODO: 调用dx_service.convert_userid_to_emp_id，需补全实现
    return jsonify({'empId': 'mock_empid'})


@manager.route('/db', methods=['GET'])
def db_handler():
    # TODO: 数据库相关，建议实现数据库查询/健康检查等
    return jsonify({'db': 'mock_db'})


@manager.route('/open_callback', methods=['POST'])
def open_callback_service_handler():
    # TODO: 回调服务，建议实现回调参数校验与业务处理
    return jsonify({'status': 'ok'})


@manager.route('/pr_link', methods=['GET'])
def pr_link_handler():
    # TODO: PR链接处理，建议实现PR信息查询与跳转
    return jsonify({'prLink': 'mock_pr_link'})
