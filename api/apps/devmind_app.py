import os

import requests as requests
from flask import request

from flask import Response, stream_with_context, jsonify

from api.service.devmind_service import DevmindService


@manager.route('/devmind/chat/<chat_id>', methods=['POST'])
def chat_with_devmind(chat_id):
    data = request.get_json()
    question = data.get("question")
    stream = data.get("stream", True)
    chat_id = chat_id or os.environ["DM_DEFAULT_CHAT_ID"]
    if not question:
        return jsonify({"code": 102, "message": "Please input your question."}), 400

    try:
        if stream:
            # 流式返回
            devmind_response = DevmindService().converse_with_assistant(
                chat_id=chat_id,
                question=question,
                stream=True
            )

            def generate():
                for line in devmind_response.iter_lines(decode_unicode=True):
                    if line:
                        yield line + '\n'

            return Response(stream_with_context(generate()), content_type='text/event-stream')
        else:
            # 普通返回
            result = DevmindService().converse_with_assistant(
                chat_id=chat_id,
                question=question,
                stream=False
            )
            return jsonify(result)
    except requests.RequestException as e:
        return jsonify({"code": 500, "message": str(e)}), 500
