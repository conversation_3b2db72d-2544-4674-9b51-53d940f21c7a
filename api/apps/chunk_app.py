import json

from flask import request, jsonify

# 使用新的服务注册表
from core.service_registry import (
    get_git_service, get_chunk_service, get_devtools_service
)


def get_options_from_request():
    # 可根据实际需要扩展
    return {
        'mis': request.headers.get('mis'),
        'ssoid': request.headers.get('ssoid'),
        'token': request.headers.get('token')
    }


@manager.route('/get_code_chunks', methods=['GET'])
def get_code_chunks():
    """
    获取指定文件的代码块
    params: file_path, project, repo
    """
    try:
        file_path = request.args.get('file_path')
        project = request.args.get('project')
        repo = request.args.get('repo')
        branch = request.args.get('branch', 'main')

        chunker = get_chunk_service(project, repo, branch)
        chunks = chunker.chunk_code_file(file_path)
        return jsonify({'success': True, 'chunks': chunks})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@manager.route('/get_diff_chunks', methods=['GET'])
def get_diff_chunks():
    """
    获取PR diff的代码块
    params: project, repo, pr_id
    """
    try:
        project = request.args.get('project')
        repo = request.args.get('repo')
        pr_id = request.args.get('pr_id')

        # 获取diff内容
        devtools_service = get_devtools_service()
        pr_info = devtools_service.fetch_pr_info(project, repo, pr_id)
        print(f"pr_info: {pr_info}")
        from_branch = pr_info.get('fromBranch')
        to_branch = pr_info.get('toBranch')
        print(f"获取分支差异: {project}, {repo}, {from_branch}, {to_branch}")

        diff_content = None
        if from_branch and to_branch:
            diff_content = devtools_service.get_all_pr_diffs(project, repo, pr_id)
            diff_content = diff_content if isinstance(diff_content, str) else diff_content.get('diff')
            print(f"diff_content: {diff_content}")

        chunker = get_chunk_service(project, repo, from_branch or 'main')
        chunks = chunker.chunk_diff_code(diff_content)
        return jsonify({'success': True, 'chunks': chunks})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@manager.route('/get_chunk_comment_qa', methods=['POST'])
def get_chunk_comment_qa():
    """
    获取PR diff的代码块-评论问答对
    params: project, repo, pr_id
    """
    try:
        project = request.args.get('project')
        repo = request.args.get('repo')
        pr_id = request.args.get('pr_id')
        # 获取diff内容
        devtools_service = DevtoolsService()
        pr_info = devtools_service.fetch_pr_info(project, repo, pr_id)
        # 格式化打印pr_info
        from_branch = pr_info.get('fromBranch')
        to_branch = pr_info.get('toBranch')
        diff_content = None
        if from_branch and to_branch:
            diff_content = devtools_service.get_all_pr_diffs(project, repo, pr_id)
            print(f"diff_content: {diff_content}")
            diff_content = diff_content if isinstance(diff_content, str) else diff_content.get('diff')
        chunker = ChunkService(git_service=git_service, project=project, repo=repo, branch=from_branch)
        chunks = chunker.chunk_diff_file(diff_content, from_branch, to_branch)
        # 获取评论
        comments_json = devtools_service.get_pr_comments(project, repo, pr_id)
        qa_pairs = chunker.build_qa_pairs_with_merged_comments(comments_json, chunks)
        return jsonify({'success': True, 'qa_pairs': qa_pairs})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500
