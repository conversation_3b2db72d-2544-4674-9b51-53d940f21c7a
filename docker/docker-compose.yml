version: '3.8'
services:
  web:
    build: ..
    container_name: flask-scaffold
    ports:
      - "5000:5000"
    environment:
      - FLASK_ENV=production
    depends_on:
      - db
    restart: always
  db:
    image: mysql:8.0
    container_name: flask-mysql
    environment:
      MYSQL_ROOT_PASSWORD: example
      MYSQL_DATABASE: test
    ports:
      - "3306:3306"
    volumes:
      - db_data:/var/lib/mysql
    restart: always
volumes:
  db_data:
