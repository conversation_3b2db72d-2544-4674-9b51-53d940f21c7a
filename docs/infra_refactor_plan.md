# Infra目录重构计划

## 当前状态
Infra目录已有部分基础设施：
- `repo/` - 数据仓库相关
- `proxy/` - 代理相关
- `thrift/` - Thrift服务
- `serializers/` - 序列化器

## 需要完善的基础设施

### 1. 数据库层完善
\`\`\`
infra/database/
├── __init__.py
├── models.py (数据模型)
├── repositories.py (数据仓库)
├── migrations/ (数据库迁移)
├── connection_pool.py (连接池管理)
└── zebra_adapter.py (Zebra数据库代理适配器)
\`\`\`

### 2. 缓存层
\`\`\`
infra/cache/
├── __init__.py
├── redis_cache.py (Redis缓存)
├── memory_cache.py (内存缓存)
├── cache_manager.py (缓存管理器)
└── cache_decorators.py (缓存装饰器)
\`\`\`

### 3. 监控系统
\`\`\`
infra/monitoring/
├── __init__.py
├── metrics.py (指标收集)
├── health_check.py (健康检查)
├── performance_monitor.py (性能监控)
├── alert_manager.py (告警管理)
└── logger.py (日志配置)
\`\`\`

### 4. 消息队列
\`\`\`
infra/messaging/
├── __init__.py
├── kafka_client.py (Kafka客户端)
├── message_handler.py (消息处理器)
└── event_bus.py (事件总线)
\`\`\`

### 5. 外部服务集成
\`\`\`
infra/external/
├── __init__.py
├── horn_client.py (Horn配置服务客户端)
├── kms_client.py (KMS密钥服务客户端)
├── daxiang_client.py (大象通知客户端)
└── mcp_client.py (MCP协议客户端)
\`\`\`

### 6. 安全相关
\`\`\`
infra/security/
├── __init__.py
├── auth_manager.py (认证管理)
├── encryption.py (加密工具)
├── rate_limiter.py (限流器)
└── security_middleware.py (安全中间件)
\`\`\`