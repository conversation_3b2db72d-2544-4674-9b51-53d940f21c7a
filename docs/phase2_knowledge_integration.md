# 第二阶段：知识库集成设计

## 概述

本文档描述如何将统一知识库与Agent系统深度集成，实现知识驱动的智能代码审查。

## 统一知识库架构

### 知识块（Chunk）统一结构

```python
# core/knowledge/unified_chunk.py
from pydantic import BaseModel, Field, validator
from typing import List, Dict, Any, Optional
from enum import Enum

class KnowledgeType(Enum):
    """知识类型枚举"""
    CODE = "code"                    # 代码块
    DEV_SPEC = "dev_spec"           # 开发规范
    BEST_PRACTICE = "best_practice"  # 最佳实践
    ANTI_PATTERN = "anti_pattern"    # 反面案例
    BUSINESS_RULE = "business_rule"  # 业务规则
    SECURITY_RULE = "security_rule"  # 安全规则

class UnifiedChunk(BaseModel):
    """统一知识块结构"""
    id: str = ""
    content: str = ""
    document_id: str = ""
    docnm_kwd: str = ""
    important_keywords: List[str] = Field(default_factory=list)
    questions: List[Dict] = Field(default_factory=list)
    question_tks: str = ""
    image_id: str = ""
    available: bool = True
    positions: List[List[int]] = Field(default_factory=list)
    knowledge_type: KnowledgeType = KnowledgeType.CODE
    extra: Dict[str, Any] = Field(default_factory=dict)
    
    # 新增字段
    priority: int = 1  # 优先级 1-10
    confidence: float = 1.0  # 置信度 0-1
    created_at: float = Field(default_factory=lambda: time.time())
    updated_at: float = Field(default_factory=lambda: time.time())
    usage_count: int = 0  # 使用次数
    effectiveness_score: float = 0.0  # 效果评分
    
    @validator('positions')
    def validate_positions(cls, value):
        for sublist in value:
            if len(sublist) != 5:
                raise ValueError("positions子列表必须包含5个元素")
        return value
```

### 知识库管理器

```python
# core/knowledge/knowledge_manager.py
from typing import List, Dict, Any, Optional
import asyncio
import logging
from collections import defaultdict

class KnowledgeManager:
    """知识库管理器"""
    
    def __init__(self, vector_store, cache_manager):
        self.vector_store = vector_store
        self.cache_manager = cache_manager
        self.knowledge_index: Dict[str, UnifiedChunk] = {}
        self.type_index: Dict[KnowledgeType, List[str]] = defaultdict(list)
        self.keyword_index: Dict[str, List[str]] = defaultdict(list)
        self.logger = logging.getLogger("knowledge_manager")
    
    async def add_knowledge(self, chunk: UnifiedChunk) -> bool:
        """添加知识块"""
        try:
            # 生成向量嵌入
            embedding = await self.vector_store.embed_text(chunk.content)
            
            # 存储到向量数据库
            await self.vector_store.add_chunk(chunk, embedding)
            
            # 更新索引
            self.knowledge_index[chunk.id] = chunk
            self.type_index[chunk.knowledge_type].append(chunk.id)
            
            # 更新关键词索引
            for keyword in chunk.important_keywords:
                self.keyword_index[keyword.lower()].append(chunk.id)
            
            # 缓存热点知识
            if chunk.usage_count > 10:
                await self.cache_manager.set(f"knowledge:{chunk.id}", chunk.dict())
            
            self.logger.info(f"知识块 {chunk.id} 添加成功")
            return True
            
        except Exception as e:
            self.logger.error(f"添加知识块失败: {str(e)}")
            return False
    
    async def search_knowledge(self, 
                             query: str, 
                             knowledge_types: List[KnowledgeType] = None,
                             limit: int = 10,
                             min_confidence: float = 0.5) -> List[UnifiedChunk]:
        """搜索相关知识"""
        try:
            # 先尝试从缓存获取
            cache_key = f"search:{hash(query)}:{str(knowledge_types)}:{limit}"
            cached_result = await self.cache_manager.get(cache_key)
            if cached_result:
                return [UnifiedChunk(**item) for item in cached_result]
            
            # 向量搜索
            similar_chunks = await self.vector_store.search_similar(query, limit * 2)
            
            # 过滤和排序
            filtered_chunks = []
            for chunk_data in similar_chunks:
                chunk = UnifiedChunk(**chunk_data['chunk'])
                similarity = chunk_data['similarity']
                
                # 类型过滤
                if knowledge_types and chunk.knowledge_type not in knowledge_types:
                    continue
                
                # 置信度过滤
                if chunk.confidence < min_confidence:
                    continue
                
                # 可用性检查
                if not chunk.available:
                    continue
                
                # 计算综合评分
                score = similarity * chunk.confidence * (1 + chunk.effectiveness_score)
                chunk.extra['search_score'] = score
                
                filtered_chunks.append(chunk)
            
            # 按评分排序
            filtered_chunks.sort(key=lambda x: x.extra.get('search_score', 0), reverse=True)
            result = filtered_chunks[:limit]
            
            # 更新使用次数
            for chunk in result:
                chunk.usage_count += 1
                await self.update_knowledge(chunk)
            
            # 缓存结果
            await self.cache_manager.set(cache_key, [chunk.dict() for chunk in result], ttl=300)
            
            return result
            
        except Exception as e:
            self.logger.error(f"搜索知识失败: {str(e)}")
            return []
    
    async def get_knowledge_by_type(self, knowledge_type: KnowledgeType, limit: int = 50) -> List[UnifiedChunk]:
        """按类型获取知识"""
        chunk_ids = self.type_index.get(knowledge_type, [])[:limit]
        return [self.knowledge_index[chunk_id] for chunk_id in chunk_ids if chunk_id in self.knowledge_index]
    
    async def update_knowledge(self, chunk: UnifiedChunk) -> bool:
        """更新知识块"""
        try:
            chunk.updated_at = time.time()
            
            # 更新向量存储
            embedding = await self.vector_store.embed_text(chunk.content)
            await self.vector_store.update_chunk(chunk, embedding)
            
            # 更新本地索引
            self.knowledge_index[chunk.id] = chunk
            
            return True
        except Exception as e:
            self.logger.error(f"更新知识块失败: {str(e)}")
            return False
    
    async def get_knowledge_stats(self) -> Dict[str, Any]:
        """获取知识库统计信息"""
        stats = {
            "total_chunks": len(self.knowledge_index),
            "by_type": {},
            "top_keywords": {},
            "avg_effectiveness": 0.0
        }
        
        # 按类型统计
        for k_type, chunk_ids in self.type_index.items():
            stats["by_type"][k_type.value] = len(chunk_ids)
        
        # 关键词统计
        keyword_counts = {k: len(v) for k, v in self.keyword_index.items()}
        stats["top_keywords"] = dict(sorted(keyword_counts.items(), key=lambda x: x[1], reverse=True)[:10])
        
        # 平均效果评分
        if self.knowledge_index:
            total_score = sum(chunk.effectiveness_score for chunk in self.knowledge_index.values())
            stats["avg_effectiveness"] = total_score / len(self.knowledge_index)
        
        return stats
```

## Agent与知识库集成

### 知识增强Agent基类

```python
# core/agents/knowledge_enhanced_agent.py
from core.agents.base_agent import BaseAgent, AgentResult
from core.knowledge.knowledge_manager import KnowledgeManager, KnowledgeType
from typing import List, Dict, Any

class KnowledgeEnhancedAgent(BaseAgent):
    """知识增强Agent基类"""
    
    def __init__(self, config, knowledge_manager: KnowledgeManager):
        super().__init__(config)
        self.knowledge_manager = knowledge_manager
        self.supported_knowledge_types: List[KnowledgeType] = []
    
    async def search_relevant_knowledge(self, query: str, limit: int = 5) -> List[UnifiedChunk]:
        """搜索相关知识"""
        return await self.knowledge_manager.search_knowledge(
            query=query,
            knowledge_types=self.supported_knowledge_types,
            limit=limit
        )
    
    async def apply_knowledge(self, input_data: Dict[str, Any], knowledge_chunks: List[UnifiedChunk]) -> Dict[str, Any]:
        """应用知识到输入数据"""
        # 子类实现具体的知识应用逻辑
        return input_data
    
    async def feedback_knowledge_effectiveness(self, chunk_id: str, effectiveness: float):
        """反馈知识效果"""
        chunk = self.knowledge_manager.knowledge_index.get(chunk_id)
        if chunk:
            # 更新效果评分（使用移动平均）
            alpha = 0.1
            chunk.effectiveness_score = alpha * effectiveness + (1 - alpha) * chunk.effectiveness_score
            await self.knowledge_manager.update_knowledge(chunk)
```

### 规则匹配Agent实现

```python
# core/agents/rule_matching_agent.py
from core.agents.knowledge_enhanced_agent import KnowledgeEnhancedAgent
from core.knowledge.unified_chunk import KnowledgeType
import re
import json

class RuleMatchingAgent(KnowledgeEnhancedAgent):
    """规则匹配Agent"""
    
    def __init__(self, config, knowledge_manager):
        super().__init__(config, knowledge_manager)
        self.supported_knowledge_types = [
            KnowledgeType.DEV_SPEC,
            KnowledgeType.SECURITY_RULE,
            KnowledgeType.ANTI_PATTERN
        ]
    
    def get_required_inputs(self) -> List[str]:
        return ["code_content", "file_path", "language"]
    
    def get_output_schema(self) -> Dict[str, Any]:
        return {
            "matched_rules": "List[Dict]",
            "violations": "List[Dict]",
            "suggestions": "List[str]"
        }
    
    def validate_input(self, input_data: Dict[str, Any]) -> bool:
        required = self.get_required_inputs()
        return all(key in input_data for key in required)
    
    async def execute(self, input_data: Dict[str, Any], task_id: str = None) -> AgentResult:
        """执行规则匹配"""
        code_content = input_data["code_content"]
        file_path = input_data["file_path"]
        language = input_data["language"]
        
        # 搜索相关规则
        search_query = f"{language} code rules {file_path}"
        relevant_rules = await self.search_relevant_knowledge(search_query, limit=20)
        
        matched_rules = []
        violations = []
        suggestions = []
        
        for rule_chunk in relevant_rules:
            # 检查规则是否匹配
            match_result = await self._check_rule_match(code_content, rule_chunk)
            if match_result["matched"]:
                matched_rules.append({
                    "rule_id": rule_chunk.id,
                    "rule_type": rule_chunk.knowledge_type.value,
                    "description": rule_chunk.content,
                    "severity": rule_chunk.extra.get("severity", "P2"),
                    "confidence": match_result["confidence"]
                })
                
                if match_result["violation"]:
                    violations.append({
                        "rule_id": rule_chunk.id,
                        "violation_type": match_result["violation_type"],
                        "line_numbers": match_result["line_numbers"],
                        "description": match_result["description"],
                        "suggestion": match_result["suggestion"]
                    })
                    suggestions.append(match_result["suggestion"])
        
        return AgentResult(
            agent_name=self.name,
            task_id=task_id or "unknown",
            status="success",
            data={
                "matched_rules": matched_rules,
                "violations": violations,
                "suggestions": suggestions,
                "total_rules_checked": len(relevant_rules),
                "violations_found": len(violations)
            }
        )
    
    async def _check_rule_match(self, code_content: str, rule_chunk: UnifiedChunk) -> Dict[str, Any]:
        """检查规则是否匹配代码"""
        result = {
            "matched": False,
            "violation": False,
            "confidence": 0.0,
            "violation_type": "",
            "line_numbers": [],
            "description": "",
            "suggestion": ""
        }
        
        try:
            # 从规则中提取检查模式
            rule_data = rule_chunk.extra
            
            if "pattern" in rule_data:
                # 正则表达式匹配
                pattern = rule_data["pattern"]
                matches = re.finditer(pattern, code_content, re.MULTILINE)
                
                if matches:
                    result["matched"] = True
                    result["violation"] = True
                    result["confidence"] = 0.9
                    result["violation_type"] = rule_data.get("violation_type", "pattern_match")
                    
                    # 计算行号
                    lines = code_content.split('\n')
                    for match in matches:
                        start_pos = match.start()
                        line_num = code_content[:start_pos].count('\n') + 1
                        result["line_numbers"].append(line_num)
                    
                    result["description"] = rule_chunk.content
                    result["suggestion"] = rule_data.get("suggestion", "请修复此问题")
            
            elif "keywords" in rule_data:
                # 关键词匹配
                keywords = rule_data["keywords"]
                found_keywords = [kw for kw in keywords if kw.lower() in code_content.lower()]
                
                if found_keywords:
                    result["matched"] = True
                    result["confidence"] = len(found_keywords) / len(keywords)
                    
                    # 根据规则类型判断是否为违规
                    if rule_chunk.knowledge_type == KnowledgeType.ANTI_PATTERN:
                        result["violation"] = True
                        result["violation_type"] = "anti_pattern"
                        result["description"] = f"发现反面模式: {', '.join(found_keywords)}"
                        result["suggestion"] = rule_chunk.extra.get("suggestion", "避免使用此模式")
        
        except Exception as e:
            self.logger.error(f"规则匹配检查失败: {str(e)}")
        
        return result
```

### LLM审查Agent增强

```python
# core/agents/llm_review_agent.py
from core.agents.knowledge_enhanced_agent import KnowledgeEnhancedAgent
from core.knowledge.unified_chunk import KnowledgeType

class LLMReviewAgent(KnowledgeEnhancedAgent):
    """LLM审查Agent"""
    
    def __init__(self, config, knowledge_manager, llm_service):
        super().__init__(config, knowledge_manager)
        self.llm_service = llm_service
        self.supported_knowledge_types = [
            KnowledgeType.BEST_PRACTICE,
            KnowledgeType.DEV_SPEC,
            KnowledgeType.CODE
        ]
    
    def get_required_inputs(self) -> List[str]:
        return ["code_content", "context", "review_type"]
    
    def get_output_schema(self) -> Dict[str, Any]:
        return {
            "review_result": "Dict",
            "suggestions": "List[str]",
            "knowledge_applied": "List[str]"
        }
    
    def validate_input(self, input_data: Dict[str, Any]) -> bool:
        required = self.get_required_inputs()
        return all(key in input_data for key in required)
    
    async def execute(self, input_data: Dict[str, Any], task_id: str = None) -> AgentResult:
        """执行LLM审查"""
        code_content = input_data["code_content"]
        context = input_data.get("context", "")
        review_type = input_data.get("review_type", "standard")
        
        # 搜索相关知识
        knowledge_query = f"code review {review_type} {context}"
        relevant_knowledge = await self.search_relevant_knowledge(knowledge_query, limit=10)
        
        # 构建增强提示词
        enhanced_prompt = await self._build_enhanced_prompt(
            code_content, context, relevant_knowledge
        )
        
        # 调用LLM
        llm_response = await self.llm_service.generate_response(enhanced_prompt)
        
        # 解析结果
        review_result = self._parse_llm_response(llm_response)
        
        # 反馈知识效果
        await self._feedback_knowledge_effectiveness(relevant_knowledge, review_result)
        
        return AgentResult(
            agent_name=self.name,
            task_id=task_id or "unknown",
            status="success",
            data={
                "review_result": review_result,
                "suggestions": review_result.get("suggestions", []),
                "knowledge_applied": [chunk.id for chunk in relevant_knowledge],
                "knowledge_count": len(relevant_knowledge)
            }
        )
    
    async def _build_enhanced_prompt(self, code_content: str, context: str, knowledge_chunks: List[UnifiedChunk]) -> str:
        """构建知识增强的提示词"""
        prompt_parts = [
            "你是一个专业的代码审查专家。请基于以下知识和最佳实践审查代码：\n",
            f"代码内容：\n```\n{code_content}\n```\n",
            f"上下文：{context}\n"
        ]
        
        if knowledge_chunks:
            prompt_parts.append("相关知识和规范：\n")
            for i, chunk in enumerate(knowledge_chunks, 1):
                prompt_parts.append(f"{i}. {chunk.content}\n")
        
        prompt_parts.append("\n请提供详细的审查意见，包括问题、建议和改进方案。")
        
        return "".join(prompt_parts)
    
    def _parse_llm_response(self, response: str) -> Dict[str, Any]:
        """解析LLM响应"""
        # 简化的解析逻辑，实际应该更复杂
        return {
            "raw_response": response,
            "suggestions": response.split('\n'),
            "severity": "P2",  # 默认严重程度
            "confidence": 0.8
        }
    
    async def _feedback_knowledge_effectiveness(self, knowledge_chunks: List[UnifiedChunk], review_result: Dict[str, Any]):
        """反馈知识效果"""
        # 根据审查结果质量评估知识效果
        effectiveness = review_result.get("confidence", 0.5)
        
        for chunk in knowledge_chunks:
            await self.feedback_knowledge_effectiveness(chunk.id, effectiveness)
```

## 知识自动提取和更新

### 代码知识提取器

```python
# core/knowledge/extractors/code_extractor.py
from typing import List, Dict, Any
import ast
import re

class CodeKnowledgeExtractor:
    """代码知识提取器"""
    
    def __init__(self):
        self.logger = logging.getLogger("code_extractor")
    
    async def extract_from_code(self, code_content: str, file_path: str) -> List[UnifiedChunk]:
        """从代码中提取知识"""
        chunks = []
        
        try:
            # Python代码解析
            if file_path.endswith('.py'):
                chunks.extend(await self._extract_python_knowledge(code_content, file_path))
            
            # Java代码解析
            elif file_path.endswith('.java'):
                chunks.extend(await self._extract_java_knowledge(code_content, file_path))
            
            # JavaScript/TypeScript代码解析
            elif file_path.endswith(('.js', '.ts')):
                chunks.extend(await self._extract_js_knowledge(code_content, file_path))
        
        except Exception as e:
            self.logger.error(f"代码知识提取失败: {str(e)}")
        
        return chunks
    
    async def _extract_python_knowledge(self, code_content: str, file_path: str) -> List[UnifiedChunk]:
        """提取Python代码知识"""
        chunks = []
        
        try:
            tree = ast.parse(code_content)
            
            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    chunk = await self._create_function_chunk(node, code_content, file_path)
                    if chunk:
                        chunks.append(chunk)
                
                elif isinstance(node, ast.ClassDef):
                    chunk = await self._create_class_chunk(node, code_content, file_path)
                    if chunk:
                        chunks.append(chunk)
        
        except Exception as e:
            self.logger.error(f"Python代码解析失败: {str(e)}")
        
        return chunks
    
    async def _create_function_chunk(self, node: ast.FunctionDef, code_content: str, file_path: str) -> UnifiedChunk:
        """创建函数知识块"""
        # 提取函数代码
        lines = code_content.split('\n')
        start_line = node.lineno
        end_line = node.end_lineno or start_line
        function_code = '\n'.join(lines[start_line-1:end_line])
        
        # 提取文档字符串
        docstring = ast.get_docstring(node) or ""
        
        # 生成问答对
        questions = []
        if docstring:
            questions.append({
                "question": f"函数 {node.name} 的作用是什么？",
                "answer": docstring
            })
        
        # 提取关键词
        keywords = [node.name, "function"]
        if node.args.args:
            keywords.extend([arg.arg for arg in node.args.args])
        
        return UnifiedChunk(
            id=f"{file_path}:function:{node.name}:{start_line}",
            content=function_code,
            document_id=file_path,
            docnm_kwd=f"函数 {node.name}",
            important_keywords=keywords,
            questions=questions,
            positions=[[start_line, 0, end_line, 0, 1]],
            knowledge_type=KnowledgeType.CODE,
            extra={
                "function_name": node.name,
                "parameters": [arg.arg for arg in node.args.args],
                "docstring": docstring,
                "complexity": self._calculate_complexity(node)
            }
        )
    
    def _calculate_complexity(self, node) -> int:
        """计算代码复杂度"""
        complexity = 1  # 基础复杂度
        
        for child in ast.walk(node):
            if isinstance(child, (ast.If, ast.While, ast.For, ast.Try)):
                complexity += 1
            elif isinstance(child, ast.BoolOp):
                complexity += len(child.values) - 1
        
        return complexity
```

## 下一步

本文档完成了知识库与Agent系统的集成设计。接下来将编写：

1. **第三阶段：知识库建设和优化**
2. **第四阶段：性能优化和监控**

这些文档将详细描述知识库的具体实现和系统性能优化方案。