# 闪购AI代码审查系统文档中心

## 📋 文档概览

本目录包含了闪购AI代码审查系统（Shangou AI CR）的完整技术文档，涵盖了从当前LangChain RAG架构到Multi-Agent MCP服务的演进规划。

## 🎯 核心文档

### 项目规划文档
- **[项目演进计划](./project_evolution_plan.md)** - 项目整体演进规划和时间表
- **[架构演进路线图](./architecture_evolution_roadmap.md)** - 可视化的架构演进指南

## 📚 阶段性文档

### 第一阶段：项目架构重构（4周）
- **[第一阶段详细计划](./phase1_architecture_refactor.md)** - 架构重构的详细实施方案

### 第二阶段：多Agent协作体系（3周）
- **[第二阶段实施指南](./phase2_implementation_guide.md)** - 综合实施指南和迁移策略
- **[Agent系统设计](./phase2_agent_system_design.md)** - Agent基础架构和通信协议
- **[智能决策链设计](./phase2_intelligent_decision_chain.md)** - 智能路由和策略决策
- **[知识库集成设计](./phase2_knowledge_integration.md)** - 统一知识库与Agent集成

### 第三阶段：知识库建设优化（2周）
- **[第三阶段详细计划](./phase3_knowledge_base.md)** - 知识库建设和优化方案

### 第四阶段：性能优化监控（1周）
- **[第四阶段详细计划](./phase4_optimization.md)** - 性能优化和监控体系

## 🏗️ 架构设计文档

### 技术架构
- **[Phase2 Agent抽象层设计](./architecture/phase2-agent-abstraction.md)** - Agent抽象层的详细设计
- **[Phase3 工作流系统设计](./architecture/phase3-workflow-system.md)** - 工作流系统和监控设计

## 📖 业务和技术指南

### 业务指南
- **[智能CR模式指南](./intelligent_cr_modes_guide.md)** - 三种CR模式的使用指南
- **[CR链对比分析](./cr_chains_comparison.md)** - 不同CR链的对比和选择

### 技术指南
- **[优化CR链指南](./optimized_cr_chain_guide.md)** - CR链的优化实践
- **[Chunk服务重构](./chunk_service_refactor.md)** - 代码分块服务的重构方案

## 📊 项目总结文档

- **[集成总结](./integration_summary.md)** - 项目集成和部署总结
- **[AICR开发计划](./aicr_development_plan.md)** - 整体开发计划和里程碑

## 🗂️ 文档分类

### 按文档类型分类

#### 📋 规划类文档
- 项目演进计划
- 架构演进路线图
- 各阶段详细计划

#### 🏗️ 设计类文档
- Agent系统设计
- 智能决策链设计
- 知识库集成设计
- 架构设计文档

#### 📖 指南类文档
- 实施指南
- 使用指南
- 技术指南

#### 📊 总结类文档
- 集成总结
- 对比分析
- 开发计划

### 按技术领域分类

#### 🤖 Agent技术
- Agent系统设计
- Agent抽象层设计
- 智能决策链设计

#### 🧠 知识管理
- 知识库集成设计
- 知识库建设优化
- Chunk服务重构

#### ⚡ 性能优化
- 性能优化监控
- 优化CR链指南

#### 🔄 工作流
- 工作流系统设计
- CR模式指南

## 📈 文档使用建议

### 新手入门路径
1. 先阅读 **[项目演进计划](./project_evolution_plan.md)** 了解整体规划
2. 查看 **[架构演进路线图](./architecture_evolution_roadmap.md)** 理解架构演进
3. 根据当前阶段阅读对应的详细文档

### 开发人员路径
1. **第一阶段开发者**：重点关注架构重构文档
2. **第二阶段开发者**：重点关注Agent系统相关文档
3. **第三阶段开发者**：重点关注知识库相关文档
4. **第四阶段开发者**：重点关注性能优化文档

### 架构师路径
1. 完整阅读所有规划和设计类文档
2. 重点关注架构演进和技术选型
3. 参考指南类文档进行具体实施

### 产品经理路径
1. 阅读项目演进计划和业务指南
2. 关注各阶段的功能特性和用户价值
3. 了解技术实现对业务的影响

## 🔄 文档维护

### 更新频率
- **规划类文档**：每月更新一次
- **设计类文档**：每个阶段开始前更新
- **指南类文档**：根据实际使用情况更新
- **总结类文档**：每个阶段结束后更新

### 版本管理
- 所有文档都有版本号和更新时间
- 重大变更会在文档中标注
- 保持文档间的一致性和关联性

### 反馈机制
- 欢迎通过Issue或PR提供文档改进建议
- 定期收集使用者反馈，优化文档结构
- 根据项目进展及时更新文档内容

## 📞 联系方式

如有文档相关问题，请联系：
- **项目架构师**：负责技术架构文档
- **技术负责人**：负责文档审核和质量控制
- **开发团队**：负责具体实施文档的维护

---

**文档中心版本**：v1.0  
**创建时间**：2024-12-19  
**维护团队**：闪购AI CR项目组
