# 第二阶段：精简Agent系统设计

## 概述

本文档详细描述了闪购AI代码审查系统的Agent系统设计。基于实际落地考虑，我们采用"3+1"精简Agent架构，确保系统既高效又可维护，同时为后续MCP集成做好准备。

## Agent系统架构

### 核心设计原则

1. **精简高效**：专注于3个核心Agent和1个编排Agent
2. **职责明确**：每个Agent有明确的单一职责
3. **松耦合**：Agent间通过标准接口通信
4. **可扩展**：支持未来功能扩展
5. **MCP兼容**：设计考虑MCP协议集成

### "3+1"Agent架构

```mermaid
graph TB
    A[编排Agent<br>Orchestrator] --> B[代码分析Agent<br>CodeAnalysis]
    A --> C[规则检查Agent<br>RuleCheck]
    A --> D[CR Agent<br>CodeReview]
    
    B --> E[代码分块]
    B --> F[复杂度分析]
    
    C --> G[规则匹配]
    C --> H[知识检索]
    
    D --> I[深度审查]
    D --> J[问题分析]
    D --> K[建议生成]
    
    style A fill:#f9a825
    style B fill:#42a5f5
    style C fill:#66bb6a
    style D fill:#ec407a
```

## Agent详细设计

### 1. 代码分析Agent (CodeAnalysisAgent)

**职责**：负责代码分块和静态分析，为后续审查提供基础。

**核心功能**：
- 智能代码分块：基于语法和语义的代码分割
- 复杂度分析：计算代码复杂度指标
- 依赖分析：识别代码间的依赖关系

**工具集**：
```python
class CodeAnalysisTools:
    def chunk_code(self, code, language, max_size=1000):
        """基于语法的代码分块"""
        # 使用现有的语法解析库实现
        pass
    
    def calculate_complexity(self, code, language):
        """计算代码复杂度"""
        # 使用现有的复杂度计算库
        pass
        
    def extract_metadata(self, code, language):
        """提取代码元数据"""
        # 提取导入、类、函数等信息
        pass
```

### 2. 规则检查Agent (RuleCheckAgent)

**职责**：执行规则匹配和相关知识检索。

**核心功能**：
- 规则匹配：应用预定义规则进行检查
- 知识检索：从知识库中检索相关信息
- 优先级排序：对匹配的规则进行排序

**工具集**：
```python
class RuleCheckTools:
    def match_rules(self, code, language, business):
        """匹配规则库中的规则"""
        # 复用现有cr_rule_config系统
        pass
    
    def retrieve_knowledge(self, query, limit=5):
        """检索相关知识"""
        # 使用简单向量检索实现
        pass
        
    def prioritize_rules(self, matched_rules):
        """对规则进行优先级排序"""
        # 基于严重性和相关性排序
        pass
```

### 3. CR Agent (CodeReviewAgent)

**职责**：执行深度代码审查，生成问题分析和改进建议。

**核心功能**：
- 深度代码审查：利用LLM进行代码质量分析
- 问题分类：将问题按严重程度和类型分类
- 改进建议：生成具体、可操作的改进建议

**工具集**：
```python
class CRTools:
    def review_code(self, code, context, rules, knowledge):
        """执行代码审查"""
        # 复用现有LLM调用逻辑
        pass
    
    def classify_issues(self, issues):
        """对问题进行分类"""
        # 按P0/P1/P2严重程度分类
        pass
        
    def generate_suggestions(self, issues):
        """生成改进建议"""
        # 基于问题生成具体建议
        pass
```

### 4. 编排Agent (OrchestratorAgent)

**职责**：协调其他Agent的工作，管理工作流执行。

**核心功能**：
- 工作流解析：解析工作流定义
- Agent调度：按依赖关系调度Agent
- 状态管理：管理工作流执行状态
- 错误处理：处理执行过程中的错误

**工具集**：
```python
class OrchestratorTools:
    def parse_workflow(self, workflow_def):
        """解析工作流定义"""
        # 解析YAML工作流配置
        pass
    
    def schedule_agents(self, workflow, context):
        """调度Agent执行"""
        # 根据依赖关系安排执行
        pass
        
    def manage_state(self, workflow_id, state):
        """管理工作流状态"""
        # 跟踪和更新执行状态
        pass
        
    def handle_errors(self, error, context):
        """处理执行错误"""
        # 实现错误恢复策略
        pass
```

## Agent通信与协作

### 标准接口定义

所有Agent实现统一的接口，确保互操作性：

```python
class BaseAgent:
    """所有Agent的基类"""
    
    def __init__(self, config=None):
        self.config = config or {}
        self.id = str(uuid.uuid4())
        
    async def execute(self, input_data):
        """执行Agent任务"""
        raise NotImplementedError
        
    def get_tools(self):
        """获取Agent工具集"""
        return {}
        
    def validate_input(self, input_data):
        """验证输入数据"""
        return True
```

### 数据模型

Agent间通过标准化的数据模型通信：

```python
# 输入模型
class AgentInput(BaseModel):
    agent_id: str
    task_id: str
    data: Dict[str, Any]
    context: Optional[Dict[str, Any]] = {}
    
# 输出模型
class AgentOutput(BaseModel):
    agent_id: str
    task_id: str
    success: bool
    data: Dict[str, Any]
    errors: Optional[List[Dict[str, Any]]] = []
    metrics: Optional[Dict[str, Any]] = {}
```

## MCP兼容设计

为确保与MCP协议兼容，我们设计了专用的适配层：

```python
class MCPAgentAdapter:
    """将Agent转换为MCP服务的适配器"""
    
    def __init__(self, agent, server_info=None):
        self.agent = agent
        self.server_info = server_info or {
            "name": f"{agent.id}-server",
            "version": "1.0.0"
        }
    
    def create_mcp_server(self, transport_provider):
        """创建MCP服务器"""
        # 创建MCP服务器实例
        pass
        
    def _create_mcp_tool(self, name, tool_fn):
        """将Agent工具转换为MCP工具规范"""
        # 创建MCP工具规范
        pass
```

## 实施路径

### 第1周：基础框架

- 实现BaseAgent抽象类
- 创建标准数据模型
- 实现基础工具集

### 第2周：核心Agent实现

- 实现CodeAnalysisAgent
- 实现RuleCheckAgent
- 实现CodeReviewAgent

### 第3周：编排与集成

- 实现OrchestratorAgent
- 创建标准工作流定义
- 集成测试与性能优化

## 验收标准

1. **功能完整性**：所有Agent能正常工作并协作
2. **性能指标**：处理时间不超过现有系统
3. **代码质量**：测试覆盖率>80%，符合PEP8规范
4. **可扩展性**：支持新Agent和工具的简单集成
5. **MCP兼容性**：设计支持未来MCP集成