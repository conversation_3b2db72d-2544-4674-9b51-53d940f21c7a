# 闪购AI代码审查系统（Shangou AI CR）项目演进计划

## 项目概述

基于云篆（AICR）开发规划，本项目将从当前的单体架构演进为"知识库驱动、Agent协同、效果可量化、架构可演进"的企业级智能代码审查平台。

## 当前项目现状

### 架构优势
- ✅ 基础架构完整：Flask Web框架、API层、服务层、基础设施层
- ✅ 核心功能实现：代码分块、LLM调用、知识库检索、结果汇总
- ✅ 多模式支持：fast、standard、deep、async_fast四种CR模式
- ✅ LangChain集成：初步集成LangChain进行链式任务编排

### 存在问题
- ❌ 代码耦合度高：服务类过于庞大（cr_lc_service.py有920行）
- ❌ 依赖关系复杂：服务间直接依赖，难以独立测试和扩展
- ❌ 缺乏统一接口：各服务接口不统一，扩展性差
- ❌ 配置管理分散：配置散落在各个文件中
- ❌ 缺乏完整的Agent架构：未形成真正的多Agent协作体系

## 演进目标

1. **架构清晰化**：模块职责明确，代码复用性高
2. **Agent协同化**：精简高效的Agent协作，智能决策
3. **知识库驱动**：统一知识管理，持续学习优化
4. **效果可量化**：完整的评估体系和监控机制
5. **架构可演进**：高扩展性，支持MCP协议集成

## 演进阶段规划

| 阶段 | 名称 | 时间 | 主要目标 | 详细文档 |
|------|------|------|----------|----------|
| 第一阶段 | 项目架构重构 | 4周 | 重构目录结构，实现模块化设计 | [阶段一详细计划](./phase1_architecture_refactor.md) |
| 第二阶段 | 精简Agent协作体系 | 3周 | 构建3+1核心Agent系统，实现智能协作 | [实施指南](./phase2_implementation_guide.md) \| [Agent系统设计](./phase2_agent_system_design.md) \| [智能决策链](./phase2_intelligent_decision_chain.md) \| [知识库集成](./phase2_knowledge_integration.md) |
| 第三阶段 | 知识库建设优化 | 2周 | 建设统一知识库，优化检索 | [阶段三详细计划](./phase3_knowledge_base.md) |
| 第四阶段 | 性能优化与MCP集成 | 1周 | 性能优化，MCP协议集成 | [阶段四详细计划](./phase4_optimization.md) |

## 总体时间表

```mermaid
gantt
    title 项目演进时间表
    dateFormat  YYYY-MM-DD
    section 第一阶段
    基础架构搭建    :a1, 2024-06-01, 1w
    核心服务重构    :a2, after a1, 1w
    Agent基础实现   :a3, after a2, 1w
    编排器和API重构 :a4, after a3, 1w
    section 第二阶段
    精简Agent设计   :b1, after a4, 1w
    工作流实现      :b2, after b1, 1w
    评估体系        :b3, after b2, 1w
    section 第三阶段
    知识库构建      :c1, after b3, 1w
    向量化存储      :c2, after c1, 1w
    section 第四阶段
    性能优化        :d1, after c2, 0.5w
    MCP集成         :d2, after d1, 0.5w
```

## 风险控制策略

### 技术风险
- **重构风险**：采用渐进式重构，保持向后兼容
- **性能风险**：建立性能基准，持续监控
- **集成风险**：分阶段集成，充分测试

### 业务风险
- **功能回归**：完整的测试覆盖
- **用户体验**：保持API兼容性
- **数据安全**：加强权限控制和数据保护

## 预期收益

### 短期收益（1-2个月）
- 代码质量提升：模块化设计，职责清晰
- 开发效率提升：统一接口，减少重复开发
- 维护成本降低：解耦设计，便于维护

### 长期收益（3-6个月）
- 系统可扩展性：新功能和Agent可快速集成
- 智能化水平：精简Agent协作，智能决策
- 知识积累：持续学习，效果不断优化

## 成功标准

1. **代码质量**：代码复用率提升50%，单个文件行数控制在500行以内
2. **性能指标**：CR处理速度提升30%，准确率达到85%以上
3. **可维护性**：新功能开发时间减少40%，Bug修复时间减少60%
4. **扩展性**：新Agent接入时间控制在1天以内