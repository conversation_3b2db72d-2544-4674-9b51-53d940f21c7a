# 登录功能复刻指南

## 1. 数据库模型设计

### 用户表 (User)
```python
class User(DataBaseModel, UserMixin):
    id = Char<PERSON>ield(max_length=32, primary_key=True)
    access_token = CharField(max_length=255, null=True, index=True)
    nickname = CharField(max_length=100, null=False, index=True)
    password = CharField(max_length=255, null=True, index=True)
    email = CharField(max_length=255, null=False, index=True)
    avatar = TextField(null=True)
    language = Char<PERSON>ield(max_length=32, null=True, default="Chinese", index=True)
    last_login_time = DateTimeField(null=True, index=True)
    is_authenticated = CharField(max_length=1, null=False, default="1", index=True)
    is_active = CharField(max_length=1, null=False, default="1", index=True)
    is_anonymous = CharField(max_length=1, null=False, default="0", index=True)
    login_channel = CharField(null=True, index=True)  # password/github/feishu/sso
    status = CharField(max_length=1, null=True, default="1", index=True)
    is_superuser = BooleanField(null=True, default=False, index=True)
    create_time = BigIntegerField(null=True, index=True)
    create_date = DateTimeField(null=True, index=True)
    update_time = BigIntegerField(null=True, index=True)
    update_date = DateTimeField(null=True, index=True)
```

### 租户表 (Tenant)
```python
class Tenant(DataBaseModel):
    id = CharField(max_length=32, primary_key=True)
    name = CharField(max_length=100, null=True, index=True)
    # 其他字段根据业务需要添加
```

### 用户租户关系表 (UserTenant)
```python
class UserTenant(DataBaseModel):
    id = CharField(max_length=32, primary_key=True)
    user_id = CharField(max_length=32, null=False, index=True)
    tenant_id = CharField(max_length=32, null=False, index=True)
    role = CharField(max_length=32, null=False, index=True)
    invited_by = CharField(max_length=32, null=False, index=True)
    status = CharField(max_length=1, null=True, default="1", index=True)
```

## 2. 配置文件设置

### service_conf.yaml
```yaml
oauth:
  # GitHub OAuth配置
  github:
    client_id: your_github_client_id
    secret_key: your_github_secret_key
    url: https://github.com/login/oauth/access_token
  
  # 飞书OAuth配置
  feishu:
    app_id: your_feishu_app_id
    app_secret: your_feishu_app_secret
    app_access_token_url: https://open.feishu.cn/open-apis/auth/v3/app_access_token/internal
    user_access_token_url: https://open.feishu.cn/open-apis/authen/v1/oidc/access_token
    grant_type: 'authorization_code'
  
  # SSO配置
  sso:
    login_uri: '/login'
    auth_uri: '/oauth2.0/access-token'
    user_info_uri: '/api/session/userinfo'
    logout_uri: '/oauth2.0/logout'
    
    env:
      test:
        client_id: 'your_sso_client_id'
        secret: 'your_sso_secret'
        sso_host: 'https://your-sso-host.com/sson'
        api_host: 'https://your-sso-host.com/open'
      
      prod:
        client_id: 'your_prod_sso_client_id'
        secret: 'your_prod_sso_secret'
        sso_host: 'https://your-prod-sso-host.com/sson'
        api_host: 'https://your-prod-sso-host.com/open'
```

## 3. 核心登录路由

### 普通密码登录
```python
@manager.route("/login", methods=["POST"])
def login():
    if not request.json:
        return get_json_result(
            data=False, 
            code=settings.RetCode.AUTHENTICATION_ERROR, 
            message="Unauthorized!"
        )

    email = request.json.get("email", "")
    users = UserService.query(email=email)
    if not users:
        return get_json_result(
            data=False,
            code=settings.RetCode.AUTHENTICATION_ERROR,
            message=f"Email: {email} is not registered!",
        )

    password = request.json.get("password")
    try:
        password = decrypt(password)  # 解密前端传来的密码
    except BaseException:
        return get_json_result(
            data=False, 
            code=settings.RetCode.SERVER_ERROR, 
            message="Fail to crypt password"
        )

    user = UserService.query_user(email, password)
    if user:
        response_data = user.to_json()
        user.access_token = get_uuid()
        login_user(user)
        user.update_time = current_timestamp()
        user.update_date = datetime_format(datetime.now())
        user.save()
        return construct_response(
            data=response_data, 
            auth=user.get_id(), 
            message="Welcome back!"
        )
    else:
        return get_json_result(
            data=False,
            code=settings.RetCode.AUTHENTICATION_ERROR,
            message="Email and password do not match!",
        )
```

### 用户注册
```python
@manager.route("/register", methods=["POST"])
@validate_request("nickname", "email", "password")
def user_add():
    req = request.json
    email_address = req["email"]

    # 验证邮箱格式
    if not re.match(r"^[\w\._-]+@([\w_-]+\.)+[\w-]{2,}$", email_address):
        return get_json_result(
            data=False,
            message=f"Invalid email address: {email_address}!",
            code=settings.RetCode.OPERATING_ERROR,
        )

    # 检查邮箱是否已注册
    if UserService.query(email=email_address):
        return get_json_result(
            data=False,
            message=f"Email: {email_address} has already registered!",
            code=settings.RetCode.OPERATING_ERROR,
        )

    nickname = req["nickname"]
    user_dict = {
        "access_token": get_uuid(),
        "email": email_address,
        "nickname": nickname,
        "password": decrypt(req["password"]),
        "login_channel": "password",
        "last_login_time": get_format_time(),
        "is_superuser": False,
    }

    user_id = get_uuid()
    try:
        users = user_register(user_id, user_dict)
        if not users:
            raise Exception(f"Fail to register {email_address}.")
        user = users[0]
        login_user(user)
        return construct_response(
            data=user.to_json(),
            auth=user.get_id(),
            message=f"{nickname}, welcome aboard!",
        )
    except Exception as e:
        rollback_user_registration(user_id)
        return get_json_result(
            data=False,
            message=f"User registration failure, error: {str(e)}",
            code=settings.RetCode.EXCEPTION_ERROR,
        )
```

### SSO登录
```python
@manager.route("/sso_login", methods=["GET"])
def sso_login():
    if not settings.SSO_CONFIG:
        return get_json_result(
            data=False, 
            code=settings.RetCode.SERVER_ERROR, 
            message="SSO配置未设置"
        )
    
    client_id = settings.SSO_CONFIG.get("client_id")
    # 构建回调URL
    frontend_url = request.headers.get('Origin') or request.host_url.rstrip('/')
    redirect_uri = urllib.parse.quote(f"{frontend_url}/api/v1/user/sso_callback")
    t = int(datetime.now().timestamp() * 1000)
    url = settings.SSO_CONFIG.get('sso_host') + settings.SSO_CONFIG.get('login_uri')
    url += f'?client_id={client_id}&redirect_uri={redirect_uri}&t={str(t)}'
    
    return redirect(url)

@manager.route("/sso_callback", methods=["GET"])
def sso_callback():
    code = request.args.get('code')
    if not code:
        return get_json_result(
            data=False, 
            code=settings.RetCode.AUTHENTICATION_ERROR, 
            message="SSO验证失败：未获取到授权码"
        )
    
    # 获取ssoid
    token_data = get_ssoid(code)
    if not token_data or not token_data.get('accessToken'):
        return get_json_result(
            data=False, 
            code=settings.RetCode.AUTHENTICATION_ERROR, 
            message="SSO验证失败：授权码无效"
        )
    
    # 获取用户信息
    ssoid = token_data['accessToken']
    user_info = get_sso_user_info(ssoid)
    if not user_info:
        return get_json_result(
            data=False, 
            code=settings.RetCode.AUTHENTICATION_ERROR, 
            message="SSO验证失败：无法获取用户信息"
        )
    
    email = user_info.get('email')
    users = UserService.query(email=email)
    
    if not users:
        # 新用户注册
        user_id = user_info.get('uid')
        nickname = user_info.get('loginName') or user_info.get('name')
        users = user_register(user_id, {
            "access_token": ssoid,
            "email": email,
            "nickname": nickname,
            "login_channel": "sso",
            "last_login_time": get_format_time(),
            "is_superuser": False,
        })
        user = users[0]
        login_user(user)
        return redirect(f"/?auth={user.get_id()}")
    
    # 已有用户登录
    user = users[0]
    user.access_token = get_uuid()
    login_user(user)
    user.save()
    return redirect(f"/?auth={user.get_id()}")
```

## 4. SSO工具函数

### sso_utils.py
```python
import hmac
import hashlib
import base64
import requests
import logging
from datetime import datetime
from enum import Enum

class RequestMethod(Enum):
    GET = 'GET'
    POST = 'POST'

def build_header(uri, method, client_id, secret, loc):
    """构建SSO BA认证头部"""
    if loc and not uri.startswith(loc):
        uri = loc + uri
    
    now_time = datetime.utcnow().strftime('%a, %d %b %Y %H:%M:%S GMT')
    string_sign = method + ' %s\\n%s' % (uri, now_time)
    sign = hmac.new(bytes(secret, 'utf-8'), bytes(string_sign, 'utf-8'), hashlib.sha1).digest()
    signature = str(base64.b64encode(sign), 'utf-8').replace("\\n", '')
    
    return {
        'Date': now_time,
        'Authorization': 'MWS' + ' ' + client_id + ':' + signature,
        'Content-Type': 'application/json;charset=UTF-8'
    }

def get_ssoid(code):
    """根据code获取ssoid"""
    config = settings.SSO_CONFIG
    url = config.get('sso_host') + config.get('auth_uri')
    t = int(datetime.now().timestamp() * 1000)
    params = {'code': code, 't': t}
    
    headers = build_header(
        config.get('auth_uri'),
        RequestMethod.GET.value,
        config.get('client_id'),
        config.get('secret'),
        '/sson'
    )
    
    try:
        result = requests.get(url=url, params=params, headers=headers)
        if result.status_code == 200:
            data = result.json()
            if data and data['code'] == 200 and data['data']:
                return data['data']
        return None
    except Exception as e:
        logging.exception(f"获取ssoid异常: {str(e)}")
        return None

def get_user_info(ssoid):
    """根据ssoid获取用户信息"""
    config = settings.SSO_CONFIG
    url = config.get('api_host') + config.get('user_info_uri')
    
    headers = build_header(
        config.get('user_info_uri'),
        RequestMethod.POST.value,
        config.get('client_id'),
        config.get('secret'),
        '/open'
    )
    
    params = {'accessToken': ssoid}
    
    try:
        result = requests.post(url=url, json=params, headers=headers)
        if result.status_code == 200:
            data = result.json()
            if data and data['code'] == 200 and data['data']:
                return data['data']
        return None
    except Exception as e:
        logging.exception(f"获取用户信息异常: {str(e)}")
        return None
```

## 5. 用户服务层

### user_service.py
```python
from api.db.services.common_service import CommonService
from api.db.db_models import User, Tenant, UserTenant
from werkzeug.security import generate_password_hash, check_password_hash

class UserService(CommonService):
    model = User

    @classmethod
    def query_user(cls, email, password):
        """验证用户登录"""
        users = cls.query(email=email)
        if users:
            user = users[0]
            if check_password_hash(user.password, password):
                return user
        return None

    @classmethod
    def save(cls, **kwargs):
        """保存用户，密码自动加密"""
        if 'password' in kwargs and kwargs['password']:
            kwargs['password'] = generate_password_hash(kwargs['password'])
        return super().save(**kwargs)

class TenantService(CommonService):
    model = Tenant

class UserTenantService(CommonService):
    model = UserTenant
```

## 6. 用户注册函数

```python
def user_register(user_id, user):
    """用户注册函数"""
    user["id"] = user_id
    
    # 创建租户
    tenant = {
        "id": user_id,
        "name": user["nickname"] + "'s Kingdom",
        # 其他默认配置
    }
    
    # 创建用户租户关系
    usr_tenant = {
        "tenant_id": user_id,
        "user_id": user_id,
        "invited_by": user_id,
        "role": "OWNER",
    }

    # 保存到数据库
    if not UserService.save(**user):
        return None
    TenantService.insert(**tenant)
    UserTenantService.insert(**usr_tenant)
    
    return UserService.query(email=user["email"])

def rollback_user_registration(user_id):
    """回滚用户注册"""
    try:
        UserService.delete_by_id(user_id)
        TenantService.delete_by_id(user_id)
        u = UserTenantService.query(tenant_id=user_id)
        if u:
            UserTenantService.delete_by_id(u[0].id)
    except Exception:
        pass
```

## 7. 前端集成

### 登录页面示例
```javascript
// 普通登录
const login = async (email, password) => {
  const response = await fetch('/api/v1/user/login', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      email: email,
      password: btoa(password) // 前端加密
    })
  });
  return response.json();
};

// SSO登录
const ssoLogin = () => {
  window.location.href = '/api/v1/user/sso_login';
};

// GitHub登录
const githubLogin = () => {
  window.location.href = '/api/v1/user/github_login';
};
```

## 8. 部署注意事项

1. **环境变量配置**：
   - `RAG_ENV`: 设置运行环境 (test/prod)
   - `SECRET_KEY`: JWT密钥
   - 数据库连接配置

2. **OAuth回调URL配置**：
   - GitHub: `http://your-domain.com/api/v1/user/github_callback`
   - 飞书: `http://your-domain.com/api/v1/user/feishu_callback`
   - SSO: `http://your-domain.com/api/v1/user/sso_callback`

3. **依赖包安装**：
```bash
pip install flask flask-login peewee werkzeug requests
```

4. **数据库初始化**：
```python
# 创建数据库表
from api.db.db_models import init_database_tables
init_database_tables()
```

## 9. 安全考虑

1. **密码加密**: 使用 `werkzeug.security` 进行密码哈希
2. **JWT Token**: 使用安全的密钥生成访问令牌
3. **HTTPS**: 生产环境必须使用HTTPS
4. **CSRF保护**: 添加CSRF令牌验证
5. **输入验证**: 严格验证所有用户输入

这个登录系统支持多种登录方式，具有良好的扩展性和安全性。你可以根据实际需求调整配置和功能。