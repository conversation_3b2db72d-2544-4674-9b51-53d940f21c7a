# 第一阶段：项目架构重构详细计划

## 阶段概述

**目标**：重构项目架构，实现模块化设计，提高代码复用性和可维护性  
**时间**：4周  
**优先级**：P0（基础架构，后续阶段的前提）

## 当前架构问题分析

### 代码结构问题
1. **服务类过于庞大**：`cr_lc_service.py`有920行，职责不清晰
2. **依赖关系混乱**：服务间直接依赖，难以单独测试
3. **配置分散**：配置散落在多个文件中，难以统一管理
4. **缺乏抽象层**：没有统一的基类和接口定义

### 目录结构问题
```
当前结构（存在问题）：
├── api/
│   ├── service/          # 服务层混在API层中
│   └── apps/            # 业务逻辑分散
├── common/              # 公共组件职责不清
├── infra/               # 基础设施层不完整
└── utils/               # 工具函数杂乱
```

## 新架构设计

### 1. 目录结构重新设计

```
shangou_ai_cr/
├── README.md                    # 项目说明文档
├── requirements.txt             # 依赖管理
├── main.py                     # 应用入口
├── config/                     # 配置管理层
│   ├── __init__.py
│   ├── base_config.py          # 基础配置类
│   ├── cr_config.py            # CR相关配置
│   ├── llm_config.py           # LLM配置
│   ├── mcp_config.py           # MCP协议配置
│   ├── environment.py          # 环境变量管理
│   └── cr_rules/               # CR规则配置
│       ├── python_base.json    # Python基础规则
│       ├── python_security.json # Python安全规则
│       ├── java_base.json      # Java基础规则
│       └── custom_rules/       # 项目定制规则
├── core/                       # 核心业务层
│   ├── __init__.py
│   ├── agents/                 # Agent实现
│   │   ├── __init__.py
│   │   ├── base_agent.py       # Agent基类
│   │   ├── chunking_agent.py   # 代码分块Agent
│   │   ├── qa_agent.py         # 问答生成Agent
│   │   ├── rule_matching_agent.py # 规则匹配Agent
│   │   ├── llm_review_agent.py # LLM审查Agent
│   │   ├── evaluation_agent.py # 评估Agent
│   │   └── feedback_agent.py   # 反馈Agent
│   ├── chains/                 # LangChain链定义
│   │   ├── __init__.py
│   │   ├── base_chain.py       # 链基类
│   │   ├── cr_chain.py         # CR主链
│   │   ├── intelligent_chain.py # 智能链
│   │   └── evaluation_chain.py # 评估链
│   ├── orchestrator/           # 编排器
│   │   ├── __init__.py
│   │   ├── agent_orchestrator.py # Agent编排器
│   │   └── workflow_manager.py   # 工作流管理器
│   └── knowledge/              # 知识库管理
│       ├── __init__.py
│       ├── chunk_manager.py    # 分块管理
│       ├── knowledge_base.py   # 知识库
│       ├── vector_store.py     # 向量存储
│       └── extractors/         # 知识提取器
├── services/                   # 服务层（重构后）
│   ├── __init__.py
│   ├── base_service.py         # 服务基类
│   ├── git_service.py          # Git服务
│   ├── llm_service.py          # LLM服务
│   ├── devmind_service.py      # DevMind服务
│   ├── notification_service.py # 通知服务
│   ├── mcp_service.py          # MCP协议服务
│   └── external/               # 外部服务适配器
│       ├── __init__.py
│       ├── horn_adapter.py     # Horn配置服务适配器
│       ├── kms_adapter.py      # KMS密钥服务适配器
│       ├── daxiang_adapter.py  # 大象通知适配器
│       └── mcp_adapter.py      # MCP协议适配器
├── api/                        # API层
│   ├── __init__.py
│   ├── v1/                     # API版本管理
│   │   ├── __init__.py
│   │   ├── cr_api.py           # CR相关API
│   │   ├── chunk_api.py        # 分块相关API
│   │   ├── knowledge_api.py    # 知识库API
│   │   └── evaluation_api.py   # 评估API
│   ├── middleware/             # 中间件
│   │   ├── __init__.py
│   │   ├── auth_middleware.py  # 认证中间件
│   │   ├── logging_middleware.py # 日志中间件
│   │   └── performance_middleware.py # 性能监控中间件
│   └── schemas/                # API数据模型
│       ├── __init__.py
│       ├── cr_schemas.py       # CR相关数据模型
│       ├── chunk_schemas.py    # 分块相关数据模型
│       └── common_schemas.py   # 通用数据模型
├── utils/                      # 工具库
│   ├── __init__.py
│   ├── code_utils.py           # 代码处理工具
│   ├── file_utils.py           # 文件处理工具
│   ├── validation_utils.py     # 验证工具
│   ├── performance_utils.py    # 性能监控工具
│   └── decorators.py           # 装饰器工具
├── infra/                      # 基础设施层
│   ├── __init__.py
│   ├── database/               # 数据库相关
│   │   ├── __init__.py
│   │   ├── models.py           # 数据模型
│   │   ├── repositories.py     # 数据仓库
│   │   └── migrations/         # 数据库迁移
│   ├── cache/                  # 缓存相关
│   │   ├── __init__.py
│   │   ├── redis_cache.py      # Redis缓存
│   │   └── memory_cache.py     # 内存缓存
│   └── monitoring/             # 监控相关
│       ├── __init__.py
│       ├── metrics.py          # 指标收集
│       ├── health_check.py     # 健康检查
│       └── logger.py           # 日志配置
├── tests/                      # 测试
│   ├── __init__.py
│   ├── unit/                   # 单元测试
│   │   ├── test_agents/        # Agent测试
│   │   ├── test_services/      # 服务测试
│   │   └── test_utils/         # 工具测试
│   ├── integration/            # 集成测试
│   │   ├── test_api/           # API测试
│   │   └── test_workflows/     # 工作流测试
│   └── fixtures/               # 测试数据
│       ├── code_samples/       # 代码样本
│       └── mock_data/          # 模拟数据
└── docs/                       # 文档
    ├── architecture.md         # 架构文档
    ├── api_reference.md        # API参考
    ├── deployment.md           # 部署文档
    └── development.md          # 开发指南
```

### 2. 核心组件设计

#### 2.1 Agent基类设计

```python
# core/agents/base_agent.py
from abc import ABC, abstractmethod
from typing import Any, Dict, Optional, List
from pydantic import BaseModel, Field
import asyncio
import logging
from datetime import datetime

class AgentConfig(BaseModel):
    """Agent配置基类"""
    name: str
    version: str = "1.0"
    enabled: bool = True
    timeout: int = 30
    retry_count: int = 3
    max_concurrent: int = 10
    dependencies: List[str] = Field(default_factory=list)

class AgentResult(BaseModel):
    """Agent执行结果"""
    success: bool
    data: Dict[str, Any] = Field(default_factory=dict)
    error: Optional[str] = None
    execution_time: float = 0.0
    metadata: Dict[str, Any] = Field(default_factory=dict)

class BaseAgent(ABC):
    """Agent基类，定义统一接口"""
    
    def __init__(self, config: AgentConfig):
        self.config = config
        self.name = config.name
        self.logger = logging.getLogger(f"agent.{self.name}")
        self._semaphore = asyncio.Semaphore(config.max_concurrent)
        
    @abstractmethod
    async def execute(self, input_data: Dict[str, Any]) -> AgentResult:
        """执行Agent任务"""
        pass
    
    @abstractmethod
    def validate_input(self, input_data: Dict[str, Any]) -> bool:
        """验证输入数据"""
        pass
    
    async def execute_with_retry(self, input_data: Dict[str, Any]) -> AgentResult:
        """带重试机制的执行"""
        async with self._semaphore:
            for attempt in range(self.config.retry_count):
                try:
                    start_time = datetime.now()
                    
                    if not self.validate_input(input_data):
                        return AgentResult(
                            success=False,
                            error="Input validation failed"
                        )
                    
                    result = await asyncio.wait_for(
                        self.execute(input_data),
                        timeout=self.config.timeout
                    )
                    
                    result.execution_time = (datetime.now() - start_time).total_seconds()
                    return result
                    
                except asyncio.TimeoutError:
                    self.logger.warning(f"Agent {self.name} timeout on attempt {attempt + 1}")
                    if attempt == self.config.retry_count - 1:
                        return AgentResult(
                            success=False,
                            error=f"Timeout after {self.config.retry_count} attempts"
                        )
                except Exception as e:
                    self.logger.error(f"Agent {self.name} error on attempt {attempt + 1}: {str(e)}")
                    if attempt == self.config.retry_count - 1:
                        return AgentResult(
                            success=False,
                            error=str(e)
                        )
                
                await asyncio.sleep(2 ** attempt)  # 指数退避
    
    def get_capabilities(self) -> Dict[str, Any]:
        """获取Agent能力描述"""
        return {
            "name": self.name,
            "version": self.config.version,
            "enabled": self.config.enabled,
            "dependencies": self.config.dependencies,
            "max_concurrent": self.config.max_concurrent
        }
    
    async def health_check(self) -> bool:
        """健康检查"""
        try:
            test_data = {"test": True}
            result = await self.execute_with_retry(test_data)
            return result.success
        except Exception:
            return False
```

#### 2.2 服务基类设计

```python
# services/base_service.py
from abc import ABC, abstractmethod
from typing import Any, Dict, Optional
import logging
from datetime import datetime
from enum import Enum

class ServiceStatus(Enum):
    """服务状态枚举"""
    INITIALIZING = "initializing"
    RUNNING = "running"
    STOPPED = "stopped"
    ERROR = "error"

class BaseService(ABC):
    """服务基类，提供统一的服务接口"""
    
    def __init__(self, name: str, config: Optional[Dict[str, Any]] = None):
        self.name = name
        self.config = config or {}
        self.logger = logging.getLogger(f"service.{name}")
        self.status = ServiceStatus.INITIALIZING
        self.start_time = None
        self.error_count = 0
        
    @abstractmethod
    async def initialize(self) -> bool:
        """初始化服务"""
        pass
    
    @abstractmethod
    async def cleanup(self) -> bool:
        """清理资源"""
        pass
    
    @abstractmethod
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        pass
    
    async def start(self) -> bool:
        """启动服务"""
        try:
            self.logger.info(f"Starting service {self.name}")
            success = await self.initialize()
            if success:
                self.status = ServiceStatus.RUNNING
                self.start_time = datetime.now()
                self.logger.info(f"Service {self.name} started successfully")
            else:
                self.status = ServiceStatus.ERROR
                self.logger.error(f"Failed to start service {self.name}")
            return success
        except Exception as e:
            self.status = ServiceStatus.ERROR
            self.logger.error(f"Error starting service {self.name}: {str(e)}")
            return False
    
    async def stop(self) -> bool:
        """停止服务"""
        try:
            self.logger.info(f"Stopping service {self.name}")
            success = await self.cleanup()
            self.status = ServiceStatus.STOPPED
            self.logger.info(f"Service {self.name} stopped")
            return success
        except Exception as e:
            self.logger.error(f"Error stopping service {self.name}: {str(e)}")
            return False
    
    def get_service_info(self) -> Dict[str, Any]:
        """获取服务信息"""
        uptime = None
        if self.start_time:
            uptime = (datetime.now() - self.start_time).total_seconds()
            
        return {
            "name": self.name,
            "status": self.status.value,
            "uptime": uptime,
            "error_count": self.error_count,
            "config": self.config
        }
    
    def increment_error_count(self):
        """增加错误计数"""
        self.error_count += 1
```

#### 2.3 配置管理设计

```python
# config/base_config.py
from pydantic import BaseModel, Field
from typing import Dict, Any, Optional, List
import os
import json
from pathlib import Path

class DatabaseConfig(BaseModel):
    """数据库配置"""
    type: str = "mysql"
    host: str = "localhost"
    port: int = 3306
    database: str = "shangou_ai_cr"
    username: str = "root"
    password: str = ""
    pool_size: int = 10

class LLMConfig(BaseModel):
    """LLM配置"""
    provider: str = "openai"
    model_name: str = "gpt-3.5-turbo"
    api_key: str = ""
    base_url: Optional[str] = None
    temperature: float = 0.1
    max_tokens: int = 4000
    timeout: int = 30

class CacheConfig(BaseModel):
    """缓存配置"""
    redis_host: str = "localhost"
    redis_port: int = 6379
    redis_db: int = 0
    redis_password: Optional[str] = None
    ttl: int = 3600
    max_memory_cache_size: int = 1000

class MonitoringConfig(BaseModel):
    """监控配置"""
    enable_metrics: bool = True
    metrics_port: int = 8080
    log_level: str = "INFO"
    log_file: Optional[str] = None

class BaseConfig(BaseModel):
    """基础配置类"""
    app_name: str = "Shangou AI CR"
    version: str = "1.0.0"
    debug: bool = False
    secret_key: str = Field(default_factory=lambda: os.urandom(32).hex())
    
    # 数据库配置
    database: DatabaseConfig = Field(default_factory=DatabaseConfig)
    
    # LLM配置
    llm: LLMConfig = Field(default_factory=LLMConfig)
    
    # 缓存配置
    cache: CacheConfig = Field(default_factory=CacheConfig)
    
    # 监控配置
    monitoring: MonitoringConfig = Field(default_factory=MonitoringConfig)

    # MCP协议配置
    mcp: MCPConfig = Field(default_factory=MCPConfig)
    
    @classmethod
    def load_from_file(cls, config_path: str) -> "BaseConfig":
        """从文件加载配置"""
        config_file = Path(config_path)
        if not config_file.exists():
            return cls()
        
        with open(config_file, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
        
        return cls(**config_data)
    
    @classmethod
    def load_from_env(cls) -> "BaseConfig":
        """从环境变量加载配置"""
        config_data = {}
        
        # 基础配置
        if os.getenv("APP_NAME"):
            config_data["app_name"] = os.getenv("APP_NAME")
        if os.getenv("DEBUG"):
            config_data["debug"] = os.getenv("DEBUG").lower() == "true"
        
        # 数据库配置
        db_config = {}
        if os.getenv("DB_HOST"):
            db_config["host"] = os.getenv("DB_HOST")
        if os.getenv("DB_PORT"):
            db_config["port"] = int(os.getenv("DB_PORT"))
        if os.getenv("DB_NAME"):
            db_config["database"] = os.getenv("DB_NAME")
        if os.getenv("DB_USER"):
            db_config["username"] = os.getenv("DB_USER")
        if os.getenv("DB_PASSWORD"):
            db_config["password"] = os.getenv("DB_PASSWORD")
        
        if db_config:
            config_data["database"] = db_config
        
        # LLM配置
        llm_config = {}
        if os.getenv("LLM_API_KEY"):
            llm_config["api_key"] = os.getenv("LLM_API_KEY")
        if os.getenv("LLM_MODEL"):
            llm_config["model_name"] = os.getenv("LLM_MODEL")
        if os.getenv("LLM_BASE_URL"):
            llm_config["base_url"] = os.getenv("LLM_BASE_URL")
        
        if llm_config:
            config_data["llm"] = llm_config

        # MCP配置
        mcp_config = {}
        if os.getenv("MCP_ENABLED"):
            mcp_config["enabled"] = os.getenv("MCP_ENABLED").lower() == "true"
        if os.getenv("MCP_SSE_URL"):
            mcp_config["client"] = {"sse_url": os.getenv("MCP_SSE_URL")}

        if mcp_config:
            config_data["mcp"] = mcp_config

        return cls(**config_data)
    
    def save_to_file(self, config_path: str):
        """保存配置到文件"""
        config_file = Path(config_path)
        config_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(self.dict(), f, indent=2, ensure_ascii=False)
```

#### 2.4 MCP协议集成设计

基于您提供的MCP Python SDK接入方式，我们设计了完整的MCP协议集成方案：

```python
# config/mcp_config.py
from pydantic import BaseModel, Field
from typing import Dict, Any, Optional, List

class MCPServerConfig(BaseModel):
    """MCP服务器配置"""
    name: str
    url: str
    timeout: int = 30
    retry_count: int = 3
    enabled: bool = True

class MCPClientConfig(BaseModel):
    """MCP客户端配置"""
    sse_url: str = "mcphub平台的接入点URL"
    timeout: int = 30
    max_connections: int = 10
    retry_interval: int = 5

class MCPConfig(BaseModel):
    """MCP协议配置"""
    enabled: bool = True
    client: MCPClientConfig = Field(default_factory=MCPClientConfig)
    servers: List[MCPServerConfig] = Field(default_factory=list)

    # Agent到MCP服务的映射
    agent_service_mapping: Dict[str, str] = Field(default_factory=lambda: {
        "chunking_agent": "code_analysis_service",
        "qa_agent": "knowledge_service",
        "rule_matching_agent": "rule_check_service",
        "llm_review_agent": "code_review_service",
        "evaluation_agent": "evaluation_service",
        "feedback_agent": "feedback_service"
    })

# services/mcp_service.py
import asyncio
import logging
from typing import Dict, Any, Optional, List
from mcp import ClientSession
from mcp.client.sse import sse_client

from config.mcp_config import MCPConfig
from services.base_service import BaseService

class MCPService(BaseService):
    """MCP协议服务，负责与MCP Hub平台的连接和通信"""

    def __init__(self, config: MCPConfig):
        super().__init__("mcp_service", config.dict())
        self.mcp_config = config
        self.session: Optional[ClientSession] = None
        self.sse_ctx = None
        self.session_ctx = None
        self.available_tools: Dict[str, Any] = {}

    async def initialize(self) -> bool:
        """初始化MCP连接"""
        try:
            if not self.mcp_config.enabled:
                self.logger.info("MCP服务已禁用")
                return True

            # 建立SSE连接
            self.sse_ctx = sse_client(url=self.mcp_config.client.sse_url)
            streams = await self.sse_ctx.__aenter__()
            self.session_ctx = ClientSession(*streams)
            self.session = await self.session_ctx.__aenter__()

            # 初始化MCP会话
            await self.session.initialize()
            self.logger.info("MCP会话已初始化")

            # 获取可用工具
            await self._load_available_tools()

            return True

        except Exception as e:
            self.logger.error(f"初始化MCP服务失败: {str(e)}")
            return False

    async def cleanup(self) -> bool:
        """清理MCP连接"""
        try:
            if self.session_ctx:
                await self.session_ctx.__aexit__(None, None, None)
            if self.sse_ctx:
                await self.sse_ctx.__aexit__(None, None, None)
            self.logger.info("MCP连接已关闭")
            return True
        except Exception as e:
            self.logger.error(f"清理MCP连接失败: {str(e)}")
            return False

    async def _load_available_tools(self):
        """加载可用工具列表"""
        try:
            response = await self.session.list_tools()
            tools = response.tools

            self.available_tools = {tool.name: tool for tool in tools}

            self.logger.info(f"加载了 {len(self.available_tools)} 个MCP工具:")
            for tool_name, tool in self.available_tools.items():
                self.logger.info(f"- {tool_name}: {tool.description}")

        except Exception as e:
            self.logger.error(f"加载MCP工具失败: {str(e)}")

    async def call_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """调用MCP工具"""
        try:
            if not self.session:
                raise RuntimeError("MCP会话未初始化")

            if tool_name not in self.available_tools:
                raise ValueError(f"工具 {tool_name} 不可用")

            # 调用工具
            result = await self.session.call_tool(tool_name, arguments)

            return {
                "success": True,
                "data": result,
                "tool_name": tool_name
            }

        except Exception as e:
            self.logger.error(f"调用MCP工具 {tool_name} 失败: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "tool_name": tool_name
            }

    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        if not self.mcp_config.enabled:
            return {
                "status": "disabled",
                "message": "MCP服务已禁用"
            }

        if not self.session:
            return {
                "status": "error",
                "message": "MCP会话未初始化"
            }

        try:
            # 尝试列出工具来验证连接
            await self.session.list_tools()
            return {
                "status": "healthy",
                "tools_count": len(self.available_tools),
                "session_active": True
            }
        except Exception as e:
            return {
                "status": "error",
                "message": f"MCP连接异常: {str(e)}"
            }

    def get_available_tools(self) -> List[str]:
        """获取可用工具列表"""
        return list(self.available_tools.keys())

    def get_tool_info(self, tool_name: str) -> Optional[Dict[str, Any]]:
        """获取工具信息"""
        tool = self.available_tools.get(tool_name)
        if tool:
            return {
                "name": tool.name,
                "description": tool.description,
                "input_schema": getattr(tool, 'input_schema', None)
            }
        return None

# services/external/mcp_adapter.py
from typing import Dict, Any, Optional
import asyncio
from core.agents.base_agent import BaseAgent, AgentResult
from services.mcp_service import MCPService

class MCPAdapter:
    """MCP协议适配器，将Agent调用转换为MCP工具调用"""

    def __init__(self, mcp_service: MCPService, agent_mapping: Dict[str, str]):
        self.mcp_service = mcp_service
        self.agent_mapping = agent_mapping  # Agent名称到MCP工具名称的映射

    async def execute_agent_via_mcp(self, agent_name: str, input_data: Dict[str, Any]) -> AgentResult:
        """通过MCP协议执行Agent任务"""
        try:
            # 获取对应的MCP工具名称
            tool_name = self.agent_mapping.get(agent_name)
            if not tool_name:
                return AgentResult(
                    success=False,
                    error=f"Agent {agent_name} 没有对应的MCP工具映射"
                )

            # 调用MCP工具
            result = await self.mcp_service.call_tool(tool_name, input_data)

            if result["success"]:
                return AgentResult(
                    success=True,
                    data=result["data"],
                    metadata={"mcp_tool": tool_name}
                )
            else:
                return AgentResult(
                    success=False,
                    error=result["error"],
                    metadata={"mcp_tool": tool_name}
                )

        except Exception as e:
            return AgentResult(
                success=False,
                error=f"MCP适配器执行失败: {str(e)}"
            )

    async def batch_execute(self, tasks: List[Dict[str, Any]]) -> List[AgentResult]:
        """批量执行MCP工具调用"""
        results = []

        # 并发执行多个任务
        async def execute_task(task):
            agent_name = task.get("agent_name")
            input_data = task.get("input_data", {})
            return await self.execute_agent_via_mcp(agent_name, input_data)

        tasks_coroutines = [execute_task(task) for task in tasks]
        results = await asyncio.gather(*tasks_coroutines, return_exceptions=True)

        # 处理异常结果
        processed_results = []
        for result in results:
            if isinstance(result, Exception):
                processed_results.append(AgentResult(
                    success=False,
                    error=f"批量执行异常: {str(result)}"
                ))
            else:
                processed_results.append(result)

        return processed_results

    def get_supported_agents(self) -> List[str]:
        """获取支持的Agent列表"""
        return list(self.agent_mapping.keys())

    async def validate_mcp_tools(self) -> Dict[str, bool]:
        """验证MCP工具可用性"""
        available_tools = self.mcp_service.get_available_tools()
        validation_result = {}

        for agent_name, tool_name in self.agent_mapping.items():
            validation_result[agent_name] = tool_name in available_tools

        return validation_result

# 使用示例
async def example_mcp_integration():
    """MCP集成使用示例"""
    from config.mcp_config import MCPConfig, MCPClientConfig

    # 配置MCP
    mcp_config = MCPConfig(
        enabled=True,
        client=MCPClientConfig(
            sse_url="mcphub平台的接入点URL"
        )
    )

    # 初始化MCP服务
    mcp_service = MCPService(mcp_config)
    await mcp_service.start()

    # 创建适配器
    adapter = MCPAdapter(
        mcp_service=mcp_service,
        agent_mapping=mcp_config.agent_service_mapping
    )

    # 验证工具可用性
    validation = await adapter.validate_mcp_tools()
    print("MCP工具验证结果:", validation)

    # 执行单个Agent任务
    result = await adapter.execute_agent_via_mcp(
        agent_name="chunking_agent",
        input_data={
            "code": "def hello(): pass",
            "language": "python"
        }
    )
    print("执行结果:", result)

    # 批量执行任务
    batch_tasks = [
        {
            "agent_name": "chunking_agent",
            "input_data": {"code": "def test1(): pass", "language": "python"}
        },
        {
            "agent_name": "rule_matching_agent",
            "input_data": {"code": "def test2(): pass", "rules": ["pep8"]}
        }
    ]

    batch_results = await adapter.batch_execute(batch_tasks)
    print("批量执行结果:", batch_results)

    # 清理资源
    await mcp_service.stop()
```

## 实施步骤

### 第1周：基础架构搭建

#### 任务清单
- [ ] 创建新的目录结构
- [ ] 实现基类和接口定义
  - [ ] BaseAgent类实现
  - [ ] BaseService类实现
  - [ ] BaseChain类实现
- [ ] 配置管理重构
  - [ ] BaseConfig类实现
  - [ ] MCPConfig类实现
  - [ ] 环境变量管理
  - [ ] 配置验证机制
- [ ] MCP协议基础集成
  - [ ] MCP Python SDK依赖安装
  - [ ] MCPService基础实现
  - [ ] MCP连接测试
- [ ] 日志和监控基础设施
  - [ ] 统一日志配置
  - [ ] 性能监控装饰器
  - [ ] 健康检查端点

#### 验收标准
1. 新目录结构创建完成，所有基类可正常导入
2. 配置系统可以从文件和环境变量加载配置，包括MCP配置
3. MCP服务可以成功连接到MCP Hub平台并列出可用工具
4. 日志系统正常工作，可以输出结构化日志
5. 基础的健康检查接口可以访问，包括MCP连接状态

### 第2周：核心服务重构

#### 任务清单
- [ ] Git服务重构
  - [ ] 提取独立的GitService类
  - [ ] 统一Git操作接口
  - [ ] 添加错误处理和重试机制
  - [ ] 单元测试覆盖
- [ ] LLM服务重构
  - [ ] 多模型支持抽象
  - [ ] 统一调用接口
  - [ ] 性能监控集成
  - [ ] 连接池管理
- [ ] 外部服务适配器
  - [ ] Horn配置服务适配器
  - [ ] KMS密钥服务适配器
  - [ ] 大象通知适配器
  - [ ] MCP协议适配器实现
  - [ ] 统一的外部服务接口

#### 验收标准
1. 所有服务继承自BaseService，接口统一
2. 服务间依赖通过依赖注入实现
3. MCP适配器可以正常调用MCP工具并返回结果
4. 每个服务都有完整的单元测试
5. 服务启动和停止流程正常

### 第3周：Agent系统实现

#### 任务清单
- [ ] ChunkingAgent实现
  - [ ] 代码分块逻辑封装
  - [ ] 多语言支持
  - [ ] 依赖分析集成
  - [ ] 性能优化
- [ ] LLMReviewAgent实现
  - [ ] LLM调用封装
  - [ ] 提示词管理
  - [ ] 结果解析和验证
  - [ ] 错误处理
- [ ] RuleMatchingAgent实现
  - [ ] 规则匹配逻辑
  - [ ] 配置化规则管理
  - [ ] 优先级处理
  - [ ] 规则缓存
- [ ] Agent与MCP集成
  - [ ] Agent到MCP工具的映射配置
  - [ ] MCP调用的Agent包装器
  - [ ] 混合执行模式（本地+MCP）

#### 验收标准
1. 所有Agent继承自BaseAgent，接口统一
2. Agent可以独立运行和测试
3. Agent支持并发执行和重试机制
4. Agent可以通过MCP协议远程调用
5. Agent执行结果结构化，便于后续处理

### 第4周：编排器和API重构

#### 任务清单
- [ ] AgentOrchestrator实现
  - [ ] Agent注册和发现机制
  - [ ] 工作流编排引擎
  - [ ] 错误处理和恢复
  - [ ] 执行状态跟踪
- [ ] API层重构
  - [ ] RESTful API设计
  - [ ] 数据模型定义（Pydantic）
  - [ ] 中间件集成
  - [ ] API文档自动生成
- [ ] 测试和文档
  - [ ] 单元测试编写
  - [ ] 集成测试
  - [ ] API文档生成
  - [ ] 部署文档更新

#### 验收标准
1. AgentOrchestrator可以动态编排Agent执行
2. API接口向后兼容，现有功能正常
3. 测试覆盖率达到80%以上
4. 文档完整，便于开发和维护

## 重构收益

### 代码质量提升
- **模块化设计**：每个模块职责清晰，便于理解和维护
- **接口统一**：统一的基类和接口，减少学习成本
- **错误处理**：完善的错误处理和重试机制
- **测试友好**：依赖注入设计，便于单元测试

### 开发效率提升
- **代码复用**：通过基类和工具函数减少重复代码
- **配置管理**：统一的配置管理，支持多环境部署
- **开发工具**：完善的日志、监控和调试工具
- **文档完善**：自动生成的API文档和架构文档

### 系统可维护性
- **依赖解耦**：服务间通过接口交互，降低耦合度
- **扩展性强**：新功能可以通过实现接口快速集成
- **监控完善**：全面的性能监控和健康检查
- **部署简化**：容器化部署，支持多环境配置

### MCP协议集成收益
- **标准化通信**：基于MCP协议的标准化Agent间通信
- **分布式架构**：支持Agent分布式部署和远程调用
- **生态兼容**：与MCP生态系统无缝集成，可复用现有工具
- **扩展灵活**：可以轻松接入第三方MCP服务和工具
- **性能优化**：通过MCP Hub实现负载均衡和资源优化

## 风险控制

### 重构风险
- **向后兼容**：保持现有API接口不变
- **渐进式重构**：分模块逐步重构，避免大爆炸式改动
- **回滚机制**：每个阶段都有回滚方案

### 质量风险
- **测试覆盖**：每个重构模块都要有完整测试
- **代码审查**：重构代码必须经过代码审查
- **性能测试**：确保重构后性能不下降

### 进度风险
- **里程碑管控**：每周设置明确的里程碑
- **风险预警**：提前识别和处理风险
- **资源保障**：确保开发资源充足

### MCP集成风险
- **依赖风险**：MCP Python SDK版本兼容性和稳定性
- **网络风险**：MCP Hub连接稳定性，需要实现重连机制
- **性能风险**：远程调用延迟，需要本地缓存和降级策略
- **安全风险**：MCP通信安全，需要实现认证和加密

---

**负责人**：[@王琪琛](mtdaxiang://www.meituan.com/profile?uid=wangqichen02&isAt=true)  
**文档版本**：v1.0  
**最后更新**：2024-06-01