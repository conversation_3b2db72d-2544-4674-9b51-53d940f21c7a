# MCP工具集API化改造方案

## 改造概述

本文档详细描述了将现有服务改造为RESTful API形式的方案，以便MCP工具集接入。改造的核心目标是：
1. 将独立服务功能通过HTTP API暴露
2. 设计统一的API接口规范
3. 支持外部MCP factory将API包装成标准MCP工具
4. 确保与MCP协议标准兼容

## 目标服务分析

### 1. DaXiangService (大象通知服务)
**核心功能**：
- 获取访问令牌 (`get_access_token`)
- 用户ID转换 (`convert_emp_id_to_user_id`, `convert_userid_to_emp_id`)
- 消息发送 (待实现)

**API化价值**：企业内部通知、用户身份管理

### 2. ChunkService (代码分块服务)
**核心功能**：
- 单文件代码分块 (`chunk_code_file`)
- 批量文件分块 (`chunk_all_files`)
- 函数索引构建 (`build_func_index`)
- Diff代码分析 (`chunk_diff_code`)

**API化价值**：代码分析、依赖关系分析、代码审查

### 3. KmService (学城文档服务)
**核心功能**：
- 创建文档 (`create_doc`)
- 文档管理 (基于学城开放平台)

**API化价值**：知识管理、文档自动化

### 4. DevtoolsService (开发工具服务)
**核心功能**：
- 认证管理 (`get_token`, `get_token_by_sso`, `get_token_by_iam`)
- SSH初始化 (`init_ssh`)
- PR创建 (`create_pr`)

**API化价值**：开发流程自动化、代码协作

### 5. GitService (Git操作服务)
**核心功能**：
- 获取代码差异 (`get_diff`)
- 文件列表获取 (`list_repo_files`)
- 文件内容读取 (`read_file`)
- 仓库操作 (`init_repo`, `get_ssh_url`)

**API化价值**：版本控制、代码管理

## 统一API设计规范

### 1. 基础规范
- **协议**：HTTP/HTTPS
- **格式**：JSON
- **版本**：v1
- **前缀**：`/api/v1/mcp-tools/`
- **认证**：Bearer Token / API Key

### 2. 响应格式标准
```json
{
  "success": true,
  "data": {},
  "error": null,
  "metadata": {
    "timestamp": "2024-01-01T00:00:00Z",
    "request_id": "uuid",
    "service": "service_name",
    "version": "1.0.0"
  }
}
```

### 3. 错误处理标准
```json
{
  "success": false,
  "data": null,
  "error": {
    "code": "ERROR_CODE",
    "message": "Human readable error message",
    "details": {}
  },
  "metadata": {
    "timestamp": "2024-01-01T00:00:00Z",
    "request_id": "uuid",
    "service": "service_name"
  }
}
```

## 服务API设计

### 1. DaXiang通知服务API

#### 1.1 获取访问令牌
```
POST /api/v1/mcp-tools/daxiang/auth/token
Content-Type: application/json

{
  "app_id": "string",
  "app_secret": "string"
}

Response:
{
  "success": true,
  "data": {
    "access_token": "string",
    "expires_in": 7200,
    "token_type": "Bearer"
  }
}
```

#### 1.2 用户ID转换
```
POST /api/v1/mcp-tools/daxiang/users/convert
Content-Type: application/json

{
  "conversion_type": "emp_to_user" | "user_to_emp",
  "ids": ["id1", "id2", "id3"],
  "context": {}
}

Response:
{
  "success": true,
  "data": {
    "conversions": {
      "id1": "converted_id1",
      "id2": "converted_id2"
    },
    "failed": ["id3"]
  }
}
```

#### 1.3 发送通知消息
```
POST /api/v1/mcp-tools/daxiang/messages/send
Content-Type: application/json

{
  "recipients": ["user_id1", "user_id2"],
  "message": {
    "title": "string",
    "content": "string",
    "type": "text" | "markdown" | "card"
  },
  "options": {
    "urgent": false,
    "silent": false
  }
}
```

### 2. 代码分块服务API

#### 2.1 单文件代码分块
```
POST /api/v1/mcp-tools/chunk/file
Content-Type: application/json

{
  "project": "string",
  "repo": "string",
  "file_path": "string",
  "branch": "string",
  "options": {
    "resolve_dependencies": true,
    "include_imports": false
  }
}

Response:
{
  "success": true,
  "data": {
    "chunks": [
      {
        "id": "string",
        "type": "function" | "class" | "method",
        "name": "string",
        "content": "string",
        "start_line": 1,
        "end_line": 10,
        "dependencies": ["dep1", "dep2"],
        "complexity": 5
      }
    ],
    "file_info": {
      "path": "string",
      "language": "python",
      "total_lines": 100
    }
  }
}
```

#### 2.2 批量文件分块
```
POST /api/v1/mcp-tools/chunk/batch
Content-Type: application/json

{
  "project": "string",
  "repo": "string",
  "branch": "string",
  "file_patterns": ["*.py", "*.js"],
  "options": {
    "resolve_code": true,
    "max_files": 100
  }
}
```

#### 2.3 构建函数索引
```
POST /api/v1/mcp-tools/chunk/index
Content-Type: application/json

{
  "project": "string",
  "repo": "string",
  "branch": "string",
  "suffixes": [".py", ".js", ".java"],
  "use_cache": true
}

Response:
{
  "success": true,
  "data": {
    "index": {
      "function_name": {
        "file_path": "string",
        "start_line": 1,
        "end_line": 10,
        "dependencies": [],
        "signature": "string"
      }
    },
    "statistics": {
      "total_functions": 150,
      "total_files": 25,
      "languages": ["python", "javascript"]
    }
  }
}
```

#### 2.4 Diff代码分析
```
POST /api/v1/mcp-tools/chunk/diff
Content-Type: application/json

{
  "project": "string",
  "repo": "string",
  "diff_content": "string",
  "context": {
    "from_branch": "string",
    "to_branch": "string"
  }
}
```

### 3. 学城文档服务API

#### 3.1 创建文档
```
POST /api/v1/mcp-tools/km/documents
Content-Type: application/json

{
  "title": "string",
  "content": "string",
  "format": "markdown" | "html" | "text",
  "metadata": {
    "category": "string",
    "tags": ["tag1", "tag2"],
    "visibility": "public" | "private" | "team"
  },
  "context": {}
}

Response:
{
  "success": true,
  "data": {
    "document_id": "string",
    "url": "string",
    "created_at": "2024-01-01T00:00:00Z",
    "status": "created"
  }
}
```

#### 3.2 文档查询
```
GET /api/v1/mcp-tools/km/documents/{document_id}

Response:
{
  "success": true,
  "data": {
    "id": "string",
    "title": "string",
    "content": "string",
    "metadata": {},
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

### 4. 开发工具服务API

#### 4.1 获取认证令牌
```
POST /api/v1/mcp-tools/devtools/auth/token
Content-Type: application/json

{
  "auth_type": "sso" | "iam",
  "credentials": {
    "mis": "string",
    "ssoid": "string",
    "username": "string",
    "password": "string"
  }
}

Response:
{
  "success": true,
  "data": {
    "tokens": {
      "STS-TOKEN": "string",
      "oceanus-remote-appkey": "string",
      "oceanus-auth": "string"
    },
    "expires_in": 300
  }
}
```

#### 4.2 SSH初始化
```
POST /api/v1/mcp-tools/devtools/ssh/init
Content-Type: application/json

{
  "force_reinit": false
}

Response:
{
  "success": true,
  "data": {
    "status": "initialized",
    "public_key_fingerprint": "string",
    "setup_output": "string"
  }
}
```

#### 4.3 创建PR
```
POST /api/v1/mcp-tools/devtools/pr/create
Content-Type: application/json

{
  "repository": {
    "project": "string",
    "repo": "string"
  },
  "pr_info": {
    "title": "string",
    "description": "string",
    "source_branch": "string",
    "target_branch": "string"
  },
  "options": {
    "auto_merge": false,
    "delete_source": false
  }
}
```

### 5. Git操作服务API

#### 5.1 获取代码差异
```
POST /api/v1/mcp-tools/git/diff
Content-Type: application/json

{
  "project": "string",
  "repo": "string",
  "from_ref": "string",
  "to_ref": "string",
  "options": {
    "context_lines": 0,
    "filter": "AM",
    "exclude_patterns": ["*.lock", "node_modules"]
  }
}

Response:
{
  "success": true,
  "data": {
    "diff_content": "string",
    "statistics": {
      "files_changed": 5,
      "insertions": 100,
      "deletions": 50
    },
    "files": [
      {
        "path": "string",
        "status": "added" | "modified" | "deleted",
        "changes": 10
      }
    ]
  }
}
```

#### 5.2 获取文件列表
```
GET /api/v1/mcp-tools/git/files
Query Parameters:
- project: string
- repo: string
- branch: string (optional)
- suffixes: string[] (optional)
- limit: number (optional)

Response:
{
  "success": true,
  "data": {
    "files": [
      {
        "path": "string",
        "type": "file" | "directory",
        "size": 1024,
        "last_modified": "2024-01-01T00:00:00Z"
      }
    ],
    "total_count": 150,
    "filtered_count": 25
  }
}
```

#### 5.3 读取文件内容
```
GET /api/v1/mcp-tools/git/files/content
Query Parameters:
- project: string
- repo: string
- file_path: string
- branch: string (optional)

Response:
{
  "success": true,
  "data": {
    "content": "string",
    "encoding": "utf-8",
    "file_info": {
      "path": "string",
      "size": 1024,
      "language": "python"
    }
  }
}
```

## MCP工具集成方案

### 1. MCP工具定义结构
每个API端点都可以被包装成一个MCP工具，工具定义如下：

```json
{
  "name": "chunk_file",
  "description": "对单个代码文件进行分块分析",
  "input_schema": {
    "type": "object",
    "properties": {
      "project": {"type": "string", "description": "项目名称"},
      "repo": {"type": "string", "description": "仓库名称"},
      "file_path": {"type": "string", "description": "文件路径"},
      "branch": {"type": "string", "description": "分支名称"},
      "options": {
        "type": "object",
        "properties": {
          "resolve_dependencies": {"type": "boolean"},
          "include_imports": {"type": "boolean"}
        }
      }
    },
    "required": ["project", "repo", "file_path"]
  }
}
```

### 2. MCP Factory实现示例
```python
class MCPToolFactory:
    def __init__(self, api_base_url: str, api_key: str):
        self.api_base_url = api_base_url
        self.api_key = api_key
    
    def create_chunk_file_tool(self):
        async def chunk_file(project: str, repo: str, file_path: str, 
                           branch: str = "main", options: dict = None):
            url = f"{self.api_base_url}/api/v1/mcp-tools/chunk/file"
            payload = {
                "project": project,
                "repo": repo,
                "file_path": file_path,
                "branch": branch,
                "options": options or {}
            }
            
            response = await self._make_request(url, payload)
            return response
        
        return {
            "name": "chunk_file",
            "description": "对单个代码文件进行分块分析",
            "function": chunk_file,
            "input_schema": {...}
        }
```

## 实施计划

### 阶段1：API框架搭建 (1周)
- [ ] 创建统一的API响应格式
- [ ] 实现API认证中间件
- [ ] 设计API路由结构
- [ ] 实现错误处理机制

### 阶段2：核心服务API化 (2周)
- [ ] DaXiang服务API实现
- [ ] Chunk服务API实现
- [ ] Git服务API实现

### 阶段3：扩展服务API化 (1周)
- [ ] KM服务API实现
- [ ] DevTools服务API实现
- [ ] API文档生成

### 阶段4：MCP集成测试 (1周)
- [ ] MCP工具包装器实现
- [ ] 集成测试
- [ ] 性能优化
- [ ] 文档完善

## 技术实现要点

### 1. API版本管理
- 使用URL路径版本控制 (`/api/v1/`)
- 支持向后兼容
- 版本废弃策略

### 2. 认证与授权
- API Key认证
- JWT Token支持
- 权限控制

### 3. 限流与监控
- 请求频率限制
- API调用监控
- 性能指标收集

### 4. 缓存策略
- 响应缓存
- 数据缓存
- 缓存失效策略

### 5. 错误处理
- 统一错误码
- 详细错误信息
- 错误日志记录

## 预期收益

### 1. 标准化
- 统一的API接口规范
- 标准化的数据格式
- 一致的错误处理

### 2. 可扩展性
- 易于添加新的工具
- 支持第三方集成
- 模块化设计

### 3. 可维护性
- 清晰的服务边界
- 独立的API测试
- 完善的文档

### 4. MCP生态集成
- 标准MCP工具支持
- 与MCP Hub无缝集成
- 支持工具组合和编排
