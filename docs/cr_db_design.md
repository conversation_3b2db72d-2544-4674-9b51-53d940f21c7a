# CR结果数据库设计方案

## 1. 设计原则

### 1.1 核心原则
- **切实可行**: 基于现有Peewee ORM架构，无需大规模重构
- **灵活可扩展**: 支持多种CR类型和业务场景扩展
- **方便维护**: 清晰的表结构设计，支持数据迁移和版本管理
- **高性能**: 合理的索引设计，支持大数据量查询

### 1.2 技术选型
- **ORM**: 继续使用Peewee，保持与现有架构一致
- **数据库**: 支持MySQL/PostgreSQL，兼容Zebra代理
- **序列化**: 使用JsonSerializedField存储复杂数据结构
- **迁移**: 基于Peewee的迁移机制

## 2. 数据库表设计

### 2.1 核心表结构

#### 2.1.1 CR任务表 (cr_tasks)
```sql
CREATE TABLE cr_tasks (
    id VARCHAR(64) PRIMARY KEY,                    -- 任务唯一ID
    project VARCHAR(100) NOT NULL,                 -- 项目名称
    repo VARCHAR(100) NOT NULL,                    -- 仓库名称
    branch VARCHAR(100) DEFAULT 'main',            -- 分支名称
    commit_hash VARCHAR(64),                       -- 提交哈希
    diff_content LONGTEXT,                         -- diff内容
    task_type ENUM('manual', 'auto', 'webhook') DEFAULT 'manual', -- 任务类型
    status ENUM('pending', 'running', 'completed', 'failed') DEFAULT 'pending', -- 任务状态
    created_by VARCHAR(64),                        -- 创建者ID
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    completed_at TIMESTAMP NULL,                   -- 完成时间
    error_message TEXT,                            -- 错误信息
    
    INDEX idx_project_repo (project, repo),
    INDEX idx_status_created (status, created_at),
    INDEX idx_created_by (created_by)
);
```

#### 2.1.2 CR结果表 (cr_results)
```sql
CREATE TABLE cr_results (
    id VARCHAR(64) PRIMARY KEY,                    -- 结果唯一ID
    task_id VARCHAR(64) NOT NULL,                  -- 关联任务ID
    summary TEXT,                                  -- 总结信息
    total_score INT DEFAULT 100,                   -- 总分
    status ENUM('passed', 'not_passed', 'warning') DEFAULT 'passed', -- 状态
    total_problems INT DEFAULT 0,                  -- 问题总数
    p0_count INT DEFAULT 0,                        -- P0问题数
    p1_count INT DEFAULT 0,                        -- P1问题数
    p2_count INT DEFAULT 0,                        -- P2问题数
    p3_count INT DEFAULT 0,                        -- P3问题数
    check_branch VARCHAR(100),                     -- 检查分支
    suggestions TEXT,                              -- 建议
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (task_id) REFERENCES cr_tasks(id) ON DELETE CASCADE,
    INDEX idx_task_id (task_id),
    INDEX idx_status_score (status, total_score)
);
```

#### 2.1.3 CR问题详情表 (cr_problems)
```sql
CREATE TABLE cr_problems (
    id VARCHAR(64) PRIMARY KEY,                    -- 问题唯一ID
    result_id VARCHAR(64) NOT NULL,                -- 关联结果ID
    file_path VARCHAR(500) NOT NULL,               -- 文件路径
    problem_type VARCHAR(100),                     -- 问题类型
    severity ENUM('P0', 'P1', 'P2', 'P3') DEFAULT 'P3', -- 严重程度
    rule_id VARCHAR(100),                          -- 规则ID
    rule_name VARCHAR(200),                        -- 规则名称
    description TEXT,                              -- 问题描述
    suggestion TEXT,                               -- 修复建议
    code_position JSON,                            -- 代码位置 [startLine, startColumn, endLine, endColumn]
    code_snippet TEXT,                             -- 代码片段
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (result_id) REFERENCES cr_results(id) ON DELETE CASCADE,
    INDEX idx_result_id (result_id),
    INDEX idx_file_severity (file_path(255), severity),
    INDEX idx_rule_id (rule_id)
);
```

#### 2.1.4 代码片段表 (code_segments)
```sql
CREATE TABLE code_segments (
    id VARCHAR(64) PRIMARY KEY,                    -- 片段唯一ID
    task_id VARCHAR(64) NOT NULL,                  -- 关联任务ID
    file_path VARCHAR(500) NOT NULL,               -- 文件路径
    file_type VARCHAR(50),                         -- 文件类型
    segment_type VARCHAR(50),                      -- 片段类型 (method, class, function等)
    segment_name VARCHAR(200),                     -- 片段名称
    content LONGTEXT,                              -- 代码内容
    start_line INT,                                -- 起始行号
    end_line INT,                                  -- 结束行号
    upstream_deps JSON,                            -- 上游依赖列表
    downstream_deps JSON,                          -- 下游依赖列表
    upstream_code JSON,                            -- 上游依赖代码
    downstream_code JSON,                          -- 下游依赖代码
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (task_id) REFERENCES cr_tasks(id) ON DELETE CASCADE,
    INDEX idx_task_file (task_id, file_path(255)),
    INDEX idx_segment_type (segment_type),
    INDEX idx_file_type (file_type)
);
```

#### 2.1.5 依赖统计表 (dependency_stats)
```sql
CREATE TABLE dependency_stats (
    id VARCHAR(64) PRIMARY KEY,                    -- 统计唯一ID
    task_id VARCHAR(64) NOT NULL,                  -- 关联任务ID
    total_segments INT DEFAULT 0,                  -- 总片段数
    total_upstream_deps INT DEFAULT 0,             -- 总上游依赖数
    total_downstream_deps INT DEFAULT 0,           -- 总下游依赖数
    avg_upstream_per_segment DECIMAL(10,2) DEFAULT 0.0, -- 平均每片段上游依赖数
    avg_downstream_per_segment DECIMAL(10,2) DEFAULT 0.0, -- 平均每片段下游依赖数
    file_type_stats JSON,                          -- 按文件类型统计
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (task_id) REFERENCES cr_tasks(id) ON DELETE CASCADE,
    UNIQUE KEY uk_task_id (task_id)
);
```

### 2.2 扩展表设计

#### 2.2.1 CR规则配置表 (cr_rules)
```sql
CREATE TABLE cr_rules (
    id VARCHAR(64) PRIMARY KEY,                    -- 规则唯一ID
    rule_name VARCHAR(200) NOT NULL,               -- 规则名称
    rule_type VARCHAR(100),                        -- 规则类型
    language VARCHAR(50),                          -- 适用语言
    severity ENUM('P0', 'P1', 'P2', 'P3') DEFAULT 'P3', -- 默认严重程度
    description TEXT,                              -- 规则描述
    pattern TEXT,                                  -- 匹配模式
    suggestion_template TEXT,                      -- 建议模板
    is_active BOOLEAN DEFAULT TRUE,                -- 是否启用
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_rule_name (rule_name),
    INDEX idx_language_active (language, is_active)
);
```

#### 2.2.2 CR历史记录表 (cr_history)
```sql
CREATE TABLE cr_history (
    id VARCHAR(64) PRIMARY KEY,                    -- 历史记录ID
    project VARCHAR(100) NOT NULL,                 -- 项目名称
    repo VARCHAR(100) NOT NULL,                    -- 仓库名称
    date DATE NOT NULL,                            -- 日期
    total_tasks INT DEFAULT 0,                     -- 当日任务总数
    passed_tasks INT DEFAULT 0,                    -- 通过任务数
    failed_tasks INT DEFAULT 0,                    -- 失败任务数
    avg_score DECIMAL(5,2) DEFAULT 0.0,            -- 平均分数
    total_problems INT DEFAULT 0,                  -- 问题总数
    p0_problems INT DEFAULT 0,                     -- P0问题数
    p1_problems INT DEFAULT 0,                     -- P1问题数
    p2_problems INT DEFAULT 0,                     -- P2问题数
    p3_problems INT DEFAULT 0,                     -- P3问题数
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_project_repo_date (project, repo, date),
    INDEX idx_date (date)
);
```

## 3. 数据模型设计

### 3.1 核心模型类

基于现有的Peewee架构，设计对应的模型类：

```python
# models/cr_models.py
from peewee import *
from datetime import datetime
from infra.database.models.db_models import DataBaseModel, JsonSerializedField

class CRTask(DataBaseModel):
    """CR任务模型"""
    id = CharField(max_length=64, primary_key=True)
    project = CharField(max_length=100, null=False)
    repo = CharField(max_length=100, null=False)
    branch = CharField(max_length=100, default='main')
    commit_hash = CharField(max_length=64, null=True)
    diff_content = TextField(null=True)
    task_type = CharField(max_length=20, default='manual')  # manual, auto, webhook
    status = CharField(max_length=20, default='pending')    # pending, running, completed, failed
    created_by = CharField(max_length=64, null=True)
    created_at = DateTimeField(default=datetime.now)
    updated_at = DateTimeField(default=datetime.now)
    completed_at = DateTimeField(null=True)
    error_message = TextField(null=True)
    
    class Meta:
        db_table = 'cr_tasks'
        indexes = (
            (('project', 'repo'), False),
            (('status', 'created_at'), False),
        )

class CRResult(DataBaseModel):
    """CR结果模型"""
    id = CharField(max_length=64, primary_key=True)
    task = ForeignKeyField(CRTask, backref='results', on_delete='CASCADE')
    summary = TextField(null=True)
    total_score = IntegerField(default=100)
    status = CharField(max_length=20, default='passed')  # passed, not_passed, warning
    total_problems = IntegerField(default=0)
    p0_count = IntegerField(default=0)
    p1_count = IntegerField(default=0)
    p2_count = IntegerField(default=0)
    p3_count = IntegerField(default=0)
    check_branch = CharField(max_length=100, null=True)
    suggestions = TextField(null=True)
    created_at = DateTimeField(default=datetime.now)
    
    class Meta:
        db_table = 'cr_results'

class CRProblem(DataBaseModel):
    """CR问题模型"""
    id = CharField(max_length=64, primary_key=True)
    result = ForeignKeyField(CRResult, backref='problems', on_delete='CASCADE')
    file_path = CharField(max_length=500, null=False)
    problem_type = CharField(max_length=100, null=True)
    severity = CharField(max_length=10, default='P3')  # P0, P1, P2, P3
    rule_id = CharField(max_length=100, null=True)
    rule_name = CharField(max_length=200, null=True)
    description = TextField(null=True)
    suggestion = TextField(null=True)
    code_position = JsonSerializedField(null=True)  # [startLine, startColumn, endLine, endColumn]
    code_snippet = TextField(null=True)
    created_at = DateTimeField(default=datetime.now)
    
    class Meta:
        db_table = 'cr_problems'

class CodeSegment(DataBaseModel):
    """代码片段模型"""
    id = CharField(max_length=64, primary_key=True)
    task = ForeignKeyField(CRTask, backref='segments', on_delete='CASCADE')
    file_path = CharField(max_length=500, null=False)
    file_type = CharField(max_length=50, null=True)
    segment_type = CharField(max_length=50, null=True)  # method, class, function等
    segment_name = CharField(max_length=200, null=True)
    content = TextField(null=True)
    start_line = IntegerField(null=True)
    end_line = IntegerField(null=True)
    upstream_deps = JsonSerializedField(default=list)
    downstream_deps = JsonSerializedField(default=list)
    upstream_code = JsonSerializedField(default=dict)
    downstream_code = JsonSerializedField(default=dict)
    created_at = DateTimeField(default=datetime.now)
    
    class Meta:
        db_table = 'code_segments'
        indexes = (
            (('task', 'file_path'), False),
        )

class DependencyStats(DataBaseModel):
    """依赖统计模型"""
    id = CharField(max_length=64, primary_key=True)
    task = ForeignKeyField(CRTask, backref='dependency_stats', on_delete='CASCADE', unique=True)
    total_segments = IntegerField(default=0)
    total_upstream_deps = IntegerField(default=0)
    total_downstream_deps = IntegerField(default=0)
    avg_upstream_per_segment = DecimalField(max_digits=10, decimal_places=2, default=0.0)
    avg_downstream_per_segment = DecimalField(max_digits=10, decimal_places=2, default=0.0)
    file_type_stats = JsonSerializedField(default=dict)
    created_at = DateTimeField(default=datetime.now)
    
    class Meta:
        db_table = 'dependency_stats'
```

## 4. 数据访问层设计

### 4.1 Repository模式

```python
# repositories/cr_repository.py
from typing import List, Optional, Dict, Any
from models.cr_models import CRTask, CRResult, CRProblem, CodeSegment, DependencyStats
import uuid
from datetime import datetime

class CRRepository:
    """CR数据访问层"""
    
    def create_task(self, project: str, repo: str, branch: str = 'main', 
                   diff_content: str = None, created_by: str = None, **kwargs) -> CRTask:
        """创建CR任务"""
        task = CRTask.create(
            id=str(uuid.uuid4()),
            project=project,
            repo=repo,
            branch=branch,
            diff_content=diff_content,
            created_by=created_by,
            **kwargs
        )
        return task
    
    def update_task_status(self, task_id: str, status: str, error_message: str = None) -> bool:
        """更新任务状态"""
        query = CRTask.update(
            status=status,
            updated_at=datetime.now(),
            error_message=error_message,
            completed_at=datetime.now() if status in ['completed', 'failed'] else None
        ).where(CRTask.id == task_id)
        return query.execute() > 0
    
    def save_cr_result(self, task_id: str, result_data: Dict[str, Any]) -> CRResult:
        """保存CR结果"""
        result = CRResult.create(
            id=str(uuid.uuid4()),
            task_id=task_id,
            **result_data
        )
        return result
    
    def save_cr_problems(self, result_id: str, problems: List[Dict[str, Any]]) -> List[CRProblem]:
        """批量保存CR问题"""
        problem_objects = []
        for problem_data in problems:
            problem = CRProblem.create(
                id=str(uuid.uuid4()),
                result_id=result_id,
                **problem_data
            )
            problem_objects.append(problem)
        return problem_objects
    
    def save_code_segments(self, task_id: str, segments: List[Dict[str, Any]]) -> List[CodeSegment]:
        """批量保存代码片段"""
        segment_objects = []
        for segment_data in segments:
            segment = CodeSegment.create(
                id=str(uuid.uuid4()),
                task_id=task_id,
                **segment_data
            )
            segment_objects.append(segment)
        return segment_objects
    
    def save_dependency_stats(self, task_id: str, stats_data: Dict[str, Any]) -> DependencyStats:
        """保存依赖统计"""
        stats = DependencyStats.create(
            id=str(uuid.uuid4()),
            task_id=task_id,
            **stats_data
        )
        return stats
    
    def get_task_by_id(self, task_id: str) -> Optional[CRTask]:
        """根据ID获取任务"""
        try:
            return CRTask.get(CRTask.id == task_id)
        except CRTask.DoesNotExist:
            return None
    
    def get_tasks_by_project(self, project: str, repo: str = None, 
                           limit: int = 50, offset: int = 0) -> List[CRTask]:
        """获取项目的CR任务列表"""
        query = CRTask.select().where(CRTask.project == project)
        if repo:
            query = query.where(CRTask.repo == repo)
        return list(query.order_by(CRTask.created_at.desc()).limit(limit).offset(offset))
    
    def get_task_with_results(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务及其完整结果"""
        try:
            task = CRTask.get(CRTask.id == task_id)
            result = CRResult.get(CRResult.task == task)
            problems = list(CRProblem.select().where(CRProblem.result == result))
            segments = list(CodeSegment.select().where(CodeSegment.task == task))
            stats = DependencyStats.get_or_none(DependencyStats.task == task)
            
            return {
                'task': task,
                'result': result,
                'problems': problems,
                'segments': segments,
                'dependency_stats': stats
            }
        except (CRTask.DoesNotExist, CRResult.DoesNotExist):
            return None
```

## 5. 实施步骤

### 5.1 第一阶段：基础表创建
1. 创建数据库迁移脚本
2. 实现核心模型类
3. 创建基础的Repository类
4. 编写单元测试

### 5.2 第二阶段：数据持久化集成
1. 修改CR服务，集成数据持久化
2. 实现批量数据保存优化
3. 添加数据验证和错误处理
4. 性能测试和优化

### 5.3 第三阶段：查询和分析功能
1. 实现复杂查询接口
2. 添加数据统计和分析功能
3. 实现数据导出功能
4. 添加数据清理和归档机制

## 6. 实施指南

### 6.1 环境准备

#### 6.1.1 数据库配置
```bash
# 设置数据库环境变量
export DB_TYPE=mysql
export DB_NAME=shangou_ai_cr
export DB_HOST=localhost
export DB_PORT=3306
export DB_USER=root
export DB_PASSWORD=your_password
```

#### 6.1.2 依赖安装
```bash
# 确保已安装Peewee和数据库驱动
pip install peewee
pip install PyMySQL  # MySQL
# 或
pip install psycopg2-binary  # PostgreSQL
```

### 6.2 数据库初始化

#### 6.2.1 运行迁移脚本
```bash
# 创建数据库表
python migrations/create_cr_tables.py
```

#### 6.2.2 验证表结构
```sql
-- 检查表是否创建成功
SHOW TABLES LIKE 'cr_%';

-- 检查表结构
DESCRIBE cr_tasks;
DESCRIBE cr_results;
DESCRIBE cr_problems;
DESCRIBE code_segments;
DESCRIBE dependency_stats;
DESCRIBE cr_rules;
```

### 6.3 服务集成

#### 6.3.1 启用数据持久化
```python
# 在CR服务配置中启用持久化
config = {
    'enable_persistence': True,
    'cr_mode': 'standard'
}

# 初始化CR服务
cr_service = CRService(config=config)
await cr_service.initialize()
```

#### 6.3.2 使用示例
```python
# 处理CR请求（自动保存到数据库）
result = await cr_service.process_cr_request(
    ctx=context,
    code_diff=diff_content,
    params={
        'project': 'my_project',
        'repo': 'my_repo',
        'fromBranch': 'feature/new-feature',
        'toBranch': 'main'
    }
)
```

### 6.4 数据查询

#### 6.4.1 基础查询
```python
from repositories.cr_repository import CRRepository

cr_repo = CRRepository()

# 获取任务列表
tasks = cr_repo.get_tasks_by_project('my_project', 'my_repo')

# 获取完整结果
full_result = cr_repo.get_task_with_full_results(task_id)

# 获取项目统计
stats = cr_repo.get_project_stats('my_project', days=30)
```

#### 6.4.2 高级查询
```python
# 查询特定状态的任务
completed_tasks = cr_repo.get_tasks_by_project(
    'my_project',
    status='completed',
    limit=100
)

# 查询问题详情
problems = cr_repo.get_problems_by_result_id(result_id)

# 查询代码片段
segments = cr_repo.get_segments_by_task_id(task_id)
```

### 6.5 性能优化

#### 6.5.1 索引优化
```sql
-- 添加自定义索引（如果需要）
CREATE INDEX idx_cr_tasks_project_status ON cr_tasks(project, status, created_at);
CREATE INDEX idx_cr_problems_severity_file ON cr_problems(severity, file_path(255));
CREATE INDEX idx_code_segments_type_file ON code_segments(segment_type, file_path(255));
```

#### 6.5.2 批量操作
```python
# 批量保存问题
problems_batch = [problem1, problem2, problem3, ...]
cr_repo.save_cr_problems(result_id, problems_batch)

# 批量保存代码片段
segments_batch = [segment1, segment2, segment3, ...]
cr_repo.save_code_segments(task_id, segments_batch)
```

### 6.6 数据维护

#### 6.6.1 数据清理
```python
# 清理90天前的旧数据
cleanup_result = cr_repo.cleanup_old_data(days=90)
print(f"清理了 {cleanup_result['deleted_tasks']} 个任务")
```

#### 6.6.2 数据备份
```bash
# MySQL备份
mysqldump -u root -p shangou_ai_cr > cr_backup_$(date +%Y%m%d).sql

# PostgreSQL备份
pg_dump -U postgres shangou_ai_cr > cr_backup_$(date +%Y%m%d).sql
```

### 6.7 监控和告警

#### 6.7.1 数据库监控
```python
# 监控数据库连接状态
def check_database_health():
    try:
        from models.cr_models import CRTask
        CRTask.select().limit(1).execute()
        return True
    except Exception as e:
        logger.error(f"数据库健康检查失败: {e}")
        return False
```

#### 6.7.2 数据质量监控
```python
# 监控数据一致性
def check_data_consistency():
    inconsistent_results = []

    # 检查结果与问题数量一致性
    results = CRResult.select()
    for result in results:
        actual_problems = CRProblem.select().where(CRProblem.result == result).count()
        if actual_problems != result.total_problems:
            inconsistent_results.append(result.id)

    return inconsistent_results
```

## 7. 扩展功能

### 7.1 API接口
```python
# 添加REST API接口
@app.route('/api/cr/tasks/<task_id>')
def get_cr_task(task_id):
    result = cr_repo.get_task_with_full_results(task_id)
    return jsonify(result)

@app.route('/api/cr/projects/<project>/stats')
def get_project_stats(project):
    stats = cr_repo.get_project_stats(project, days=30)
    return jsonify(stats)
```

### 7.2 数据导出
```python
# 导出CR报告
def export_cr_report(project, repo, start_date, end_date):
    tasks = cr_repo.get_tasks_by_project(project, repo)
    # 生成Excel或PDF报告
    return generate_report(tasks)
```

### 7.3 实时通知
```python
# 集成消息通知
def notify_cr_completion(task_id):
    task = cr_repo.get_task_by_id(task_id)
    result = cr_repo.get_result_by_task_id(task_id)

    # 发送通知
    send_notification(
        user=task.created_by,
        message=f"CR任务完成，得分: {result.total_score}"
    )
```

## 8. 总结

这个CR数据库设计方案提供了：

### 8.1 核心优势
- **切实可行**: 基于现有Peewee架构，无需大规模重构
- **灵活可扩展**: 支持多种CR类型和业务场景
- **方便维护**: 清晰的表结构和完善的数据访问层
- **高性能**: 合理的索引设计和批量操作支持

### 8.2 技术特点
- **统一架构**: 与现有数据库模型保持一致
- **完整功能**: 覆盖CR全流程的数据持久化
- **数据完整性**: 外键约束和事务支持
- **扩展性**: 支持新的CR规则和统计维度

### 8.3 实施建议
1. **分阶段实施**: 先实现核心功能，再逐步扩展
2. **数据迁移**: 制定详细的数据迁移计划
3. **性能测试**: 在生产环境前进行充分的性能测试
4. **监控告警**: 建立完善的监控和告警机制

这个设计方案基于您现有的技术栈，提供了完整的数据持久化解决方案，既保持了架构的一致性，又具备了良好的扩展性和维护性。
