# MCP工具集API文档

## 概述

本文档描述了Shangou AI CR项目的MCP工具集API接口。这些API按照第一阶段架构重构要求设计，提供统一的RESTful接口，支持MCP工具集成。

## 基础信息

- **Base URL**: `http://localhost:9000/mcp-tools`
- **Content-Type**: `application/json`
- **API版本**: `v1.0.0`

## 统一响应格式

### 成功响应
```json
{
  "success": true,
  "data": {},
  "error": null,
  "metadata": {
    "timestamp": "2024-01-01T00:00:00Z",
    "request_id": "uuid",
    "service": "service_name",
    "version": "1.0.0"
  }
}
```

### 错误响应
```json
{
  "success": false,
  "data": null,
  "error": {
    "code": "ERROR_CODE",
    "message": "Human readable error message",
    "details": {}
  },
  "metadata": {
    "timestamp": "2024-01-01T00:00:00Z",
    "request_id": "uuid",
    "service": "service_name",
    "version": "1.0.0"
  }
}
```

## API接口详情

### 1. 系统管理API

#### 1.1 健康检查
```
GET /health
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "services": {
      "daxiang": "available",
      "git": "available",
      "chunk": "available",
      "mcp": "healthy"
    },
    "timestamp": "2024-01-01T00:00:00Z",
    "config": {
      "mcp_enabled": true,
      "api_version": "1.0.0"
    }
  }
}
```

#### 1.2 获取工具清单
```
GET /tools/manifest
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "tools": [
      {
        "name": "chunk_file",
        "description": "对单个代码文件进行分块分析",
        "category": "code_analysis",
        "endpoint": "/chunk/file",
        "method": "POST",
        "input_schema": {
          "type": "object",
          "properties": {
            "project": {"type": "string"},
            "repo": {"type": "string"},
            "file_path": {"type": "string"}
          },
          "required": ["project", "repo", "file_path"]
        }
      }
    ],
    "categories": {
      "code_analysis": [...],
      "git": [...],
      "notification": [...]
    },
    "total_count": 13
  }
}
```

### 2. MCP服务管理API

#### 2.1 MCP服务状态
```
GET /mcp/status
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "service_info": {
      "name": "mcp_service",
      "status": "running",
      "uptime": 3600,
      "error_count": 0
    },
    "health_check": {
      "status": "healthy",
      "tools_count": 6,
      "session_active": true
    },
    "available_tools": ["tool1", "tool2"],
    "tool_mappings": {
      "daxiang_service": "daxiang_notification_tool"
    }
  }
}
```

#### 2.2 列出MCP工具
```
GET /mcp/tools
```

#### 2.3 调用MCP工具
```
POST /mcp/call
```

**请求体**:
```json
{
  "tool_name": "code_analysis_tool",
  "arguments": {
    "code": "def hello(): pass",
    "language": "python"
  }
}
```

### 3. 大象通知服务API

#### 3.1 获取访问令牌
```
POST /daxiang/auth/token
```

**请求体**:
```json
{
  "context": {}
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "access_token": "token_string",
    "token_type": "Bearer",
    "expires_in": 7200
  }
}
```

#### 3.2 用户ID转换
```
POST /daxiang/users/convert
```

**请求体**:
```json
{
  "conversion_type": "emp_to_user",
  "ids": ["emp001", "emp002"],
  "context": {}
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "conversions": {
      "emp001": "user001",
      "emp002": "user002"
    },
    "failed": [],
    "conversion_type": "emp_to_user"
  }
}
```

### 4. 代码分块服务API

#### 4.1 单文件代码分块
```
POST /chunk/file
```

**请求体**:
```json
{
  "project": "my-project",
  "repo": "my-repo",
  "file_path": "src/main.py",
  "branch": "main"
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "chunks": [
      {
        "id": "chunk_1",
        "type": "function",
        "name": "main",
        "content": "def main():\n    pass",
        "start_line": 1,
        "end_line": 2,
        "dependencies": [],
        "complexity": 1
      }
    ],
    "file_info": {
      "path": "src/main.py",
      "language": "python",
      "total_lines": 10
    },
    "chunk_count": 1
  }
}
```

#### 4.2 批量文件分块
```
POST /chunk/batch
```

**请求体**:
```json
{
  "project": "my-project",
  "repo": "my-repo",
  "branch": "main",
  "file_patterns": ["*.py", "*.js"],
  "options": {
    "resolve_code": true,
    "max_files": 100
  }
}
```

### 5. Git操作服务API

#### 5.1 获取代码差异
```
POST /git/diff
```

**请求体**:
```json
{
  "project": "my-project",
  "repo": "my-repo",
  "from_ref": "main",
  "to_ref": "feature-branch"
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "diff_content": "diff --git a/file.py b/file.py\n...",
    "statistics": {
      "files_changed": 2,
      "insertions": 10,
      "deletions": 5
    },
    "files": [
      {
        "path": "src/main.py",
        "status": "modified",
        "changes": 8
      }
    ],
    "refs": {
      "from": "main",
      "to": "feature-branch"
    }
  }
}
```

#### 5.2 获取文件列表
```
GET /git/files?project=my-project&repo=my-repo&branch=main&suffixes=.py&limit=100
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "files": [
      {
        "path": "src/main.py",
        "type": "file",
        "language": "python"
      }
    ],
    "total_count": 50,
    "filtered_count": 25,
    "filters": {
      "branch": "main",
      "suffixes": [".py"],
      "limit": 100
    }
  }
}
```

#### 5.3 读取文件内容
```
GET /git/files/content?project=my-project&repo=my-repo&file_path=src/main.py
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "content": "def main():\n    print('Hello World')",
    "encoding": "utf-8",
    "file_info": {
      "path": "src/main.py",
      "size": 35,
      "language": "python",
      "line_count": 2
    }
  }
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| `MISSING_REQUIRED_FIELDS` | 缺少必需字段 |
| `INVALID_JSON` | 无效的JSON格式 |
| `INVALID_CONVERSION_TYPE` | 无效的转换类型 |
| `MISSING_PARAMETERS` | 缺少必需参数 |
| `SERVICE_NOT_AVAILABLE` | 服务不可用 |
| `INTERNAL_ERROR` | 内部服务器错误 |

## MCP工具集成指南

### 1. 工具定义
每个API端点都可以被包装成MCP工具：

```json
{
  "name": "chunk_file",
  "description": "对单个代码文件进行分块分析",
  "input_schema": {
    "type": "object",
    "properties": {
      "project": {"type": "string"},
      "repo": {"type": "string"},
      "file_path": {"type": "string"}
    },
    "required": ["project", "repo", "file_path"]
  }
}
```

### 2. 工具调用
```python
async def call_api_tool(endpoint, method, data):
    url = f"http://localhost:9000/mcp-tools{endpoint}"
    
    if method == "GET":
        response = requests.get(url, params=data)
    else:
        response = requests.post(url, json=data)
    
    return response.json()
```

### 3. 批量调用
支持通过MCP适配器进行批量工具调用，提高效率。

## 使用建议

1. **错误处理**: 始终检查响应中的`success`字段
2. **重试机制**: 对于网络错误实现指数退避重试
3. **缓存**: 对于不经常变化的数据（如文件列表）实现缓存
4. **限流**: 注意API调用频率限制
5. **监控**: 使用健康检查接口监控服务状态
