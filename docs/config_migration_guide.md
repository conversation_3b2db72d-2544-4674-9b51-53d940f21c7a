# 配置目录合并迁移指南

## 📋 概述

本指南说明如何将项目中的 `conf/` 和 `config/` 目录合并为统一的 `config/` 目录，以提高配置管理的一致性和可维护性。

## 🎯 合并目标

- **统一配置管理**: 所有配置文件集中在 `config/` 目录
- **保持向后兼容**: 旧的YAML配置文件仍可使用
- **推进新架构**: 鼓励使用基于Pydantic的配置类
- **清晰的目录结构**: 按功能分类组织配置文件

## 📁 新目录结构

```
config/
├── __init__.py                 # 配置模块初始化
├── base_config.py             # 新架构配置类（推荐）
├── config_loader.py           # 统一配置加载器
├── config.example.json        # 配置示例
├── README.md                  # 配置说明文档
├── cr_rules/                  # CR规则配置
│   ├── java_enterprise.json
│   ├── python_security.json
│   └── python_web_backend.json
├── legacy/                    # 旧YAML配置（兼容性）
│   ├── database_conf.yaml    # 从 conf/database_conf.yaml 迁移
│   ├── service_conf.yaml     # 从 conf/service_conf.yaml 迁移
│   ├── km_conf.yaml          # 从 conf/km_conf.yaml 迁移
│   └── org_conf.yaml         # 从 conf/org_conf.yaml 迁移
├── services/                  # 服务配置
│   └── envs/                  # 从 conf/envs/ 迁移
│       ├── base.yaml
│       └── test.yaml
├── security/                  # 证书和密钥
│   ├── private.pem           # 从 conf/private.pem 迁移
│   └── public.pem            # 从 conf/public.pem 迁移
└── third_party/              # 第三方服务配置
    ├── zebra/                # 从 conf/zebra/ 迁移
    │   └── config.py
    └── squirrel/             # 从 conf/squirrel/ 迁移
        ├── __init__.py
        ├── mt_redis.yaml
        └── squirrel-proxy.conf
```

## 🚀 执行迁移

### 步骤1: 运行迁移脚本

```bash
# 执行自动迁移脚本
python scripts/merge_config_directories.py
```

脚本会自动：
- 创建新的目录结构
- 复制文件到对应位置
- 生成配置加载器
- 创建说明文档

### 步骤2: 验证迁移结果

```bash
# 检查新目录结构
ls -la config/

# 验证文件完整性
find config/ -name "*.yaml" -o -name "*.json" -o -name "*.py"
```

### 步骤3: 更新代码引用（可选）

如果选择更新代码引用，脚本会自动替换：
- `from conf.` → `from config.legacy.`
- `import conf.` → `import config.legacy.`
- `conf/` → `config/legacy/`

## 📝 使用新配置系统

### 推荐方式：使用配置类

```python
from config.base_config import BaseConfig

# 从环境变量加载（推荐）
config = BaseConfig.load_from_env()

# 从文件加载
config = BaseConfig.load_from_file("config/app_config.json")

# 访问配置
db_config = config.database
llm_config = config.llm
api_config = config.api
```

### 兼容方式：使用统一加载器

```python
from config.config_loader import get_config_loader, load_app_config

# 获取配置加载器
loader = get_config_loader()

# 加载新配置
app_config = load_app_config()

# 加载旧YAML配置（兼容性）
db_config = loader.load_legacy_yaml("database_conf.yaml")
service_config = loader.load_legacy_yaml("service_conf.yaml")

# 加载CR规则
python_rules = loader.load_cr_rules("python_security.json")
```

### 直接加载旧配置（临时方案）

```python
from config.config_loader import load_legacy_config

# 直接加载旧配置文件
config = load_legacy_config("database_conf.yaml")
```

## 🔄 迁移策略

### 阶段1：保持兼容（立即执行）
- 运行迁移脚本
- 验证现有功能正常
- 不修改现有代码逻辑

### 阶段2：逐步迁移（1-2周内）
- 新功能使用新配置类
- 重构关键模块使用新配置
- 更新文档和示例

### 阶段3：完全迁移（1个月内）
- 所有模块使用新配置类
- 移除旧配置文件（可选）
- 清理兼容性代码

## ⚠️ 注意事项

### 1. 路径更新
如果代码中硬编码了配置文件路径，需要手动更新：

```python
# 旧路径
config_path = "conf/database_conf.yaml"

# 新路径
config_path = "config/legacy/database_conf.yaml"
```

### 2. 环境变量
新配置类支持环境变量，建议设置：

```bash
export DB_HOST=localhost
export DB_PORT=3306
export DB_NAME=shangou_ai_cr
export LLM_API_KEY=your_api_key
```

### 3. 配置验证
新配置类基于Pydantic，会自动验证配置：

```python
# 配置验证示例
try:
    config = BaseConfig.load_from_env()
    print("✅ 配置验证通过")
except ValidationError as e:
    print(f"❌ 配置验证失败: {e}")
```

## 🧪 测试验证

### 1. 功能测试
```python
# 测试配置加载
from config.config_loader import get_config_loader

loader = get_config_loader()

# 测试数据库配置
db_config = loader.get_database_config()
assert db_config is not None

# 测试服务配置
service_config = loader.get_service_config("llm")
assert service_config is not None
```

### 2. 兼容性测试
```python
# 测试旧配置文件加载
from config.config_loader import load_legacy_config

try:
    config = load_legacy_config("database_conf.yaml")
    print("✅ 旧配置兼容性正常")
except Exception as e:
    print(f"❌ 旧配置兼容性问题: {e}")
```

## 🔧 故障排除

### 问题1：配置文件找不到
```
FileNotFoundError: 配置文件不存在
```

**解决方案**：
- 检查文件是否正确迁移
- 验证路径是否正确
- 确认文件权限

### 问题2：配置验证失败
```
ValidationError: 配置验证失败
```

**解决方案**：
- 检查环境变量设置
- 验证配置文件格式
- 查看详细错误信息

### 问题3：导入路径错误
```
ModuleNotFoundError: No module named 'conf'
```

**解决方案**：
- 更新导入语句
- 使用新的配置加载器
- 检查PYTHONPATH设置

## 📚 参考资料

- [Pydantic配置管理](https://pydantic-docs.helpmanual.io/usage/settings/)
- [YAML配置最佳实践](https://yaml.org/spec/1.2/spec.html)
- [Python配置管理模式](https://12factor.net/config)

## 🎉 完成检查清单

- [ ] 运行迁移脚本
- [ ] 验证目录结构
- [ ] 测试配置加载
- [ ] 更新代码引用
- [ ] 验证功能正常
- [ ] 更新文档
- [ ] 团队培训

完成以上步骤后，配置目录合并就完成了！🎊
