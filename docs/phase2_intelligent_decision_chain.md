# 智能决策链设计文档

## 概述

智能决策链是Agent系统的核心组件，负责根据代码特征和上下文信息动态选择最优的处理策略。本文档详细描述智能决策链的设计架构和实现方案。

## 决策链架构

### 决策流程图

```mermaid
graph TD
    A[代码输入] --> B[复杂度分析Agent]
    B --> C[策略决策Agent]
    C --> D{选择策略}
    D -->|简单| E[Fast模式链]
    D -->|中等| F[Standard模式链]
    D -->|复杂| G[Deep模式链]
    E --> H[结果汇总]
    F --> H
    G --> H
    H --> I[质量评估Agent]
    I --> J[最终输出]
```

### 核心组件设计

#### 1. 复杂度分析Agent

```python
# core/agents/complexity_analyzer_agent.py
from core.agents.base_agent import BaseAgent, AgentResult
from typing import Dict, Any, List
import ast
import re

class ComplexityAnalyzerAgent(BaseAgent):
    """代码复杂度分析Agent"""
    
    def get_required_inputs(self) -> List[str]:
        return ["code_content", "file_path"]
    
    def get_output_schema(self) -> Dict[str, Any]:
        return {
            "complexity_score": "float",
            "metrics": "dict",
            "recommendations": "list"
        }
    
    def validate_input(self, input_data: Dict[str, Any]) -> bool:
        return "code_content" in input_data and "file_path" in input_data
    
    async def execute(self, input_data: Dict[str, Any], task_id: str = None) -> AgentResult:
        """分析代码复杂度"""
        code_content = input_data["code_content"]
        file_path = input_data["file_path"]
        
        # 计算各种复杂度指标
        metrics = {
            "cyclomatic_complexity": self._calculate_cyclomatic_complexity(code_content),
            "cognitive_complexity": self._calculate_cognitive_complexity(code_content),
            "lines_of_code": len(code_content.splitlines()),
            "function_count": self._count_functions(code_content),
            "class_count": self._count_classes(code_content),
            "nesting_depth": self._calculate_nesting_depth(code_content),
            "dependency_count": self._count_dependencies(code_content)
        }
        
        # 计算综合复杂度分数 (0-1)
        complexity_score = self._calculate_overall_complexity(metrics)
        
        # 生成建议
        recommendations = self._generate_recommendations(metrics, complexity_score)
        
        return AgentResult(
            agent_name=self.name,
            task_id=task_id,
            status="success",
            data={
                "complexity_score": complexity_score,
                "metrics": metrics,
                "recommendations": recommendations
            }
        )
    
    def _calculate_cyclomatic_complexity(self, code: str) -> int:
        """计算圈复杂度"""
        try:
            tree = ast.parse(code)
            complexity = 1  # 基础复杂度
            
            for node in ast.walk(tree):
                if isinstance(node, (ast.If, ast.While, ast.For, ast.AsyncFor)):
                    complexity += 1
                elif isinstance(node, ast.ExceptHandler):
                    complexity += 1
                elif isinstance(node, ast.BoolOp):
                    complexity += len(node.values) - 1
                    
            return complexity
        except:
            return 1
    
    def _calculate_cognitive_complexity(self, code: str) -> int:
        """计算认知复杂度"""
        # 简化的认知复杂度计算
        nesting_level = 0
        cognitive_complexity = 0
        
        lines = code.splitlines()
        for line in lines:
            stripped = line.strip()
            
            # 计算嵌套层级
            if any(keyword in stripped for keyword in ['if ', 'for ', 'while ', 'try:', 'except']):
                cognitive_complexity += nesting_level + 1
                if stripped.endswith(':'):
                    nesting_level += 1
            elif stripped in ['else:', 'elif', 'finally:']:
                cognitive_complexity += nesting_level
            elif stripped.startswith(('def ', 'class ', 'async def')):
                nesting_level = 0
                
        return cognitive_complexity
    
    def _count_functions(self, code: str) -> int:
        """统计函数数量"""
        try:
            tree = ast.parse(code)
            return len([node for node in ast.walk(tree) 
                       if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef))])
        except:
            return len(re.findall(r'def\s+\w+\s*$', code))
    
    def _count_classes(self, code: str) -> int:
        """统计类数量"""
        try:
            tree = ast.parse(code)
            return len([node for node in ast.walk(tree) if isinstance(node, ast.ClassDef)])
        except:
            return len(re.findall(r'class\s+\w+', code))
    
    def _calculate_nesting_depth(self, code: str) -> int:
        """计算最大嵌套深度"""
        max_depth = 0
        current_depth = 0
        
        for line in code.splitlines():
            stripped = line.strip()
            if any(keyword in stripped for keyword in ['if ', 'for ', 'while ', 'try:', 'with ']):
                if stripped.endswith(':'):
                    current_depth += 1
                    max_depth = max(max_depth, current_depth)
            elif stripped in ['else:', 'elif', 'except:', 'finally:']:
                continue
            elif not stripped or stripped.startswith('#'):
                continue
            else:
                # 检查是否是块结束
                if current_depth > 0 and not line.startswith(' ' * (current_depth * 4)):
                    current_depth = len(line) - len(line.lstrip()) // 4
                    
        return max_depth
    
    def _count_dependencies(self, code: str) -> int:
        """统计依赖数量"""
        import_count = len(re.findall(r'^import\s+', code, re.MULTILINE))
        from_import_count = len(re.findall(r'^from\s+.*import', code, re.MULTILINE))
        return import_count + from_import_count
    
    def _calculate_overall_complexity(self, metrics: Dict[str, int]) -> float:
        """计算综合复杂度分数"""
        # 权重配置
        weights = {
            "cyclomatic_complexity": 0.25,
            "cognitive_complexity": 0.25,
            "lines_of_code": 0.15,
            "function_count": 0.10,
            "class_count": 0.10,
            "nesting_depth": 0.10,
            "dependency_count": 0.05
        }
        
        # 归一化阈值
        thresholds = {
            "cyclomatic_complexity": 10,
            "cognitive_complexity": 15,
            "lines_of_code": 500,
            "function_count": 20,
            "class_count": 10,
            "nesting_depth": 5,
            "dependency_count": 20
        }
        
        score = 0.0
        for metric, value in metrics.items():
            if metric in weights:
                normalized = min(value / thresholds[metric], 1.0)
                score += weights[metric] * normalized
                
        return min(score, 1.0)
    
    def _generate_recommendations(self, metrics: Dict[str, int], complexity_score: float) -> List[str]:
        """生成优化建议"""
        recommendations = []
        
        if complexity_score < 0.3:
            recommendations.append("代码复杂度较低，建议使用Fast模式进行快速审查")
        elif complexity_score < 0.7:
            recommendations.append("代码复杂度中等，建议使用Standard模式进行标准审查")
        else:
            recommendations.append("代码复杂度较高，建议使用Deep模式进行深度审查")
            
        if metrics["cyclomatic_complexity"] > 10:
            recommendations.append("圈复杂度过高，建议拆分函数")
        if metrics["nesting_depth"] > 4:
            recommendations.append("嵌套层级过深，建议重构代码结构")
        if metrics["lines_of_code"] > 300:
            recommendations.append("代码行数过多，建议拆分文件或模块")
            
        return recommendations
```

#### 2. 策略决策Agent

```python
# core/agents/strategy_decision_agent.py
from core.agents.base_agent import BaseAgent, AgentResult
from typing import Dict, Any, List
from enum import Enum

class CRStrategy(Enum):
    """CR策略枚举"""
    FAST = "fast"
    STANDARD = "standard"
    DEEP = "deep"
    CUSTOM = "custom"

class StrategyDecisionAgent(BaseAgent):
    """策略决策Agent"""
    
    def get_required_inputs(self) -> List[str]:
        return ["complexity_score", "file_type", "change_type"]
    
    def get_output_schema(self) -> Dict[str, Any]:
        return {
            "strategy": "string",
            "confidence": "float",
            "reasoning": "string",
            "estimated_time": "float"
        }
    
    def validate_input(self, input_data: Dict[str, Any]) -> bool:
        required_fields = ["complexity_score"]
        return all(field in input_data for field in required_fields)
    
    async def execute(self, input_data: Dict[str, Any], task_id: str = None) -> AgentResult:
        """决策CR策略"""
        complexity_score = input_data["complexity_score"]
        file_type = input_data.get("file_type", "unknown")
        change_type = input_data.get("change_type", "modification")
        
        # 决策逻辑
        strategy, confidence, reasoning = self._decide_strategy(
            complexity_score, file_type, change_type
        )
        
        # 估算处理时间
        estimated_time = self._estimate_processing_time(strategy, complexity_score)
        
        return AgentResult(
            agent_name=self.name,
            task_id=task_id,
            status="success",
            data={
                "strategy": strategy.value,
                "confidence": confidence,
                "reasoning": reasoning,
                "estimated_time": estimated_time
            }
        )
    
    def _decide_strategy(self, complexity_score: float, file_type: str, change_type: str) -> tuple:
        """决策策略"""
        # 基于复杂度的基础决策
        if complexity_score < 0.3:
            base_strategy = CRStrategy.FAST
            base_confidence = 0.8
        elif complexity_score < 0.7:
            base_strategy = CRStrategy.STANDARD
            base_confidence = 0.7
        else:
            base_strategy = CRStrategy.DEEP
            base_confidence = 0.9
        
        # 根据文件类型调整
        if file_type in ["test", "config"]:
            if base_strategy == CRStrategy.DEEP:
                base_strategy = CRStrategy.STANDARD
                base_confidence *= 0.9
        elif file_type in ["core", "security"]:
            if base_strategy == CRStrategy.FAST:
                base_strategy = CRStrategy.STANDARD
                base_confidence *= 0.9
        
        # 根据变更类型调整
        if change_type == "new_file":
            if base_strategy == CRStrategy.FAST:
                base_strategy = CRStrategy.STANDARD
                base_confidence *= 0.8
        elif change_type == "deletion":
            base_strategy = CRStrategy.FAST
            base_confidence = 0.9
        
        # 生成推理说明
        reasoning = self._generate_reasoning(complexity_score, file_type, change_type, base_strategy)
        
        return base_strategy, base_confidence, reasoning
    
    def _estimate_processing_time(self, strategy: CRStrategy, complexity_score: float) -> float:
        """估算处理时间（秒）"""
        base_times = {
            CRStrategy.FAST: 5.0,
            CRStrategy.STANDARD: 15.0,
            CRStrategy.DEEP: 45.0
        }
        
        base_time = base_times.get(strategy, 15.0)
        complexity_factor = 1.0 + complexity_score
        
        return base_time * complexity_factor
    
    def _generate_reasoning(self, complexity_score: float, file_type: str, 
                          change_type: str, strategy: CRStrategy) -> str:
        """生成决策推理"""
        reasons = []
        
        if complexity_score < 0.3:
            reasons.append("代码复杂度低")
        elif complexity_score < 0.7:
            reasons.append("代码复杂度中等")
        else:
            reasons.append("代码复杂度高")
        
        if file_type in ["core", "security"]:
            reasons.append("核心/安全相关文件")
        elif file_type in ["test", "config"]:
            reasons.append("测试/配置文件")
        
        if change_type == "new_file":
            reasons.append("新增文件")
        elif change_type == "deletion":
            reasons.append("删除操作")
        
        return f"基于{', '.join(reasons)}，选择{strategy.value}策略"
```

#### 3. 模式链管理器

```python
# core/chains/mode_chain_manager.py
from typing import Dict, Any, Optional
from core.agents.base_agent import BaseAgent
from core.chains.base_chain import BaseChain
import asyncio

class ModeChainManager:
    """模式链管理器"""
    
    def __init__(self):
        self.chains: Dict[str, BaseChain] = {}
        self.agents: Dict[str, BaseAgent] = {}
    
    def register_chain(self, mode: str, chain: BaseChain):
        """注册模式链"""
        self.chains[mode] = chain
    
    def register_agent(self, name: str, agent: BaseAgent):
        """注册Agent"""
        self.agents[name] = agent
    
    async def execute_mode_chain(self, mode: str, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """执行指定模式的链"""
        if mode not in self.chains:
            raise ValueError(f"未找到模式链: {mode}")
        
        chain = self.chains[mode]
        return await chain.execute(input_data)

class FastModeChain(BaseChain):
    """快速模式链 - 纯LLM处理"""
    
    async def execute(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """执行快速模式链"""
        # 1. 直接LLM审查
        llm_agent = self.get_agent("llm_review_agent")
        llm_result = await llm_agent.safe_execute(input_data)
        
        # 2. 简单结果格式化
        return {
            "mode": "fast",
            "result": llm_result.data,
            "processing_time": llm_result.execution_time,
            "confidence": 0.7
        }

class StandardModeChain(BaseChain):
    """标准模式链 - LLM + 知识库检索"""
    
    async def execute(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """执行标准模式链"""
        # 1. 知识库检索
        knowledge_agent = self.get_agent("knowledge_retrieval_agent")
        knowledge_result = await knowledge_agent.safe_execute(input_data)
        
        # 2. 增强输入数据
        enhanced_input = {
            **input_data,
            "knowledge_context": knowledge_result.data
        }
        
        # 3. LLM审查
        llm_agent = self.get_agent("llm_review_agent")
        llm_result = await llm_agent.safe_execute(enhanced_input)
        
        # 4. 结果合并
        return {
            "mode": "standard",
            "result": llm_result.data,
            "knowledge_used": knowledge_result.data,
            "processing_time": knowledge_result.execution_time + llm_result.execution_time,
            "confidence": 0.8
        }

class DeepModeChain(BaseChain):
    """深度模式链 - 多轮分析 + 自检"""
    
    async def execute(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """执行深度模式链"""
        results = []
        total_time = 0.0
        
        # 1. 第一轮：标准分析
        standard_chain = StandardModeChain()
        standard_result = await standard_chain.execute(input_data)
        results.append(standard_result)
        total_time += standard_result["processing_time"]
        
        # 2. 第二轮：规则匹配
        rule_agent = self.get_agent("rule_matching_agent")
        rule_result = await rule_agent.safe_execute(input_data)
        results.append({"rule_analysis": rule_result.data})
        total_time += rule_result.execution_time
        
        # 3. 第三轮：自检复审
        review_input = {
            **input_data,
            "previous_results": results
        }
        
        self_review_agent = self.get_agent("self_review_agent")
        self_review_result = await self_review_agent.safe_execute(review_input)
        total_time += self_review_result.execution_time
        
        # 4. 结果整合
        return {
            "mode": "deep",
            "result": self_review_result.data,
            "analysis_rounds": len(results) + 1,
            "detailed_results": results,
            "processing_time": total_time,
            "confidence": 0.9
        }
```

## 智能路由实现

### 动态路由器

```python
# core/orchestrator/intelligent_router.py
from typing import Dict, Any, List, Optional
from core.agents.base_agent import BaseAgent
import asyncio
import time

class IntelligentRouter:
    """智能路由器 - 动态选择处理路径"""
    
    def __init__(self, complexity_agent: BaseAgent, strategy_agent: BaseAgent, 
                 chain_manager: ModeChainManager):
        self.complexity_agent = complexity_agent
        self.strategy_agent = strategy_agent
        self.chain_manager = chain_manager
        self.routing_history: List[Dict] = []
    
    async def route_request(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """智能路由请求"""
        start_time = time.time()
        
        # 1. 复杂度分析
        complexity_result = await self.complexity_agent.safe_execute(input_data)
        if complexity_result.status != "success":
            return self._create_error_response("复杂度分析失败", complexity_result.error_message)
        
        # 2. 策略决策
        strategy_input = {
            **input_data,
            **complexity_result.data
        }
        strategy_result = await self.strategy_agent.safe_execute(strategy_input)
        if strategy_result.status != "success":
            return self._create_error_response("策略决策失败", strategy_result.error_message)
        
        # 3. 执行选定策略
        selected_strategy = strategy_result.data["strategy"]
        chain_result = await self.chain_manager.execute_mode_chain(selected_strategy, input_data)
        
        # 4. 记录路由历史
        routing_record = {
            "timestamp": start_time,
            "complexity_score": complexity_result.data["complexity_score"],
            "selected_strategy": selected_strategy,
            "confidence": strategy_result.data["confidence"],
            "processing_time": time.time() - start_time,
            "success": True
        }
        self.routing_history.append(routing_record)
        
        # 5. 返回增强结果
        return {
            **chain_result,
            "routing_info": {
                "complexity_analysis": complexity_result.data,
                "strategy_decision": strategy_result.data,
                "total_processing_time": time.time() - start_time
            }
        }
    
    def _create_error_response(self, error_type: str, error_message: str) -> Dict[str, Any]:
        """创建错误响应"""
        return {
            "status": "error",
            "error_type": error_type,
            "error_message": error_message,
            "timestamp": time.time()
        }
    
    def get_routing_statistics(self) -> Dict[str, Any]:
        """获取路由统计信息"""
        if not self.routing_history:
            return {"message": "暂无路由历史"}
        
        strategy_counts = {}
        total_time = 0
        success_count = 0
        
        for record in self.routing_history:
            strategy = record["selected_strategy"]
            strategy_counts[strategy] = strategy_counts.get(strategy, 0) + 1
            total_time += record["processing_time"]
            if record["success"]:
                success_count += 1
        
        return {
            "total_requests": len(self.routing_history),
            "success_rate": success_count / len(self.routing_history),
            "average_processing_time": total_time / len(self.routing_history),
            "strategy_distribution": strategy_counts,
            "most_used_strategy": max(strategy_counts.items(), key=lambda x: x[1])[0]
        }
```

## 自适应学习机制

### 策略优化器

```python
# core/learning/strategy_optimizer.py
from typing import Dict, Any, List
import numpy as np
from collections import defaultdict

class StrategyOptimizer:
    """策略优化器 - 基于历史数据优化决策"""
    
    def __init__(self):
        self.performance_history: List[Dict] = []
        self.strategy_performance: Dict[str, List[float]] = defaultdict(list)
        self.complexity_thresholds = {"fast": 0.3, "standard": 0.7}
    
    def record_performance(self, complexity_score: float, strategy: str, 
                          performance_score: float, processing_time: float):
        """记录性能数据"""
        record = {
            "complexity_score": complexity_score,
            "strategy": strategy,
            "performance_score": performance_score,
            "processing_time": processing_time,
            "timestamp": time.time()
        }
        
        self.performance_history.append(record)
        self.strategy_performance[strategy].append(performance_score)
    
    def optimize_thresholds(self) -> Dict[str, float]:
        """优化复杂度阈值"""
        if len(self.performance_history) < 50:
            return self.complexity_thresholds
        
        # 分析不同复杂度区间的策略效果
        complexity_ranges = [
            (0.0, 0.2), (0.2, 0.4), (0.4, 0.6), (0.6, 0.8), (0.8, 1.0)
        ]
        
        best_thresholds = {}
        
        for i, (low, high) in enumerate(complexity_ranges):
            range_records = [r for r in self.performance_history 
                           if low <= r["complexity_score"] < high]
            
            if not range_records:
                continue
            
            # 计算每种策略在该复杂度范围内的平均性能
            strategy_avg_performance = {}
            for strategy in ["fast", "standard", "deep"]:
                strategy_records = [r for r in range_records if r["strategy"] == strategy]
                if strategy_records:
                    avg_perf = np.mean([r["performance_score"] for r in strategy_records])
                    strategy_avg_performance[strategy] = avg_perf
            
            # 找到最佳策略
            if strategy_avg_performance:
                best_strategy = max(strategy_avg_performance.items(), key=lambda x: x[1])[0]
                
                # 更新阈值
                if best_strategy == "fast" and i < 2:
                    best_thresholds["fast"] = high
                elif best_strategy == "standard" and i >= 1 and i <= 3:
                    if "fast" not in best_thresholds:
                        best_thresholds["fast"] = low
                    best_thresholds["standard"] = high
        
        # 应用优化后的阈值
        if best_thresholds:
            self.complexity_thresholds.update(best_thresholds)
        
        return self.complexity_thresholds
    
    def get_strategy_recommendation(self, complexity_score: float) -> str:
        """基于优化后的阈值推荐策略"""
        thresholds = self.optimize_thresholds()
        
        if complexity_score < thresholds["fast"]:
            return "fast"
        elif complexity_score < thresholds["standard"]:
            return "standard"
        else:
            return "deep"
```

## 总结

智能决策链通过以下机制实现智能化：

1. **多维度分析**：复杂度、文件类型、变更类型等多维度分析
2. **动态策略选择**：基于分析结果动态选择最优处理策略
3. **自适应学习**：基于历史性能数据持续优化决策阈值
4. **性能监控**：全程监控处理时间和效果，为优化提供数据支持

这种设计确保了系统能够根据不同的代码特征选择最合适的处理方式，在保证质量的同时优化处理效率。