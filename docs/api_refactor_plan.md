# API目录重构计划

## 当前问题分析

### 1. 旧服务未迁移
- `api/service/cr_lc_service.py` (920行) - 核心CR服务，未迁移到新的services目录
- `api/service/` 目录下还有其他旧服务文件

### 2. API版本管理不完整
- `api/v1/` 目录只有 `mcp_tools/`，缺少其他API端点
- 缺少统一的API路由管理

### 3. 缺少中间件和数据模型
- 没有认证中间件
- 没有统一的数据模型定义
- 缺少API文档生成

## 重构方案

### 第一步：迁移旧服务
\`\`\`
api/service/cr_lc_service.py → 重构为多个服务：
  - services/cr_service.py (核心CR逻辑)
  - services/llm_service.py (LLM调用服务)
  - core/orchestrator/cr_orchestrator.py (CR编排器)
\`\`\`

### 第二步：完善API版本管理
\`\`\`
api/v1/
├── __init__.py
├── cr_api.py (CR相关API)
├── chunk_api.py (分块相关API)
├── knowledge_api.py (知识库API)
├── evaluation_api.py (评估API)
└── mcp_tools/ (已存在)
\`\`\`

### 第三步：添加中间件
\`\`\`
api/middleware/
├── __init__.py
├── auth_middleware.py (认证中间件)
├── logging_middleware.py (日志中间件)
├── performance_middleware.py (性能监控中间件)
└── error_handler.py (错误处理中间件)
\`\`\`

### 第四步：添加数据模型
\`\`\`
api/schemas/
├── __init__.py
├── cr_schemas.py (CR相关数据模型)
├── chunk_schemas.py (分块相关数据模型)
├── common_schemas.py (通用数据模型)
└── response_schemas.py (响应数据模型)
\`\`\`