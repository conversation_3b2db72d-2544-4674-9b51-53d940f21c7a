# Phase 2: Agent抽象层设计与实现

## 概述

第二阶段的目标是在现有LangChain RAG基础上，构建统一的Agent抽象层，为后续Multi-Agent架构奠定基础。本阶段将保持现有API完全兼容，内部逐步重构为Agent化架构。

## 架构设计

### 核心组件架构

```
agents/
├── base/
│   ├── agent.py              # Agent基类
│   ├── orchestrator.py       # Agent编排器
│   ├── data_models.py        # 数据模型
│   └── exceptions.py         # 异常定义
├── implementations/
│   ├── chunking_agent.py     # 代码分块Agent
│   ├── rule_matching_agent.py # 规则匹配Agent
│   ├── llm_review_agent.py   # LLM审查Agent
│   ├── evaluation_agent.py   # 效果评估Agent
│   └── feedback_agent.py     # 反馈处理Agent
├── workflows/
│   ├── workflow_manager.py   # 工作流管理器
│   └── definitions/          # 工作流定义文件
└── adapters/
    ├── legacy_adapter.py     # 向后兼容适配器
    └── knowledge_adapter.py  # 知识库适配器
```

### 数据流设计

```mermaid
flowchart TD
    A[Legacy API] --> B[Legacy Adapter]
    B --> C[Workflow Manager]
    C --> D[Agent Orchestrator]
    
    D --> E[Chunking Agent]
    D --> F[Rule Matching Agent]
    D --> G[LLM Review Agent]
    D --> H[Evaluation Agent]
    
    E --> I[Unified Knowledge Base]
    F --> I
    G --> I
    H --> I
    
    I --> J[Vector DB]
    I --> K[Structured DB]
    I --> L[Chunk Store]
```

## 核心接口设计

### Agent基类

```python
# agents/base/agent.py
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from enum import Enum

class AgentStatus(Enum):
    PENDING = "pending"
    RUNNING = "running"
    SUCCESS = "success"
    FAILED = "failed"
    SKIPPED = "skipped"

@dataclass
class AgentContext:
    """Agent执行上下文"""
    workflow_id: str
    session_id: str
    language: str
    business: str
    metadata: Dict[str, Any]

@dataclass
class AgentInput:
    """标准化Agent输入"""
    data: Dict[str, Any]
    context: AgentContext
    previous_results: Dict[str, Any] = None

@dataclass
class AgentOutput:
    """标准化Agent输出"""
    status: AgentStatus
    result: Dict[str, Any]
    metrics: Dict[str, Any]
    errors: List[str] = None
    next_agents: List[str] = None

class BaseAgent(ABC):
    """Agent基类"""
    
    def __init__(self, agent_id: str, config: Dict[str, Any]):
        self.agent_id = agent_id
        self.config = config
        self.metrics = {}
        self.dependencies = []
    
    @abstractmethod
    async def execute(self, input_data: AgentInput) -> AgentOutput:
        """执行Agent核心逻辑"""
        pass
    
    @abstractmethod
    def validate_input(self, input_data: AgentInput) -> bool:
        """验证输入数据有效性"""
        pass
    
    def get_dependencies(self) -> List[str]:
        """获取依赖的Agent列表"""
        return self.dependencies
    
    async def pre_execute(self, input_data: AgentInput) -> bool:
        """执行前置检查"""
        return self.validate_input(input_data)
    
    async def post_execute(self, output: AgentOutput) -> AgentOutput:
        """执行后置处理"""
        return output
```

### Agent编排器

```python
# agents/base/orchestrator.py
class AgentOrchestrator:
    """Agent编排器 - 负责Agent的调度和执行"""
    
    def __init__(self, knowledge_base, monitor=None):
        self.agents: Dict[str, BaseAgent] = {}
        self.execution_graph = {}
        self.knowledge_base = knowledge_base
        self.monitor = monitor
    
    def register_agent(self, agent: BaseAgent):
        """注册Agent到编排器"""
        self.agents[agent.agent_id] = agent
        self._update_execution_graph()
    
    async def execute_workflow(self, workflow_def: Dict[str, Any], 
                             initial_data: Dict[str, Any]) -> Dict[str, Any]:
        """执行完整工作流"""
        context = self._create_context(workflow_def, initial_data)
        execution_plan = self._build_execution_plan(workflow_def)
        
        results = {}
        for stage in execution_plan:
            stage_results = await self._execute_stage(stage, context, results)
            results.update(stage_results)
        
        return self._format_final_result(results)
    
    async def _execute_stage(self, stage: List[str], context: AgentContext, 
                           previous_results: Dict[str, Any]) -> Dict[str, Any]:
        """并行执行同一阶段的Agents"""
        tasks = []
        for agent_id in stage:
            agent = self.agents[agent_id]
            input_data = AgentInput(
                data=self._prepare_agent_input(agent_id, context, previous_results),
                context=context,
                previous_results=previous_results
            )
            tasks.append(self._execute_single_agent(agent, input_data))
        
        # 并行执行
        stage_results = await asyncio.gather(*tasks, return_exceptions=True)
        return self._process_stage_results(stage, stage_results)
```

## 具体Agent实现

### LLM审查Agent

```python
# agents/implementations/llm_review_agent.py
class LLMReviewAgent(BaseAgent):
    """LLM审查Agent - 复用现有intelligent_cr_chain逻辑"""
    
    def __init__(self, agent_id: str, config: Dict[str, Any]):
        super().__init__(agent_id, config)
        self.dependencies = ["chunking_agent", "rule_matching_agent"]
        self._init_cr_components()
    
    def _init_cr_components(self):
        """初始化CR组件 - 复用现有代码"""
        from common.intelligent_cr_chain import IntelligentCRChain
        from utils.cr_prompt_builder import CRPromptFactory
        
        self.prompt_builder = CRPromptFactory.create_builder(
            self.config.get("language", "python"),
            self.config.get("business", "default")
        )
        
        # 复用现有的CR链逻辑
        self.cr_chain = IntelligentCRChain(
            llm=self.config["llm"],
            devmind_service=self.config["devmind_service"],
            dataset_ids=self.config.get("dataset_ids", []),
            prompt_builder=self.prompt_builder
        )
    
    async def execute(self, input_data: AgentInput) -> AgentOutput:
        """执行LLM审查"""
        try:
            # 从前置Agent获取数据
            chunks = input_data.previous_results.get("chunking_agent", {}).get("chunks", [])
            matched_rules = input_data.previous_results.get("rule_matching_agent", {}).get("rules", [])
            
            # 执行审查
            mode = self.config.get("mode", "standard")
            problems = []
            
            for chunk in chunks:
                chunk_problems = await self._review_chunk(chunk, matched_rules, mode)
                problems.extend(chunk_problems)
            
            # 去重和优化
            optimized_problems = self._optimize_problems(problems)
            
            return AgentOutput(
                status=AgentStatus.SUCCESS,
                result={
                    "problems": optimized_problems,
                    "total_chunks": len(chunks),
                    "rules_applied": len(matched_rules)
                },
                metrics={
                    "execution_time": self._get_execution_time(),
                    "problem_count": len(optimized_problems),
                    "llm_calls": self.cr_chain.performance_stats.get("llm_calls", 0)
                },
                next_agents=["evaluation_agent"]
            )
            
        except Exception as e:
            return AgentOutput(
                status=AgentStatus.FAILED,
                result={},
                metrics={},
                errors=[str(e)]
            )
    
    async def _review_chunk(self, chunk: Dict[str, Any], rules: List[Dict[str, Any]], 
                          mode: str) -> List[Dict[str, Any]]:
        """审查单个代码块"""
        # 复用现有CR链的逻辑
        if mode == "fast":
            return await self.cr_chain._fast_review(
                chunk["content"], chunk["full_code"], "", ""
            )
        elif mode == "standard":
            return await self.cr_chain._standard_review(
                chunk["content"], chunk["full_code"], "", ""
            )
        elif mode == "deep":
            return await self.cr_chain._deep_review_with_self_check(
                chunk["content"], chunk["full_code"], "", ""
            )
```

### 规则匹配Agent

```python
# agents/implementations/rule_matching_agent.py
class RuleMatchingAgent(BaseAgent):
    """规则匹配Agent - 复用现有cr_rule_config逻辑"""
    
    def __init__(self, agent_id: str, config: Dict[str, Any]):
        super().__init__(agent_id, config)
        self.dependencies = ["chunking_agent"]
        self._init_rule_components()
    
    def _init_rule_components(self):
        """初始化规则组件"""
        from utils.cr_rule_config import cr_rule_manager
        
        self.rule_manager = cr_rule_manager
        self.language = self.config.get("language", "python")
        self.business = self.config.get("business", "default")
    
    async def execute(self, input_data: AgentInput) -> AgentOutput:
        """执行规则匹配"""
        try:
            chunks = input_data.previous_results.get("chunking_agent", {}).get("chunks", [])
            
            # 获取适用的规则集
            rule_set = self.rule_manager.get_rule_set(self.language, self.business)
            if not rule_set:
                rule_set = self.rule_manager.get_rule_set(self.language, "default")
            
            matched_rules = []
            rule_matches = {}
            
            for chunk in chunks:
                chunk_matches = self._match_chunk_rules(chunk, rule_set)
                matched_rules.extend(chunk_matches)
                rule_matches[chunk["id"]] = chunk_matches
            
            return AgentOutput(
                status=AgentStatus.SUCCESS,
                result={
                    "rules": matched_rules,
                    "rule_matches": rule_matches,
                    "rule_set_info": {
                        "language": rule_set.language,
                        "business": rule_set.business,
                        "total_rules": len(rule_set.rules)
                    }
                },
                metrics={
                    "matched_rules_count": len(matched_rules),
                    "chunks_processed": len(chunks)
                },
                next_agents=["llm_review_agent"]
            )
            
        except Exception as e:
            return AgentOutput(
                status=AgentStatus.FAILED,
                result={},
                metrics={},
                errors=[str(e)]
            )
```

## 向后兼容适配器

```python
# agents/adapters/legacy_adapter.py
class LegacyAdapter:
    """向后兼容适配器 - 保持现有API不变"""
    
    def __init__(self, orchestrator: AgentOrchestrator):
        self.orchestrator = orchestrator
        self._setup_default_workflow()
    
    def build_intelligent_cr_chain(self, llm, devmind_service, dataset_ids,
                                  mode='standard', language='python', business='default'):
        """向后兼容的构建函数"""
        # 创建Agent配置
        agent_configs = {
            "chunking_agent": {
                "max_chunk_size": 1000,
                "overlap": 100,
                "language": language
            },
            "rule_matching_agent": {
                "language": language,
                "business": business
            },
            "llm_review_agent": {
                "llm": llm,
                "devmind_service": devmind_service,
                "dataset_ids": dataset_ids,
                "mode": mode,
                "language": language,
                "business": business
            }
        }
        
        # 返回兼容的链对象
        return LegacyCRChain(self.orchestrator, agent_configs, mode)

class LegacyCRChain:
    """兼容现有CR链接口的包装器"""
    
    def __init__(self, orchestrator: AgentOrchestrator, agent_configs: Dict[str, Any], mode: str):
        self.orchestrator = orchestrator
        self.agent_configs = agent_configs
        self.mode = mode
    
    async def __call__(self, chain_input: Dict[str, Any]) -> Dict[str, Any]:
        """保持现有调用接口"""
        workflow_def = {
            "name": f"{self.mode}_cr_workflow",
            "agents": self.agent_configs,
            "flow": self._get_workflow_flow()
        }
        
        # 转换输入格式
        initial_data = {
            "code_position": chain_input.get("codePosition"),
            "diff_content": chain_input.get("diff_content"),
            "full_code": chain_input.get("full_code"),
            "upstream_code": chain_input.get("upstream_code", {}),
            "downstream_code": chain_input.get("downstream_code", {})
        }
        
        # 执行工作流
        result = await self.orchestrator.execute_workflow(workflow_def, initial_data)
        
        # 转换输出格式以保持兼容
        return self._format_legacy_output(result)
```

## 实施步骤

### 第一步：基础架构搭建（2周）

1. **创建Agent基础框架**
   - [ ] 实现BaseAgent抽象类
   - [ ] 创建数据模型（AgentInput/AgentOutput）
   - [ ] 实现基础的AgentOrchestrator

2. **建立测试框架**
   - [ ] 创建Agent单元测试基类
   - [ ] 实现Mock Agent用于测试
   - [ ] 建立集成测试环境

### 第二步：核心Agent实现（3周）

1. **LLM审查Agent**
   - [ ] 包装现有intelligent_cr_chain逻辑
   - [ ] 实现分块处理能力
   - [ ] 添加性能监控

2. **规则匹配Agent**
   - [ ] 集成cr_rule_config系统
   - [ ] 实现规则缓存机制
   - [ ] 优化匹配性能

3. **代码分块Agent**
   - [ ] 实现智能代码分块算法
   - [ ] 支持多语言分块策略
   - [ ] 添加重叠处理逻辑

### 第三步：向后兼容层（1周）

1. **Legacy Adapter实现**
   - [ ] 包装现有API接口
   - [ ] 确保输入输出格式兼容
   - [ ] 添加性能对比测试

2. **渐进式迁移**
   - [ ] 创建特性开关
   - [ ] 实现A/B测试框架
   - [ ] 建立回滚机制

## 风险评估与应对

### 技术风险

1. **性能风险**
   - **风险**：Agent化可能带来额外开销
   - **应对**：实现性能基准测试，确保不低于现有性能

2. **兼容性风险**
   - **风险**：新架构可能破坏现有功能
   - **应对**：完整的回归测试套件，渐进式迁移

3. **复杂性风险**
   - **风险**：架构复杂度增加，维护成本上升
   - **应对**：详细文档，清晰的接口设计

### 业务风险

1. **迁移风险**
   - **风险**：迁移过程中服务不稳定
   - **应对**：蓝绿部署，快速回滚机制

2. **学习成本**
   - **风险**：团队需要时间适应新架构
   - **应对**：分阶段培训，详细文档支持

## 成功指标

1. **功能指标**
   - [ ] 100%向后兼容现有API
   - [ ] 新Agent架构覆盖所有现有功能
   - [ ] 支持至少3种工作流模式

2. **性能指标**
   - [ ] 响应时间不超过现有系统110%
   - [ ] 内存使用不超过现有系统120%
   - [ ] 支持并发处理能力

3. **质量指标**
   - [ ] 单元测试覆盖率>90%
   - [ ] 集成测试通过率100%
   - [ ] 零生产环境故障
