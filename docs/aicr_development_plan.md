# 1. 背景

## 1.1 项目背景与前端系统能力现状
长期以来，CR的效果往往受限于CR参与人员的风格与习惯且需投入大量的时间来保证效果，在CR时间难以保证的现状下该流程很容易流于形式，导致大家的建议往往是一些命名、风格性的建议；而code平台推出的智能评审功能由于模型能力与任务拆解的问题，导致CR的实际效果与团队预设规则关联度非常低，实际使用价值不大。与此同时，在问题分级、代码设计场景目前无工具提效或保证效果下限，代码是否通过交付标准也缺乏智能识别与评估。

当前系统已具备如下核心能力：
  - **代码差异分析**：自动获取并分析Git分支间的代码差异
  - **LLM代码审查**：调用大语言模型（如Claude 3.5 Sonnet）进行智能审查
  - **问题分类与建议**：按严重程度（P0/P1/P2）和类别分类，给出修改建议
  - **学城文档生成**：自动生成结构化审查报告并发布
  - **大象消息通知**：通过大象机器人推送审查结果

### 系统架构总览
<!-- 此处可添加系统架构图 -->

# 2. 目标

闪购AI代码审查系统（Shangou AI CR）以"知识库驱动、Agent协同、效果可量化、架构可演进"为核心，致力于打造企业级高效、智能、可持续演进的全栈CR辅助Agent平台。主要目标包括：
  - 多入口集成（Web、IDE、CI/CD），提升开发体验
  - 自动化代码分块与结构化分析，支持多语言（Python/Java/JS/TS等）
  - 支持多Agent协作，流程自动化、可追溯
  - 基于大语言模型（LLM）实现智能代码审查与建议生成
  - 构建多层次、可扩展的知识库，融合公司规范、最佳实践、反面案例等
  - 提供量化评估体系，持续优化审查效果

# 3. 问题和挑战

- **分块内容不完整**：当前的做法是只关注diff代码本身，缺失了上下文的信息，导致大模型在CR时得到的结果可能出现片面、漏报甚至误判。
- **完全依赖提示词**：提示词只能提供最基本的对于代码的识别且难扩展，长度受限，甚至可能会出现幻觉问题，不能给到很好的审查和结果建议。
- **多Agent协作复杂**：多Agent解耦、接口标准化难度较大，涉及上下游数据流转和任务编排。
- **LLM响应与准确率**：大模型响应速度慢、准确率不达标时需有降级与兜底方案。
- **数据质量与知识融合**：分块准确率、知识冗余、规则冲突等问题需持续优化。
- **性能与并发**：高并发下的检索、分片、限流等性能瓶颈。
- **用户反馈利用率低**：建议采纳率、反馈采集率不高，影响系统自优化。
- **兼容性与集成**：多模型/多入口兼容性问题。

# 4. 设计方案

```mermaid
flowchart TD
    A[架构设计] --> B[数据设计]
    A --> C[接口与集成设计]
    A --> D[流程与编排设计]
    A --> E[安全与权限设计]
    A --> F[性能与扩展性设计]
    A --> G[评估与反馈闭环设计]
    B --> B1[统一知识库结构]
    B --> B2[数据清洗与流转]
    C --> C1[多入口集成]
    C --> C2[Agent接口标准]
    D --> D1[主流程编排]
    D --> D2[多Agent协作]
    F --> F1[高并发与限流]
    F --> F2[异步与分布式]
    G --> G1[评估指标]
    G --> G2[自动化测评]
```

## 4.1 架构设计

### 4.1.1 系统分层与Agent协作
- **Chunking Agent**：负责代码分块与元数据提取
- **QA Agent**：自动生成代码-注释问答对
- **Rule Matching Agent**：根据分块内容匹配审查规则
- **LLM Review Agent**：调用大模型进行代码审查与建议生成
- **Evaluation Agent**：自动化评估AI审查效果
- **Feedback Agent**：收集用户反馈并驱动规则/模型优化

各Agent通过LangChain统一编排，支持多轮对话、上下文管理和流程自动化。所有知识、代码块、规范、案例等统一embedding，支持高效语义检索。

### 4.1.2 统一知识库数据结构（以Chunk为核心）
所有知识类型（代码分块、开发规范、优秀案例、反面案例等）均采用以Chunk为核心的统一结构，强调问题（question）与代码段（positions）的精确关联，便于多类型知识融合与扩展。

#### Chunk数据结构（Python伪代码）
```python
class Chunk(BaseModel):
    id: str = ""  # 唯一标识
    content: str = ""  # 代码内容或知识内容
    document_id: str = ""  # 所属文档/文件ID
    docnm_kwd: str = ""  # 文档/文件名或关键词
    important_keywords: list = Field(default_factory=list)  # 重要关键词
    questions: list = Field(default_factory=list)  # 关联问题（可为问答对结构）
    question_tks: str = ""  # 问题token信息
    image_id: str = ""  # 相关图片/可视化ID
    available: bool = True  # 是否可用
    positions: list[list[int]] = Field(default_factory=list)  # 代码片段或知识点在原文件中的位置，支持多段
    knowledge_type: str = ""  # 知识类型（如code, dev_spec, best_practice, anti_pattern等）
    extra: dict = Field(default_factory=dict)  # 其他可扩展字段

    @validator('positions')
    def validate_positions(cls, value):
        for sublist in value:
            if len(sublist) != 5:
                raise ValueError("Each sublist in positions must have a length of 5")
        return value
```
**说明**：positions字段用于精确标记该chunk在原文件中的所有位置，每个子列表为 [start_line, end_line, start_col, end_col, block_type]，支持多段代码或知识点合并为一个chunk。

### 4.1.3 典型知识类型与Json示例

#### 代码分块知识
```python
{
  "id": "order_service.py:function:create_order:120",
  "content": "def create_order(...): ...",
  "document_id": "order_service.py",
  "docnm_kwd": "订单服务",
  "important_keywords": ["订单", "创建", "幂等性"],
  "questions": [
    {"question": "create_order函数的主要作用是什么？", "answer": "用于创建新订单，包含幂等性校验。"},
    {"question": "该函数如何保证幂等性？", "answer": "通过唯一请求ID和数据库约束实现。"}
  ],
  "question_tks": "create_order, 幂等性, 订单",
  "image_id": "img_12345",
  "available": true,
  "positions": [[120, 150, 0, 20, 1]],
  "knowledge_type": "code",
  "extra": {}
}
```

#### 开发规范知识
```python
{
  "id": "spec_python_naming_001",
  "content": "变量名应采用小写字母和下划线，避免使用单字符名和保留字。",
  "document_id": "python_dev_spec.md",
  "docnm_kwd": "Python开发规范",
  "important_keywords": ["命名规范", "可读性"],
  "questions": [
    {"question": "Python变量命名的推荐方式是什么？", "answer": "应采用小写字母和下划线。"}
  ],
  "question_tks": "变量命名, PEP8",
  "image_id": "",
  "available": true,
  "positions": [[10, 12, 0, 30, 2]],
  "knowledge_type": "dev_spec",
  "extra": {
    "example_good": "user_name = 'Tom'",
    "example_bad": "UserName = 'Tom'",
    "source": "公司Python开发规范v2.0",
    "applicable_scope": "全公司Python项目",
    "updated_at": "2024-06-01"
  }
}
```

#### 优秀案例知识
```python
{
  "id": "case_async_opt_001",
  "content": "通过asyncio实现高并发接口，显著提升响应速度。",
  "document_id": "order_service_pr1234.md",
  "docnm_kwd": "订单服务优化案例",
  "important_keywords": ["性能优化", "异步编程"],
  "questions": [
    {"question": "如何提升接口的并发性能？", "answer": "采用asyncio实现异步IO。"}
  ],
  "question_tks": "asyncio, 性能, 并发",
  "image_id": "",
  "available": true,
  "positions": [[30, 40, 0, 20, 3]],
  "knowledge_type": "best_practice",
  "extra": {
    "code_snippet": "async def fetch_data(): ...",
    "context": "订单服务接口优化",
    "effect": "接口QPS提升3倍，平均延迟降低50%",
    "source": "订单服务PR#1234",
    "applicable_scope": "高并发接口",
    "updated_at": "2024-05-20"
  }
}
```

#### 反面案例知识
```python
{
  "id": "anti_pattern_sql_inject_001",
  "content": "直接拼接SQL字符串，存在注入风险。",
  "document_id": "java_security_cases.md",
  "docnm_kwd": "Java安全案例",
  "important_keywords": ["安全", "SQL注入"],
  "questions": [
    {"question": "为什么不能直接拼接SQL字符串？", "answer": "存在SQL注入风险。"}
  ],
  "question_tks": "SQL注入, 安全",
  "image_id": "",
  "available": true,
  "positions": [[50, 55, 0, 30, 4]],
  "knowledge_type": "anti_pattern",
  "extra": {
    "bad_code": "String sql = \"SELECT * FROM user WHERE name='\" + name + \"'\";",
    "good_code": "PreparedStatement ps = conn.prepareStatement(\"SELECT * FROM user WHERE name=?\");",
    "risk": "攻击者可通过name参数注入恶意SQL",
    "source": "安全组审查案例",
    "applicable_scope": "所有数据库操作",
    "updated_at": "2024-04-15"
  }
}
```

### 4.1.4 数据来源与清洗方法
  - **数据来源**：公司内部文档、历史代码库、开源项目、专家经验、外部标准等。
  - **清洗方法**：结构化提取、去重归类、质量筛选、元数据补充、标准化格式。

### 4.1.5 效果评估体系与自动化测评

#### 评估指标体系
  1. **准确率（Precision）**
     - 准确率 = AI发现的真实问题数 / AI发现的总问题数
  2. **召回率（Recall）**
     - 召回率 = AI发现的真实问题数 / 所有真实问题数
  3. **F1分数**
     - F1 = 2 × (准确率 × 召回率) / (准确率 + 召回率)
  4. **分类准确率**
     - 分类准确率 = 分类正确的问题数 / 总问题数
  5. **建议采纳率**
     - 采纳率 = 被采纳的AI建议数 / AI建议总数
  6. **平均审查时长**
     - 平均时长 = 总审查时长 / 审查次数
  7. **过时率**
     - 过时率 = 被开发者看到并在后续被修改的评论数 / 被开发者实际看到的评论总数 × 100%

#### 自动化测评方案
  - **自动标注对比**：采集历史PR的人工审查结论，自动与AI审查结果对齐，批量计算准确率、召回率等。
  - **回归测试集**：构建标准化的代码审查测试集，定期回归评测。
  - **持续集成评测**：每次模型/规则更新后自动触发评测，生成评估报告。
  - **用户行为追踪**：自动统计建议采纳率、反馈率等用户行为指标。

# 5. 执行计划

本系统采用分阶段推进策略，确保各项能力有序落地，风险可控。

| 事项 | 主要目标/任务 | 负责人/角色 | 预计时间 | 主要风险/应对措施 | 交付标准 |
| --- | --- | --- | --- | --- | --- |
| 1 | 主流程完成并进行系统性评估 | 王琪琛 | 5.23-5.27 | DevMind 自定义代码解析和检索功能 | 完成基本功能的实现和效果评估，输出评估文档 |
| 2 | MCP服务框架搭建、多Agent注册与调度、分块API、知识库结构 | 王琪琛 | 6.10 - 6.18 | 架构设计、分块准确率/接口文档、单测 | MCP服务可用、分块API上线、结构统一 |
| 3 | LLM Review Agent、自动化评测Agent、多模型支持、QA Agent | 王琪琛 | 6.15 - 6.26 | LLM响应慢、规则冲突/降级、人工review | LLM审查可用、规则融合、QA上线 |
| 4 | 检索接口、精准召回、前端高亮、反馈闭环、CI/CD集成、性能优化 | 王琪琛 | 6.20 - 7.5 | 检索性能、行号映射、并发瓶颈/分片、限流 | 检索能力上线、CI集成、性能达标 |
| 5 | 数据清洗与批量导入、企业定制、数据分析、Agent自优化 | 王琪琛 | 6.29 - 7.6 | 数据质量、反馈利用率/规则校验、激励机制 | 数据导入完成、优化效果可量化 |

## 资源需求与风险管理

### 资源需求
  - **人力**：当前节奏需1人（不包含前端）
  - **基础设施**：LLM API、向量数据库、存储、测试环境
  - **知识资源**：最佳实践资料、编码部规范文档、历史版本的pr任务或者代码仓库

### 主要风险与应对措施
| 风险类型 | 具体风险描述 | 应对措施 |
| --- | --- | --- |
| 架构设计风险 | 多Agent协作复杂、接口不统一 | 及时评审、接口文档同步 |
| 算法/模型风险 | LLM响应慢、准确率不达标 | 降级方案、异步处理、持续评测 |
| 数据质量风险 | 分块准确率低、知识冗余 | 持续单元测试、数据清洗、人工抽查 |
| 规则冲突风险 | 规则优先级、融合冲突 | 优先级机制、人工review |
| 性能与并发风险 | 检索瓶颈、并发处理不足 | 分片、限流、异步、分布式 |
| 用户反馈利用率低 | 反馈采集率、采纳率不高 | 激励机制、自动归档、持续优化 |
| 兼容性与集成风险 | 多模型/多入口兼容性问题 | 接口适配层、分阶段联调 |