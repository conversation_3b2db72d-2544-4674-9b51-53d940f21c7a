# Utils目录重构计划

## 当前问题分析

### 1. 工具函数职责不清
- `__init__.py` (11KB) - 包含大量工具函数，应该拆分
- `cr_utils.py` (13KB) - CR相关工具，应该移到core模块
- `api_utils.py` (12KB) - API工具函数
- `citadel_uitls.py` (14KB) - 拼写错误，应该是citadel_utils.py

### 2. 配置相关工具分散
- `cr_rule_config.py` (15KB) - 规则配置，应该移到config目录
- `cr_prompt_builder.py` (12KB) - 提示词构建器，应该移到core/prompts/

### 3. 文件命名不规范
- `crypt_uitls.py` - 拼写错误
- `citadel_uitls.py` - 拼写错误

## 重构方案

### 第一步：拆分大文件
\`\`\`
utils/__init__.py → 拆分为：
  - utils/common_utils.py (通用工具函数)
  - utils/crypto_utils.py (加密相关)
  - utils/time_utils.py (时间相关)
  - utils/network_utils.py (网络相关)
\`\`\`

### 第二步：迁移业务相关工具
\`\`\`
utils/cr_utils.py → core/utils/cr_utils.py
utils/chunk_utils.py → core/utils/chunk_utils.py
utils/cr_prompt_builder.py → core/prompts/prompt_builder.py
utils/cr_rule_config.py → config/cr_rule_config.py
\`\`\`

### 第三步：修复命名错误
\`\`\`
utils/crypt_uitls.py → utils/crypto_utils.py
utils/citadel_uitls.py → utils/citadel_utils.py
\`\`\`

### 第四步：重新组织工具类别
\`\`\`
utils/
├── __init__.py (只保留导入)
├── validation_utils.py (验证工具)
├── performance_utils.py (性能监控工具)
├── decorators.py (装饰器工具)
├── file_utils.py (文件处理工具)
├── crypto_utils.py (加密工具)
├── citadel_utils.py (Citadel集成工具)
├── api_utils.py (API工具)
├── web_utils.py (Web工具)
├── sso_utils.py (SSO工具)
├── log_utils.py (日志工具)
├── thread_pool_manager.py (线程池管理)
└── report2raptor_utils.py (报告工具)
\`\`\`