# 第三阶段：知识库建设优化

## 概述

第三阶段专注于知识库的建设和优化，为Agent系统提供高质量的知识支持。本阶段将实现统一的知识结构、高效的检索机制和持续的知识更新流程。

## 知识库架构

### 1. 统一知识结构

所有知识类型采用统一的结构，便于存储和检索：

```python
# core/knowledge/unified_chunk.py
class UnifiedChunk(BaseModel):
    """统一知识块结构"""
    
    id: str
    content: str
    type: str  # CODE, DEV_SPEC, BEST_PRACTICE, ANTI_PATTERN, BUSINESS_RULE, SECURITY_RULE
    metadata: Dict[str, Any]
    embedding: Optional[List[float]] = None
    tags: List[str] = []
    source: Optional[str] = None
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    
    class Config:
        schema_extra = {
            "example": {
                "id": "chunk_123",
                "content": "def validate_input(data):\n    if not isinstance(data, dict):\n        raise ValueError('Input must be a dictionary')",
                "type": "BEST_PRACTICE",
                "metadata": {
                    "language": "python",
                    "topic": "input_validation",
                    "complexity": 0.3
                },
                "tags": ["validation", "security", "python"],
                "source": "company_guidelines"
            }
        }
```

### 2. 知识库管理器

实现知识库的增删改查和检索功能：

```python
# core/knowledge/knowledge_manager.py
class KnowledgeManager:
    """知识库管理器"""
    
    def __init__(self, vector_store, config=None):
        self.vector_store = vector_store
        self.config = config or {}
        self.embedding_model = self._init_embedding_model()
    
    def _init_embedding_model(self):
        """初始化嵌入模型"""
        # 根据配置初始化嵌入模型
        pass
    
    async def add_chunk(self, chunk: UnifiedChunk) -> str:
        """添加知识块"""
        # 生成嵌入向量
        if not chunk.embedding:
            chunk.embedding = await self._generate_embedding(chunk.content)
        
        # 存储到向量库
        chunk_id = await self.vector_store.add(
            chunk.dict(exclude={"embedding"}),
            chunk.embedding
        )
        
        return chunk_id
    
    async def get_chunk(self, chunk_id: str) -> Optional[UnifiedChunk]:
        """获取知识块"""
        chunk_data = await self.vector_store.get(chunk_id)
        if not chunk_data:
            return None
        
        return UnifiedChunk(**chunk_data)
    
    async def update_chunk(self, chunk: UnifiedChunk) -> bool:
        """更新知识块"""
        # 更新嵌入向量
        if
        # 更新嵌入向量
        if not chunk.embedding:
            chunk.embedding = await self._generate_embedding(chunk.content)
        
        # 更新时间戳
        chunk.updated_at = datetime.now()
        
        # 更新向量库
        success = await self.vector_store.update(
            chunk.id,
            chunk.dict(exclude={"embedding"}),
            chunk.embedding
        )
        
        return success
    
    async def delete_chunk(self, chunk_id: str) -> bool:
        """删除知识块"""
        return await self.vector_store.delete(chunk_id)
    
    async def search(self, query: str, limit: int = 10, filters: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """搜索知识块"""
        # 生成查询向量
        query_embedding = await self._generate_embedding(query)
        
        # 执行向量搜索
        results = await self.vector_store.search(
            query_embedding,
            limit=limit,
            filters=filters
        )
        
        return results
    
    async def _generate_embedding(self, text: str) -> List[float]:
        """生成文本嵌入向量"""
        # 使用嵌入模型生成向量
        # 实际实现取决于所选模型
        pass
```

### 3. 向量存储接口

定义统一的向量存储接口，支持多种后端：

```python
# core/knowledge/vector_store/base.py
class BaseVectorStore(ABC):
    """向量存储基类"""
    
    @abstractmethod
    async def add(self, data: Dict[str, Any], embedding: List[float]) -> str:
        """添加数据和向量"""
        pass
    
    @abstractmethod
    async def get(self, id: str) -> Optional[Dict[str, Any]]:
        """获取数据"""
        pass
    
    @abstractmethod
    async def update(self, id: str, data: Dict[str, Any], embedding: List[float]) -> bool:
        """更新数据和向量"""
        pass
    
    @abstractmethod
    async def delete(self, id: str) -> bool:
        """删除数据"""
        pass
    
    @abstractmethod
    async def search(self, query_embedding: List[float], limit: int = 10, filters: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """搜索相似向量"""
        pass
```

### 4. 知识提取器

实现从不同源提取知识的组件：

```python
# core/knowledge/extractors/base_extractor.py
class BaseKnowledgeExtractor(ABC):
    """知识提取器基类"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.supported_types: List[str] = []
    
    @abstractmethod
    async def extract(self, source_data: Dict[str, Any]) -> List[UnifiedChunk]:
        """提取知识块"""
        pass
    
    @abstractmethod
    def can_handle(self, source_type: str) -> bool:
        """判断是否能处理指定类型的数据源"""
        pass
```

## 知识库实现

### 1. Faiss向量存储实现

```python
# core/knowledge/vector_store/faiss_store.py
class FaissVectorStore(BaseVectorStore):
    """基于Faiss的向量存储"""
    
    def __init__(self, dimension: int, index_type: str = "flat"):
        self.dimension = dimension
        self.index_type = index_type
        self.index = self._create_index()
        self.data_store = {}  # 简单内存存储，实际应使用数据库
        self.id_map = {}  # ID到索引的映射
        self.next_id = 0
    
    def _create_index(self):
        """创建Faiss索引"""
        if self.index_type == "flat":
            return faiss.IndexFlatIP(self.dimension)  # 内积相似度
        elif self.index_type == "ivf":
            quantizer = faiss.IndexFlatIP(self.dimension)
            return faiss.IndexIVFFlat(quantizer, self.dimension, 100)
        elif self.index_type == "hnsw":
            return faiss.IndexHNSWFlat(self.dimension, 32)
        else:
            raise ValueError(f"Unsupported index type: {self.index_type}")
    
    async def add(self, data: Dict[str, Any], embedding: List[float]) -> str:
        """添加数据和向量"""
        # 生成ID
        chunk_id = str(uuid.uuid4())
        
        # 添加到索引
        embedding_array = np.array([embedding]).astype(np.float32)
        self.index.add(embedding_array)
        
        # 保存映射
        self.id_map[chunk_id] = self.next_id
        self.next_id += 1
        
        # 保存数据
        self.data_store[chunk_id] = data
        
        return chunk_id
    
    async def get(self, id: str) -> Optional[Dict[str, Any]]:
        """获取数据"""
        return self.data_store.get(id)
    
    async def update(self, id: str, data: Dict[str, Any], embedding: List[float]) -> bool:
        """更新数据和向量"""
        if id not in self.id_map:
            return False
        
        # 更新数据
        self.data_store[id] = data
        
        # 更新向量（Faiss不支持直接更新，需要重建索引）
        # 实际应用中应使用支持更新的向量数据库
        
        return True
    
    async def delete(self, id: str) -> bool:
        """删除数据"""
        if id not in self.id_map:
            return False
        
        # 删除数据
        del self.data_store[id]
        
        # 删除映射
        del self.id_map[id]
        
        # Faiss不支持直接删除，需要重建索引
        # 实际应用中应使用支持删除的向量数据库
        
        return True
    
    async def search(self, query_embedding: List[float], limit: int = 10, filters: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """搜索相似向量"""
        # 准备查询向量
        query_array = np.array([query_embedding]).astype(np.float32)
        
        # 执行搜索
        scores, indices = self.index.search(query_array, limit)
        
        # 收集结果
        results = []
        for score, idx in zip(scores[0], indices[0]):
            # 查找ID
            chunk_id = None
            for id, index in self.id_map.items():
                if index == idx:
                    chunk_id = id
                    break
            
            if chunk_id and chunk_id in self.data_store:
                data = self.data_store[chunk_id]
                
                # 应用过滤器
                if filters and not self._apply_filters(data, filters):
                    continue
                
                results.append({
                    "id": chunk_id,
                    "score": float(score),
                    "data": data
                })
        
        return results
    
    def _apply_filters(self, data: Dict[str, Any], filters: Dict[str, Any]) -> bool:
        """应用过滤器"""
        for key, value in filters.items():
            if key not in data:
                return False
            
            if isinstance(value, list):
                if data[key] not in value:
                    return False
            elif data[key] != value:
                return False
        
        return True
```

### 2. 代码知识提取器

```python
# core/knowledge/extractors/code_extractor.py
class CodeKnowledgeExtractor(BaseKnowledgeExtractor):
    """代码知识提取器"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.supported_types = ["python", "java", "javascript", "typescript"]
    
    def can_handle(self, source_type: str) -> bool:
        return source_type in self.supported_types
    
    async def extract(self, source_data: Dict[str, Any]) -> List[UnifiedChunk]:
        """从代码中提取知识"""
        code_content = source_data["content"]
        file_path = source_data["file_path"]
        language = source_data.get("language", "python")
        
        chunks = []
        
        if language == "python":
            chunks.extend(await self._extract_python_knowledge(code_content, file_path))
        elif language == "java":
            chunks.extend(await self._extract_java_knowledge(code_content, file_path))
        elif language in ["javascript", "typescript"]:
            chunks.extend(await self._extract_js_knowledge(code_content, file_path))
        
        return chunks
    
    async def _extract_python_knowledge(self, code: str, file_path: str) -> List[UnifiedChunk]:
        """提取Python代码知识"""
        chunks = []
        
        try:
            import ast
            tree = ast.parse(code)
            
            # 提取函数知识
            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    chunk = await self._create_function_chunk(node, code, file_path)
                    if chunk:
                        chunks.append(chunk)
                elif isinstance(node, ast.ClassDef):
                    chunk = await self._create_class_chunk(node, code, file_path)
                    if chunk:
                        chunks.append(chunk)
        
        except Exception as e:
            print(f"Python代码解析失败: {str(e)}")
        
        return chunks
    
    async def _create_function_chunk(self, node, code, file_path) -> Optional[UnifiedChunk]:
        """创建函数知识块"""
        # 获取函数源码
        source_lines = code.splitlines()
        start_line = node.lineno - 1
        end_line = node.end_lineno if hasattr(node, 'end_lineno') else start_line
        function_code = "\n".join(source_lines[start_line:end_line])
        
        # 提取函数文档字符串
        docstring = ast.get_docstring(node) or ""
        
        # 提取参数信息
        params = []
        for arg in node.args.args:
            param_name = arg.arg
            param_type = ""
            if arg.annotation:
                param_type = code[arg.annotation.col_offset:arg.annotation.end_col_offset]
            params.append({"name": param_name, "type": param_type})
        
        # 创建知识块
        chunk = UnifiedChunk(
            id=f"{file_path}:function:{node.name}",
            content=function_code,
            type="CODE",
            metadata={
                "language": "python",
                "file_path": file_path,
                "function_name": node.name,
                "docstring": docstring,
                "params": params,
                "line_start": start_line + 1,
                "line_end": end_line + 1
            },
            tags=["python", "function", node.name],
            source=file_path
        )
        
        return chunk
```

### 3. 文档知识提取器

```python
# core/knowledge/extractors/document_extractor.py
class DocumentKnowledgeExtractor(BaseKnowledgeExtractor):
    """文档知识提取器"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.supported_types = ["markdown", "rst", "txt", "docx"]
    
    def can_handle(self, source_type: str) -> bool:
        return source_type in self.supported_types
    
    async def extract(self, source_data: Dict[str, Any]) -> List[UnifiedChunk]:
        """从文档中提取知识"""
        content = source_data["content"]
        doc_type = source_data.get("type", "markdown")
        doc_path = source_data.get("path", "")
        
        chunks = []
        
        if doc_type == "markdown":
            chunks.extend(await self._extract_markdown_knowledge(content, doc_path))
        elif doc_type in ["txt", "rst"]:
            chunks.extend(await self._extract_text_knowledge(content, doc_path))
        
        return chunks
    
    async def _extract_markdown_knowledge(self, content: str, doc_path: str) -> List[UnifiedChunk]:
        """提取Markdown文档知识"""
        chunks = []
        
        # 按标题分段
        sections = self._split_by_headers(content)
        
        for i, section in enumerate(sections):
            if len(section["content"].strip()) < 50:  # 跳过太短的段落
                continue
            
            chunk = UnifiedChunk(
                id=f"{doc_path}:section:{i}",
                content=section["content"],
                type="DEV_SPEC" if "规范" in section["title"] else "BEST_PRACTICE",
                metadata={
                    "document_id": doc_path,
                    "section_title": section["title"],
                    "section_level": section["level"],
                    "doc_type": "markdown"
                },
                tags=self._extract_keywords(section["content"]),
                source=doc_path
            )
            chunks.append(chunk)
        
        return chunks
    
    def _split_by_headers(self, content: str) -> List[Dict[str, Any]]:
        """按标题分割文档"""
        import re
        
        # 匹配Markdown标题
        header_pattern = r'^(#{1,6})\s+(.+)$'
        
        lines = content.splitlines()
        sections = []
        current_section = {"level": 0, "title": "", "content": ""}
        
        for line in lines:
            match = re.match(header_pattern, line)
            if match:
                # 保存当前段落
                if current_section["content"]:
                    sections.append(current_section)
                
                # 创建新段落
                level = len(match.group(1))
                title = match.group(2)
                current_section = {"level": level, "title": title, "content": line + "\n"}
            else:
                current_section["content"] += line + "\n"
        
        # 保存最后一个段落
        if current_section["content"]:
            sections.append(current_section)
        
        return sections
    
    def _extract_keywords(self, content: str) -> List[str]:
        """提取关键词"""
        # 简单实现，实际可能需要更复杂的算法
        import re
        
        # 移除代码块
        content = re.sub(r'```.*?```', '', content, flags=re.DOTALL)
        
        # 移除Markdown标记
        content = re.sub(r'[#*_`]', '', content)
        
        # 分词并计数
        words = re.findall(r'\b\w+\b', content.lower())
        word_count = {}
        for word in words:
            if len(word) > 3:  # 忽略太短的词
                word_count[word] = word_count.get(word, 0) + 1
        
        # 按频率排序
        sorted_words = sorted(word_count.items(), key=lambda x: x[1], reverse=True)
        
        # 返回前10个关键词
        return [word for word, count in sorted_words[:10]]
```

## 知识库管理流程

### 1. 知识采集流程

```python
# core/knowledge/collection/knowledge_collector.py
class KnowledgeCollector:
    """知识采集器"""
    
    def __init__(self, extractors: List[BaseKnowledgeExtractor], knowledge_manager: KnowledgeManager):
        self.extractors = extractors
        self.knowledge_manager = knowledge_manager
    
    async def collect_from_repository(self, repo_path: str) -> List[str]:
        """从代码仓库采集知识"""
        chunk_ids = []
        
        # 扫描代码文件
        code_files = self._scan_code_files(repo_path)
        for file_path in code_files:
            file_chunks = await self._extract_from_file(file_path)
            for chunk in file_chunks:
                chunk_id = await self.knowledge_manager.add_chunk(chunk)
                chunk_ids.append(chunk_id)
        
        # 扫描文档文件
        doc_files = self._scan_doc_files(repo_path)
        for file_path in doc_files:
            doc_chunks = await self._extract_from_document(file_path)
            for chunk in doc_chunks:
                chunk_id = await self.knowledge_manager.add_chunk(chunk)
                chunk_ids.append(chunk_id)
        
        return chunk_ids
    
    def _scan_code_files(self, repo_path: str) -> List[str]:
        """扫描代码文件"""
        code_files = []
        
        for root, _, files in os.walk(repo_path):
            for file in files:
                file_path = os.path.join(root, file)
                file_ext = os.path.splitext(file)[1].lower()
                
                # 判断文件类型
                file_type = None
                if file_ext in ['.py']:
                    file_type = "python"
                elif file_ext in ['.java']:
                    file_type = "java"
                elif file_ext in ['.js']:
                    file_type = "javascript"
                elif file_ext in ['.ts']:
                    file_type = "typescript"
                
                if file_type:
                    code_files.append(file_path)
        
        return code_files
    
    async def _extract_from_file(self, file_path: str) -> List[UnifiedChunk]:
        """从文件中提取知识"""
        file_ext = os.path.splitext(file_path)[1].lower()
        
        # 确定文件类型
        file_type = None
        if file_ext in ['.py']:
            file_type = "python"
        elif file_ext in ['.java']:
            file_type = "java"
        elif file_ext in ['.js']:
            file_type = "javascript"
        elif file_ext in ['.ts']:
            file_type = "typescript"
        
        if not file_type:
            return []
        
        # 查找合适的提取器
        extractor = None
        for e in self.extractors:
            if e.can_handle(file_type):
                extractor = e
                break
        
        if not extractor:
            return []
        
        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 提取知识
        source_data = {
            "content": content,
            "file_path": file_path,
            "language": file_type
        }
        
        return await extractor.extract(source_data)
```

### 2. 知识质量评估

```python
# core/knowledge/quality/quality_assessor.py
class KnowledgeQualityAssessor:
    """知识质量评估器"""
    
    async def assess_chunk_quality(self, chunk: UnifiedChunk) -> float:
        """评估知识块质量"""
        score = 0.0
        
        # 内容完整性评估
        content_score = self._assess_content_completeness(chunk)
        score += content_score * 0.3
        
        # 结构化程度评估
        structure_score = self._assess_structure_quality(chunk)
        score += structure_score * 0.2
        
        # 关键词质量评估
        keyword_score = self._assess_keyword_quality(chunk)
        score += keyword_score * 0.2
        
        # 历史效果评估
        effectiveness_score = chunk.metadata.get("effectiveness_score", 0.5)
        score += effectiveness_score * 0.3
        
        return min(score, 1.0)
    
    def _assess_content_completeness(self, chunk: UnifiedChunk) -> float:
        """评估内容完整性"""
        content = chunk.content
        
        # 长度评估
        length_score = min(len(content) / 500, 1.0)
        
        # 结构评估（是否有标题、段落等）
        structure_indicators = [
            "##" in content,  # 标题
            "\n\n" in content,  # 段落分隔
            "```" in content,  # 代码块
            len(content.split('\n')) > 3  # 多行内容
        ]
        structure_score = sum(structure_indicators) / len(structure_indicators)
        
        return (length_score + structure_score) / 2
```

## 知识库集成

### 1. 与Agent系统集成

```python
# core/agents/knowledge_enhanced_agent.py
class KnowledgeEnhancedAgent(BaseAgent):
    """知识增强Agent"""
    
    def __init__(self, config=None, knowledge_manager=None):
        super().__init__(config)
        self.knowledge_manager = knowledge_manager
    
    async def enhance_with_knowledge(self, query: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """使用知识增强上下文"""
        # 构建过滤器
        filters = {}
        if "language" in context:
            filters["language"] = context["language"]
        
        # 搜索相关知识
        knowledge_results = await self.knowledge_manager.search(
            query=query,
            limit=5,
            filters=filters
        )
        
        # 提取知识内容
        knowledge_content = []
        for result in knowledge_results:
            knowledge_content.append({
                "content": result["data"]["content"],
                "type": result["data"]["type"],
                "score": result["score"],
                "metadata": result["data"].get("metadata", {})
            })
        
        # 更新使用计数
        for result in knowledge_results:
            chunk_id = result["id"]
            chunk = await self.knowledge_manager.get_chunk(chunk_id)
            if chunk:
                if "usage_count" not in chunk.metadata:
                    chunk.metadata["usage_count"] = 0
                chunk.metadata["usage_count"] += 1
                await self.knowledge_manager.update_chunk(chunk)
        
        return {
            "knowledge": knowledge_content,
            "count": len(knowledge_content)
        }
```

### 2. 知识反馈机制

```python
# core/knowledge/feedback/feedback_processor.py
class KnowledgeFeedbackProcessor:
    """知识反馈处理器"""
    
    def __init__(self, knowledge_manager: KnowledgeManager):
        self.knowledge_manager = knowledge_manager
    
    async def process_feedback(self, feedback: Dict[str, Any]) -> bool:
        """处理知识反馈"""
        chunk_id = feedback.get("chunk_id")
        if not chunk_id:
            return False
        
        # 获取知识块
        chunk = await self.knowledge_manager.get_chunk(chunk_id)
        if not chunk:
            return False
        
        # 更新效果评分
        effectiveness = feedback.get("effectiveness", 0)
        if "effectiveness_scores" not in chunk.metadata:
            chunk.metadata["effectiveness_scores"] = []
        
        chunk.metadata["effectiveness_scores"].append(effectiveness)
        
        # 计算平均效果评分
        scores = chunk.metadata["effectiveness_scores"]
        chunk.metadata["effectiveness_score"] = sum(scores) / len(scores)
        
        # 处理反馈评论
        comment = feedback.get("comment", "")
        if comment:
            if "feedback_comments" not in chunk.metadata:
                chunk.metadata["feedback_comments"] = []
            chunk.metadata["feedback_comments"].append({
                "comment": comment,
                "timestamp": datetime.now().isoformat()
            })
        
        # 更新知识块
        success = await self.knowledge_manager.update_chunk(chunk)
        
        return success
```

## 实施计划

### 第1周：知识提取和存储

**任务清单：**
- [x] 实现统一知识块结构
- [x] 实现向量存储接口和Faiss实现
- [x] 实现代码知识提取器
- [x] 实现文档知识提取器

**验收标准：**
1. 能够从代码和文档中提取结构化知识
2. 向量化引擎正常工作，支持相似度搜索
3. 知识块能够正确存储和检索

### 第2周：知识检索和优化

**任务清单：**
- [x] 实现知识管理器
- [x] 实现知识质量评估系统
- [x] 实现知识反馈机制
- [x] 与Agent系统集成

**验收标准：**
1. 检索引擎能够返回高质量的相关知识
2. 知识质量评估准确有效
3. Agent系统能够正确使用知识库

## 预期效果

1. **知识覆盖率**：覆盖90%以上的常见代码审查场景
2. **检索准确率**：相关知识检索准确率达到85%以上
3. **响应速度**：知识检索平均响应时间<200ms
4. **知识质量**：自动评估的知识质量分数>0.8

## 风险控制

1. **数据质量风险**：建立多层质量评估机制
2. **性能风险**：使用缓存和索引优化
3. **存储风险**：实现数据备份和恢复机制
4. **兼容性风险**：保持向后兼容的API接口