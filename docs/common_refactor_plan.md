# Common目录重构计划

## 当前问题分析

### 1. 职责混乱
- `cr_chain.py` (22KB) - 包含链式处理逻辑，应该移到 `core/chains/`
- `code_parsers.py` (47KB) - 代码解析器，应该移到 `core/parsers/`
- `dependency_analyzer.py` (12KB) - 依赖分析器，应该移到 `core/analyzers/`
- `intelligent_cr_chain.py` (26KB) - 智能链，应该移到 `core/chains/`

### 2. 文件过大
- `code_parsers.py` 有47KB，违反单一职责原则
- `cr_chain.py` 有22KB，需要拆分

### 3. 配置分散
- `settings.py` 与新的 `config/base_config.py` 重复
- 配置管理不统一

## 重构方案

### 第一步：迁移链式处理组件到core/chains/
\`\`\`
common/cr_chain.py → core/chains/cr_chain.py
common/fast_cr_chain.py → core/chains/fast_cr_chain.py
common/async_fast_cr_chain.py → core/chains/async_fast_cr_chain.py
common/intelligent_cr_chain.py → core/chains/intelligent_cr_chain.py
common/optimized_cr_chain.py → core/chains/optimized_cr_chain.py
\`\`\`

### 第二步：迁移分析器组件到core/analyzers/
\`\`\`
common/dependency_analyzer.py → core/analyzers/dependency_analyzer.py
common/cross_module_analyzer.py → core/analyzers/cross_module_analyzer.py
common/code_position_analyzer.py → core/analyzers/code_position_analyzer.py
\`\`\`

### 第三步：重构代码解析器
\`\`\`
common/code_parsers.py → 拆分为：
  - core/parsers/base_parser.py
  - core/parsers/python_parser.py
  - core/parsers/java_parser.py
  - core/parsers/javascript_parser.py
  - core/parsers/parser_factory.py
\`\`\`

### 第四步：整合配置管理
- 废弃 `common/settings.py`
- 统一使用 `config/base_config.py`
- 迁移所有配置到新系统

### 第五步：重构缓存和工具
\`\`\`
common/chunk_cache.py → core/cache/chunk_cache.py
common/cr_types.py → core/types/cr_types.py
\`\`\`