# Core目录扩展计划

## 当前状态
Core目录已有基础设施：
- `logging.py` - 日志配置
- `database.py` - 数据库连接
- `file_utils.py` - 文件工具
- `service_registry.py` - 服务注册

## 需要添加的核心组件

### 1. Agent系统 (按架构设计)
\`\`\`
core/agents/
├── __init__.py
├── base_agent.py (Agent基类)
├── chunking_agent.py (代码分块Agent)
├── qa_agent.py (问答生成Agent)
├── rule_matching_agent.py (规则匹配Agent)
├── llm_review_agent.py (LLM审查Agent)
├── evaluation_agent.py (评估Agent)
└── feedback_agent.py (反馈Agent)
\`\`\`

### 2. 链式处理系统
\`\`\`
core/chains/
├── __init__.py
├── base_chain.py (链基类)
├── cr_chain.py (从common迁移)
├── fast_cr_chain.py (从common迁移)
├── async_fast_cr_chain.py (从common迁移)
├── intelligent_chain.py (从common迁移)
└── evaluation_chain.py (评估链)
\`\`\`

### 3. 编排器系统
\`\`\`
core/orchestrator/
├── __init__.py
├── agent_orchestrator.py (Agent编排器)
├── workflow_manager.py (工作流管理器)
└── cr_orchestrator.py (CR编排器)
\`\`\`

### 4. 分析器系统
\`\`\`
core/analyzers/
├── __init__.py
├── dependency_analyzer.py (从common迁移)
├── cross_module_analyzer.py (从common迁移)
├── code_position_analyzer.py (从common迁移)
└── performance_analyzer.py (性能分析器)
\`\`\`

### 5. 解析器系统
\`\`\`
core/parsers/
├── __init__.py
├── base_parser.py (解析器基类)
├── python_parser.py (Python解析器)
├── java_parser.py (Java解析器)
├── javascript_parser.py (JavaScript解析器)
└── parser_factory.py (解析器工厂)
\`\`\`

### 6. 提示词管理
\`\`\`
core/prompts/
├── __init__.py
├── prompt_builder.py (从utils迁移)
├── cr_prompts.py (CR提示词模板)
└── prompt_templates/ (提示词模板目录)
\`\`\`

### 7. 缓存系统
\`\`\`
core/cache/
├── __init__.py
├── chunk_cache.py (从common迁移)
├── memory_cache.py (内存缓存)
└── redis_cache.py (Redis缓存)
\`\`\`

### 8. 类型定义
\`\`\`
core/types/
├── __init__.py
├── cr_types.py (从common迁移)
├── agent_types.py (Agent类型定义)
└── chain_types.py (链类型定义)
\`\`\`

### 9. 工具集合
\`\`\`
core/utils/
├── __init__.py
├── cr_utils.py (从utils迁移)
├── chunk_utils.py (从utils迁移)
└── validation_utils.py (验证工具)
\`\`\`