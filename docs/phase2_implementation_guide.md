# 第二阶段：精简Agent协作体系实施指南

## 概述

第二阶段是CR系统演进的核心阶段，将从当前的LangChain RAG架构演进为精简的"3+1"Agent协作架构。本阶段注重实用性和落地效果，为后续MCP集成做好准备。

## 架构演进路径

### 当前架构 → 目标架构

```mermaid
graph TB
    subgraph "当前架构"
        A1[cr_lc_service.py<br/>920行单体服务] --> B1[LangChain RAG]
        B1 --> C1[LLM调用]
        C1 --> D1[结果汇总]
    end
    
    subgraph "目标架构"
        A2[编排Agent] --> B2[代码分析Agent]
        A2 --> C2[规则检查Agent]
        A2 --> D2[CR Agent]
        
        B2 --> E2[代码分块]
        B2 --> F2[复杂度分析]
        
        C2 --> G2[规则匹配]
        C2 --> H2[知识检索]
        
        D2 --> I2[深度审查]
        D2 --> J2[问题分析]
    end
```

## 实施策略

### 1. 精简Agent设计

基于实际落地考虑，我们采用"3+1"精简Agent架构：

1. **代码分析Agent**：负责代码分块和静态分析
2. **规则检查Agent**：执行规则匹配和知识检索
3. **CR Agent**：执行深度代码审查和问题分析
4. **编排Agent**：协调其他Agent工作（可选）

这种设计既保证了系统功能完整性，又避免了过度复杂化，便于实际落地。

### 2. 实用工具集

每个Agent配备实用且易于实现的工具集：

```python
# 代码分析工具示例
class CodeAnalysisTools:
    def chunk_code(self, code, language, max_size=1000):
        """基于语法的代码分块"""
        # 使用现有的语法解析库实现
        pass
    
    def calculate_complexity(self, code, language):
        """计算代码复杂度"""
        # 使用现有的复杂度计算库
        pass
```

### 3. 工作流设计

采用简洁的三步式工作流设计：

```python
class CRWorkflow:
    """代码审查工作流 - 三步式设计"""
    
    async def execute(self, code, context=None):
        """执行审查工作流"""
        # 步骤1: 代码分析
        analysis_result = await self.code_analysis_agent.execute({...})
        
        # 步骤2: 规则检查 (可与步骤1并行)
        rule_result = await self.rule_check_agent.execute({...})
        
        # 步骤3: CR审查
        cr_result = await self.cr_agent.execute({...})
        
        return self._format_result(...)
```

## 迁移策略

### 渐进式迁移方案

1. **第1阶段：并行运行**
   - 保持现有cr_lc_service.py不变
   - 新建Agent系统并行运行
   - 通过配置开关控制流量分配

2. **第2阶段：功能迁移**
   - 逐步将cr_lc_service中的功能迁移到Agent
   - 保持API接口完全兼容
   - 建立A/B测试机制

3. **第3阶段：完全切换**
   - 所有流量切换到Agent系统
   - 废弃旧的cr_lc_service
   - 清理冗余代码

### 兼容性保证

```python
# api/adapters/legacy_adapter.py
class LegacyAdapter:
    """向后兼容适配器"""
    
    def __init__(self, agent_orchestrator):
        self.orchestrator = agent_orchestrator
    
    async def cr_review(self, request_data):
        """兼容原有的cr_review接口"""
        # 1. 请求格式转换
        agent_input = self._convert_legacy_request(request_data)
        
        # 2. 调用新的Agent系统
        result = await self.orchestrator.execute_workflow(agent_input)
        
        # 3. 响应格式转换
        legacy_response = self._convert_to_legacy_response(result)
        
        return legacy_response
```

## MCP兼容设计

为确保未来与MCP协议的兼容性，我们设计了专用的适配层：

```python
class MCPAgentAdapter:
    """将Agent转换为MCP服务的适配器"""
    
    def __init__(self, agent, server_info=None):
        self.agent = agent
        self.server_info = server_info or {
            "name": f"{agent.id}-server",
            "version": "1.0.0"
        }
    
    def create_mcp_server(self, transport_provider):
        """创建MCP服务器"""
        # 创建MCP服务器实例
        server = McpServer.sync(transport_provider)
            .serverInfo(self.server_info["name"], self.server_info["version"])
            .build()
            
        # 将Agent工具转换为MCP工具
        for tool_name, tool_fn in self.agent.get_tools().items():
            server.addTool(self._create_mcp_tool(tool_name, tool_fn))
            
        return server
```

## 核心Agent实现

### 1. 代码分析Agent

```python
# core/agents/code_analysis_agent.py
class CodeAnalysisAgent(BaseAgent):
    """代码分析Agent - 负责代码分块和静态分析"""
    
    def __init__(self, config=None):
        super().__init__(config)
        self.tools = CodeAnalysisTools()
    
    async def execute(self, input_data):
        """执行代码分析"""
        # 验证输入
        if not self.validate_input(input_data):
            return self._error_response("Invalid input data")
        
        code = input_data.get("code", "")
        language = input_data.get("language", "python")
        mode = input_data.get("mode", "standard")
        
        try:
            # 1. 代码分块
            chunks = self.tools.chunk_code(code, language)
            
            # 2. 计算复杂度
            complexity = self.tools.calculate_complexity(code, language)
            
            # 3. 提取元数据
            metadata = self.tools.extract_metadata(code, language)
            
            return {
                "success": True,
                "chunks": chunks,
                "complexity": complexity,
                "metadata": metadata,
                "metrics": {
                    "chunk_count": len(chunks),
                    "avg_chunk_size": sum(len(c["content"]) for c in chunks) / len(chunks) if chunks else 0,
                    "complexity_score": complexity.get("overall_score", 0)
                }
            }
        except Exception as e:
            return self._error_response(f"Analysis failed: {str(e)}")
```

### 2. 规则检查Agent

```python
# core/agents/rule_check_agent.py
class RuleCheckAgent(BaseAgent):
    """规则检查Agent - 执行规则匹配和知识检索"""
    
    def __init__(self, config=None):
        super().__init__(config)
        self.tools = RuleCheckTools()
    
    async def execute(self, input_data):
        """执行规则检查"""
        # 验证输入
        if not self.validate_input(input_data):
            return self._error_response("Invalid input data")
        
        code = input_data.get("code", "")
        chunks = input_data.get("chunks", [])
        language = input_data.get("language", "python")
        business = input_data.get("business", "general")
        
        try:
            # 1. 规则匹配
            matched_rules = []
            for chunk in chunks:
                chunk_rules = self.tools.match_rules(
                    chunk["content"], language, business
                )
                for rule in chunk_rules:
                    rule["chunk_id"] = chunk["id"]
                    matched_rules.append(rule)
            
            # 2. 知识检索
            # 从代码中提取关键词
            keywords = self._extract_keywords(code, chunks)
            
            # 检索相关知识
            relevant_knowledge = []
            for keyword in keywords[:5]:  # 限制关键词数量
                knowledge = await self.tools.retrieve_knowledge(keyword, limit=3)
                relevant_knowledge.extend(knowledge)
            
            # 3. 优先级排序
            prioritized_rules = self.tools.prioritize_rules(matched_rules)
            
            return {
                "success": True,
                "matched_rules": prioritized_rules,
                "relevant_knowledge": relevant_knowledge,
                "metrics": {
                    "rule_count": len(matched_rules),
                    "knowledge_count": len(relevant_knowledge),
                    "high_priority_rules": sum(1 for r in prioritized_rules if r.get("priority") == "high")
                }
            }
        except Exception as e:
            return self._error_response(f"Rule check failed: {str(e)}")
    
    def _extract_keywords(self, code, chunks):
        """从代码中提取关键词"""
        # 简单实现，实际可能需要更复杂的算法
        keywords = []
        # 提取关键词逻辑
        return keywords
```

### 3. CR Agent

```python
# core/agents/cr_agent.py
class CRAgent(BaseAgent):
    """CR Agent - 执行深度代码审查和问题分析"""
    
    def __init__(self, config=None):
        super().__init__(config)
        self.tools = CRTools()
    
    async def execute(self, input_data):
        """执行代码审查"""
        # 验证输入
        if not self.validate_input(input_data):
            return self._error_response("Invalid input data")
        
        code = input_data.get("code", "")
        chunks = input_data.get("chunks", [])
        rules = input_data.get("rules", [])
        knowledge = input_data.get("knowledge", [])
        mode = input_data.get("mode", "standard")
        
        try:
            # 1. 深度代码审查
            review_results = []
            for chunk in chunks:
                # 准备上下文
                context = {
                    "chunk_id": chunk["id"],
                    "related_rules": [r for r in rules if r.get("chunk_id") == chunk["id"]],
                    "mode": mode
                }
                
                # 执行审查
                result = await self.tools.review_code(
                    chunk["content"], context, rules, knowledge
                )
                review_results.append(result)
            
            # 2. 问题分类
            classified_issues = self.tools.classify_issues(
                [issue for result in review_results for issue in result.get("issues", [])]
            )
            
            # 3. 生成建议
            suggestions = self.tools.generate_suggestions(classified_issues)
            
            return {
                "success": True,
                "review_results": review_results,
                "issues": classified_issues,
                "suggestions": suggestions,
                "metrics": {
                    "total_issues": len(classified_issues),
                    "p0_issues": sum(1 for i in classified_issues if i.get("severity") == "P0"),
                    "p1_issues": sum(1 for i in classified_issues if i.get("severity") == "P1"),
                    "p2_issues": sum(1 for i in classified_issues if i.get("severity") == "P2")
                }
            }
        except Exception as e:
            return self._error_response(f"Code review failed: {str(e)}")
```

## 测试策略

### 1. 单元测试

```python
# tests/agents/test_code_analysis_agent.py
class TestCodeAnalysisAgent:
    async def test_python_code_analysis(self):
        agent = CodeAnalysisAgent()
        result = await agent.execute({
            "code": "def hello():\n    print('Hello, world!')",
            "language": "python"
        })
        
        assert result["success"] is True
        assert len(result["chunks"]) > 0
        assert "complexity" in result
```

### 2. 集成测试

```python
# tests/integration/test_agent_workflow.py
class TestAgentWorkflow:
    async def test_standard_workflow(self):
        workflow = CRWorkflow(mode="standard")
        result = await workflow.execute(
            code="def hello():\n    print('Hello, world!')",
            context={"language": "python"}
        )
        
        assert result["success"] is True
        assert "analysis" in result
        assert "rules" in result
        assert "cr" in result
```

### 3. 性能测试

```python
# tests/performance/test_agent_performance.py
class TestAgentPerformance:
    async def test_performance_comparison(self):
        # 准备测试数据
        test_code = load_test_file("large_python_file.py")
        
        # 测试旧系统
        start_time = time.time()
        old_result = await legacy_cr_service.review(test_code)
        old_time = time.time() - start_time
        
        # 测试新系统
        start_time = time.time()
        workflow = CRWorkflow(mode="standard")
        new_result = await workflow.execute(test_code)
        new_time = time.time() - start_time
        
        # 比较性能
        assert new_time <= old_time * 1.1  # 允许10%的性能波动
```

## 监控和可观测性

### 1. Agent性能指标

```python
# core/monitoring/agent_metrics.py
class AgentMetricsCollector:
    """收集Agent性能指标"""
    
    def __init__(self):
        self.metrics = {}
    
    def record_execution(self, agent_id, start_time, end_time, success, data):
        """记录执行指标"""
        execution_time = end_time - start_time
        
        if agent_id not in self.metrics:
            self.metrics[agent_id] = {
                "total_executions": 0,
                "successful_executions": 0,
                "failed_executions": 0,
                "total_time": 0,
                "avg_time": 0,
                "min_time": float('inf'),
                "max_time": 0
            }
        
        m = self.metrics[agent_id]
        m["total_executions"] += 1
        if success:
            m["successful_executions"] += 1
        else:
            m["failed_executions"] += 1
        
        m["total_time"] += execution_time
        m["avg_time"] = m["total_time"] / m["total_executions"]
        m["min_time"] = min(m["min_time"], execution_time)
        m["max_time"] = max(m["max_time"], execution_time)
```

### 2. 日志记录

```python
# core/monitoring/agent_logger.py
class AgentLogger:
    """Agent日志记录器"""
    
    def __init__(self, log_level="INFO"):
        self.log_level = log_level
        # 初始化日志器
    
    def log_execution_start(self, agent_id, input_data):
        """记录执行开始"""
        # 记录开始日志
    
    def log_execution_end(self, agent_id, result, execution_time):
        """记录执行结束"""
        # 记录结束日志
    
    def log_error(self, agent_id, error, input_data=None):
        """记录错误"""
        # 记录错误日志
```

## 风险控制

### 1. 技术风险

- **Agent故障隔离**：单个Agent故障不影响整体系统
- **降级机制**：Agent不可用时自动降级到简单模式
- **数据一致性**：确保知识库和缓存数据一致性

### 2. 业务风险

- **功能回归**：完整的测试覆盖确保功能不丢失
- **性能回退**：性能基准测试和持续监控
- **用户体验**：保持API兼容性，用户无感知升级

## 成功标准

1. **功能完整性**：所有现有功能正常工作
2. **性能提升**：处理速度提升30%以上
3. **扩展性**：新Agent接入时间<1天
4. **稳定性**：系统可用性>99.5%
5. **代码质量**：单个文件行数<500行，测试覆盖率>80%

## 下一步

第二阶段完成后，将进入第三阶段（知识库建设优化）和第四阶段（性能优化与MCP集成），最终实现完整的企业级智能代码审查平台。