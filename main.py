import asyncio
import logging
import sys
import signal
from typing import Optional

from api.apps import app
from app import initialize_services
from config.base_config import BaseConfig
from core.logging import init_logging
from services.service_manager import ServiceManager

# 全局服务管理器
service_manager: Optional[ServiceManager] = None

def signal_handler(signum, frame):
    """信号处理器"""
    logging.info(f"接收到信号 {signum}，开始优雅关闭...")
    cleanup_and_exit()

def cleanup_and_exit():
    """清理资源并退出"""
    global service_manager
    if service_manager:
        logging.info("开始停止所有服务...")
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(service_manager.stop_all_services())
            logging.info("所有服务已停止")
        except Exception as e:
            logging.error(f"停止服务时出错: {str(e)}")
        finally:
            # 确保所有待处理的任务完成
            pending_tasks = asyncio.all_tasks(loop)
            if pending_tasks:
                logging.info(f"等待 {len(pending_tasks)} 个待处理任务完成...")
                loop.run_until_complete(asyncio.gather(*pending_tasks, return_exceptions=True))
            loop.close()
    sys.exit(0)

def main():
    """主函数"""
    global service_manager

    try:
        # 加载配置
        config = BaseConfig.load_from_env()
        config.debug = True

        # 初始化日志系统
        init_logging(config.monitoring, 'main')
        logging.info("=== 启动应用程序 ===")

        # 注册信号处理器
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

        # 初始化所有服务
        logging.info("开始初始化所有服务...")
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            service_manager = loop.run_until_complete(initialize_services(config))
            logging.info("✅ 所有服务初始化完成")

            # 检查服务状态
            services_status = loop.run_until_complete(service_manager.get_services_status())
            logging.info(f"✅ 服务状态检查完成，共 {len(services_status)} 个服务")

            # 输出服务健康状态
            for service_name, status in services_status.items():
                health = status.get('health', {})
                health_status = health.get('status', 'unknown')
                logging.info(f"  - {service_name}: {health_status}")

        except Exception as e:
            logging.error(f"❌ 服务初始化失败: {str(e)}")
            raise
        finally:
            # 确保所有待处理的任务完成
            pending_tasks = asyncio.all_tasks(loop)
            if pending_tasks:
                logging.info(f"等待 {len(pending_tasks)} 个待处理任务完成...")
                loop.run_until_complete(asyncio.gather(*pending_tasks, return_exceptions=True))
            loop.close()

        # 启动Flask应用
        logging.info("=== 启动Flask应用 ===")
        app.run(host="0.0.0.0", port=9000, debug=config.debug)

    except KeyboardInterrupt:
        logging.info("接收到键盘中断信号")
        cleanup_and_exit()
    except Exception as e:
        logging.error(f"❌ 应用启动失败: {str(e)}")
        cleanup_and_exit()

if __name__ == "__main__":
    main()