#!/usr/bin/env python3
"""
Python代码审查规则系统使用示例

本示例展示如何使用优化后的CR规则系统进行代码审查。
"""

import json
from typing import List, Dict, Any
from utils.cr_rule_config import cr_rule_manager, ProblemLevel, CRRule, CRRuleSet


def demo_basic_usage():
    """演示基本使用方法"""
    print("=" * 60)
    print("1. 基本使用演示")
    print("=" * 60)
    
    # 获取Python默认规则集
    python_rules = cr_rule_manager.get_rule_set("python", "default")
    
    print(f"规则集信息:")
    print(f"  语言: {python_rules.language}")
    print(f"  业务: {python_rules.business}")
    print(f"  版本: {python_rules.version}")
    print(f"  描述: {python_rules.description}")
    print(f"  总规则数: {len(python_rules.rules)}")
    
    # 按级别统计规则
    p0_rules = python_rules.get_rules_by_level(ProblemLevel.P0)
    p1_rules = python_rules.get_rules_by_level(ProblemLevel.P1)
    p2_rules = python_rules.get_rules_by_level(ProblemLevel.P2)
    
    print(f"\n规则级别分布:")
    print(f"  P0级别(严重): {len(p0_rules)}条")
    print(f"  P1级别(重要): {len(p1_rules)}条")
    print(f"  P2级别(一般): {len(p2_rules)}条")


def demo_rule_details():
    """演示规则详情查看"""
    print("\n" + "=" * 60)
    print("2. 规则详情演示")
    print("=" * 60)
    
    python_rules = cr_rule_manager.get_rule_set("python", "default")
    
    # 查看P0级别的安全漏洞规则
    security_rule = python_rules.get_rule_by_id("P0-2")
    if security_rule:
        print(f"规则ID: {security_rule.id}")
        print(f"级别: {security_rule.level.value}")
        print(f"标题: {security_rule.title}")
        print(f"描述: {security_rule.description}")
        print(f"示例:")
        for i, example in enumerate(security_rule.examples, 1):
            print(f"  {i}. {example}")
        print(f"关键词: {', '.join(security_rule.keywords)}")


def demo_code_review_simulation():
    """演示代码审查模拟"""
    print("\n" + "=" * 60)
    print("3. 代码审查模拟演示")
    print("=" * 60)
    
    # 模拟有问题的代码片段
    problematic_code = """
def get_user_data(user_id):
    # P0-2: SQL注入风险
    cursor.execute(f"SELECT * FROM users WHERE id = {user_id}")
    
    # P0-4: 资源泄漏
    file = open('user_data.txt', 'r')
    data = file.read()
    
    # P1-1: PEP8违反 - 函数名应使用下划线
    def getUserName():
        return "admin"
    
    # P1-2: 魔法数字
    if status == 200:
        return data
    
    # P1-3: 异常处理不当
    try:
        result = process_data(data)
    except:
        pass
    
    # P2-1: 命名不清晰
    d = {"name": "user"}
    return d
"""
    
    print("待审查代码:")
    print(problematic_code)
    
    # 模拟审查结果
    review_result = simulate_code_review(problematic_code)
    
    print("\n审查结果:")
    print(json.dumps(review_result, ensure_ascii=False, indent=2))


def simulate_code_review(code: str) -> Dict[str, Any]:
    """模拟代码审查过程"""
    python_rules = cr_rule_manager.get_rule_set("python", "default")
    
    # 模拟发现的问题
    issues = [
        {
            "level": "P0",
            "rule_id": "P0-2",
            "title": "安全漏洞",
            "description": "存在SQL注入风险",
            "risk": "可能导致数据库被恶意访问，造成数据泄露",
            "suggestion": "使用参数化查询替代字符串拼接",
            "code_position": "example.py:3",
            "target_code": "cursor.execute(f\"SELECT * FROM users WHERE id = {user_id}\")",
            "suggested_code": "cursor.execute(\"SELECT * FROM users WHERE id = ?\", (user_id,))"
        },
        {
            "level": "P0",
            "rule_id": "P0-4",
            "title": "资源泄漏",
            "description": "文件未正确关闭",
            "risk": "可能导致文件句柄泄漏，影响系统性能",
            "suggestion": "使用with语句确保文件正确关闭",
            "code_position": "example.py:6-7",
            "target_code": "file = open('user_data.txt', 'r')\ndata = file.read()",
            "suggested_code": "with open('user_data.txt', 'r') as file:\n    data = file.read()"
        },
        {
            "level": "P1",
            "rule_id": "P1-1",
            "title": "PEP8规范违反",
            "description": "函数名使用驼峰命名",
            "risk": "违反Python代码规范，影响代码可读性",
            "suggestion": "使用下划线命名法",
            "code_position": "example.py:10",
            "target_code": "def getUserName():",
            "suggested_code": "def get_user_name():"
        },
        {
            "level": "P1",
            "rule_id": "P1-2",
            "title": "魔法数字和字符串",
            "description": "硬编码的HTTP状态码",
            "risk": "降低代码可维护性",
            "suggestion": "定义常量",
            "code_position": "example.py:14",
            "target_code": "if status == 200:",
            "suggested_code": "HTTP_OK = 200\nif status == HTTP_OK:"
        }
    ]
    
    return {
        "summary": {
            "total_issues": len(issues),
            "p0_count": len([i for i in issues if i["level"] == "P0"]),
            "p1_count": len([i for i in issues if i["level"] == "P1"]),
            "p2_count": len([i for i in issues if i["level"] == "P2"]),
            "overall_quality": "较差"
        },
        "issues": issues
    }


def demo_custom_rules():
    """演示自定义规则集创建"""
    print("\n" + "=" * 60)
    print("4. 自定义规则集演示")
    print("=" * 60)
    
    # 创建基于默认规则的自定义规则集
    custom_rules = cr_rule_manager.create_custom_rule_set(
        language="python",
        business="web_backend",
        base_rule_set="python_default"
    )
    
    # 添加自定义规则
    web_specific_rule = CRRule(
        id="WEB-P1-1",
        level=ProblemLevel.P1,
        title="Web安全最佳实践",
        description="Web后端开发中的安全最佳实践检查",
        examples=[
            "未验证用户输入",
            "缺少CSRF保护",
            "敏感信息记录到日志",
            "未设置安全响应头"
        ],
        keywords=["Web安全", "CSRF", "XSS", "输入验证", "响应头"]
    )
    
    custom_rules.rules.append(web_specific_rule)
    custom_rules.description = "Web后端Python代码审查规范"
    
    # 注册自定义规则集
    cr_rule_manager.register_rule_set(custom_rules)
    
    print(f"自定义规则集创建成功:")
    print(f"  语言: {custom_rules.language}")
    print(f"  业务: {custom_rules.business}")
    print(f"  总规则数: {len(custom_rules.rules)}")
    print(f"  新增规则: {web_specific_rule.id} - {web_specific_rule.title}")


def demo_rule_file_operations():
    """演示规则文件操作"""
    print("\n" + "=" * 60)
    print("5. 规则文件操作演示")
    print("=" * 60)
    
    # 获取自定义规则集
    custom_rules = cr_rule_manager.get_rule_set("python", "web_backend")
    
    if custom_rules:
        # 保存到文件
        file_path = "config/cr_rules/python_web_backend.json"
        try:
            cr_rule_manager.save_rule_set_to_file(custom_rules, file_path)
            print(f"规则集已保存到: {file_path}")
            
            # 从文件加载
            loaded_rules = cr_rule_manager.load_rule_set_from_file(file_path)
            print(f"从文件加载规则集成功:")
            print(f"  规则数量: {len(loaded_rules.rules)}")
            print(f"  版本: {loaded_rules.version}")
            
        except Exception as e:
            print(f"文件操作失败: {e}")
            print("注意: 请确保config/cr_rules/目录存在")


def demo_available_rule_sets():
    """演示查看可用规则集"""
    print("\n" + "=" * 60)
    print("6. 可用规则集列表")
    print("=" * 60)
    
    available_sets = cr_rule_manager.list_available_rule_sets()
    
    print("当前可用的规则集:")
    for rule_set_key in available_sets:
        rule_set = cr_rule_manager.rule_sets[rule_set_key]
        print(f"  {rule_set_key}:")
        print(f"    语言: {rule_set.language}")
        print(f"    业务: {rule_set.business}")
        print(f"    版本: {rule_set.version}")
        print(f"    规则数: {len(rule_set.rules)}")
        print(f"    描述: {rule_set.description}")
        print()


def demo_best_practices():
    """演示最佳实践建议"""
    print("\n" + "=" * 60)
    print("7. 最佳实践建议")
    print("=" * 60)
    
    print("🎯 代码审查最佳实践:")
    print()
    
    print("1. 规则级别使用建议:")
    print("   • P0级别: 设为构建失败条件，必须修复")
    print("   • P1级别: 强烈建议修复，可设为警告")
    print("   • P2级别: 优化建议，可在代码审查时讨论")
    print()
    
    print("2. 团队协作建议:")
    print("   • 定期review和更新规则集")
    print("   • 建立规则例外处理机制")
    print("   • 结合静态分析工具使用")
    print("   • 关注新增和修改的代码")
    print()
    
    print("3. CI/CD集成建议:")
    print("   • 在pre-commit阶段进行P0级别检查")
    print("   • 在PR阶段生成完整审查报告")
    print("   • 定期生成代码质量趋势报告")
    print("   • 与代码覆盖率工具结合使用")
    print()
    
    print("4. 规则定制建议:")
    print("   • 基于项目特点调整规则权重")
    print("   • 为不同模块设置不同规则集")
    print("   • 考虑遗留代码的特殊处理")
    print("   • 平衡代码质量和开发效率")


def main():
    """主函数 - 运行所有演示"""
    print("Python代码审查规则系统 - 使用示例")
    print("Version 2.0 - 基于PEP规范和最佳实践")
    
    try:
        demo_basic_usage()
        demo_rule_details()
        demo_code_review_simulation()
        demo_custom_rules()
        demo_rule_file_operations()
        demo_available_rule_sets()
        demo_best_practices()
        
        print("\n" + "=" * 60)
        print("✅ 所有演示完成!")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n❌ 演示过程中出现错误: {e}")
        print("请检查依赖和配置是否正确")


if __name__ == "__main__":
    main()