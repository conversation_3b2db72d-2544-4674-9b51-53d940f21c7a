#!/usr/bin/env python3
"""
MCP协议集成示例
演示如何使用MCP Python SDK与MCP Hub平台进行通信

基于您提供的MCP接入代码进行扩展，展示完整的集成流程
"""

import asyncio
import logging
from typing import Dict, Any, List
from mcp import ClientSession
from mcp.client.sse import sse_client

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# MCP Hub平台接入点URL（需要替换为实际URL）
MCP_SSE_URL = "mcphub平台的接入点URL"

class MCPIntegrationExample:
    """MCP集成示例类"""
    
    def __init__(self, sse_url: str = MCP_SSE_URL):
        self.sse_url = sse_url
        self.session = None
        self.sse_ctx = None
        self.session_ctx = None
        self.available_tools = {}
    
    async def connect(self):
        """建立MCP连接"""
        try:
            logger.info("正在连接MCP Hub平台...")
            
            # 建立SSE连接
            self.sse_ctx = sse_client(url=self.sse_url)
            streams = await self.sse_ctx.__aenter__()
            self.session_ctx = ClientSession(*streams)
            self.session = await self.session_ctx.__aenter__()
            
            # 初始化MCP会话
            await self.session.initialize()
            logger.info("MCP会话已初始化成功")
            
            # 获取可用工具
            await self.load_available_tools()
            
            return True
            
        except Exception as e:
            logger.error(f"连接MCP Hub失败: {str(e)}")
            return False
    
    async def disconnect(self):
        """断开MCP连接"""
        try:
            if self.session_ctx:
                await self.session_ctx.__aexit__(None, None, None)
            if self.sse_ctx:
                await self.sse_ctx.__aexit__(None, None, None)
            logger.info("MCP连接已断开")
        except Exception as e:
            logger.error(f"断开MCP连接时出错: {str(e)}")
    
    async def load_available_tools(self):
        """加载可用工具列表"""
        try:
            response = await self.session.list_tools()
            tools = response.tools
            
            self.available_tools = {tool.name: tool for tool in tools}
            
            logger.info(f"发现 {len(self.available_tools)} 个可用的MCP工具:")
            for tool_name, tool in self.available_tools.items():
                logger.info(f"  - {tool_name}: {tool.description}")
                
        except Exception as e:
            logger.error(f"加载MCP工具失败: {str(e)}")
    
    async def call_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """调用MCP工具"""
        try:
            if tool_name not in self.available_tools:
                return {
                    "success": False,
                    "error": f"工具 {tool_name} 不可用"
                }
            
            logger.info(f"正在调用MCP工具: {tool_name}")
            logger.debug(f"调用参数: {arguments}")
            
            # 调用工具
            result = await self.session.call_tool(tool_name, arguments)
            
            logger.info(f"工具 {tool_name} 调用成功")
            return {
                "success": True,
                "data": result,
                "tool_name": tool_name
            }
            
        except Exception as e:
            logger.error(f"调用MCP工具 {tool_name} 失败: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "tool_name": tool_name
            }
    
    async def batch_call_tools(self, calls: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """批量调用MCP工具"""
        results = []
        
        for call in calls:
            tool_name = call.get("tool_name")
            arguments = call.get("arguments", {})
            
            result = await self.call_tool(tool_name, arguments)
            results.append(result)
        
        return results
    
    def get_tool_info(self, tool_name: str) -> Dict[str, Any]:
        """获取工具详细信息"""
        tool = self.available_tools.get(tool_name)
        if tool:
            return {
                "name": tool.name,
                "description": tool.description,
                "input_schema": getattr(tool, 'input_schema', None)
            }
        return {"error": f"工具 {tool_name} 不存在"}

async def demo_code_analysis():
    """演示代码分析功能"""
    mcp_client = MCPIntegrationExample()
    
    try:
        # 连接MCP Hub
        if not await mcp_client.connect():
            logger.error("无法连接到MCP Hub，演示终止")
            return
        
        # 示例：代码分块分析
        code_chunk_result = await mcp_client.call_tool(
            "code_analysis_service",
            {
                "code": """
def calculate_fibonacci(n):
    if n <= 1:
        return n
    return calculate_fibonacci(n-1) + calculate_fibonacci(n-2)

def main():
    result = calculate_fibonacci(10)
    print(f"Fibonacci(10) = {result}")
""",
                "language": "python",
                "analysis_type": "complexity"
            }
        )
        
        logger.info("代码分析结果:")
        logger.info(code_chunk_result)
        
        # 示例：规则检查
        rule_check_result = await mcp_client.call_tool(
            "rule_check_service",
            {
                "code": "def badFunction(): pass",  # 违反命名规范
                "rules": ["pep8_naming", "complexity_check"],
                "language": "python"
            }
        )
        
        logger.info("规则检查结果:")
        logger.info(rule_check_result)
        
        # 示例：批量调用
        batch_calls = [
            {
                "tool_name": "knowledge_service",
                "arguments": {
                    "query": "Python最佳实践",
                    "context": "代码审查"
                }
            },
            {
                "tool_name": "evaluation_service", 
                "arguments": {
                    "code_quality_score": 85,
                    "issues_count": 3,
                    "complexity": "medium"
                }
            }
        ]
        
        batch_results = await mcp_client.batch_call_tools(batch_calls)
        logger.info("批量调用结果:")
        for i, result in enumerate(batch_results):
            logger.info(f"  调用 {i+1}: {result}")
    
    finally:
        # 断开连接
        await mcp_client.disconnect()

async def demo_agent_integration():
    """演示Agent与MCP的集成"""
    mcp_client = MCPIntegrationExample()
    
    try:
        if not await mcp_client.connect():
            return
        
        # 模拟Agent工作流
        logger.info("开始Agent工作流演示...")
        
        # 1. 代码分块
        chunking_result = await mcp_client.call_tool(
            "code_analysis_service",
            {
                "action": "chunk",
                "code": "大段代码内容...",
                "chunk_size": 100
            }
        )
        
        # 2. 知识检索
        if chunking_result["success"]:
            knowledge_result = await mcp_client.call_tool(
                "knowledge_service",
                {
                    "query": "代码审查最佳实践",
                    "chunks": chunking_result["data"]
                }
            )
        
        # 3. LLM审查
        review_result = await mcp_client.call_tool(
            "code_review_service",
            {
                "code": "代码内容",
                "knowledge": knowledge_result["data"] if 'knowledge_result' in locals() else None,
                "review_type": "comprehensive"
            }
        )
        
        logger.info("Agent工作流完成")
        logger.info(f"最终审查结果: {review_result}")
    
    finally:
        await mcp_client.disconnect()

async def main():
    """主函数"""
    logger.info("=== MCP集成示例演示 ===")
    
    # 演示1：基础代码分析
    logger.info("\n1. 代码分析演示")
    await demo_code_analysis()
    
    # 演示2：Agent集成
    logger.info("\n2. Agent集成演示")
    await demo_agent_integration()
    
    logger.info("\n=== 演示完成 ===")

if __name__ == "__main__":
    # 运行演示
    asyncio.run(main())
