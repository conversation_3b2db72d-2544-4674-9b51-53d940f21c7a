"""
增强的CR结果聚合功能演示

展示优化后的结果聚合步骤如何生成完整的UI展示数据
"""

import asyncio
import json
import logging
import sys
import os
from typing import Dict, Any, List
from unittest.mock import Mock

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 导入相关模块
from core.agents.result_aggregation_agent import ResultAggregationAgent


def create_sample_review_results() -> List[Dict[str, Any]]:
    """创建示例审查结果"""
    return [
        {
            'checkBranch': 'feature/user-management',
            'sumCheckResult': '不通过',
            'resultDesc': 'P0:2个,P1:3个,P2:5个',
            'totalProblem': '10',
            'problemList': [
                {
                    'scene': 'commonCheck',
                    'num': '8',
                    'checkResult': '不通过',
                    'detail': [
                        {
                            'level': 'P0',
                            'problem': '空指针异常风险',
                            'suggestion': '在调用user.getName()前添加空值检查',
                            'targetCode': 'String name = user.getName();',
                            'codePosition': [15, 1, 15, 30]
                        },
                        {
                            'level': 'P0',
                            'problem': 'SQL注入漏洞',
                            'suggestion': '使用参数化查询替代字符串拼接',
                            'targetCode': 'SELECT * FROM users WHERE id = " + userId',
                            'codePosition': [25, 1, 25, 45]
                        },
                        {
                            'level': 'P1',
                            'problem': '变量命名不规范',
                            'suggestion': '使用驼峰命名法',
                            'targetCode': 'String user_name = "test";',
                            'codePosition': [8, 1, 8, 25]
                        },
                        {
                            'level': 'P1',
                            'problem': '方法过长',
                            'suggestion': '将方法拆分为多个小方法',
                            'targetCode': 'public void processUser() { ... }',
                            'codePosition': [30, 1, 80, 2]
                        },
                        {
                            'level': 'P1',
                            'problem': '缺少异常处理',
                            'suggestion': '添加try-catch块处理可能的异常',
                            'targetCode': 'File file = new File(path);',
                            'codePosition': [45, 1, 45, 25]
                        }
                    ]
                },
                {
                    'scene': 'customCheck',
                    'num': '2',
                    'checkResult': '通过',
                    'detail': [
                        {
                            'level': 'P2',
                            'problem': '代码注释不足',
                            'suggestion': '为复杂逻辑添加注释',
                            'targetCode': 'if (condition1 && condition2) { ... }',
                            'codePosition': [60, 1, 65, 2]
                        },
                        {
                            'level': 'P2',
                            'problem': '魔法数字',
                            'suggestion': '将数字常量定义为常量',
                            'targetCode': 'if (count > 100) { ... }',
                            'codePosition': [70, 1, 70, 20]
                        }
                    ]
                }
            ]
        },
        {
            'checkBranch': 'feature/user-management',
            'sumCheckResult': '通过',
            'resultDesc': 'P2:3个',
            'totalProblem': '3',
            'problemList': [
                {
                    'scene': 'commonCheck',
                    'num': '3',
                    'checkResult': '通过',
                    'detail': [
                        {
                            'level': 'P2',
                            'problem': '代码重复',
                            'suggestion': '提取公共方法',
                            'targetCode': 'validateUser(user);',
                            'codePosition': [90, 1, 95, 2]
                        },
                        {
                            'level': 'P2',
                            'problem': '性能优化建议',
                            'suggestion': '使用StringBuilder替代字符串拼接',
                            'targetCode': 'String result = str1 + str2 + str3;',
                            'codePosition': [100, 1, 100, 35]
                        },
                        {
                            'level': 'P2',
                            'problem': '代码风格',
                            'suggestion': '统一代码格式',
                            'targetCode': 'if(condition){...}',
                            'codePosition': [110, 1, 110, 15]
                        }
                    ]
                }
            ]
        }
    ]


def create_sample_segments() -> List[Dict[str, Any]]:
    """创建示例代码片段"""
    return [
        {
            'id': 'segment_1',
            'content': '''
public class UserService {
    public void processUser(User user) {
        String name = user.getName();
        // ... more code
    }
}
            '''.strip(),
            'file_path': 'src/main/java/UserService.java',
            'start_line': 1,
            'end_line': 30
        },
        {
            'id': 'segment_2',
            'content': '''
public class UserRepository {
    public User findById(String userId) {
        String sql = "SELECT * FROM users WHERE id = " + userId;
        // ... more code
    }
}
            '''.strip(),
            'file_path': 'src/main/java/UserRepository.java',
            'start_line': 1,
            'end_line': 25
        }
    ]


async def demo_enhanced_result_aggregation():
    """演示增强的结果聚合功能"""
    logger.info("=== 增强的CR结果聚合功能演示 ===")
    
    # 创建模拟LLM服务
    mock_llm_service = Mock()
    mock_llm_service.get_llm.return_value = Mock()
    
    # 创建结果聚合代理
    aggregation_agent = ResultAggregationAgent(
        llm_service=mock_llm_service,
        config={
            'preserve_details': True,
            'merge_strategy': 'intelligent'
        }
    )
    
    # 准备输入数据
    review_results = create_sample_review_results()
    segments = create_sample_segments()
    
    input_data = {
        'results': review_results,
        'options': {
            'cr_mode': 'standard',
            'enable_parallel_processing': True
        },
        'segments': segments,
        'metadata': {
            'cr_mode': 'standard',
            'parallel_processing': True,
            'segments_count': len(segments),
            'review_results_count': len(review_results)
        }
    }
    
    logger.info(f"输入数据: {len(review_results)} 个审查结果, {len(segments)} 个代码片段")
    
    # 执行结果聚合和增强
    try:
        result = await aggregation_agent.execute(input_data)
        
        if result.success:
            logger.info("✅ 结果聚合和增强成功")
            
            # 展示增强后的结果结构
            enhanced_data = result.data
            
            print("\n" + "="*60)
            print("📊 增强后的CR结果结构")
            print("="*60)
            
            # 1. 总结信息
            summary = enhanced_data.get('summary', {})
            print(f"\n📋 总结信息:")
            print(f"  检查分支: {summary.get('checkBranch', 'N/A')}")
            print(f"  审查时间: {summary.get('reviewTime', 'N/A')}")
            print(f"  审查者: {summary.get('reviewer', 'N/A')}")
            print(f"  总体结果: {summary.get('overallResult', 'N/A')}")
            print(f"  问题总数: {summary.get('totalProblems', 0)}")
            print(f"  CR模式: {summary.get('crMode', 'N/A')}")
            
            # 2. 评分信息
            scoring = enhanced_data.get('scoring', {})
            print(f"\n🎯 评分信息:")
            print(f"  总体评分: {scoring.get('overallScore', 0)}/100")
            print(f"  质量等级: {scoring.get('qualityGrade', 'N/A')}")
            print(f"  是否通过: {'✅' if scoring.get('isPassed', False) else '❌'}")
            
            dimensions = scoring.get('dimensions', {})
            if dimensions:
                print(f"  各维度评分:")
                for dim_name, dim_data in dimensions.items():
                    score = dim_data.get('score', 0)
                    max_score = dim_data.get('maxScore', 0)
                    desc = dim_data.get('description', '')
                    print(f"    {dim_name}: {score}/{max_score} - {desc}")
            
            # 3. 统计信息
            statistics = enhanced_data.get('statistics', {})
            print(f"\n📈 统计信息:")
            print(f"  问题总数: {statistics.get('totalProblems', 0)}")
            print(f"  严重问题: {statistics.get('criticalCount', 0)}")
            print(f"  警告问题: {statistics.get('warningCount', 0)}")
            print(f"  中等问题: {statistics.get('moderateCount', 0)}")
            print(f"  轻微问题: {statistics.get('minorCount', 0)}")
            print(f"  代码片段数: {statistics.get('segmentsCount', 0)}")
            
            # 4. 审查指标
            metrics = enhanced_data.get('reviewMetrics', {})
            print(f"\n📊 审查指标:")
            print(f"  质量分数: {metrics.get('qualityScore', 0)}")
            print(f"  风险级别: {metrics.get('riskLevel', 'N/A')}")
            print(f"  问题密度: {metrics.get('problemDensity', 'N/A')}")
            print(f"  覆盖率: {metrics.get('coverageRate', 'N/A')}")
            print(f"  审查效率: {metrics.get('reviewEfficiency', 'N/A')}")
            
            # 5. 改进建议
            recommendations = enhanced_data.get('recommendations', [])
            print(f"\n💡 改进建议:")
            for i, rec in enumerate(recommendations, 1):
                print(f"  {i}. {rec}")
            
            # 6. 问题列表示例
            problems = enhanced_data.get('problems', [])
            print(f"\n🐛 问题列表 (前3个):")
            for i, problem in enumerate(problems[:3], 1):
                level = problem.get('level', 'N/A')
                desc = problem.get('problem', 'N/A')
                suggestion = problem.get('suggestion', 'N/A')
                print(f"  {i}. [{level}] {desc}")
                print(f"     建议: {suggestion}")
            
            if len(problems) > 3:
                print(f"     ... 还有 {len(problems) - 3} 个问题")
            
            # 7. 元数据信息
            metadata = result.metadata
            print(f"\n🔧 处理元数据:")
            print(f"  聚合类型: {metadata.get('aggregation_type', 'N/A')}")
            print(f"  输入数量: {metadata.get('input_count', 0)}")
            print(f"  增强应用: {'✅' if metadata.get('enhancement_applied', False) else '❌'}")
            print(f"  片段数量: {metadata.get('segments_count', 0)}")
            
            print("\n" + "="*60)
            print("✅ 演示完成 - 结果聚合和增强功能正常工作")
            print("="*60)
            
        else:
            logger.error(f"❌ 结果聚合失败: {result.error}")
            
    except Exception as e:
        logger.error(f"❌ 演示过程中发生异常: {str(e)}")


async def main():
    """主函数"""
    await demo_enhanced_result_aggregation()


if __name__ == '__main__':
    asyncio.run(main())
