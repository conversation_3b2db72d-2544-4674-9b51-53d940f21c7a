#!/usr/bin/env python3
"""
配置系统使用示例
演示合并后的配置系统如何使用
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def demo_new_config_system():
    """演示新配置系统"""
    print("=== 新配置系统演示 ===")
    
    try:
        from config.base_config import BaseConfig
        
        # 1. 从环境变量加载配置
        print("\n1. 从环境变量加载配置...")
        config = BaseConfig.load_from_env()
        print(f"✅ 应用名称: {config.app_name}")
        print(f"✅ 数据库类型: {config.database.type}")
        print(f"✅ LLM模型: {config.llm.model_name}")
        print(f"✅ API端口: {config.api.port}")
        
        # 2. 获取特定服务配置
        print("\n2. 获取特定服务配置...")
        db_config = config.get_service_config("database")
        llm_config = config.get_service_config("llm")
        print(f"✅ 数据库配置: {db_config['host']}:{db_config['port']}")
        print(f"✅ LLM配置: {llm_config['provider']}")
        
        # 3. 配置验证
        print("\n3. 配置验证...")
        print(f"✅ 配置类型验证通过")
        print(f"✅ 必填字段验证通过")
        
        return True
        
    except Exception as e:
        print(f"❌ 新配置系统演示失败: {e}")
        return False


def demo_legacy_compatibility():
    """演示旧配置兼容性"""
    print("\n=== 旧配置兼容性演示 ===")
    
    try:
        from config.config_loader import get_config_loader, load_legacy_config
        
        # 1. 使用配置加载器
        print("\n1. 使用统一配置加载器...")
        loader = get_config_loader()
        
        # 2. 加载旧YAML配置
        print("\n2. 加载旧YAML配置...")
        try:
            db_config = loader.load_legacy_yaml("database_conf.yaml")
            print(f"✅ 数据库配置加载成功")
            print(f"   Zebra配置: {db_config.get('zebra', {}).get('app_key', 'N/A')}")
        except Exception as e:
            print(f"⚠️  数据库配置加载失败: {e}")
        
        try:
            service_config = loader.load_legacy_yaml("service_conf.yaml")
            print(f"✅ 服务配置加载成功")
        except Exception as e:
            print(f"⚠️  服务配置加载失败: {e}")
        
        # 3. 便捷函数
        print("\n3. 使用便捷函数...")
        try:
            config = load_legacy_config("database_conf.yaml")
            print(f"✅ 便捷函数加载成功")
        except Exception as e:
            print(f"⚠️  便捷函数加载失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 旧配置兼容性演示失败: {e}")
        return False


def demo_cr_rules_loading():
    """演示CR规则加载"""
    print("\n=== CR规则加载演示 ===")
    
    try:
        from config.config_loader import get_config_loader
        
        loader = get_config_loader()
        
        # 加载不同的CR规则
        rule_files = [
            "python_security.json",
            "python_web_backend.json", 
            "java_enterprise.json"
        ]
        
        for rule_file in rule_files:
            try:
                rules = loader.load_cr_rules(rule_file)
                rule_count = len(rules.get('rules', []))
                print(f"✅ {rule_file}: {rule_count} 条规则")
            except Exception as e:
                print(f"⚠️  {rule_file}: 加载失败 - {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ CR规则加载演示失败: {e}")
        return False


def demo_config_migration_benefits():
    """演示配置迁移的好处"""
    print("\n=== 配置迁移好处演示 ===")
    
    print("✅ 统一配置管理:")
    print("   - 所有配置集中在config/目录")
    print("   - 清晰的目录结构分类")
    print("   - 统一的加载接口")
    
    print("\n✅ 向后兼容性:")
    print("   - 旧YAML配置仍可使用")
    print("   - 渐进式迁移策略")
    print("   - 不影响现有功能")
    
    print("\n✅ 新架构优势:")
    print("   - 基于Pydantic的类型验证")
    print("   - 环境变量自动加载")
    print("   - 配置文件和代码分离")
    
    print("\n✅ 维护性提升:")
    print("   - 配置结构清晰")
    print("   - 文档完善")
    print("   - 错误提示友好")
    
    return True


def main():
    """主函数"""
    print("配置系统合并演示开始...")
    
    demos = [
        ("新配置系统", demo_new_config_system),
        ("旧配置兼容性", demo_legacy_compatibility),
        ("CR规则加载", demo_cr_rules_loading),
        ("迁移好处", demo_config_migration_benefits)
    ]
    
    success_count = 0
    
    for demo_name, demo_func in demos:
        try:
            if demo_func():
                success_count += 1
                print(f"✅ {demo_name} 演示成功")
            else:
                print(f"❌ {demo_name} 演示失败")
        except Exception as e:
            print(f"💥 {demo_name} 演示异常: {e}")
    
    # 总结
    print(f"\n{'='*50}")
    print("演示总结")
    print(f"{'='*50}")
    print(f"成功演示: {success_count}/{len(demos)}")
    print(f"成功率: {success_count/len(demos)*100:.1f}%")
    
    if success_count == len(demos):
        print("\n🎉 配置系统合并成功！")
        print("📝 建议:")
        print("   1. 新功能使用新配置类")
        print("   2. 逐步迁移现有代码")
        print("   3. 保持向后兼容性")
        print("   4. 更新团队文档")
    elif success_count > 0:
        print("\n⚠️  配置系统部分功能正常")
        print("请检查失败的演示并修复问题")
    else:
        print("\n❌ 配置系统存在问题")
        print("请检查配置文件和依赖")
    
    return success_count == len(demos)


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
