"""
服务管理器

按照第一阶段架构重构要求实现统一的服务管理
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional
from services.base_service import BaseService, ServiceStatus
from services.mcp_service import MCPService
from services.external.mcp_adapter import MCPAdapter, service_registry
from config.base_config import BaseConfig


class ServiceManager:
    """服务管理器，负责管理所有服务的生命周期"""
    
    def __init__(self, config: BaseConfig):
        self.config = config
        self.services: Dict[str, BaseService] = {}
        self.logger = logging.getLogger("service_manager")
        self.mcp_adapter: Optional[MCPAdapter] = None
        
    def register_service(self, service: BaseService):
        """注册服务"""
        self.services[service.name] = service
        self.logger.info(f"Registered service: {service.name}")
    
    def unregister_service(self, service_name: str):
        """取消注册服务"""
        if service_name in self.services:
            del self.services[service_name]
            self.logger.info(f"Unregistered service: {service_name}")
    
    async def start_all_services(self) -> Dict[str, bool]:
        """启动所有服务"""
        results = {}
        print("services: ", self.services)
        for service_name, service in self.services.items():
            try:
                self.logger.info(f"Starting service: {service_name}")
                success = await service.start()
                results[service_name] = success
                
                if success:
                    self.logger.info(f"Service {service_name} started successfully")
                else:
                    self.logger.error(f"Failed to start service {service_name}")
                    
            except Exception as e:
                self.logger.error(f"Error starting service {service_name}: {str(e)}")
                results[service_name] = False
        
        # 初始化MCP适配器
        if "mcp_service" in self.services and results.get("mcp_service", False):
            mcp_service = self.services["mcp_service"]
            self.mcp_adapter = MCPAdapter(mcp_service, service_registry.get_all_mappings())
            self.logger.info("MCP adapter initialized")
        
        return results
    
    async def stop_all_services(self) -> Dict[str, bool]:
        """停止所有服务"""
        results = {}
        
        # 按相反顺序停止服务
        service_names = list(self.services.keys())
        service_names.reverse()
        
        for service_name in service_names:
            service = self.services[service_name]
            try:
                self.logger.info(f"Stopping service: {service_name}")
                success = await service.stop()
                results[service_name] = success
                
                if success:
                    self.logger.info(f"Service {service_name} stopped successfully")
                else:
                    self.logger.error(f"Failed to stop service {service_name}")
                    
            except Exception as e:
                self.logger.error(f"Error stopping service {service_name}: {str(e)}")
                results[service_name] = False
        
        return results
    
    async def restart_service(self, service_name: str) -> bool:
        """重启指定服务"""
        if service_name not in self.services:
            self.logger.error(f"Service {service_name} not found")
            return False
        
        service = self.services[service_name]
        
        try:
            # 停止服务
            await service.stop()
            
            # 启动服务
            success = await service.start()
            
            if success:
                self.logger.info(f"Service {service_name} restarted successfully")
            else:
                self.logger.error(f"Failed to restart service {service_name}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"Error restarting service {service_name}: {str(e)}")
            return False
    
    async def get_services_status(self) -> Dict[str, Dict[str, Any]]:
        """获取所有服务状态"""
        status = {}
        
        for service_name, service in self.services.items():
            try:
                service_info = service.get_service_info()
                health_check = await service.health_check()
                
                status[service_name] = {
                    "info": service_info,
                    "health": health_check
                }
                
            except Exception as e:
                status[service_name] = {
                    "info": {"name": service_name, "status": "error"},
                    "health": {"status": "error", "message": str(e)}
                }
        
        return status
    
    def get_service(self, service_name: str) -> Optional[BaseService]:
        """获取指定服务"""
        return self.services.get(service_name)
    
    def list_services(self) -> List[str]:
        """列出所有服务名称"""
        return list(self.services.keys())
    
    def get_running_services(self) -> List[str]:
        """获取正在运行的服务列表"""
        running_services = []
        for service_name, service in self.services.items():
            if service.status == ServiceStatus.RUNNING:
                running_services.append(service_name)
        return running_services
    
    def get_failed_services(self) -> List[str]:
        """获取失败的服务列表"""
        failed_services = []
        for service_name, service in self.services.items():
            if service.status == ServiceStatus.ERROR:
                failed_services.append(service_name)
        return failed_services
    
    async def health_check_all(self) -> Dict[str, str]:
        """对所有服务进行健康检查"""
        health_status = {}
        
        for service_name, service in self.services.items():
            try:
                health_info = await service.health_check()
                health_status[service_name] = health_info.get("status", "unknown")
            except Exception as e:
                health_status[service_name] = "error"
                self.logger.error(f"Health check failed for {service_name}: {str(e)}")
        
        return health_status
    
    def get_mcp_adapter(self) -> Optional[MCPAdapter]:
        """获取MCP适配器"""
        return self.mcp_adapter
    
    async def execute_via_mcp(self, service_name: str, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """通过MCP执行服务调用"""
        if not self.mcp_adapter:
            return {
                "success": False,
                "error": "MCP adapter not available"
            }
        
        return await self.mcp_adapter.execute_service_via_mcp(service_name, input_data)


def create_service_manager(config: BaseConfig) -> ServiceManager:
    """创建服务管理器并注册所有服务"""
    manager = ServiceManager(config)

    # 注册MCP服务
    if config.mcp.enabled:
        try:
            mcp_service = MCPService(config.mcp)
            manager.register_service(mcp_service)
        except Exception as e:
            logging.warning(f"注册MCP服务失败: {str(e)}")

    # 注册基础服务
    try:
        # 注册KMS服务
        from services.kms_service import KmsService
        kms_service = KmsService(config.get_service_config("kms"))
        manager.register_service(kms_service)
    except Exception as e:
        logging.warning(f"注册KMS服务失败: {str(e)}")

    try:
        # 注册组织架构服务
        from services.org_service import OrgService
        org_service = OrgService(config.get_service_config("org"))
        manager.register_service(org_service)
    except Exception as e:
        logging.warning(f"注册组织架构服务失败: {str(e)}")

    try:
        # 注册Git服务
        from services.git_service import GitService
        git_service = GitService(config.get_service_config("git"))
        manager.register_service(git_service)
    except Exception as e:
        logging.warning(f"注册Git服务失败: {str(e)}")

    try:
        # 注册LLM服务
        from services.llm_service import LLMService
        llm_service = LLMService(config.get_service_config("llm"))
        manager.register_service(llm_service)
    except Exception as e:
        logging.warning(f"注册LLM服务失败: {str(e)}")

    try:
        # 注册SSO服务
        from services.sso_service import SSOService
        sso_service = SSOService(config.get_service_config("sso"))
        manager.register_service(sso_service)
    except Exception as e:
        logging.warning(f"注册SSO服务失败: {str(e)}")

    # 注册依赖服务（需要基础服务）
    kms_svc = manager.get_service("kms_service")
    org_svc = manager.get_service("org_service")

    if kms_svc:
        try:
            # 注册大象服务
            from services.daxiang_service import DaXiangService
            daxiang_service = DaXiangService(kms_svc, config.get_service_config("daxiang"))
            manager.register_service(daxiang_service)
        except Exception as e:
            logging.warning(f"注册大象服务失败: {str(e)}")

        try:
            # 注册开发工具服务
            from services.devtools_service import DevtoolsService
            devtools_service = DevtoolsService(kms_svc, config.get_service_config("devtools"))
            manager.register_service(devtools_service)
        except Exception as e:
            logging.warning(f"注册开发工具服务失败: {str(e)}")

    if kms_svc and org_svc:
        daxiang_svc = manager.get_service("daxiang_service")
        if daxiang_svc:
            try:
                # 注册学城服务
                from services.km_service import KmService
                km_service = KmService(kms_svc, daxiang_svc, org_svc, config.get_service_config("km"))
                manager.register_service(km_service)
            except Exception as e:
                logging.warning(f"注册学城服务失败: {str(e)}")

    # 注册CR服务（需要其他服务作为依赖）
    try:
        from services.cr_service import CRService
        # CR服务需要特殊的初始化，传递依赖服务
        horn_service = None  # 这里可能需要创建HornService

        # 确保LLM服务已经注册到全局注册表
        llm_svc = manager.get_service("llm_service")
        if llm_svc:
            # 临时注册到全局注册表，确保CR服务能够获取到
            from core.service_registry import register_service as global_register
            global_register("llm_service", llm_svc)

        cr_service = CRService(
            horn_service=horn_service,
            kms_service=kms_svc,
            dx_service=manager.get_service("daxiang_service"),
            git_service=manager.get_service("git_service"),
            devmind_service=None,  # 这里可能需要创建DevmindService
            config=config.get_service_config("cr")
        )
        manager.register_service(cr_service)
    except Exception as e:
        logging.warning(f"注册CR服务失败: {str(e)}")
        import traceback
        traceback.print_exc()

    return manager


# 全局服务管理器实例
_service_manager: Optional[ServiceManager] = None


def get_service_manager() -> Optional[ServiceManager]:
    """获取全局服务管理器实例"""
    return _service_manager


def init_service_manager(config: BaseConfig) -> ServiceManager:
    """初始化全局服务管理器"""
    global _service_manager
    _service_manager = create_service_manager(config)
    logging.info(f"全局服务管理器已初始化")
    return _service_manager


def set_service_manager(manager: ServiceManager):
    """设置全局服务管理器"""
    global _service_manager
    _service_manager = manager
    logging.info("全局服务管理器已设置")


async def start_all_services(config: BaseConfig) -> Dict[str, bool]:
    """启动所有服务的便捷函数"""
    manager = init_service_manager(config)
    return await manager.start_all_services()


async def stop_all_services() -> Dict[str, bool]:
    """停止所有服务的便捷函数"""
    if _service_manager:
        return await _service_manager.stop_all_services()
    return {}
