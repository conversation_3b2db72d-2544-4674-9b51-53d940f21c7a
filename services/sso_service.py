"""
SSO服务 - 新架构版本
负责用户认证和信息获取，继承自BaseService
对接公司SSO服务和组织架构API
"""

import logging
import requests
from typing import Any, Dict, Optional
from .base_service import BaseService


class SSOService(BaseService):
    """
    SSO服务，负责用户认证和信息获取
    继承自BaseService，符合新架构要求
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化SSO服务
        
        Args:
            config: 服务配置
        """
        super().__init__("sso_service", config)
        
        # 默认配置
        self.default_config = {
            'org_domain': 'http://org2.gateway.it.test.sankuai.com',
            'timeout': 30,
            'max_retries': 3,
            'enable_cache': True,
            'cache_ttl': 300  # 5分钟缓存
        }
        
        # 合并配置
        self.config = {**self.default_config, **self.config}
        
        # 服务状态
        self.org_domain = self.config.get('org_domain')
        self.session = None
        self.user_cache = {}  # 简单的用户信息缓存
        
    async def initialize(self) -> bool:
        """初始化服务"""
        try:
            self.logger.info("初始化SSO服务")
            
            # 创建HTTP会话
            self.session = requests.Session()
            self.session.timeout = self.config.get('timeout', 30)
            
            # 设置默认请求头
            self.session.headers.update({
                'User-Agent': 'SSO-Service/1.0',
                'Content-Type': 'application/json'
            })
            
            # 验证组织架构API连通性
            if await self._test_org_api_connectivity():
                self.logger.info("组织架构API连通性测试通过")
            else:
                self.logger.warning("组织架构API连通性测试失败，服务将以降级模式运行")
            
            self.logger.info("SSO服务初始化完成")
            return True
            
        except Exception as e:
            self.logger.error(f"SSO服务初始化失败: {str(e)}")
            self.increment_error_count()
            return False
    
    async def cleanup(self) -> bool:
        """清理资源"""
        try:
            self.logger.info("清理SSO服务资源")
            
            if self.session:
                self.session.close()
                self.session = None
            
            # 清理缓存
            self.user_cache.clear()
            
            return True
        except Exception as e:
            self.logger.error(f"SSO服务清理失败: {str(e)}")
            return False
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            health_status = {
                'service': self.name,
                'status': 'healthy',
                'org_domain': self.org_domain,
                'session_active': self.session is not None,
                'cache_size': len(self.user_cache),
                'timestamp': self._get_current_timestamp()
            }
            
            # 测试组织架构API连通性
            try:
                api_healthy = await self._test_org_api_connectivity()
                health_status['org_api_healthy'] = api_healthy
            except Exception as e:
                health_status['org_api_healthy'] = False
                health_status['org_api_error'] = str(e)
            
            return health_status
            
        except Exception as e:
            return {
                'service': self.name,
                'status': 'unhealthy',
                'error': str(e),
                'timestamp': self._get_current_timestamp()
            }
    
    async def _test_org_api_connectivity(self) -> bool:
        """测试组织架构API连通性"""
        try:
            # 使用一个简单的API调用测试连通性
            url = f"{self.org_domain}/api/org2/health"  # 假设有健康检查端点
            response = requests.get(url, timeout=5)
            return response.status_code == 200
        except Exception:
            return False
    
    def get_user_info(self, request_context: Any) -> Dict[str, Any]:
        """
        获取用户信息
        
        Args:
            request_context: 请求上下文，包含SDK和用户信息
            
        Returns:
            用户信息结果
        """
        try:
            # 在实际环境中，应该从request_context.sdk.sso.userInfo获取用户信息
            user_info = getattr(request_context, 'user_info', {})
            if not user_info and hasattr(request_context, 'sdk') and hasattr(request_context.sdk, 'sso'):
                user_info = getattr(request_context.sdk.sso, 'userInfo', {})
            
            # 记录用户信息获取
            if user_info:
                self.logger.debug(f"获取用户信息成功: {user_info.get('mis_id', 'unknown')}")
            else:
                self.logger.warning("未能获取用户信息")
            
            return {'success': True, 'data': user_info}
        except Exception as e:
            self.logger.error(f"获取用户信息失败: {str(e)}")
            self.increment_error_count()
            return {'success': False, 'error': str(e)}
    
    def get_access_token(self, request_context: Any) -> Dict[str, Any]:
        """
        获取访问令牌
        
        Args:
            request_context: 请求上下文，包含SDK和访问令牌
            
        Returns:
            访问令牌结果
        """
        try:
            # 在实际环境中，应该从request_context.sdk.sso.accessToken获取访问令牌
            access_token = None
            if hasattr(request_context, 'sdk') and hasattr(request_context.sdk, 'sso'):
                access_token = getattr(request_context.sdk.sso, 'accessToken', None)
            
            if access_token:
                self.logger.debug("获取访问令牌成功")
            else:
                self.logger.warning("未能获取访问令牌")
            
            return {'success': True, 'data': access_token}
        except Exception as e:
            self.logger.error(f"获取访问令牌失败: {str(e)}")
            self.increment_error_count()
            return {'success': False, 'error': str(e)}
    
    def get_emp_id(self, request_headers: Dict[str, str]) -> str:
        """
        获取员工ID
        
        Args:
            request_headers: 请求头
            
        Returns:
            员工ID
        """
        try:
            mis_id = request_headers.get('mis-id')
            if not mis_id:
                self.logger.warning("请求头中缺少mis-id")
                return ''
            
            emp_id = self.get_emp_id_by_mis_id(mis_id)
            return emp_id
        except Exception as e:
            self.logger.error(f"获取员工ID失败: {str(e)}")
            self.increment_error_count()
            return ''
    
    def get_emp_id_by_mis_id(self, mis_id: Optional[str] = None) -> str:
        """
        根据misId获取员工ID
        
        Args:
            mis_id: 员工MIS ID
            
        Returns:
            员工ID
        """
        try:
            if not mis_id:
                raise ValueError("misId is empty")
            
            # 检查缓存
            if self.config.get('enable_cache', True) and mis_id in self.user_cache:
                cached_data = self.user_cache[mis_id]
                if self._is_cache_valid(cached_data):
                    self.logger.debug(f"从缓存获取员工ID: {mis_id}")
                    return cached_data['emp_id']
            
            # 构建请求URL
            url = f"{self.org_domain}/api/org2/emps?mises={mis_id}"
            
            # 发送请求
            if self.session:
                response = self.session.get(url)
            else:
                response = requests.get(url, timeout=self.config.get('timeout', 30))
            
            response.raise_for_status()  # 如果响应状态码不是200，抛出异常
            
            # 解析响应
            data = response.json().get('data', {})
            emp_id = data.get('empId', '')
            
            # 更新缓存
            if self.config.get('enable_cache', True) and emp_id:
                self.user_cache[mis_id] = {
                    'emp_id': emp_id,
                    'timestamp': self._get_current_timestamp(),
                    'data': data
                }
            
            self.logger.debug(f"获取员工ID成功: {mis_id} -> {emp_id}")
            return emp_id
            
        except Exception as e:
            self.logger.error(f"获取员工ID失败: {str(e)}")
            self.increment_error_count()
            raise ValueError(f"获取员工ID失败: {str(e)}")
    
    def clear_cache(self):
        """清理用户缓存"""
        self.user_cache.clear()
        self.logger.info("用户缓存已清理")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        return {
            'cache_size': len(self.user_cache),
            'cache_enabled': self.config.get('enable_cache', True),
            'cache_ttl': self.config.get('cache_ttl', 300)
        }
    
    def _is_cache_valid(self, cached_data: Dict[str, Any]) -> bool:
        """检查缓存是否有效"""
        try:
            from datetime import datetime, timedelta
            
            cache_time = datetime.fromisoformat(cached_data['timestamp'])
            ttl = self.config.get('cache_ttl', 300)
            
            return datetime.now() - cache_time < timedelta(seconds=ttl)
        except Exception:
            return False
    
    def _get_current_timestamp(self) -> str:
        """获取当前时间戳"""
        from datetime import datetime
        return datetime.now().isoformat()


# 兼容性函数，保持与原有代码的兼容性
def create_sso_service(config: Optional[Dict[str, Any]] = None) -> SSOService:
    """
    创建SSO服务实例
    
    Args:
        config: 服务配置
        
    Returns:
        SSOService: SSO服务实例
    """
    return SSOService(config)


# 向后兼容的类别名
SsoService = SSOService
