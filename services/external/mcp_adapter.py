"""
MCP协议适配器

按照第一阶段架构重构要求实现MCP协议适配
"""

import asyncio
from typing import Dict, Any, List, Optional
from services.mcp_service import MCPService


class MCPAdapter:
    """MCP协议适配器，将服务调用转换为MCP工具调用"""
    
    def __init__(self, mcp_service: MCPService, service_mapping: Dict[str, str]):
        self.mcp_service = mcp_service
        self.service_mapping = service_mapping  # 服务名称到MCP工具名称的映射
        
    async def execute_service_via_mcp(self, service_name: str, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """通过MCP协议执行服务任务"""
        try:
            # 获取对应的MCP工具名称
            tool_name = self.service_mapping.get(service_name)
            if not tool_name:
                return {
                    "success": False,
                    "error": f"Service {service_name} 没有对应的MCP工具映射",
                    "service_name": service_name
                }
            
            # 调用MCP工具
            result = await self.mcp_service.call_tool(tool_name, input_data)
            
            # 添加服务信息到结果中
            result["service_name"] = service_name
            return result
                
        except Exception as e:
            return {
                "success": False,
                "error": f"MCP适配器执行失败: {str(e)}",
                "service_name": service_name
            }
    
    async def batch_execute(self, tasks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """批量执行MCP工具调用"""
        results = []
        
        # 并发执行多个任务
        async def execute_task(task):
            service_name = task.get("service_name")
            input_data = task.get("input_data", {})
            return await self.execute_service_via_mcp(service_name, input_data)
        
        tasks_coroutines = [execute_task(task) for task in tasks]
        results = await asyncio.gather(*tasks_coroutines, return_exceptions=True)
        
        # 处理异常结果
        processed_results = []
        for result in results:
            if isinstance(result, Exception):
                processed_results.append({
                    "success": False,
                    "error": f"批量执行异常: {str(result)}"
                })
            else:
                processed_results.append(result)
        
        return processed_results
    
    def get_supported_services(self) -> List[str]:
        """获取支持的服务列表"""
        return list(self.service_mapping.keys())
    
    async def validate_mcp_tools(self) -> Dict[str, bool]:
        """验证MCP工具可用性"""
        available_tools = self.mcp_service.get_available_tools()
        validation_result = {}
        
        for service_name, tool_name in self.service_mapping.items():
            validation_result[service_name] = tool_name in available_tools
            
        return validation_result
    
    def get_tool_mapping(self) -> Dict[str, str]:
        """获取服务到工具的映射"""
        return self.service_mapping.copy()
    
    async def check_mcp_health(self) -> Dict[str, Any]:
        """检查MCP服务健康状态"""
        return await self.mcp_service.health_check()


class ServiceToMCPRegistry:
    """服务到MCP工具的注册表"""
    
    def __init__(self):
        self.mappings: Dict[str, str] = {}
        self.descriptions: Dict[str, str] = {}
    
    def register_service(self, service_name: str, mcp_tool_name: str, description: str = ""):
        """注册服务到MCP工具的映射"""
        self.mappings[service_name] = mcp_tool_name
        self.descriptions[service_name] = description
    
    def unregister_service(self, service_name: str):
        """取消注册服务"""
        self.mappings.pop(service_name, None)
        self.descriptions.pop(service_name, None)
    
    def get_mapping(self, service_name: str) -> Optional[str]:
        """获取服务对应的MCP工具名称"""
        return self.mappings.get(service_name)
    
    def get_all_mappings(self) -> Dict[str, str]:
        """获取所有映射"""
        return self.mappings.copy()
    
    def get_service_description(self, service_name: str) -> str:
        """获取服务描述"""
        return self.descriptions.get(service_name, "")
    
    def list_services(self) -> List[Dict[str, str]]:
        """列出所有注册的服务"""
        return [
            {
                "service_name": service_name,
                "mcp_tool_name": tool_name,
                "description": self.descriptions.get(service_name, "")
            }
            for service_name, tool_name in self.mappings.items()
        ]


# 全局服务注册表
service_registry = ServiceToMCPRegistry()

# 注册默认的服务映射
service_registry.register_service(
    "daxiang_service", 
    "daxiang_notification_tool", 
    "大象通知服务，用于发送企业内部通知"
)

service_registry.register_service(
    "chunk_service", 
    "code_analysis_tool", 
    "代码分块服务，用于代码分析和依赖关系分析"
)

service_registry.register_service(
    "git_service", 
    "git_operations_tool", 
    "Git操作服务，用于版本控制和代码管理"
)

service_registry.register_service(
    "km_service", 
    "knowledge_management_tool", 
    "学城文档服务，用于知识管理和文档自动化"
)

service_registry.register_service(
    "devtools_service", 
    "development_tools", 
    "开发工具服务，用于开发流程自动化和代码协作"
)
