"""
学城文档服务

按照第一阶段架构重构要求，将原有KmService迁移到新架构
"""

import logging
import os
from pathlib import Path
from typing import Any, Dict, Optional

import yaml

from services.base_service import BaseService
from services.daxiang_service import DaXiangService
from services.kms_service import KmsService
from services.org_service import OrgService
import octo_rpc
from octo_rpc import NonMeshClient, load
octo_rpc.log.setLevel(logging.CRITICAL)


ROOT_DIR = Path(__file__).resolve().parent.parent.__str__()


class CreateDocParams:
    """
    创建学城文档的参数
    """

    def __init__(
            self,
            title: str,
            content: str,
            operator_emp_id: Optional[str] = None,
            parent_id: Optional[str] = None,
            template_id: Optional[str] = None,
            space_id: Optional[str] = None,
            copy_from_content_id=None,
            **kwargs
    ):
        self.title = title
        self.content = content
        self.operatorEmpId = int(operator_emp_id) if operator_emp_id else None
        self.templateId = template_id
        self.parentId = parent_id
        self.spaceId = space_id
        self.copyFromContentId = copy_from_content_id
        self.__dict__.update(kwargs)

    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典
        """
        return {k: v for k, v in self.__dict__.items() if v is not None}


class KmService(BaseService):
    """学城文档服务，负责学城文档的创建与管理"""

    def __init__(self, kms_service: KmsService, daxiang_service: DaXiangService, 
                 org_service: OrgService, config: Optional[Dict[str, Any]] = None):
        super().__init__("km_service", config)
        self.octo_rpc_available = octo_rpc.OctoStatus in (1, 2, 5)
        self.kms_service = kms_service
        self.daxiang_service = daxiang_service
        self.org_service = org_service
        
        # 环境配置
        self.env = os.environ.get('INF_BOM_ENV', 'test')
        self.swimlane = os.environ.get('INF_BOM_SWIMLANE', None)
        
        # 服务配置
        self.km_config = {}
        self.thrift_client = None
        self.open_km_service = None



    async def initialize(self) -> bool:
        """初始化学城服务"""
        try:
            # 加载配置
            self.km_config = await self._load_km_config()
            
            if not self.km_config:
                self.logger.warning("学城配置为空，服务将以降级模式运行")
                return True

            if not self.octo_rpc_available:
                self.logger.warning("Octo RPC不可用，Thrift客户端功能受限")
                return True

            # 初始化Thrift客户端
            try:
                self.open_km_service = load(ROOT_DIR + "/infra/thrift/km/OpenKmService.thrift")
                self.thrift_client = NonMeshClient(
                    appkey=self.km_config.get('localAppKey'),
                    env=self.env,
                    service=self.open_km_service.XmOpenKmServiceI,
                    service_name="com.sankuai.xm.openplatform.api.service.open.XmOpenKmServiceI",
                    remote_appkey=self.km_config.get('remoteAppKey'),
                    timeout=30000,
                )
                self.logger.info("学城Thrift客户端初始化成功")
            except Exception as e:
                self.logger.warning(f"学城Thrift客户端初始化失败: {str(e)}")
                self.thrift_client = None

            self.logger.info(f"学城服务初始化成功，环境: {self.env}")
            return True
            
        except Exception as e:
            self.logger.error(f"学城服务初始化失败: {str(e)}")
            return False

    async def _load_km_config(self) -> Dict[str, Any]:
        """加载学城配置"""
        try:
            config_path = 'config/legacy/km_conf.yaml'
            if not os.path.exists(config_path):
                self.logger.warning(f"学城配置文件不存在: {config_path}")
                return {}
                
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
                env_config = config.get(self.env, {})
                
            self.logger.info(f"成功加载学城配置，环境: {self.env}")
            return env_config
            
        except Exception as e:
            self.logger.error(f"加载学城配置失败: {str(e)}")
            return {}

    async def cleanup(self) -> bool:
        """清理资源"""
        try:
            # 清理Thrift客户端
            self.thrift_client = None
            self.open_km_service = None
            
            self.logger.info("学城服务资源清理完成")
            return True
            
        except Exception as e:
            self.logger.error(f"学城服务资源清理失败: {str(e)}")
            return False

    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            # 检查依赖服务
            if not self.daxiang_service:
                return {
                    "status": "error",
                    "message": "大象服务不可用"
                }

            if not self.org_service:
                return {
                    "status": "error",
                    "message": "组织架构服务不可用"
                }

            # 检查配置
            if not self.km_config:
                return {
                    "status": "degraded",
                    "message": "学城配置未加载"
                }

            # 检查必要的配置项
            required_keys = ['localAppKey', 'remoteAppKey', 'domain']
            missing_keys = [key for key in required_keys if not self.km_config.get(key)]
            
            if missing_keys:
                return {
                    "status": "error",
                    "message": f"缺少必要配置: {', '.join(missing_keys)}"
                }

            # 检查Thrift客户端
            if not self.octo_rpc_available:
                return {
                    "status": "degraded",
                    "message": "Octo RPC不可用，功能受限"
                }

            if not self.thrift_client:
                return {
                    "status": "degraded",
                    "message": "Thrift客户端未初始化"
                }

            return {
                "status": "healthy",
                "message": "学城服务正常",
                "environment": self.env,
                "config_loaded": True,
                "thrift_client_available": self.thrift_client is not None,
                "octo_rpc_available": self.octo_rpc_available
            }
                
        except Exception as e:
            return {
                "status": "error",
                "message": f"健康检查异常: {str(e)}"
            }

    async def create_doc(self, request_context: Any, params: CreateDocParams) -> str:
        """
        创建学城2.0文档
        
        Args:
            request_context: 请求上下文
            params: 创建文档的参数
            
        Returns:
            创建后的文档URL
        """
        try:
            if not self.thrift_client or not self.open_km_service:
                raise ValueError("学城Thrift客户端不可用")

            # 获取访问令牌
            access_token = self.daxiang_service.get_access_token(request_context)
            if not access_token:
                raise ValueError("无法获取大象访问令牌")

            # 如果没有操作者员工ID，从组织架构服务获取
            if not hasattr(params, 'operatorEmpId') or not params.operatorEmpId:
                try:
                    emp_info = self.org_service.get_emp_info(
                        getattr(request_context, 'headers', {}),
                        getattr(request_context, 'args', {})
                    )
                    if emp_info and emp_info.get('empId'):
                        params.operatorEmpId = int(emp_info['empId'])
                except Exception as e:
                    self.logger.warning(f"获取员工信息失败: {str(e)}")

            # 设置spaceId（如果请求上下文中有）
            if hasattr(request_context, 'json') and request_context.json:
                space_id = request_context.json.get("spaceId")
                if space_id:
                    params.spaceId = space_id

            self.logger.info(f"创建学城文档，标题: {getattr(params, 'title', 'Unknown')}")

            # 调用Thrift服务创建文档
            res = self.thrift_client.addCollaborationContent(
                request=self.open_km_service.CollaborationContentReq(**params.to_dict()),
                accessToken=access_token
            )

            # 解析响应
            status = getattr(res, 'status', None)
            info = getattr(res, 'info', None)

            if not status or status.code != 0:
                error_msg = f"学城API返回错误: {status}"
                self.logger.error(error_msg)
                raise ValueError(error_msg)

            # 构建文档URL
            doc_url = f"{self.km_config.get('domain')}/collabpage/{info}"
            self.logger.info(f"学城文档创建成功: {doc_url}")
            
            return doc_url

        except Exception as e:
            self.logger.error(f"创建学城文档失败: {str(e)}")
            self.increment_error_count()
            raise ValueError(f"创建学城文档失败: {str(e)}")

    async def get_doc_info(self, doc_id: str, request_context: Any = None) -> Dict[str, Any]:
        """
        获取文档信息
        
        Args:
            doc_id: 文档ID
            request_context: 请求上下文
            
        Returns:
            文档信息
        """
        try:
            if not self.thrift_client or not self.open_km_service:
                raise ValueError("学城Thrift客户端不可用")

            # 获取访问令牌
            access_token = self.daxiang_service.get_access_token(request_context)
            if not access_token:
                raise ValueError("无法获取大象访问令牌")

            # 这里需要根据实际的学城API来实现获取文档信息的功能
            # 目前的Thrift接口可能不包含此功能
            self.logger.warning("获取文档信息功能暂未实现")
            
            return {
                "doc_id": doc_id,
                "status": "not_implemented",
                "message": "获取文档信息功能暂未实现"
            }

        except Exception as e:
            self.logger.error(f"获取文档信息失败: {str(e)}")
            self.increment_error_count()
            raise ValueError(f"获取文档信息失败: {str(e)}")

    async def update_doc(self, doc_id: str, params: CreateDocParams, request_context: Any = None) -> bool:
        """
        更新文档
        
        Args:
            doc_id: 文档ID
            params: 更新参数
            request_context: 请求上下文
            
        Returns:
            是否更新成功
        """
        try:
            if not self.thrift_client or not self.open_km_service:
                raise ValueError("学城Thrift客户端不可用")

            # 获取访问令牌
            access_token = self.daxiang_service.get_access_token(request_context)
            if not access_token:
                raise ValueError("无法获取大象访问令牌")

            # 这里需要根据实际的学城API来实现更新文档的功能
            # 目前的Thrift接口可能不包含此功能
            self.logger.warning("更新文档功能暂未实现")
            
            return False

        except Exception as e:
            self.logger.error(f"更新文档失败: {str(e)}")
            self.increment_error_count()
            raise ValueError(f"更新文档失败: {str(e)}")

    def get_doc_url(self, doc_id: str) -> str:
        """
        根据文档ID构建文档URL
        
        Args:
            doc_id: 文档ID
            
        Returns:
            文档URL
        """
        if not self.km_config or not self.km_config.get('domain'):
            raise ValueError("学城域名配置缺失")
            
        return f"{self.km_config.get('domain')}/collabpage/{doc_id}"

