"""
开发工具服务

按照第一阶段架构重构要求，将原有DevtoolsService迁移到新架构
"""

import base64
import hashlib
import hmac
import json
import subprocess
import time
from typing import Dict, Any, Optional, Union

import requests

from services.base_service import BaseService
from services.kms_service import KmsService

# 可选依赖
try:
    from kms_sdk.kms import Kms
    from sts_sdk.model.enums import AuthAction
    from sts_sdk.model.subject import STSRequest, SignParam
    from sts_sdk.service.signature_service_factory import STSServiceFactory
    STS_SDK_AVAILABLE = True
except ImportError:
    STS_SDK_AVAILABLE = False


class DevtoolsService(BaseService):
    """开发工具服务，负责与开发工具网关交互"""

    def __init__(self, kms_service: KmsService, config: Optional[Dict[str, Any]] = None):
        super().__init__("devtools_service", config)
        self.kms_service = kms_service
        
        # 设置客户端和服务端appkey
        self.CLIENT_APPKEY = 'com.sankuai.yunzhuan.devhelper'
        self.SERVER_APPKEY = 'com.sankuai.devtools.gateway.service'
        self.DOMAIN = 'https://dev-api.vip.sankuai.com/mcode'
        
        self.sts_available = STS_SDK_AVAILABLE

    async def initialize(self) -> bool:
        """初始化开发工具服务"""
        try:
            if not self.sts_available:
                self.logger.warning("STS SDK不可用，部分功能将受限")

            # 检查KMS服务可用性
            if not self.kms_service:
                self.logger.error("KMS服务不可用")
                return False

            self.logger.info("开发工具服务初始化成功")
            return True
            
        except Exception as e:
            self.logger.error(f"开发工具服务初始化失败: {str(e)}")
            return False

    async def cleanup(self) -> bool:
        """清理资源"""
        try:
            # 开发工具服务通常不需要特殊清理
            self.logger.info("开发工具服务资源清理完成")
            return True
            
        except Exception as e:
            self.logger.error(f"开发工具服务资源清理失败: {str(e)}")
            return False

    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            # 检查KMS服务
            if not self.kms_service:
                return {
                    "status": "error",
                    "message": "KMS服务不可用"
                }

            # 检查KMS服务健康状态
            kms_health = await self.kms_service.health_check()
            if kms_health.get("status") not in ["healthy", "degraded"]:
                return {
                    "status": "degraded",
                    "message": "KMS服务异常",
                    "kms_status": kms_health.get("status")
                }

            # 检查STS SDK可用性
            if not self.sts_available:
                return {
                    "status": "degraded",
                    "message": "STS SDK不可用，认证功能受限"
                }

            # 尝试获取token来验证服务可用性
            try:
                tokens = self.get_token()
                if tokens and tokens.get("STS-TOKEN"):
                    return {
                        "status": "healthy",
                        "message": "开发工具服务正常",
                        "sts_available": self.sts_available,
                        "domain": self.DOMAIN
                    }
                else:
                    return {
                        "status": "degraded",
                        "message": "无法获取认证token"
                    }
            except Exception as e:
                return {
                    "status": "error",
                    "message": f"认证测试失败: {str(e)}"
                }
                
        except Exception as e:
            return {
                "status": "error",
                "message": f"健康检查异常: {str(e)}"
            }

    def oceanus_auth(self) -> str:
        """获取Oceanus认证Token"""
        try:
            s1_json = {
                "algo": "0001",
                "type": "patriot",
                "time": str(round(time.time() * 1000))
            }
            s2_json = {
                "ns": self.CLIENT_APPKEY,
                "name": self.SERVER_APPKEY
            }
            s1 = base64.b64encode(json.dumps(s1_json).encode()).decode()
            s2 = base64.b64encode(json.dumps(s2_json).encode()).decode()
            
            # 从KMS获取token
            token_result = self.kms_service.get_key(self.CLIENT_APPKEY, "auth_client_" + self.SERVER_APPKEY)
            if not token_result.get("success"):
                raise Exception("无法从KMS获取认证token")
                
            token_data = token_result.get("data")
            if isinstance(token_data, str):
                token_json = json.loads(token_data)
                token = token_json[0] if isinstance(token_json, list) else token_json
            else:
                token = token_data
                
            secret = hmac.new(str(token).encode(), s2.encode(), hashlib.sha1).hexdigest().upper()
            return f'{s1}.{s2}.{secret}'
            
        except Exception as e:
            self.logger.error(f"Oceanus认证失败: {str(e)}")
            self.increment_error_count()
            return ""

    def get_token_by_sso(self, mis: str, ssoid: str, online: bool = True) -> str:
        """获取SSO生成的STS鉴权"""
        try:
            if not self.sts_available:
                raise Exception("STS SDK不可用")
                
            sts_sign_service = STSServiceFactory.create(
                STSRequest(self.SERVER_APPKEY, "SSOTicket", "online" if online else None))
            sign_param = SignParam(
                client_id=mis,
                aud="",
                scp="",
                roles="",
                ext_map={'ssoid': ssoid}
            )
            auth_token = sts_sign_service.sign(sign_param)
            return auth_token.at
            
        except Exception as e:
            self.logger.error(f"SSO认证失败: {str(e)}")
            self.increment_error_count()
            return ""

    def get_token_by_iam(self, iamname: str, iampwd: str, online: bool = True) -> str:
        """获取IAM生成的STS鉴权"""
        try:
            if not self.sts_available:
                raise Exception("STS SDK不可用")
                
            sts_sign_service = STSServiceFactory.create(
                STSRequest(self.SERVER_APPKEY, "IAMTicket", AuthAction.SIGN, "online" if online else None))
            sign_param = SignParam(client_id=iamname, aud="", scp="", roles="", ext_map={'iampwd': iampwd})
            auth_token = sts_sign_service.sign(sign_param)
            return auth_token.at
            
        except Exception as e:
            self.logger.error(f"IAM认证失败: {str(e)}")
            self.increment_error_count()
            return ""

    def get_token(self, options: Optional[Dict[str, str]] = None) -> Dict[str, str]:
        """获取认证Token"""
        try:
            token = ""
            mis = options.get("mis") if options else None
            ssoid = options.get("ssoid") if options else None
            
            if mis and ssoid:
                token = self.get_token_by_sso(mis, ssoid, True)
                self.logger.info("SSO认证成功")
            else:
                password_result = self.kms_service.get_key(self.CLIENT_APPKEY, "git_yunzhuan_password")
                if password_result.get("success"):
                    password = password_result.get("data")
                    token = self.get_token_by_iam('git_yunzhuan', password, True)
                else:
                    self.logger.warning("无法获取IAM密码")
                    
            oceanus_token = self.oceanus_auth()

            return {
                "STS-TOKEN": token,
                "oceanus-remote-appkey": self.SERVER_APPKEY,
                "oceanus-auth": oceanus_token,
                "Cache-Control": "max-age=300",
            }
            
        except Exception as e:
            self.logger.error(f"获取认证token失败: {str(e)}")
            self.increment_error_count()
            return {}

    def get_pr_info(self, url: str, params: Dict[str, Any], options: Optional[Dict[str, str]] = None) -> Dict[str, Any]:
        """获取PR信息（GET）"""
        try:
            header_info = self.get_token(options)
            url_with_params = f"{self.DOMAIN}{url}"
            
            response = requests.get(
                url_with_params,
                headers=header_info,
                params=params,
                timeout=30
            )
            response.raise_for_status()
            return response.json()
            
        except Exception as e:
            self.logger.error(f"获取PR信息失败: {str(e)}")
            self.increment_error_count()
            raise

    def set_comment(self, url: str, comments: str, options: Optional[Dict[str, str]] = None) -> Dict[str, Any]:
        """设置评论（POST）"""
        try:
            header_info = self.get_token(options)
            url_with_params = f"{self.DOMAIN}{url}"
            
            response = requests.post(
                url_with_params,
                headers=header_info,
                json={"text": comments},
                timeout=30
            )
            response.raise_for_status()
            return response.json()
            
        except Exception as e:
            self.logger.error(f"设置评论失败: {str(e)}")
            self.increment_error_count()
            raise

    def init_ssh(self) -> Dict[str, Any]:
        """初始化SSH"""
        try:
            public_key_result = self.kms_service.get_key(self.CLIENT_APPKEY, "code_id_ed25519_pub")
            private_key_result = self.kms_service.get_key(self.CLIENT_APPKEY, "code_id_ed25519")
            
            if not public_key_result.get("success") or not private_key_result.get("success"):
                raise Exception("获取SSH密钥失败")
                
            public_key = public_key_result.get("data")
            private_key = private_key_result.get("data")
            
            command = f'bash init_ssh.sh "{private_key}" "{public_key}"'
            process = subprocess.Popen(
                command,
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                timeout=60
            )
            stdout, stderr = process.communicate()
            
            self.logger.info("SSH初始化完成")
            return {
                "stdout": stdout,
                "stderr": stderr,
                "success": True
            }
            
        except Exception as e:
            self.logger.error(f"SSH初始化失败: {str(e)}")
            self.increment_error_count()
            return {"error": str(e), "success": False}

    def create_pr(self, url: str, params: Dict[str, Any], options: Optional[Dict[str, str]] = None) -> Union[str, int]:
        """创建PR（POST）"""
        try:
            header_info = self.get_token(options)
            url_with_params = f"{self.DOMAIN}{url}"
            
            response = requests.post(
                url_with_params,
                headers=header_info,
                json=params,
                timeout=30
            )
            response.raise_for_status()
            
            pr_id = response.json().get("id")
            self.logger.info(f"PR创建成功，ID: {pr_id}")
            return pr_id
            
        except requests.exceptions.HTTPError as e:
            if e.response.status_code == 409:
                errors = e.response.json().get("errors", [])
                if errors and errors[0].get("existingPullRequest", {}).get("id"):
                    existing_pr_id = errors[0]["existingPullRequest"]["id"]
                    self.logger.info(f"PR已存在，ID: {existing_pr_id}")
                    return existing_pr_id
                raise Exception(f"PR创建失败: {errors[0].get('message')}")
            raise
        except Exception as e:
            self.logger.error(f"创建PR失败: {str(e)}")
            self.increment_error_count()
            raise

    def fetch_pr_info(self, project: str, repo: str, pr_number: str, 
                     options: Optional[Dict[str, str]] = None) -> Dict[str, str]:
        """获取PR信息"""
        try:
            options = options or {}
            options.update({
                "project": project,
                "repo": repo,
                "id": pr_number
            })
            
            pr_info_response = self.pr_info_handler(options)
            
            from_branch = pr_info_response.get("fromRef", {}).get("displayId")
            to_branch = pr_info_response.get("toRef", {}).get("displayId")
            
            return {
                "project": project,
                "fromBranch": from_branch,
                "toBranch": to_branch
            }
            
        except Exception as e:
            self.logger.error(f"获取PR信息失败: {str(e)}")
            self.increment_error_count()
            return {
                "project": project,
                "fromBranch": None,
                "toBranch": None
            }

    def pr_info_handler(self, ctx) -> Dict[str, Any]:
        """处理PR信息请求"""
        try:
            # 兼容不同的参数传递方式
            if hasattr(ctx, 'args'):
                project = ctx.args.get('project')
                repo = ctx.args.get('repo')
                pr_id = ctx.args.get('id')
            elif hasattr(ctx, 'params'):
                project = ctx.params.get('project')
                repo = ctx.params.get('repo')
                pr_id = ctx.params.get('id')
            else:
                project = ctx.get('project')
                repo = ctx.get('repo')
                pr_id = ctx.get('id')

            # 真实API请求
            pr_url = f"{self.DOMAIN}/rest/api/2.0/projects/{project}/repos/{repo}/pull-requests/{pr_id}"
            headers = self.get_token(ctx)

            pr_resp = requests.get(pr_url, headers=headers, timeout=30)
            pr_resp.raise_for_status()
            
            return pr_resp.json()
            
        except Exception as e:
            self.logger.error(f"处理PR信息请求失败: {str(e)}")
            self.increment_error_count()
            return {
                'success': False,
                'error': str(e)
            }

    def get_pr_comments(self, project: str, repo: str, pr_id: str, 
                       options: Optional[Dict[str, str]] = None) -> Dict[str, Any]:
        """获取PR评论"""
        try:
            url = f"https://dev.sankuai.com/rest/api/5.0/projects/{project}/repos/{repo}/pull-requests/{pr_id}/assignments"
            params = {'states': 'open'}
            header_info = self.get_token(options)
            
            resp = requests.get(url, params=params, headers=header_info, timeout=30)
            resp.raise_for_status()
            
            return resp.json()
            
        except Exception as e:
            self.logger.error(f"获取PR评论失败: {str(e)}")
            self.increment_error_count()
            raise

    def get_all_pr_diffs(self, project: str, repo: str, pr_id: str, 
                        options: Optional[Dict[str, str]] = None) -> Dict[str, Any]:
        """获取指定PR的所有变更文件的diff内容"""
        try:
            # 1. 获取变更文件列表，处理分页
            changes_url = f"/rest/api/2.0/projects/{project}/repos/{repo}/pull-requests/{pr_id}/changes"
            all_files = []
            start = 0
            
            while True:
                params = {"start": start}
                header_info = self.get_token(options)
                url_with_params = f"{self.DOMAIN}{changes_url}"
                
                resp = requests.get(url_with_params, headers=header_info, params=params, timeout=30)
                resp.raise_for_status()
                
                data = resp.json()
                values = data.get("values", [])
                all_files.extend(values)
                
                if data.get("lastPage", True):
                    break
                    
                start = data.get("nextPageStart", 0)
                if not values or start == 0:
                    break

            # 2. 针对每个文件，获取diff内容
            diffs = {}
            for file_info in all_files:
                file_path = file_info.get("path", {}).get("toString")
                if not file_path:
                    continue
                    
                diff_url = f"/rest/api/2.0/projects/{project}/repos/{repo}/pull-requests/{pr_id}/diff/{file_path}"
                url_with_params = f"{self.DOMAIN}{diff_url}"
                
                try:
                    diff_resp = requests.get(url_with_params, headers=header_info, timeout=30)
                    diff_resp.raise_for_status()
                    diffs[file_path] = diff_resp.json()
                except Exception as e:
                    self.logger.warning(f"获取文件 {file_path} 的diff失败: {str(e)}")
                    diffs[file_path] = {"error": str(e)}
                    
            self.logger.info(f"获取PR diff完成，共 {len(diffs)} 个文件")
            return diffs
            
        except Exception as e:
            self.logger.error(f"获取PR diff失败: {str(e)}")
            self.increment_error_count()
            return {"success": False, "error": str(e)}
