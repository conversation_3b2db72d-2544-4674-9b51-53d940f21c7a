"""
MCP协议服务

按照第一阶段架构重构要求实现MCP协议集成
"""

import asyncio
import logging
from typing import Dict, Any, Optional, List
from services.base_service import BaseService
from config.base_config import MCPConfig

try:
    from mcp import ClientSession
    from mcp.client.sse import sse_client
    MCP_AVAILABLE = True
except ImportError:
    MCP_AVAILABLE = False
    logging.warning("MCP SDK not available. Install with: pip install mcp>=1.0.0")


class MCPService(BaseService):
    """MCP协议服务，负责与MCP Hub平台的连接和通信"""
    
    def __init__(self, config: MCPConfig):
        super().__init__("mcp_service", config.dict())
        self.mcp_config = config
        self.session: Optional[ClientSession] = None
        self.sse_ctx = None
        self.session_ctx = None
        self.available_tools: Dict[str, Any] = {}
        self._connection_healthy = False
        
    async def initialize(self) -> bool:
        """初始化MCP连接"""
        try:
            if not self.mcp_config.enabled:
                self.logger.info("MCP服务已禁用")
                return True

            if not MCP_AVAILABLE:
                self.logger.warning("MCP SDK不可用，服务将以禁用模式运行")
                return True  # 返回True，允许应用继续运行

            # 建立SSE连接
            try:
                self.sse_ctx = sse_client(url=self.mcp_config.client.sse_url)
                streams = await self.sse_ctx.__aenter__()
                self.session_ctx = ClientSession(*streams)
                self.session = await self.session_ctx.__aenter__()

                # 初始化MCP会话
                await self.session.initialize()
                self.logger.info("MCP会话已初始化")

                # 获取可用工具
                await self._load_available_tools()
                self._connection_healthy = True

                return True

            except asyncio.TimeoutError:
                self.logger.warning("MCP连接超时，服务将以禁用模式运行")
                await self._cleanup_partial_connection()
                return True  # 返回True，允许应用继续运行
            except ConnectionError as e:
                self.logger.warning(f"MCP连接失败: {str(e)}，服务将以禁用模式运行")
                await self._cleanup_partial_connection()
                return True  # 返回True，允许应用继续运行
            except Exception as e:
                self.logger.warning(f"MCP初始化失败: {str(e)}，服务将以禁用模式运行")
                await self._cleanup_partial_connection()
                return True  # 返回True，允许应用继续运行

        except Exception as e:
            self.logger.error(f"初始化MCP服务失败: {str(e)}")
            self._connection_healthy = False
            return True  # 返回True，允许应用继续运行

    async def _cleanup_partial_connection(self):
        """清理部分连接状态"""
        try:
            if self.session_ctx:
                await self.session_ctx.__aexit__(None, None, None)
                self.session_ctx = None
            if self.sse_ctx:
                await self.sse_ctx.__aexit__(None, None, None)
                self.sse_ctx = None
            self.session = None
            self._connection_healthy = False
        except Exception as e:
            self.logger.debug(f"清理部分连接时出现异常: {str(e)}")

    async def cleanup(self) -> bool:
        """清理MCP连接"""
        try:
            if self.session_ctx:
                await self.session_ctx.__aexit__(None, None, None)
            if self.sse_ctx:
                await self.sse_ctx.__aexit__(None, None, None)
            self._connection_healthy = False
            self.logger.info("MCP连接已关闭")
            return True
        except Exception as e:
            self.logger.error(f"清理MCP连接失败: {str(e)}")
            return False
    
    async def _load_available_tools(self):
        """加载可用工具列表"""
        try:
            if not self.session:
                return
                
            response = await self.session.list_tools()
            tools = response.tools
            
            self.available_tools = {tool.name: tool for tool in tools}
            
            self.logger.info(f"加载了 {len(self.available_tools)} 个MCP工具:")
            for tool_name, tool in self.available_tools.items():
                self.logger.info(f"- {tool_name}: {tool.description}")
                
        except Exception as e:
            self.logger.error(f"加载MCP工具失败: {str(e)}")
    
    async def call_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """调用MCP工具"""
        try:
            if not self.session:
                raise RuntimeError("MCP会话未初始化")
                
            if tool_name not in self.available_tools:
                raise ValueError(f"工具 {tool_name} 不可用")
            
            # 调用工具
            result = await self.session.call_tool(tool_name, arguments)
            
            return {
                "success": True,
                "data": result,
                "tool_name": tool_name
            }
            
        except Exception as e:
            self.logger.error(f"调用MCP工具 {tool_name} 失败: {str(e)}")
            self.increment_error_count()
            return {
                "success": False,
                "error": str(e),
                "tool_name": tool_name
            }
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        if not self.mcp_config.enabled:
            return {
                "status": "disabled",
                "message": "MCP服务已禁用"
            }
        
        if not MCP_AVAILABLE:
            return {
                "status": "error",
                "message": "MCP SDK不可用"
            }
            
        if not self.session:
            return {
                "status": "error", 
                "message": "MCP会话未初始化"
            }
        
        try:
            # 尝试列出工具来验证连接
            await self.session.list_tools()
            self._connection_healthy = True
            return {
                "status": "healthy",
                "tools_count": len(self.available_tools),
                "session_active": True,
                "connection_healthy": self._connection_healthy
            }
        except Exception as e:
            self._connection_healthy = False
            return {
                "status": "error",
                "message": f"MCP连接异常: {str(e)}",
                "connection_healthy": self._connection_healthy
            }
    
    def get_available_tools(self) -> List[str]:
        """获取可用工具列表"""
        return list(self.available_tools.keys())
    
    def get_tool_info(self, tool_name: str) -> Optional[Dict[str, Any]]:
        """获取工具信息"""
        tool = self.available_tools.get(tool_name)
        if tool:
            return {
                "name": tool.name,
                "description": tool.description,
                "input_schema": getattr(tool, 'input_schema', None)
            }
        return None
    
    def is_tool_available(self, tool_name: str) -> bool:
        """检查工具是否可用"""
        return tool_name in self.available_tools
    
    async def reconnect(self) -> bool:
        """重新连接MCP服务"""
        self.logger.info("尝试重新连接MCP服务")
        await self.cleanup()
        return await self.initialize()
    
    def get_agent_tool_mapping(self) -> Dict[str, str]:
        """获取Agent到MCP工具的映射"""
        return self.mcp_config.agent_service_mapping.copy()
    
    async def batch_call_tools(self, calls: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """批量调用MCP工具"""
        results = []
        
        for call in calls:
            tool_name = call.get("tool_name")
            arguments = call.get("arguments", {})
            
            if not tool_name:
                results.append({
                    "success": False,
                    "error": "Missing tool_name in call",
                    "tool_name": None
                })
                continue
            
            result = await self.call_tool(tool_name, arguments)
            results.append(result)
        
        return results
