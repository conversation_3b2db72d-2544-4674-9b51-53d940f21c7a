"""
代码分块服务

按照第一阶段架构重构要求，将原有ChunkService迁移到新架构
"""

import os
from typing import List, Dict, Any, Optional

from services.base_service import BaseService
from services.git_service import GitService

# 导入原有的工具类和常量
try:
    from consts.chunk_consts import (
        CONFIG_SUFFIXES, SUPPORTED_CODE_SUFFIXES, DEFAULT_CACHE_TTL,
        DEFAULT_MAX_LINES, DEFAULT_MAX_DEP_DEPTH, DEFAULT_CONTEXT_LINES
    )
    from utils.chunk_utils import (
        is_import_only_block, detect_source_roots, split_diff_by_file,
        extract_new_code_from_diff, split_diff_by_continuous_lines,
        find_best_chunk_for_comment, find_unique_key_for_dep
    )
    from core.cache.chunk_cache import ChunkCache
    from common.code_parsers import CodeParserFactory
    from core.analyzers.dependency_analyzer import DependencyAnalyzer
    from core.analyzers.cross_module_analyzer import CrossModuleAnalyzer
    CHUNK_UTILS_AVAILABLE = True
except ImportError:
    CHUNK_UTILS_AVAILABLE = False
    # 定义默认常量
    CONFIG_SUFFIXES = ('.json', '.yaml', '.yml', '.xml', '.properties', '.ini')
    SUPPORTED_CODE_SUFFIXES = ['.py', '.java', '.js', '.ts', '.cpp', '.c', '.go', '.rs']
    DEFAULT_CACHE_TTL = 3600
    DEFAULT_MAX_LINES = 100
    DEFAULT_MAX_DEP_DEPTH = 3
    DEFAULT_CONTEXT_LINES = 5


class ChunkService(BaseService):
    """代码分块服务，负责代码分析和分块处理"""

    def __init__(self, git_service: GitService, project: Optional[str], repo: Optional[str],
                 branch: Optional[str] = None, config: Optional[Dict[str, Any]] = None):
        super().__init__("chunk_service", config)
        self.git_service = git_service
        self.project = project
        self.repo = repo
        self.branch = branch or "master"
        # 初始化组件
        self.cache = None
        self.func_index = {}
        self.dependency_analyzer = None
        self.cross_module_analyzer = None
        self.chunk_utils_available = CHUNK_UTILS_AVAILABLE

    async def initialize(self) -> bool:
        """初始化代码分块服务"""
        try:
            if not self.chunk_utils_available:
                self.logger.warning("代码分块工具类不可用，将使用基础功能")
                return True

            # 初始化缓存
            self.cache = ChunkCache()
            # 初始化跨模块分析器
            try:
                # 从git_service获取项目根目录
                if hasattr(self.git_service, 'base_dir') and self.project and self.repo:
                    project_root = self.git_service.get_repo_path(self.project, self.repo)
                    self.cross_module_analyzer = CrossModuleAnalyzer(project_root)
                    self.logger.info("跨模块分析器初始化成功")
            except Exception as e:
                self.logger.warning(f"跨模块分析器初始化失败: {e}")
                self.cross_module_analyzer = None

            # 构建函数索引（只有在有效项目信息时）
            if (self.project and self.repo and
                self.project != "default" and self.repo != "default" and
                self.project != "unknown" and self.repo != "unknown"):
                try:
                    self.func_index = self.build_func_index()
                    self.dependency_analyzer = DependencyAnalyzer(
                        self.func_index,
                        cross_module_analyzer=self.cross_module_analyzer
                    )
                    self.logger.info(f"为项目 {self.project}/{self.repo} 构建了函数索引")
                except Exception as e:
                    self.logger.warning(f"构建函数索引失败: {str(e)}，将在需要时重试")
                    self.func_index = {}
            else:
                self.logger.info(f"项目信息无效 ({self.project}/{self.repo})，跳过函数索引构建")

            self.logger.info("代码分块服务初始化成功")
            return True
        except Exception as e:
            self.logger.error(f"代码分块服务初始化失败: {str(e)}")
            return False

    async def cleanup(self) -> bool:
        """清理资源"""
        try:
            # 清理缓存和索引
            self.cache = None
            self.func_index = {}
            self.dependency_analyzer = None
            self.cross_module_analyzer = None
            self.logger.info("代码分块服务资源清理完成")
            return True
        except Exception as e:
            self.logger.error(f"代码分块服务资源清理失败: {str(e)}")
            return False

    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            # 检查依赖服务
            if not self.git_service:
                return {
                    "status": "error",
                    "message": "Git服务不可用"
                }

            # 检查Git服务健康状态
            git_health = await self.git_service.health_check()
            if git_health.get("status") != "healthy":
                return {
                    "status": "degraded",
                    "message": "Git服务异常",
                    "git_status": git_health.get("status")
                }

            # 检查工具类可用性
            if not self.chunk_utils_available:
                return {
                    "status": "degraded",
                    "message": "代码分块工具类不可用，功能受限"
                }

            return {
                "status": "healthy",
                "message": "代码分块服务正常",
                "project": self.project,
                "repo": self.repo,
                "branch": self.branch,
                "func_index_size": len(self.func_index),
                "utils_available": self.chunk_utils_available
            }
        except Exception as e:
            return {
                "status": "error",
                "message": f"健康检查异常: {str(e)}"
            }

    def get_core_code_files(self, suffixes: List[str]) -> List[str]:
        """获取核心代码文件列表"""
        try:
            if not self.project or not self.repo:
                raise ValueError("项目或仓库信息缺失")
            files = self.git_service.list_repo_files(
                self.project, self.repo, branch=self.branch, suffixes=suffixes
            )
            self.logger.info(f"找到 {len(files)} 个匹配的代码文件")
            return files
        except Exception as e:
            self.logger.error(f"获取代码文件列表失败: {str(e)}")
            self.increment_error_count()
            return []

    def chunk_code_file(self, file_path: str) -> List[Dict[str, Any]]:
        """对单个代码文件进行分块"""
        try:
            if not self.project or not self.repo:
                raise ValueError("项目或仓库信息缺失")

            content = self.git_service.read_file(self.project, self.repo, file_path)
            if not content or not content.strip():
                self.logger.warning(f"文件内容为空: {file_path}")
                return []

            if not self.chunk_utils_available:
                # 基础分块功能
                return self._basic_chunk_file(content, file_path)

            # 获取对应的解析器
            parser = CodeParserFactory.get_parser(file_path)
            if not parser:
                self.logger.warning(f"不支持的文件类型: {file_path}")
                return []

            # 解析代码块
            chunks = parser.parse_chunks(content, file_path, resolve_code=True)

            # 标准化依赖关系
            self._standardize_dependencies(chunks)

            # 过滤纯导入依赖块
            filtered_chunks = [c for c in chunks if not is_import_only_block(c['content'])]
            self.logger.info(f"文件 {file_path} 分块完成，生成 {len(filtered_chunks)} 个块")
            return filtered_chunks

        except Exception as e:
            self.logger.error(f"文件分块失败: {file_path} - {str(e)}")
            self.increment_error_count()
            return []

    def chunk_all_files(self, suffixes: List[str], resolve_code: bool = True) -> List[Dict[str, Any]]:
        """对所有文件进行分块"""
        try:
            code_files = self.get_core_code_files(suffixes)
            all_chunks = []

            for file_path in code_files:
                try:
                    if not self.chunk_utils_available:
                        # 基础分块
                        content = self.git_service.read_file(self.project, self.repo, file_path)
                        if content:
                            chunks = self._basic_chunk_file(content, file_path)
                            all_chunks.extend(chunks)
                        continue

                    parser = CodeParserFactory.get_parser(file_path)
                    if not parser:
                        continue

                    content = self.git_service.read_file(self.project, self.repo, file_path)
                    if not content:
                        continue

                    chunks = parser.parse_chunks(content, file_path, resolve_code=resolve_code)
                    if chunks:
                        all_chunks.extend(chunks)

                except Exception as e:
                    self.logger.warning(f"处理文件 {file_path} 时出错: {e}")
                    continue

            self.logger.info(f"批量分块完成，共生成 {len(all_chunks)} 个块")
            return all_chunks

        except Exception as e:
            self.logger.error(f"批量文件分块失败: {str(e)}")
            self.increment_error_count()
            return []

    def build_func_index(self, suffixes: Optional[List[str]] = None, use_cache: bool = True,
                        cache_ttl: int = DEFAULT_CACHE_TTL) -> Dict[str, Any]:
        """构建项目函数索引"""
        try:
            if not suffixes:
                suffixes = SUPPORTED_CODE_SUFFIXES

            if not self.chunk_utils_available:
                self.logger.warning("工具类不可用，返回空索引")
                return {}

            # 尝试从缓存加载
            cache_key = None
            if use_cache and self.cache:
                cache_key = self.cache.get_cache_key(self.project, self.repo, self.branch, suffixes)
                cached_data = self.cache.load_cache(cache_key, cache_ttl)
                if cached_data:
                    self.func_index = cached_data
                    self.logger.info(f"从缓存加载函数索引，共 {len(cached_data)} 个函数")
                    return self.func_index

            self.logger.info(f"开始构建函数索引，支持的文件类型: {suffixes}")

            # 获取所有文件并检测源码根目录
            all_files = self.get_core_code_files(suffixes)
            if not all_files:
                self.logger.warning("未找到匹配的代码文件")
                return {}

            roots = detect_source_roots(all_files)
            self.logger.info(f"检测到源码根目录: {roots}")

            # 过滤源码文件
            src_files = self._filter_source_files(all_files, roots)
            self.logger.info(f"最终使用 {len(src_files)} 个源码文件构建索引")

            # 分块处理
            all_chunks = self._process_source_files(src_files)
            self.logger.info(f"获取到 {len(all_chunks)} 个分块")

            # 构建函数索引
            func_index = self._build_func_index_from_chunks(all_chunks)

            # 补全跨文件依赖
            self._complete_cross_file_dependencies(func_index)

            # 缓存结果
            if use_cache and self.cache and cache_key:
                self.cache.save_cache(cache_key, func_index)

            self.func_index = func_index
            self.logger.info(f"函数索引构建完成，共 {len(func_index)} 个唯一名")

            return func_index

        except Exception as e:
            self.logger.error(f"构建函数索引失败: {str(e)}")
            self.increment_error_count()
            return {}

    def chunk_diff_code(self, diff_content: str) -> List[Dict[str, Any]]:
        """对diff内容进行切块"""
        try:
            if not diff_content or not diff_content.strip():
                self.logger.warning("diff内容为空")
                return []

            if not self.chunk_utils_available:
                # 基础diff处理
                return self._basic_chunk_diff(diff_content)

            self.logger.info(f"开始处理diff内容，长度: {len(diff_content)}")
            file_blocks = split_diff_by_file(diff_content)
            self.logger.info(f"分割出 {len(file_blocks)} 个文件块")

            all_chunks = []
            for file_path, diff_lines in file_blocks:
                # 跳过配置文件
                if file_path.endswith(CONFIG_SUFFIXES):
                    self.logger.debug(f"配置文件 {file_path} 跳过CR分块")
                    continue

                # 检查文件类型
                parser = CodeParserFactory.get_parser(file_path)
                if not parser:
                    self.logger.debug(f"文件 {file_path} 不是支持的语言类型，跳过")
                    continue

                # 处理diff文件
                chunks = self._process_diff_file(file_path, diff_lines, parser)
                all_chunks.extend(chunks)

            self.logger.info(f"diff分块完成，总共生成 {len(all_chunks)} 个块")
            return all_chunks

        except Exception as e:
            self.logger.error(f"diff分块失败: {str(e)}")
            self.increment_error_count()
            return []

    # ==================== 私有方法 ====================

    def _basic_chunk_file(self, content: str, file_path: str) -> List[Dict[str, Any]]:
        """基础文件分块功能（当工具类不可用时）"""
        # 尝试使用解析器进行更精确的分块
        try:
            from core.parsers import CodeParserFactory
            parser = CodeParserFactory.get_parser(file_path)
            if parser:
                chunks = parser.parse_chunks(content, file_path, resolve_code=True)
                if chunks:
                    self.logger.debug(f"使用解析器分块 {file_path}: {len(chunks)} 个块")
                    return chunks
        except Exception as e:
            self.logger.debug(f"解析器分块失败，使用基础分块: {e}")

        # 基础分块逻辑
        lines = content.splitlines()
        chunks = []

        # 简单按函数/类分块
        current_chunk = []
        current_start = 1
        indent_level = 0

        for i, line in enumerate(lines, 1):
            stripped = line.strip()

            # 检测函数或类定义
            if (stripped.startswith('def ') or stripped.startswith('class ') or
                stripped.startswith('function ') or stripped.startswith('public ') or
                stripped.startswith('private ') or stripped.startswith('protected ')):

                # 保存之前的块
                if current_chunk:
                    # 尝试从内容中提取函数/类名
                    chunk_type, chunk_name = self._extract_name_from_chunk(current_chunk, file_path)
                    chunks.append({
                        'type': chunk_type,
                        'name': chunk_name,
                        'file': file_path,
                        'start_line': current_start,
                        'end_line': i - 1,
                        'content': '\n'.join(current_chunk),
                        'upstream': [],
                        'downstream': []
                    })

                # 开始新块
                current_chunk = [line]
                current_start = i
                indent_level = len(line) - len(line.lstrip())
            else:
                current_chunk.append(line)

        # 保存最后一个块
        if current_chunk:
            # 尝试从内容中提取函数/类名
            chunk_type, chunk_name = self._extract_name_from_chunk(current_chunk, file_path)
            chunks.append({
                'type': chunk_type,
                'name': chunk_name,
                'file': file_path,
                'start_line': current_start,
                'end_line': len(lines),
                'content': '\n'.join(current_chunk),
                'upstream': [],
                'downstream': []
            })

        return chunks

    def _extract_name_from_chunk(self, chunk_lines: List[str], file_path: str) -> tuple:
        """从代码块中提取函数/类名和类型"""
        import re

        for line in chunk_lines:
            stripped = line.strip()

            # Python函数
            if stripped.startswith('def '):
                match = re.match(r'def\s+([a-zA-Z_][a-zA-Z0-9_]*)', stripped)
                if match:
                    return 'function', match.group(1)

            # Python类
            elif stripped.startswith('class '):
                match = re.match(r'class\s+([a-zA-Z_][a-zA-Z0-9_]*)', stripped)
                if match:
                    return 'class', match.group(1)

            # JavaScript/TypeScript函数
            elif stripped.startswith('function '):
                match = re.match(r'function\s+([a-zA-Z_][a-zA-Z0-9_]*)', stripped)
                if match:
                    return 'function', match.group(1)

            # Java方法
            elif any(stripped.startswith(modifier) for modifier in ['public ', 'private ', 'protected ']):
                # 尝试匹配Java方法
                match = re.search(r'(public|private|protected)\s+.*?\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\(', stripped)
                if match:
                    method_name = match.group(2)
                    # 如果在Java文件中，尝试获取类名
                    if file_path.endswith('.java'):
                        class_name = self._extract_java_class_name(file_path)
                        if class_name:
                            return 'method', f"{class_name}.{method_name}"
                    return 'method', method_name

        # 如果没有找到特定的函数/类定义，返回通用块
        return 'code_block', f'block_{chunk_lines[0] if chunk_lines else "unknown"}'

    def _extract_java_class_name(self, file_path: str) -> str:
        """从Java文件路径中提取类名"""
        import os
        # 从文件名中提取类名（去掉.java后缀）
        base_name = os.path.basename(file_path)
        if base_name.endswith('.java'):
            return base_name[:-5]  # 去掉.java后缀
        return base_name

    def _basic_chunk_diff(self, diff_content: str) -> List[Dict[str, Any]]:
        """基础diff分块功能"""
        lines = diff_content.splitlines()
        chunks = []
        current_file = None
        current_content = []
        for line in lines:
            if line.startswith('+++'):
                # 新文件开始
                if current_file and current_content:
                    chunks.append({
                        'type': 'diff_block',
                        'name': f'diff_{current_file}',
                        'file': current_file,
                        'content': '\n'.join(current_content),
                        'from_diff': True
                    })
                current_file = line[4:].strip()
                if current_file.startswith('b/'):
                    current_file = current_file[2:]
                current_content = []
            elif line.startswith('+') and not line.startswith('+++'):
                # 新增的行
                current_content.append(line[1:])
        # 保存最后一个文件
        if current_file and current_content:
            chunks.append({
                'type': 'diff_block',
                'name': f'diff_{current_file}',
                'file': current_file,
                'content': '\n'.join(current_content),
                'from_diff': True
            })
        return chunks

    def _standardize_dependencies(self, chunks: List[Dict]):
        """标准化依赖关系为唯一名"""
        if not self.chunk_utils_available:
            return
        for chunk in chunks:
            for dep_type in ['upstream', 'downstream']:
                if dep_type in chunk:
                    new_deps = []
                    for dep in chunk[dep_type]:
                        unique_keys = find_unique_key_for_dep(self.func_index or {}, dep)
                        if unique_keys:
                            new_deps.extend(unique_keys)
                        else:
                            new_deps.append(dep)
                    chunk[dep_type] = new_deps

    def _filter_source_files(self, all_files: List[str], roots: List[str]) -> List[str]:
        """过滤源码文件"""
        if not roots:
            return all_files
        src_files = []
        for f in all_files:
            for root in roots:
                if f.startswith(root + os.sep) or f == root:
                    src_files.append(f)
                    break

        # 如果过滤后文件太少，使用所有文件
        if len(src_files) < len(all_files) * 0.5:
            self.logger.warning("根目录过滤掉了太多文件，使用所有文件进行索引构建")
            src_files = all_files

        return src_files

    def _process_source_files(self, src_files: List[str]) -> List[Dict]:
        """处理源码文件，进行分块"""
        all_chunks = []
        for file_path in src_files:
            try:
                if not self.chunk_utils_available:
                    content = self.git_service.read_file(self.project, self.repo, file_path)
                    if content:
                        chunks = self._basic_chunk_file(content, file_path)
                        all_chunks.extend(chunks)
                    continue

                parser = CodeParserFactory.get_parser(file_path)
                if not parser:
                    continue

                file_content = self.git_service.read_file(self.project, self.repo, file_path)
                if not file_content:
                    continue

                chunks = parser.parse_chunks(file_content, file_path, resolve_code=True)
                if chunks:
                    all_chunks.extend(chunks)
            except Exception as e:
                self.logger.warning(f"处理文件 {file_path} 时出错: {e}")
                continue

        return all_chunks

    def _build_func_index_from_chunks(self, all_chunks: List[Dict]) -> Dict:
        """从分块构建函数索引"""
        func_index = {}

        def add_to_index(index, key, chunk):
            if key in index:
                if isinstance(index[key], list):
                    index[key].append(chunk)
                else:
                    index[key] = [index[key], chunk]
            else:
                index[key] = chunk

        for chunk in all_chunks:
            if chunk['type'] in ('function', 'method', 'constructor', 'arrow', 'lambda', 'class'):
                file_path = chunk['file']
                name = chunk['name']
                unique_name = f"{file_path}:{name}"
                simple_name = f"{file_path}:{name.split('.')[-1]}"
                global_name = f"global:{name}"

                add_to_index(func_index, unique_name, chunk)
                add_to_index(func_index, global_name, chunk)
                add_to_index(func_index, simple_name, chunk)

        return func_index

    def _complete_cross_file_dependencies(self, func_index: Dict):
        """补全跨文件依赖"""
        if not self.chunk_utils_available:
            return
        self.logger.info("开始补全跨文件依赖...")
        # 这里可以添加跨文件依赖补全逻辑
        self.logger.info("跨文件依赖补全完成")

    def _process_diff_file(self, file_path: str, diff_lines: List[str], parser) -> List[Dict]:
        """处理单个diff文件"""
        self.logger.debug(f"处理diff文件: {file_path}")

        if not self.chunk_utils_available:
            # 基础处理
            code_lines = [line[1:] for line in diff_lines if line.startswith('+') and not line.startswith('+++')]
            code = '\n'.join(code_lines)
            return self._basic_chunk_file(code, file_path)

        code = extract_new_code_from_diff(diff_lines)
        if not code.strip():
            self.logger.debug(f"文件 {file_path} 提取的代码为空，跳过")
            return []

        # 尝试获取完整文件内容进行分析
        try:
            full_content = self.git_service.read_file(self.project, self.repo, file_path)
            if full_content and code.strip() in full_content:
                chunks = parser.parse_chunks(full_content, file_path, resolve_code=True)
                # 标记包含diff代码的块
                for c in chunks:
                    c['from_diff'] = code.strip() in c['content']
            else:
                chunks = parser.parse_chunks(code, file_path, resolve_code=True)
                for c in chunks:
                    c['from_diff'] = True
        except Exception as e:
            self.logger.debug(f"读取完整文件失败: {str(e)}，使用diff代码进行分析")
            chunks = parser.parse_chunks(code, file_path, resolve_code=True)
            for c in chunks:
                c['from_diff'] = True

        # 过滤纯导入依赖块
        filtered_chunks = [c for c in chunks if not is_import_only_block(c['content'])]

        return filtered_chunks

    def enrich_diff_segments_with_context(self, diff_content: str, max_dep_depth: int = None) -> List[Dict]:
        """
        基于diff内容，返回每个片段的所有覆盖代码块、递归正向依赖、反向依赖

        Args:
            diff_content: diff内容
            max_dep_depth: 最大依赖深度

        Returns:
            List[Dict]: 增强后的代码片段列表
        """
        if max_dep_depth is None:
            max_dep_depth = DEFAULT_MAX_DEP_DEPTH if self.chunk_utils_available else 3

        self.logger.info("开始进行diff上下文增强分析...")

        try:
            # 应用依赖分析增强 - 直接集成到主流程中
            self._apply_dependency_enhancement()

            # 确保函数索引已构建
            if not self.func_index:
                self.logger.info("函数索引为空，开始构建...")
                self.func_index = self.build_func_index()

            self.logger.info(f"函数索引构建完成，共 {len(self.func_index)} 个函数")

            if not self.chunk_utils_available:
                # 基础实现：简单分段
                segments = self._basic_enrich_diff_segments(diff_content)
            else:
                # 完整实现：使用工具函数
                segments = self._full_enrich_diff_segments(diff_content, max_dep_depth)

            # 应用增强的依赖解析到每个片段
            segments = self._enhance_segments_dependencies(segments)

            # 输出依赖分析日志
            try:
                from utils.cr_log_utils import CRLogFormatter
                CRLogFormatter.format_dependency_analysis(segments, self.logger)
            except Exception as e:
                self.logger.warning(f"输出依赖分析日志失败: {str(e)}")

            return segments

        except Exception as e:
            self.logger.error(f"diff上下文增强分析失败: {str(e)}")
            # 降级到基础实现
            return self._basic_enrich_diff_segments(diff_content)

    def _apply_dependency_enhancement(self):
        """应用依赖分析增强逻辑"""
        try:
            # 确保项目信息有效
            if (not self.project or not self.repo or
                self.project in ("default", "unknown") or
                self.repo in ("default", "unknown")):

                self.logger.info("项目信息无效，使用增强默认值")
                self.project = "enhanced_project"
                self.repo = "enhanced_repo"

            # 如果函数索引为空，尝试强制构建
            if not self.func_index:
                self.logger.info("强制构建函数索引...")
                self.func_index = self.build_func_index(use_cache=False)

                # 如果仍然为空，使用手动构建
                if not self.func_index:
                    self.logger.info("使用手动构建函数索引...")
                    self.func_index = self._manual_build_func_index()

        except Exception as e:
            self.logger.warning(f"应用依赖分析增强失败: {e}")

    def _manual_build_func_index(self) -> Dict[str, Any]:
        """手动构建函数索引"""
        func_index = {}

        try:
            # 获取所有代码文件
            suffixes = ['.py', '.java', '.js', '.ts']
            files = self.get_core_code_files(suffixes)

            self.logger.info(f"手动构建: 找到 {len(files)} 个代码文件")

            for file_path in files:
                try:
                    chunks = self.chunk_code_file(file_path)

                    for chunk in chunks:
                        if 'name' in chunk and chunk['name']:
                            # 创建唯一键
                            unique_key = f"{file_path}:{chunk['name']}"
                            func_index[unique_key] = chunk

                            # 同时创建全局键（如果函数名唯一）
                            global_key = f"global:{chunk['name']}"
                            if global_key not in func_index:
                                func_index[global_key] = chunk

                    self.logger.debug(f"手动构建: 处理文件 {file_path}: {len(chunks)} 个块")

                except Exception as e:
                    self.logger.warning(f"手动构建: 处理文件 {file_path} 失败: {e}")
                    continue

            self.logger.info(f"手动构建完成，共 {len(func_index)} 个函数")
            return func_index

        except Exception as e:
            self.logger.error(f"手动构建函数索引失败: {e}")
            return {}

    def _enhance_segments_dependencies(self, segments: List[Dict]) -> List[Dict]:
        """增强片段的依赖信息"""
        enhanced_segments = []

        for segment in segments:
            try:
                enhanced_segment = self._enhance_single_segment_dependencies(segment)
                enhanced_segments.append(enhanced_segment)
            except Exception as e:
                self.logger.warning(f"增强片段依赖失败: {e}")
                enhanced_segments.append(segment)

        return enhanced_segments

    def _enhance_single_segment_dependencies(self, segment: Dict) -> Dict:
        """增强单个片段的依赖信息"""
        # 如果已有依赖信息且不为空，直接返回
        if (segment.get('upstream') or segment.get('downstream') or
            segment.get('upstream_code') or segment.get('downstream_code')):
            return segment

        # 尝试从代码内容中提取依赖
        content = segment.get('content', '')
        file_path = segment.get('file', '')

        if content and file_path.endswith('.py'):
            upstream, downstream = self._extract_python_dependencies(content, file_path)
            segment['upstream'] = upstream
            segment['downstream'] = downstream

            # 尝试获取依赖代码
            segment['upstream_code'] = self._get_dependency_code(upstream)
            segment['downstream_code'] = self._get_dependency_code(downstream)

            self.logger.debug(f"增强片段依赖: {file_path} - 上游{len(upstream)}个, 下游{len(downstream)}个")
        elif content and file_path.endswith('.java'):
            upstream, downstream = self._extract_java_dependencies(content, file_path)
            segment['upstream'] = upstream
            segment['downstream'] = downstream

            # 尝试获取依赖代码
            segment['upstream_code'] = self._get_dependency_code(upstream)
            segment['downstream_code'] = self._get_dependency_code(downstream)

            self.logger.debug(f"增强片段依赖: {file_path} - 上游{len(upstream)}个, 下游{len(downstream)}个")

        return segment

    def _extract_python_dependencies(self, content: str, file_path: str) -> tuple:
        """从Python代码中提取依赖关系"""
        import ast
        import re

        upstream = []
        downstream = []

        try:
            # 使用正则表达式提取函数调用
            function_calls = re.findall(r'\b([a-zA-Z_][a-zA-Z0-9_]*)\s*$', content)
            upstream.extend(function_calls)

            # 使用AST解析更精确的依赖
            try:
                tree = ast.parse(content)
                for node in ast.walk(tree):
                    if isinstance(node, ast.Call):
                        if isinstance(node.func, ast.Name):
                            upstream.append(node.func.id)
                        elif isinstance(node.func, ast.Attribute):
                            upstream.append(node.func.attr)
            except:
                pass

            # 去重
            upstream = list(set(upstream))

            # 过滤内置函数和常见库函数
            builtin_functions = {
                'print', 'len', 'str', 'int', 'float', 'list', 'dict', 'set', 'tuple',
                'range', 'enumerate', 'zip', 'map', 'filter', 'sorted', 'reversed',
                'open', 'isinstance', 'hasattr', 'getattr', 'setattr', 'delattr'
            }
            upstream = [func for func in upstream if func not in builtin_functions]

            self.logger.debug(f"提取依赖: {file_path} - 原始{len(function_calls)}个, 过滤后{len(upstream)}个")

        except Exception as e:
            self.logger.debug(f"提取依赖失败: {e}")

        return upstream, downstream

    def _extract_java_dependencies(self, content: str, file_path: str) -> tuple:
        """从Java代码中提取依赖关系"""
        import re

        upstream = []
        downstream = []

        try:
            # 1. 直接方法调用: methodName()
            direct_calls = re.findall(r'(\w+)\s*\(', content)
            for call in direct_calls:
                if call not in ['if', 'for', 'while', 'switch', 'catch', 'return', 'new', 'super', 'this']:
                    upstream.append(call)

            # 2. 对象方法调用: obj.method()
            object_calls = re.findall(r'(\w+)\.(\w+)\s*\(', content)
            for obj, method in object_calls:
                if obj != 'this' and obj != 'super':
                    upstream.append(f"{obj}.{method}")

            # 3. 类实例化: new ClassName()
            new_calls = re.findall(r'new\s+(\w+)\s*\(', content)
            for class_name in new_calls:
                upstream.append(f"{class_name}.__init__")

            # 4. this.method() 调用
            this_calls = re.findall(r'this\.(\w+)\s*\(', content)
            for method in this_calls:
                upstream.append(method)

            # 5. 静态方法调用: ClassName.staticMethod()
            static_calls = re.findall(r'([A-Z]\w+)\.(\w+)\s*\(', content)
            for class_name, method in static_calls:
                upstream.append(f"{class_name}.{method}")

            # 去重
            upstream = list(set(upstream))

            # 过滤Java关键字和常见方法
            java_keywords = {
                'System', 'String', 'Object', 'Class', 'Integer', 'Boolean', 'Double', 'Float',
                'println', 'print', 'equals', 'hashCode', 'toString', 'valueOf', 'length'
            }
            upstream = [func for func in upstream if not any(keyword in func for keyword in java_keywords)]

            self.logger.debug(f"提取Java依赖: {file_path} - 过滤后{len(upstream)}个")

        except Exception as e:
            self.logger.debug(f"提取Java依赖失败: {e}")

        return upstream, downstream

    def _get_dependency_code(self, dependencies: List[str]) -> Dict[str, str]:
        """获取依赖代码"""
        dependency_code = {}
        if not self.func_index:
            self.logger.debug("函数索引为空，无法获取依赖代码")
            return dependency_code

        for dep in dependencies:
            found = False

            # 尝试多种键格式
            possible_keys = [
                dep,  # 直接匹配
                f"global:{dep}",  # 全局函数
            ]

            # 添加文件特定的键
            for key in self.func_index.keys():
                if key.endswith(f":{dep}") or key.endswith(f".{dep}"):
                    possible_keys.append(key)

            # 对于Java方法，尝试更多匹配模式
            if '.' in dep:
                # 类.方法 格式
                class_name, method_name = dep.rsplit('.', 1)
                possible_keys.extend([
                    method_name,  # 只用方法名
                    f"{class_name}.{method_name}",  # 完整名称
                    f"global:{method_name}",  # 全局方法名
                ])

            # 尝试模糊匹配
            for key in self.func_index.keys():
                if dep in key or key.endswith(dep):
                    possible_keys.append(key)

            # 查找匹配的代码
            for key in possible_keys:
                if key in self.func_index:
                    chunk = self.func_index[key]
                    content = None

                    if isinstance(chunk, dict):
                        content = chunk.get('content', '')
                    elif isinstance(chunk, list) and chunk:
                        # 取第一个匹配项
                        first_item = chunk[0]
                        if isinstance(first_item, dict):
                            content = first_item.get('content', '')

                    if content and content.strip():
                        dependency_code[dep] = content
                        found = True
                        break

            if not found:
                self.logger.debug(f"未找到依赖 {dep} 的代码，尝试的键: {possible_keys[:3]}...")

        self.logger.debug(f"获取依赖代码: {len(dependencies)}个依赖, {len(dependency_code)}个有代码")
        return dependency_code
    def split_code_diff_into_segments(self, diff_content: str, max_lines: int = None) -> List[Dict]:
        """按文件和最大行数将diff内容切分为多个片段"""
        if max_lines is None:
            max_lines = DEFAULT_MAX_LINES if self.chunk_utils_available else 50

        if not self.chunk_utils_available:
            # 基础实现
            lines = diff_content.split('\n')
            segments = []
            for i in range(0, len(lines), max_lines):
                segment_lines = lines[i:i + max_lines]
                segments.append({
                    "codePosition": f"diff:{i + 1}-{i + len(segment_lines)}",
                    "content": '\n'.join(segment_lines)
                })
            return segments

        # 使用工具函数实现
        file_blocks = split_diff_by_file(diff_content)
        segments = []

        for file_path, diff_lines in file_blocks:
            code = extract_new_code_from_diff(diff_lines)
            if not code.strip():
                continue

            code_lines = code.splitlines()
            for i in range(0, len(code_lines), max_lines):
                segment_lines = code_lines[i:i + max_lines]
                segment_content = '\n'.join(segment_lines)
                code_position = f"{file_path}:{i + 1}-{i + len(segment_lines)}"
                segments.append({
                    "codePosition": code_position,
                    "content": segment_content
                })

        return segments

    def _basic_enrich_diff_segments(self, diff_content: str) -> List[Dict]:
        """基础的diff片段增强实现"""
        self.logger.info("使用基础实现进行diff片段增强")

        # 简单按行分割
        lines = diff_content.split('\n')
        segments = []

        current_file = None
        current_content = []
        line_num = 0

        for line in lines:
            line_num += 1

            # 检测文件头
            if line.startswith('diff --git'):
                if current_file and current_content:
                    segment = self._create_enhanced_segment(current_file, current_content)
                    segments.append(segment)

                # 提取文件名
                parts = line.split()
                if len(parts) >= 4:
                    current_file = parts[3][2:]  # 去掉 "b/" 前缀
                current_content = []

            elif current_file and (line.startswith('+') or line.startswith('-') or line.startswith(' ')):
                # 添加代码行（去掉diff标记）
                code_line = line[1:] if len(line) > 0 else ''
                current_content.append(code_line)

        # 处理最后一个文件
        if current_file and current_content:
            segment = self._create_enhanced_segment(current_file, current_content)
            segments.append(segment)

        self.logger.info(f"基础实现生成了 {len(segments)} 个片段")
        return segments

    def _create_enhanced_segment(self, file_path: str, content_lines: List[str]) -> Dict:
        """创建增强的代码片段"""
        content = '\n'.join(content_lines)

        # 清理文件路径
        clean_file_path = self._clean_file_path(file_path)

        # 检测文件类型
        file_type = self._detect_file_type(clean_file_path)

        # 基础片段信息
        segment = {
            'file': clean_file_path,
            'file_path': clean_file_path,  # 保持兼容性
            'type': file_type,  # 添加文件类型
            'content': content,
            'start_line': 1,
            'end_line': len(content_lines),
            'upstream': [],
            'downstream': [],
            'upstream_code': {},
            'downstream_code': {}
        }

        # 尝试进行基础的依赖分析
        try:
            # 如果有函数索引，尝试分析依赖
            if self.func_index:
                # 使用基础分块分析代码
                chunks = self._basic_chunk_file(content, file_path)
                if chunks:
                    # 取第一个有效的代码块
                    main_chunk = chunks[0]
                    segment['upstream'] = main_chunk.get('upstream', [])
                    segment['downstream'] = main_chunk.get('downstream', [])

                    # 确保依赖分析器已初始化
                    if not self.dependency_analyzer and self.chunk_utils_available:
                        try:
                            self.dependency_analyzer = DependencyAnalyzer(
                                self.func_index,
                                cross_module_analyzer=self.cross_module_analyzer
                            )
                            self.logger.debug("依赖分析器初始化成功")
                        except Exception as e:
                            self.logger.debug(f"依赖分析器初始化失败: {str(e)}")

                    # 尝试获取依赖代码
                    if self.dependency_analyzer:
                        try:
                            upstream_code = self.dependency_analyzer.collect_multi_level_deps(
                                main_chunk, 'upstream', max_depth=2
                            )
                            downstream_code = self.dependency_analyzer.collect_multi_level_deps(
                                main_chunk, 'downstream', max_depth=2
                            )
                            segment['upstream_code'] = upstream_code
                            segment['downstream_code'] = downstream_code

                            self.logger.debug(f"片段 {file_path} 依赖分析: 上游{len(upstream_code)}个, 下游{len(downstream_code)}个")
                        except Exception as dep_e:
                            self.logger.debug(f"依赖代码收集失败: {str(dep_e)}")
                    else:
                        # 使用基础的依赖查找
                        self._collect_basic_dependencies(segment, main_chunk)

        except Exception as e:
            self.logger.debug(f"基础依赖分析失败: {str(e)}")

        return segment

    def _collect_basic_dependencies(self, segment: Dict, main_chunk: Dict):
        """收集基础依赖信息"""
        try:
            # 基础的依赖代码收集
            upstream_deps = main_chunk.get('upstream', [])
            downstream_deps = main_chunk.get('downstream', [])

            upstream_code = {}
            downstream_code = {}

            # 在函数索引中查找依赖代码
            for dep in upstream_deps[:3]:  # 限制数量
                if dep in self.func_index:
                    dep_data = self.func_index[dep]
                    if isinstance(dep_data, dict):
                        content = dep_data.get('content', '')
                        if content:
                            upstream_code[dep] = content
                    elif isinstance(dep_data, list) and dep_data:
                        # 取第一个匹配项
                        first_item = dep_data[0]
                        if isinstance(first_item, dict):
                            content = first_item.get('content', '')
                            if content:
                                upstream_code[dep] = content

            for dep in downstream_deps[:3]:  # 限制数量
                if dep in self.func_index:
                    dep_data = self.func_index[dep]
                    if isinstance(dep_data, dict):
                        content = dep_data.get('content', '')
                        if content:
                            downstream_code[dep] = content
                    elif isinstance(dep_data, list) and dep_data:
                        # 取第一个匹配项
                        first_item = dep_data[0]
                        if isinstance(first_item, dict):
                            content = first_item.get('content', '')
                            if content:
                                downstream_code[dep] = content

            segment['upstream_code'] = upstream_code
            segment['downstream_code'] = downstream_code

            self.logger.debug(f"基础依赖收集: 上游{len(upstream_code)}个, 下游{len(downstream_code)}个")

        except Exception as e:
            self.logger.debug(f"基础依赖收集失败: {str(e)}")

    def _full_enrich_diff_segments(self, diff_content: str, max_dep_depth: int) -> List[Dict]:
        """完整的diff片段增强实现（使用工具函数）"""
        self.logger.info("使用完整实现进行diff片段增强")

        try:
            # 分析diff文件
            file_blocks = split_diff_by_file(diff_content)
            self.logger.info(f"分割出 {len(file_blocks)} 个文件块")

            # 收集所有覆盖的代码块
            all_covered_chunks, diff_segment_infos = self._collect_covered_chunks(file_blocks)

            # 去重并生成CR任务
            unique_chunks = self._deduplicate_chunks(all_covered_chunks)
            segments = self._generate_cr_tasks(unique_chunks, diff_segment_infos, max_dep_depth)

            # 处理未命中任何块的diff
            segments.extend(self._handle_unmatched_diffs(diff_segment_infos))

            self.logger.info(f"完整实现生成了 {len(segments)} 个片段")
            return segments

        except Exception as e:
            self.logger.error(f"完整实现失败，降级到基础实现: {str(e)}")
            return self._basic_enrich_diff_segments(diff_content)

    def _collect_covered_chunks(self, file_blocks: List[tuple]) -> tuple:
        """收集所有被diff覆盖的代码块"""
        all_covered_chunks = []
        diff_segment_infos = []

        for file_path, diff_lines in file_blocks:
            self.logger.debug(f"处理文件: {file_path}")

            # 跳过配置文件
            if file_path.endswith(CONFIG_SUFFIXES):
                self.logger.debug(f"配置文件 {file_path} 跳过CR分块")
                continue

            # 获取完整文件内容进行分块
            all_chunks = self._get_file_chunks_for_context(file_path)
            self.logger.debug(f"文件 {file_path} 分块结果: {len(all_chunks)} 个块")

            # diff切块
            if self.chunk_utils_available:
                file_diff_segments = split_diff_by_continuous_lines(diff_lines)
            else:
                # 基础实现
                file_diff_segments = [{'start_line': 1, 'end_line': len(diff_lines), 'lines': diff_lines}]

            self.logger.debug(f"文件 {file_path} diff切分为 {len(file_diff_segments)} 个片段")

            # 找到覆盖的代码块
            for seg in file_diff_segments:
                covering_chunks = self._find_covering_chunks(seg, all_chunks)
                diff_segment_infos.append({
                    'file_path': file_path,
                    'seg_start': seg['start_line'],
                    'seg_end': seg['end_line'],
                    'seg_code': '\n'.join([l for l in seg['lines'] if l.strip()]),
                    'covering_chunks': covering_chunks
                })
                all_covered_chunks.extend(covering_chunks)

        return all_covered_chunks, diff_segment_infos

    def _get_file_chunks_for_context(self, file_path: str) -> List[Dict]:
        """获取文件的代码块用于上下文分析"""
        try:
            full_file_content = self.git_service.read_file(self.project, self.repo, file_path)
            if full_file_content:
                # 使用基础分块
                return self._basic_chunk_file(full_file_content, file_path)
        except Exception as e:
            self.logger.debug(f"读取或分析文件 {file_path} 失败: {str(e)}")

        return []

    def _find_covering_chunks(self, seg: Dict, all_chunks: List[Dict]) -> List[Dict]:
        """找到覆盖指定片段的代码块"""
        covering_chunks = []

        for chunk in all_chunks:
            chunk_start = chunk.get('start_line', 1)
            chunk_end = chunk.get('end_line', chunk_start)
            seg_start = seg.get('start_line', 1)
            seg_end = seg.get('end_line', seg_start)

            # 检查是否有重叠
            if not (chunk_end < seg_start or chunk_start > seg_end):
                covering_chunks.append(chunk)

        return covering_chunks

    def _deduplicate_chunks(self, chunks: List[Dict]) -> List[Dict]:
        """去重代码块"""
        seen = set()
        unique_chunks = []

        for chunk in chunks:
            # 使用文件路径和起始行号作为唯一标识
            chunk_id = f"{chunk.get('file', '')}:{chunk.get('start_line', 0)}"
            if chunk_id not in seen:
                seen.add(chunk_id)
                unique_chunks.append(chunk)

        return unique_chunks

    def _generate_cr_tasks(self, unique_chunks: List[Dict], diff_segment_infos: List[Dict], max_dep_depth: int) -> List[Dict]:
        """生成CR任务"""
        segments = []

        for chunk in unique_chunks:
            # 获取并清理文件路径
            file_path = self._clean_file_path(chunk.get('file', ''))
            file_type = self._detect_file_type(file_path)

            # 基础片段信息
            segment = {
                'file': file_path,
                'file_path': file_path,  # 保持兼容性
                'type': file_type,  # 添加文件类型
                'content': chunk.get('content', ''),
                'start_line': chunk.get('start_line', 1),
                'end_line': chunk.get('end_line', 1),
                'upstream': chunk.get('upstream', []),
                'downstream': chunk.get('downstream', []),
                'upstream_code': {},
                'downstream_code': {}
            }

            # 使用已有的依赖解析方法获取依赖代码
            try:
                if self.dependency_analyzer:
                    # 使用完整的依赖分析器
                    upstream_code = self.dependency_analyzer.collect_multi_level_deps(
                        chunk, 'upstream', max_depth=max_dep_depth
                    )
                    downstream_code = self.dependency_analyzer.collect_multi_level_deps(
                        chunk, 'downstream', max_depth=max_dep_depth
                    )
                    segment['upstream_code'] = upstream_code
                    segment['downstream_code'] = downstream_code
                else:
                    # 使用基础的依赖查找
                    self._collect_basic_dependencies(segment, chunk)

            except Exception as e:
                self.logger.debug(f"获取依赖代码失败: {str(e)}")
                # 降级到基础方法
                self._collect_basic_dependencies(segment, chunk)

            segments.append(segment)

        return segments

    def _handle_unmatched_diffs(self, diff_segment_infos: List[Dict]) -> List[Dict]:
        """处理未命中任何块的diff"""
        unmatched_segments = []

        for info in diff_segment_infos:
            if not info.get('covering_chunks'):
                # 获取并清理文件路径
                file_path = self._clean_file_path(info['file_path'])
                file_type = self._detect_file_type(file_path)

                # 创建基础片段
                segment = {
                    'file': file_path,
                    'file_path': file_path,  # 保持兼容性
                    'type': file_type,  # 添加文件类型
                    'content': info['seg_code'],
                    'start_line': info['seg_start'],
                    'end_line': info['seg_end'],
                    'upstream': [],
                    'downstream': [],
                    'upstream_code': {},
                    'downstream_code': {}
                }
                unmatched_segments.append(segment)

        return unmatched_segments

    def build_project_func_index(self, suffixes=None, use_cache=True, cache_ttl=None):
        """
        构建项目函数索引

        Args:
            suffixes: 支持的文件后缀列表
            use_cache: 是否使用缓存
            cache_ttl: 缓存TTL

        Returns:
            Dict: 函数索引字典
        """
        if suffixes is None:
            suffixes = SUPPORTED_CODE_SUFFIXES if self.chunk_utils_available else ['.py', '.java', '.js', '.ts']

        if cache_ttl is None:
            cache_ttl = DEFAULT_CACHE_TTL if self.chunk_utils_available else 3600

        # 验证项目信息
        if (not self.project or not self.repo or
            self.project in ("default", "unknown") or
            self.repo in ("default", "unknown")):
            self.logger.warning(f"项目信息无效 ({self.project}/{self.repo})，返回空索引")
            self.func_index = {}
            return self.func_index

        self.logger.info(f"开始构建函数索引，项目: {self.project}/{self.repo}，支持的文件类型: {suffixes}")

        try:
            # 尝试从缓存加载（如果缓存可用）
            if use_cache and hasattr(self, 'cache') and self.cache:
                cache_key = self._get_cache_key(suffixes)
                cached_data = self.cache.load_cache(cache_key, cache_ttl)
                if cached_data:
                    self.func_index = cached_data
                    self.logger.info(f"从缓存加载函数索引，共 {len(cached_data)} 个函数")
                    return self.func_index

            # 获取所有文件
            all_files = self.git_service.list_repo_files(self.project, self.repo, branch=self.branch, suffixes=suffixes)
            self.logger.info(f"找到 {len(all_files)} 个匹配的文件")

            if not all_files:
                self.logger.warning("没有找到匹配的文件")
                self.func_index = {}
                return self.func_index

            # 检测源码根目录（如果工具函数可用）
            if self.chunk_utils_available:
                roots = detect_source_roots(all_files)
                self.logger.info(f"检测到源码根目录: {roots}")
                src_files = self._filter_source_files(all_files, roots)
            else:
                src_files = all_files

            self.logger.info(f"最终使用 {len(src_files)} 个源码文件构建索引")

            # 分块处理
            all_chunks = self._process_source_files_for_index(src_files)
            self.logger.info(f"获取到 {len(all_chunks)} 个分块")

            # 构建函数索引
            func_index = self._build_func_index_from_chunks(all_chunks)

            # 补全跨文件依赖（如果可用）
            if self.chunk_utils_available:
                self._complete_cross_file_dependencies(func_index)

            # 缓存结果（如果缓存可用）
            if use_cache and hasattr(self, 'cache') and self.cache:
                cache_key = self._get_cache_key(suffixes)
                self.cache.save_cache(cache_key, func_index)

            self.func_index = func_index
            self.logger.info(f"函数索引构建完成，共 {len(func_index)} 个唯一名（含多重索引）")

            return func_index

        except Exception as e:
            self.logger.error(f"构建函数索引失败: {str(e)}")
            # 返回空索引，避免完全失败
            self.func_index = {}
            return self.func_index

    def _get_cache_key(self, suffixes):
        """生成缓存键"""
        suffixes_str = ','.join(sorted(suffixes))
        return f"{self.project}:{self.repo}:{self.branch}:{suffixes_str}"

    def _filter_source_files(self, all_files: List[str], roots: List[str]) -> List[str]:
        """过滤源码文件"""
        if not self.chunk_utils_available:
            return all_files

        src_files = []
        for f in all_files:
            for root in roots:
                if f.startswith(root + os.sep) or f == root:
                    src_files.append(f)
                    break

        # 如果过滤后文件太少，使用所有文件
        if len(src_files) < len(all_files) * 0.5:
            self.logger.warning("根目录过滤掉了太多文件，使用所有文件进行索引构建")
            src_files = all_files

        return src_files

    def _process_source_files_for_index(self, src_files: List[str]) -> List[Dict]:
        """处理源码文件，进行分块（用于索引构建）"""
        all_chunks = []
        for file_path in src_files:
            try:
                # 使用基础分块方法
                file_content = self.git_service.read_file(self.project, self.repo, file_path)
                if not file_content:
                    continue

                chunks = self._basic_chunk_file(file_content, file_path)
                if chunks:
                    all_chunks.extend(chunks)

            except Exception as e:
                self.logger.debug(f"处理文件 {file_path} 时出错: {e}")
                continue

        return all_chunks

    def _build_func_index_from_chunks(self, all_chunks: List[Dict]) -> Dict:
        """从分块构建函数索引"""
        func_index = {}

        def add_to_index(index, key, chunk):
            if key in index:
                if isinstance(index[key], list):
                    index[key].append(chunk)
                else:
                    index[key] = [index[key], chunk]
            else:
                index[key] = chunk

        for chunk in all_chunks:
            chunk_type = chunk.get('type', '')
            if chunk_type in ('function', 'method', 'constructor', 'arrow', 'lambda', 'class'):
                file_path = chunk.get('file', '')
                name = chunk.get('name', '')

                if not name:
                    continue

                # 生成多种索引键
                unique_name = f"{file_path}:{name}"
                simple_name = f"{file_path}:{name.split('.')[-1]}"
                global_name = f"global:{name}"

                add_to_index(func_index, unique_name, chunk)
                add_to_index(func_index, global_name, chunk)
                add_to_index(func_index, simple_name, chunk)

                # Java特殊处理：为类方法添加更多索引键
                if "." in name and file_path.endswith('.java'):
                    class_name, method_name = name.rsplit(".", 1)

                    # 添加方法名索引
                    method_key = f"global:{method_name}"
                    add_to_index(func_index, method_key, chunk)

                    # 添加类名.方法名索引（不带文件路径）
                    class_method_key = f"global:{class_name}.{method_name}"
                    add_to_index(func_index, class_method_key, chunk)

                    # 添加简化的类名.方法名索引
                    simple_class_name = class_name.split(".")[-1]  # 取最后一部分类名
                    simple_class_method_key = f"global:{simple_class_name}.{method_name}"
                    add_to_index(func_index, simple_class_method_key, chunk)

        return func_index

    def _complete_cross_file_dependencies(self, func_index: Dict):
        """补全跨文件依赖"""
        if not self.chunk_utils_available:
            return

        self.logger.info("开始补全跨文件依赖...")

        try:
            for key, chunk in func_index.items():
                chunk_list = chunk if isinstance(chunk, list) else [chunk]
                for c in chunk_list:
                    if not isinstance(c, dict):
                        continue

                    new_upstream = []
                    for dep in c.get('upstream', []):
                        unique_keys = find_unique_key_for_dep(func_index, dep)
                        if unique_keys:
                            for unique_key in unique_keys:
                                target = func_index[unique_key]
                                target_list = target if isinstance(target, list) else [target]
                                for t in target_list:
                                    if not isinstance(t, dict):
                                        continue
                                    if 'downstream' not in t:
                                        t['downstream'] = []
                                    if key not in t['downstream']:
                                        t['downstream'].append(key)
                            new_upstream.extend(unique_keys)
                        else:
                            new_upstream.append(dep)
                    c['upstream'] = new_upstream

        except Exception as e:
            self.logger.error(f"补全跨文件依赖失败: {str(e)}")

        self.logger.info("跨文件依赖补全完成")

    def _clean_file_path(self, file_path: str) -> str:
        """清理文件路径"""
        if not file_path:
            return "unknown"

        # 去掉常见的前缀
        if file_path.startswith('b/'):
            file_path = file_path[2:]
        elif file_path.startswith('a/'):
            file_path = file_path[2:]

        # 去掉开头的斜杠
        if file_path.startswith('/'):
            file_path = file_path[1:]

        return file_path
    def _detect_file_type(self, file_path: str) -> str:
        """检测文件类型"""
        if not file_path:
            return "unknown"

        # 根据文件扩展名检测类型
        if file_path.endswith('.py'):
            return "python"
        elif file_path.endswith('.java'):
            return "java"
        elif file_path.endswith('.js'):
            return "javascript"
        elif file_path.endswith('.ts'):
            return "typescript"
        elif file_path.endswith('.cpp') or file_path.endswith('.c'):
            return "c++"
        elif file_path.endswith('.go'):
            return "go"
        elif file_path.endswith('.rs'):
            return "rust"
        else:
            return "unknown"
