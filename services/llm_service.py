"""
LLM服务 - 新架构版本
负责大模型实例的初始化与配置，继承自BaseService
"""
import json
import logging
from typing import Optional, Dict, Any

from core import service_registry
from .base_service import BaseService

# 延迟导入LangChain相关模块，避免启动时的依赖问题
try:
    from langchain_openai import ChatOpenAI
    LANGCHAIN_AVAILABLE = True
except ImportError:
    LANGCHAIN_AVAILABLE = False
    ChatOpenAI = None


class OpenAIConfig:
    """OpenAI配置类"""
    
    def __init__(self,
                 api_key: str,
                 base_url: str = "https://aigc.sankuai.com/v1/openai/native",
                 temperature: float = 0.1,
                 model_name: str = "anthropic.claude-3.5-sonnet",
                 max_tokens: int = 8192,
                 max_completion_tokens: int = 8002):
        self.api_key = api_key
        self.base_url = base_url
        self.temperature = temperature
        self.model_name = model_name
        self.max_tokens = max_tokens
        self.max_completion_tokens = max_completion_tokens

    def __str__(self):
        return str(self.__dict__)

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'api_key': self.api_key,
            'base_url': self.base_url,
            'temperature': self.temperature,
            'model_name': self.model_name,
            'max_tokens': self.max_tokens,
            'max_completion_tokens': self.max_completion_tokens
        }


class LLMService(BaseService):
    """
    LLM服务，负责大模型实例的初始化与配置
    继承自BaseService，符合新架构要求
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化LLM服务
        
        Args:
            config: 服务配置，包含LLM配置信息
        """
        super().__init__("llm_service", config)
        
        # 默认配置
        self.kms_service = service_registry.get_kms_service()
        self.default_config = {
            'api_key': self.kms_service.get_key("com.sankuai.yunzhuan.devhelper", "modal_app_id").get('data'),
            'base_url': 'https://aigc.sankuai.com/v1/openai/native',
            'temperature': 0.1,
            'model_name': 'anthropic.claude-3.5-sonnet',
            'max_tokens': 8192,
            'max_completion_tokens': 8002,
            'enable_retry': True,
            'max_retries': 3,
            'timeout': 60
        }
        print(self.default_config)
        
        # 合并配置
        self.config = {**self.default_config, **self.config}
        
        # LLM实例和配置
        self.llm = None
        self.openai_config = None
        
    async def initialize(self) -> bool:
        """初始化服务"""
        try:
            self.logger.info("初始化LLM服务")
            
            # 检查LangChain依赖
            if not LANGCHAIN_AVAILABLE:
                self.logger.warning("LangChain依赖不可用，LLM服务将以降级模式运行")
                return True  # 允许服务启动，但功能受限
            
            # 创建OpenAI配置
            self.openai_config = OpenAIConfig(
                api_key=self.config.get('api_key', 'mock_api_key'),
                base_url=self.config.get('base_url', 'https://aigc.sankuai.com/v1/openai/native'),
                temperature=self.config.get('temperature', 0.1),
                model_name=self.config.get('model_name', 'anthropic.claude-3.5-sonnet'),
                max_tokens=self.config.get('max_tokens', 8192),
                max_completion_tokens=self.config.get('max_completion_tokens', 8002)
            )
            print("openai_config: ", self.openai_config)
            # 初始化LLM实例
            await self._init_llm()
            
            self.logger.info("LLM服务初始化完成")
            return True
            
        except Exception as e:
            self.logger.error(f"LLM服务初始化失败: {str(e)}")
            self.increment_error_count()
            return False
    
    async def cleanup(self) -> bool:
        """清理资源"""
        try:
            self.logger.info("清理LLM服务资源")
            self.llm = None
            self.openai_config = None
            return True
        except Exception as e:
            self.logger.error(f"LLM服务清理失败: {str(e)}")
            return False
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            health_status = {
                'service': self.name,
                'status': 'healthy',
                'langchain_available': LANGCHAIN_AVAILABLE,
                'llm_initialized': self.llm is not None,
                'config_loaded': self.openai_config is not None,
                'model_name': self.config.get('model_name', 'unknown'),
                'timestamp': self._get_current_timestamp()
            }
            
            # 如果LLM可用，尝试简单的健康检查
            if self.llm and LANGCHAIN_AVAILABLE:
                try:
                    # 这里可以添加简单的LLM调用测试
                    health_status['llm_responsive'] = True
                except Exception as e:
                    health_status['llm_responsive'] = False
                    health_status['llm_error'] = str(e)
            
            return health_status
            
        except Exception as e:
            return {
                'service': self.name,
                'status': 'unhealthy',
                'error': str(e),
                'timestamp': self._get_current_timestamp()
            }
    
    async def _init_llm(self):
        """初始化LLM实例"""
        try:
            if not LANGCHAIN_AVAILABLE:
                self.logger.warning("❌LangChain不可用，跳过LLM实例初始化")
                return
            
            # 使用langchain库初始化ChatModel实例
            self.llm = ChatOpenAI(
                api_key=self.openai_config.api_key,
                base_url=self.openai_config.base_url,
                model=self.openai_config.model_name,
                temperature=self.openai_config.temperature,
                max_completion_tokens=self.openai_config.max_completion_tokens,
                max_tokens=self.openai_config.max_tokens
            )

            
            self.logger.info(f"LLM实例初始化成功: {self.openai_config.model_name}")
            
        except Exception as e:
            # 捕获初始化过程中的异常并记录
            error_msg = f"LLM实例初始化失败: {e}"
            self.logger.error(error_msg)
            raise RuntimeError(error_msg)

    def update_config(self, new_config: Dict[str, Any]) -> bool:
        """
        更新配置并重新初始化LLM实例
        
        Args:
            new_config: 新的配置
            
        Returns:
            bool: 更新是否成功
        """
        try:
            # 更新配置
            self.config.update(new_config)
            
            # 如果OpenAI配置存在，更新它
            if self.openai_config:
                for k, v in new_config.items():
                    if hasattr(self.openai_config, k):
                        setattr(self.openai_config, k, v)
            
            # 重新初始化LLM实例
            if LANGCHAIN_AVAILABLE:
                import asyncio
                asyncio.create_task(self._init_llm())
            
            self.logger.info("LLM配置更新成功")
            return True
            
        except Exception as e:
            self.logger.error(f"LLM配置更新失败: {str(e)}")
            self.increment_error_count()
            return False

    def get_llm(self):
        """获取LLM实例"""
        if not LANGCHAIN_AVAILABLE:
            self.logger.warning("LangChain不可用，返回None")
            return None
        return self.llm

    def get_max_tokens(self) -> int:
        """获取最大token数"""
        return self.config.get('max_tokens', 8192)
    
    def get_config(self) -> Dict[str, Any]:
        """获取当前配置"""
        if self.openai_config:
            return self.openai_config.to_dict()
        return self.config.copy()

    def print_config(self):
        """打印配置信息"""
        if self.openai_config:
            print(f"LLM配置: {self.openai_config}")
        else:
            print(f"LLM配置: {self.config}")
    
    def is_available(self) -> bool:
        """检查LLM服务是否可用"""
        return LANGCHAIN_AVAILABLE and self.llm is not None
    
    def _get_current_timestamp(self) -> str:
        """获取当前时间戳"""
        from datetime import datetime
        return datetime.now().isoformat()


# 兼容性函数，保持与原有代码的兼容性
def create_llm_service(config: Optional[Dict[str, Any]] = None) -> LLMService:
    """
    创建LLM服务实例
    
    Args:
        config: 服务配置
        
    Returns:
        LLMService: LLM服务实例
    """
    return LLMService(config)


# 向后兼容的配置类别名
LLMConfig = OpenAIConfig
