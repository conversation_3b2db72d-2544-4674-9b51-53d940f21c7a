"""
服务层模块

提供统一的服务接口和管理
"""

from .base_service import BaseService
from .service_manager import ServiceManager

# 条件导入服务，避免依赖问题
try:
    from .cr_service import CRService
    CR_SERVICE_AVAILABLE = True
except ImportError:
    CR_SERVICE_AVAILABLE = False
    CRService = None

try:
    from .chunk_service import ChunkService
    CHUNK_SERVICE_AVAILABLE = True
except ImportError:
    CHUNK_SERVICE_AVAILABLE = False
    ChunkService = None

try:
    from .git_service import GitService
    GIT_SERVICE_AVAILABLE = True
except ImportError:
    GIT_SERVICE_AVAILABLE = False
    GitService = None

try:
    from .daxiang_service import DaXiangService
    DAXIANG_SERVICE_AVAILABLE = True
except ImportError:
    DAXIANG_SERVICE_AVAILABLE = False
    DaXiangService = None

try:
    from .km_service import KmService
    KM_SERVICE_AVAILABLE = True
except ImportError:
    KM_SERVICE_AVAILABLE = False
    KmService = None

try:
    from .kms_service import KmsService
    KMS_SERVICE_AVAILABLE = True
except ImportError:
    KMS_SERVICE_AVAILABLE = False
    KmsService = None

try:
    from .org_service import OrgService
    ORG_SERVICE_AVAILABLE = True
except ImportError:
    ORG_SERVICE_AVAILABLE = False
    OrgService = None

try:
    from .devtools_service import DevtoolsService
    DEVTOOLS_SERVICE_AVAILABLE = True
except ImportError:
    DEVTOOLS_SERVICE_AVAILABLE = False
    DevtoolsService = None

try:
    from .llm_service import LLMService
    LLM_SERVICE_AVAILABLE = True
except ImportError:
    LLM_SERVICE_AVAILABLE = False
    LLMService = None

try:
    from .sso_service import SSOService
    SSO_SERVICE_AVAILABLE = True
except ImportError:
    SSO_SERVICE_AVAILABLE = False
    SSOService = None



__all__ = [
    'BaseService',
    'ServiceManager'
]

# 动态添加可用的服务到__all__
if CR_SERVICE_AVAILABLE:
    __all__.append('CRService')
if CHUNK_SERVICE_AVAILABLE:
    __all__.append('ChunkService')
if GIT_SERVICE_AVAILABLE:
    __all__.append('GitService')
if DAXIANG_SERVICE_AVAILABLE:
    __all__.append('DaXiangService')
if KM_SERVICE_AVAILABLE:
    __all__.append('KmService')
if KMS_SERVICE_AVAILABLE:
    __all__.append('KmsService')
if ORG_SERVICE_AVAILABLE:
    __all__.append('OrgService')
if DEVTOOLS_SERVICE_AVAILABLE:
    __all__.append('DevtoolsService')
if LLM_SERVICE_AVAILABLE:
    __all__.append('LLMService')
if SSO_SERVICE_AVAILABLE:
    __all__.append('SSOService')