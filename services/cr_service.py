"""
代码审查服务

重构后的代码审查核心服务，使用编排器和Agent架构
"""

import logging
from typing import Any, Dict, Optional, List
from services.base_service import BaseService
from core.orchestrator.workflow_manager import get_workflow_manager
from core.orchestrator.cr_orchestrator import CROrchestrator
from core.service_registry import get_service


class CRService(BaseService):
    """代码审查服务"""
    
    def __init__(self,
                 horn_service=None,
                 kms_service=None,
                 dx_service=None,
                 git_service=None,
                 devmind_service=None,
                 config: Optional[Dict[str, Any]] = None):
        """
        初始化代码审查服务

        Args:
            horn_service: Horn配置服务
            kms_service: KMS密钥服务
            dx_service: 大象服务
            git_service: Git服务
            devmind_service: DevMind服务
            config: 服务配置
        """
        super().__init__("cr_service", config)

        # 服务依赖 - 支持直接传入或从注册表获取
        self.horn_service = horn_service
        self.kms_service = kms_service
        self.dx_service = dx_service
        self.git_service = git_service
        self.devmind_service = devmind_service

        # 其他服务
        self.llm_service = None
        self.chunk_service = None

        # 工作流管理器
        self.workflow_manager = get_workflow_manager()
        self.cr_orchestrator_id = None

        # 默认配置
        self.default_config = {
            'cr_mode': 'standard',  # fast, standard, deep
            'enable_parallel_processing': True,
            'max_concurrent_tasks': 5,
            'enable_performance_monitoring': True,
            'timeout_seconds': 600
        }

        # 合并配置
        self.config = {**self.default_config, **self.config}
    
    async def initialize(self) -> bool:
        """初始化服务"""
        try:
            self.logger.info("初始化代码审查服务")
            
            # 获取依赖服务
            await self._initialize_dependencies()
            
            # 创建CR编排器
            await self._create_cr_orchestrator()
            
            self.logger.info("代码审查服务初始化完成")
            return True
            
        except Exception as e:
            self.logger.error(f"代码审查服务初始化失败: {str(e)}")
            return False
    
    async def cleanup(self) -> bool:
        """清理资源"""
        try:
            self.logger.info("清理代码审查服务资源")
            
            # 移除编排器
            if self.cr_orchestrator_id:
                self.workflow_manager.remove_orchestrator(self.cr_orchestrator_id)
            
            self.logger.info("代码审查服务资源清理完成")
            return True
            
        except Exception as e:
            self.logger.error(f"代码审查服务资源清理失败: {str(e)}")
            return False
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        health_status = {
            'service': 'cr_service',
            'status': 'healthy',
            'dependencies': {},
            'orchestrator': None
        }
        
        try:
            # 检查依赖服务
            dependencies = [
                'llm_service', 'devmind_service', 'chunk_service', 
                'git_service', 'horn_service', 'kms_service'
            ]
            
            for dep in dependencies:
                service = getattr(self, dep, None)
                if service and hasattr(service, 'health_check'):
                    dep_health = await service.health_check()
                    health_status['dependencies'][dep] = dep_health.get('status', 'unknown')
                else:
                    health_status['dependencies'][dep] = 'not_available'
            
            # 检查编排器状态
            if self.cr_orchestrator_id:
                orchestrator = self.workflow_manager.get_orchestrator(self.cr_orchestrator_id)
                if orchestrator:
                    health_status['orchestrator'] = orchestrator.get_status()
                else:
                    health_status['orchestrator'] = 'not_found'
            
            return health_status
            
        except Exception as e:
            health_status['status'] = 'unhealthy'
            health_status['error'] = str(e)
            return health_status
    
    async def _initialize_dependencies(self):
        """初始化依赖服务"""
        # 如果服务没有通过构造函数传入，尝试从服务注册表获取
        if not self.horn_service:
            self.horn_service = get_service('horn_service')
        if not self.kms_service:
            self.kms_service = get_service('kms_service')
        if not self.git_service:
            from core.service_registry import get_git_service
            self.git_service = get_git_service()
        if not self.devmind_service:
            self.devmind_service = get_service('devmind_service')
        if not self.dx_service:
            self.dx_service = get_service('dx_service')

        # 初始化LLM服务 - 必须使用新架构的LLM服务
        self.llm_service = get_service('llm_service')

        # 检查是否存在LLM服务实例
        if not self.llm_service:
            raise RuntimeError("LLM服务初始化失败，无法创建CR服务。请检查LLM服务配置。")

        # 初始化代码分块服务
        self.chunk_service = get_service('chunk_service')
        if not self.chunk_service:
            self.logger.info("代码分块服务将在需要时动态创建（基于具体项目信息）")

        self.logger.info("依赖服务初始化完成")

    def _get_or_create_chunk_service(self, project: str, repo: str, branch: str = "main"):
        """
        获取或创建代码分块服务

        Args:
            project: 项目名
            repo: 仓库名
            branch: 分支名

        Returns:
            ChunkService实例或None
        """
        try:
            # 如果已有chunk_service且项目信息匹配，直接返回
            if (self.chunk_service and
                hasattr(self.chunk_service, 'project') and
                hasattr(self.chunk_service, 'repo') and
                self.chunk_service.project == project and
                self.chunk_service.repo == repo):
                return self.chunk_service

            # 创建新的chunk_service
            if self.git_service:
                from core.service_registry import get_chunk_service
                chunk_service = get_chunk_service(project, repo, branch)
                self.logger.info(f"为项目 {project}/{repo} 创建了代码分块服务")
                return chunk_service
            else:
                self.logger.warning("Git服务不可用，无法创建代码分块服务")
                return None

        except Exception as e:
            self.logger.error(f"创建代码分块服务失败: {str(e)}")
            return None



    async def _create_cr_orchestrator(self):
        """创建CR编排器"""
        # 改进的依赖检查
        missing_services = []
        if not self.llm_service:
            missing_services.append("llm_service")
        if not self.git_service:
            missing_services.append("git_service")

        if missing_services:
            self.logger.warning(f"⚠️ 缺少关键依赖服务: {', '.join(missing_services)}")
            self.logger.info("🔄 尝试重新初始化缺少的服务...")

            # 尝试重新初始化缺少的服务
            await self._retry_service_initialization()

            # 再次检查
            if not all([self.llm_service, self.git_service]):
                raise RuntimeError(f"关键依赖服务不可用: {', '.join(missing_services)}。请检查服务配置和初始化状态。")

        self.cr_orchestrator_id = f"cr_orchestrator_{self.name}"

        orchestrator = self.workflow_manager.create_orchestrator(
            'cr_orchestrator',
            self.cr_orchestrator_id,
            self.config,
            llm_service=self.llm_service,
            devmind_service=self.devmind_service,
            chunk_service=None,  # chunk_service将在需要时动态设置
            git_service=self.git_service
        )

        self.logger.info(f"✅ CR编排器已创建: {self.cr_orchestrator_id}")
        print(f"✅ CR编排器: {orchestrator.__dict__.get('llm_service').llm}")
        return orchestrator

    async def _retry_service_initialization(self):
        """重试服务初始化"""
        try:
            # 重试LLM服务初始化
            if not self.llm_service:
                self.logger.info("🔄 重试LLM服务初始化...")
                try:
                    from core.service_registry import get_llm_service
                    self.llm_service = await get_llm_service()
                    if self.llm_service:
                        self.logger.info("✅ LLM服务初始化成功")
                except Exception as e:
                    self.logger.warning(f"❌ LLM服务初始化失败: {str(e)}")
            elif hasattr(self.llm_service, 'initialize') and callable(getattr(self.llm_service, 'initialize')):
                # 如果LLM服务存在但未初始化，尝试初始化
                try:
                    self.logger.info("🔄 初始化现有LLM服务...")
                    success = await self.llm_service.initialize()
                    if success:
                        self.logger.info("✅ 现有LLM服务初始化成功")
                    else:
                        self.logger.warning("❌ 现有LLM服务初始化失败")
                except Exception as e:
                    self.logger.warning(f"❌ 现有LLM服务初始化异常: {str(e)}")

            # 重试Git服务初始化
            if not self.git_service:
                self.logger.info("🔄 重试Git服务初始化...")
                try:
                    from core.service_registry import get_git_service
                    self.git_service = get_git_service()
                    if self.git_service:
                        self.logger.info("✅ Git服务初始化成功")
                except Exception as e:
                    self.logger.warning(f"❌ Git服务初始化失败: {str(e)}")

        except Exception as e:
            self.logger.warning(f"❌ 服务重试初始化失败: {str(e)}")
    
    async def process_cr_request(self, 
                               ctx: Any, 
                               code_diff: str, 
                               params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        处理代码评审请求
        
        Args:
            ctx: 上下文信息
            code_diff: Git diff格式的代码差异字符串
            params: 可选参数，包含PR链接和任务Git信息等
            
        Returns:
            Dict: 代码审查结果
        """
        try:
            # 提取参数
            project = params.get('project') or getattr(ctx.params, 'project', None) if params else None
            repo = params.get('repo') or getattr(ctx.params, 'repo', None) if params else None
            from_branch = params.get('fromBranch') or getattr(ctx.params, 'fromBranch', None) if params else None
            to_branch = params.get('toBranch') or getattr(ctx.params, 'toBranch', None) if params else None
            
            self.logger.info(f"开始CR: {project}, {repo}, {from_branch}, {to_branch}")
            
            if not code_diff:
                raise ValueError('code_diff不能为空')
            
            # 准备工作流输入
            workflow_input = {
                'code_diff': code_diff,
                'options': {
                    'project': project,
                    'repo': repo,
                    'fromBranch': from_branch,
                    'toBranch': to_branch,
                    **(params or {})
                }
            }

            # 动态创建或更新chunk_service（如果编排器可用）
            if self.cr_orchestrator_id and self.workflow_manager.get_orchestrator(self.cr_orchestrator_id):
                chunk_service = self._get_or_create_chunk_service(project, repo, from_branch or 'main')
                if chunk_service:
                    try:
                        orchestrator = self.workflow_manager.get_orchestrator(self.cr_orchestrator_id)
                        if orchestrator:
                            # 使用编排器的update_chunk_service方法
                            if hasattr(orchestrator, 'update_chunk_service'):
                                orchestrator.update_chunk_service(chunk_service)
                                self.logger.info(f"已为项目 {project}/{repo} 更新编排器和Agent的代码分块服务")
                            else:
                                # 降级到直接设置属性
                                orchestrator.chunk_service = chunk_service
                                self.logger.info(f"已为项目 {project}/{repo} 更新编排器的代码分块服务（直接设置）")
                    except Exception as e:
                        self.logger.warning(f"更新编排器chunk_service失败: {str(e)}")
                else:
                    self.logger.warning(f"无法为项目 {project}/{repo} 创建代码分块服务")
            
            # 使用新架构处理CR请求
            if not self.cr_orchestrator_id or not self.workflow_manager.get_orchestrator(self.cr_orchestrator_id):
                raise RuntimeError("CR编排器不可用，无法处理代码审查请求。请检查服务初始化状态。")

            self.logger.info("使用新架构处理CR请求")
            result = await self.workflow_manager.execute_workflow(
                self.cr_orchestrator_id,
                workflow_input
            )
            self.logger.info(f"新架构处理结果: {result}")

            if not result.success:
                raise RuntimeError(f"新架构代码审查处理失败: {result.error}")

            self.logger.info("新架构代码审查请求处理成功")

            # 输出详细的审查结果和依赖分析日志
            try:
                from utils.cr_log_utils import CRLogFormatter

                # 获取代码片段信息（如果有的话）
                segments = result.metadata.get('segments', []) if hasattr(result, 'metadata') and result.metadata else []

                # 输出完整的CR日志
                CRLogFormatter.log_cr_summary(result.data, segments, self.logger)

            except Exception as e:
                self.logger.warning(f"输出详细日志失败: {str(e)}")

            # 直接返回编排器的聚合结果（已经包含完整的增强信息）
            final_result = result.data

            # 数据一致性验证（但不添加重复字段）
            if 'summary' in final_result:
                summary = final_result['summary']

                # 获取实际问题数量进行验证
                actual_problems_count = len(final_result.get('problems', []))
                summary_problems_count = summary.get('totalProblems', 0)

                # 数据一致性验证和修正
                if actual_problems_count != summary_problems_count:
                    self.logger.warning(f"⚠️  问题数量不一致: summary={summary_problems_count}, actual={actual_problems_count}")
                    # 使用实际问题数量
                    summary['totalProblems'] = actual_problems_count

                # 验证最终结果的一致性
                overall_result = summary.get('overallResult', '通过')

                if actual_problems_count > 0 and overall_result == '通过':
                    # 有问题但显示通过，需要进一步检查
                    has_critical = any(
                        p.get('severity') == 'CRITICAL' or p.get('level') == 'P0'
                        for p in final_result.get('problems', [])
                    )
                    if has_critical:
                        summary['overallResult'] = '不通过'
                        summary['isPassed'] = False
                        self.logger.warning(f"⚠️  修正最终结果: 有严重问题但显示通过，已修正为不通过")

                self.logger.info(f"✅ 数据一致性验证完成: {summary.get('overallResult')}, 问题{actual_problems_count}个")

            self.logger.info(f"✅ 返回优化后的聚合结果，包含完整的UI展示数据")
            return final_result
                
        except Exception as e:
            error_msg = f"处理代码评审请求失败: {str(e)}"
            self.logger.error(error_msg)
            raise RuntimeError(error_msg)




    
    def set_cr_mode(self, mode: str):
        """
        设置CR模式

        Args:
            mode: CR模式 (fast, standard, deep, async_fast)
        """
        valid_modes = ["fast", "standard", "deep", "async_fast"]
        if mode not in valid_modes:
            raise ValueError(f"无效的CR模式: {mode}，支持的模式: {valid_modes}")

        old_mode = self.config.get('cr_mode', 'standard')
        self.config['cr_mode'] = mode

        # 更新编排器配置
        if self.cr_orchestrator_id:
            orchestrator = self.workflow_manager.get_orchestrator(self.cr_orchestrator_id)
            if orchestrator and hasattr(orchestrator, 'set_cr_mode'):
                orchestrator.set_cr_mode(mode)
                self.logger.info(f"✅ 编排器CR模式已同步: {old_mode} → {mode}")

        # 输出模式说明
        mode_descriptions = {
            "fast": "纯LLM能力，不检索知识库，快速响应",
            "standard": "LLM智能决策是否检索知识库，平衡思考链路",
            "deep": "深度思考链路 + 自检复审，全面分析",
            "async_fast": "异步快速模式，兼容旧版本"
        }

        self.logger.info(f"✅ CR模式已设置为: {mode}")
        self.logger.info(f"📝 模式说明: {mode_descriptions.get(mode, '未知模式')}")
    
    def get_cr_mode(self) -> str:
        """获取当前CR模式"""
        return self.config.get('cr_mode', 'standard')
    
    def enable_parallel_processing(self, enable: bool = True):
        """启用/禁用并行处理"""
        self.config['enable_parallel_processing'] = enable
        
        # 更新编排器配置
        if self.cr_orchestrator_id:
            orchestrator = self.workflow_manager.get_orchestrator(self.cr_orchestrator_id)
            if orchestrator and hasattr(orchestrator, 'enable_parallel_processing'):
                orchestrator.enable_parallel_processing(enable)
        
        self.logger.info(f"并行处理已{'启用' if enable else '禁用'}")
    
    def get_service_stats(self) -> Dict[str, Any]:
        """
        获取服务统计信息

        Returns:
            Dict: 统计信息
        """
        stats = {
            'service_name': self.name,
            'cr_mode': self.get_cr_mode(),
            'parallel_processing': self.config.get('enable_parallel_processing', True),
            'max_concurrent_tasks': self.config.get('max_concurrent_tasks', 5),
            'orchestrator_id': self.cr_orchestrator_id
        }

        # 获取编排器状态
        if self.cr_orchestrator_id:
            orchestrator = self.workflow_manager.get_orchestrator(self.cr_orchestrator_id)
            if orchestrator:
                stats['orchestrator_status'] = orchestrator.get_status()

        return stats


    def get_cr_config_by_project(self, task_git_info: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        获取项目对应的CR配置（兼容旧接口）

        Args:
            task_git_info: 任务Git信息，包含repo等信息

        Returns:
            Dict: 项目CR配置
        """
        if not self.horn_service:
            return {
                "lang": "python",
                "crConfigKey": "pythonBaseCrRules",
                "customCrConfigKey": ""
            }

        # 获取所有项目配置
        project_configs = (self.horn_service.fetch_config("CR_CHECKER")).get("projectConfig", [])

        # 默认配置
        default_config = {
            "lang": "python",
            "crConfigKey": "pythonBaseCrRules",
            "customCrConfigKey": ""
        }

        # 如果没有任务Git信息或没有repo信息，返回默认配置
        if not task_git_info or not task_git_info.get('repo'):
            return default_config

        # 查找匹配的项目配置
        repo = task_git_info['repo']
        for project_config in project_configs:
            if project_config.get('project') == repo:
                return project_config

        # 如果没有找到匹配的项目配置，返回默认配置
        return default_config

    async def summary_review2km_json(self,
                                   initial_result: str,
                                   pr_link: Optional[str] = None,
                                   task_git_info: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        生成学城文档JSON结构（兼容旧接口）

        Args:
            initial_result: LLM返回的初始结果
            pr_link: PR链接
            task_git_info: 任务Git信息

        Returns:
            Dict: 学城文档JSON结构
        """
        # 这里可以调用原有的实现或者重新实现
        # 为了兼容性，暂时抛出未实现异常
        raise NotImplementedError("summary_review2km_json 方法需要在后续版本中实现")

    def extract_cr_info_from_dx_chain(self, text: str) -> Dict[str, Any]:
        """
        从大象链提取CR信息（兼容旧接口）

        Args:
            text: 大象消息文本

        Returns:
            Dict: 解析后的CR信息
        """
        # 这里可以调用原有的实现或者重新实现
        # 为了兼容性，暂时抛出未实现异常
        raise NotImplementedError("extract_cr_info_from_dx_chain 方法需要在后续版本中实现")

    def extract_pr_info_from_dx_chain(self, text: str) -> Dict[str, Any]:
        """
        从大象链提取PR信息（兼容旧接口）

        Args:
            text: 大象消息文本

        Returns:
            Dict: 解析后的PR信息
        """
        # 这里可以调用原有的实现或者重新实现
        # 为了兼容性，暂时抛出未实现异常
        raise NotImplementedError("extract_pr_info_from_dx_chain 方法需要在后续版本中实现")

    def init_inf(self):
        """兼容旧接口的初始化方法"""
        # 同步初始化，兼容旧代码
        import asyncio

        # 检查是否已经初始化过
        if hasattr(self, '_initialized') and self._initialized:
            return

        # 尝试获取当前事件循环
        try:
            loop = asyncio.get_running_loop()
            # 如果在运行的事件循环中，不能直接调用run_until_complete
            self.logger.warning("在运行的事件循环中调用init_inf，无法执行异步初始化")
            raise RuntimeError("无法在运行的事件循环中执行异步初始化，请使用await initialize()方法")
        except RuntimeError:
            # 没有运行的事件循环，可以创建新的
            pass

        # 创建新的事件循环并运行初始化
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(self.initialize())
            self._initialized = True
        finally:
            loop.close()

    def generate_cr_tpl(self, cr_result: Dict[str, Any]) -> Dict[str, Any]:
        """生成CR模板（新架构实现）"""
        from datetime import datetime

        # 使用新架构的模板生成逻辑
        template = {
            "title": f"代码审查报告-{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            "timestamp": datetime.now().isoformat(),
            "summary": {
                "totalProblems": cr_result.get('totalProblem', 0),
                "checkResult": cr_result.get('sumCheckResult', '未知'),
                "checkBranch": cr_result.get('checkBranch', '未知')
            },
            "content": cr_result,
            "metadata": {
                "generator": "新架构CR服务",
                "version": "2.0",
                "format": "enhanced"
            }
        }

        # 如果有详细问题列表，添加到模板中
        if 'problemList' in cr_result:
            template["problemDetails"] = cr_result['problemList']

        return template

    def _enhance_cr_result(self, cr_result: Dict[str, Any], params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        增强CR结果格式，添加总结信息和详细统计

        Args:
            cr_result: 原始CR结果
            params: 请求参数

        Returns:
            增强后的CR结果
        """
        from utils.cr_result_enhancer import CRResultEnhancer

        enhancer = CRResultEnhancer()

        # 构建分支信息
        branch_info = None
        if params:
            branch_info = {
                'project': params.get('project', ''),
                'repo': params.get('repo', ''),
                'fromBranch': params.get('fromBranch', ''),
                'toBranch': params.get('toBranch', '')
            }

        # 增强CR结果
        enhanced_result = enhancer.enhance_cr_result(
            cr_result,
            reviewer="AI代码审查",
            branch_info=branch_info
        )

        # 转换为crResult格式（用户期望的格式）
        cr_result_format = enhancer.to_cr_result_format(enhanced_result)

        self.logger.info(f"CR结果增强完成: 评分{cr_result_format['crResult']['overallResults']['score']}, "
                       f"问题{cr_result_format['crResult']['overallResults']['totalProblems']}个, "
                       f"任务{len(cr_result_format['crResult']['taskResults'])}个")

        return cr_result_format
