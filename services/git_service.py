"""
Git操作服务

按照第一阶段架构重构要求，将原有GitService迁移到新架构
"""

import os
import shutil
from pathlib import Path
from typing import Any, Dict, Optional, Union, List

from services.base_service import BaseService
import git  # pip install gitpython
BASE_PATH = Path(__file__).resolve().parent.parent.__str__()


class GitService(BaseService):
    """
    Git操作服务，负责仓库操作与diff获取
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__("git_service", config)
        self.base_dir = None

    async def initialize(self) -> bool:
        """初始化Git服务"""
        return self._sync_initialize()

    def _sync_initialize(self) -> bool:
        """同步初始化Git服务"""
        try:
            # 设置基础目录
            self.base_dir = os.path.join(BASE_PATH, 'temp-repos')

            # 创建base目录（如不存在）
            if not os.path.exists(self.base_dir):
                os.makedirs(self.base_dir, exist_ok=True)
                self.logger.info(f"创建Git仓库缓存目录: {self.base_dir}")

            self.logger.info("Git服务初始化成功")
            return True

        except Exception as e:
            self.logger.error(f"Git服务初始化失败: {str(e)}")
            return False

    async def cleanup(self) -> bool:
        """清理资源"""
        try:
            # 可选择性清理临时仓库
            # 这里不自动删除，因为仓库可能被其他进程使用
            self.logger.info("Git服务资源清理完成")
            return True
            
        except Exception as e:
            self.logger.error(f"Git服务资源清理失败: {str(e)}")
            return False

    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            if not self.base_dir or not os.path.exists(self.base_dir):
                return {
                    "status": "error",
                    "message": "Git缓存目录不存在"
                }

            # 检查Git命令是否可用
            try:
                import subprocess
                result = subprocess.run(['git', '--version'], capture_output=True, text=True, timeout=5)
                if result.returncode == 0:
                    git_version = result.stdout.strip()
                    return {
                        "status": "healthy",
                        "message": "Git服务正常",
                        "git_version": git_version,
                        "cache_dir": self.base_dir,
                        "cached_repos": len([d for d in os.listdir(self.base_dir) if os.path.isdir(os.path.join(self.base_dir, d))])
                    }
                else:
                    return {
                        "status": "error",
                        "message": "Git命令不可用"
                    }
            except Exception as e:
                return {
                    "status": "error",
                    "message": f"Git命令检查失败: {str(e)}"
                }
                
        except Exception as e:
            return {
                "status": "error",
                "message": f"健康检查异常: {str(e)}"
            }

    @staticmethod
    def init_repo(repo_path: str):
        """初始化仓库，返回git.Repo对象"""
        return git.Repo(repo_path)

    @staticmethod
    def get_ssh_url(ref: Union[Dict[str, Any], Any]) -> str:
        """
        获取SSH地址，支持 PRRef 结构和 project/repo 结构
        PRRef: {'repository': {'links': {'clone': [{'href': ..., 'name': ...}, ...]}}}
        普通: {'project': ..., 'repo': ...}
        """
        if isinstance(ref, dict) and 'repository' in ref:
            # PRRef 情况
            clone_links = ref['repository'].get('links', {}).get('clone', [])
            for link in clone_links:
                if link.get('name') == 'ssh':
                    return link.get('href', '')
            return ''
        # 普通 project/repo 情况
        return f"*******************:{ref['project']}/{ref['repo']}.git"

    def get_repo_path(self, project: str, repo: str) -> str:
        """获取本地仓库路径"""
        if not self.base_dir:
            # 尝试自动初始化
            self.logger.warning("Git服务base_dir未设置，尝试自动初始化")
            self._sync_initialize()

        if not self.base_dir:
            raise RuntimeError("Git服务未初始化")
        return os.path.join(self.base_dir, f"{project}_{repo}")

    def get_diff(
            self,
            project: str,
            repo: str,
            from_ref: Union[Dict[str, Any], str],
            to_ref: Union[Dict[str, Any], str]
    ) -> Optional[str]:
        """
        获取 diff 内容，仿照 Node.js 逻辑，支持自动 clone、fetch、merge-base、diff 过滤
        Args:
            project: 项目名
            repo: 仓库名
            from_ref: 起始分支/commit/PRRef
            to_ref: 目标分支/commit/PRRef
        Returns:
            过滤后的 diff 字符串
        """
        try:
            repo_path = self.get_repo_path(project, repo)
            ssh_url = self.get_ssh_url({'project': project, 'repo': repo})

            # 兼容 PRRef 结构
            from_branch = from_ref['displayId'] if isinstance(from_ref, dict) and 'displayId' in from_ref else from_ref
            to_branch = to_ref['displayId'] if isinstance(to_ref, dict) and 'displayId' in to_ref else to_ref

            self.logger.info(f"获取diff: {project}/{repo} {from_branch} -> {to_branch}")

            # 如果本地仓库不存在，先 clone
            if not os.path.exists(repo_path):
                if os.path.exists(repo_path):
                    shutil.rmtree(repo_path)
                self.logger.info(f"克隆仓库: {ssh_url} -> {repo_path}")
                git.Repo.clone_from(ssh_url, repo_path)
            
            repo_obj = git.Repo(repo_path)

            # fetch 最新
            self.logger.debug("Fetching latest changes...")
            repo_obj.git.fetch('--all')

            # 确保分支都 fetch 下来
            repo_obj.git.fetch('origin', from_branch)
            repo_obj.git.fetch('origin', to_branch)

            # 计算 merge-base
            merge_base = repo_obj.git.merge_base(
                f'refs/remotes/origin/{to_branch}',
                f'refs/remotes/origin/{from_branch}'
            )
            if isinstance(merge_base, list):
                merge_base = merge_base[0]
            merge_base_sha = str(merge_base).strip()

            self.logger.debug(f"Merge base: {merge_base_sha}")

            # 生成 diff
            diff = repo_obj.git.diff(
                '--diff-filter=AM',
                '-U0',
                merge_base_sha,
                f'refs/remotes/origin/{from_branch}',
                '--',
                '.',
                ':(exclude)package-lock.json',
                ':(exclude)*.lock',
                ':(exclude)pnpm-lock.yaml',
                ':(exclude).pnpm',
                ':(exclude)node_modules',
                ':(exclude)*.log'
            )

            # 过滤掉删除的行，只保留新增的行
            filtered_diff = '\n'.join([line for line in diff.split('\n') if not line.startswith('-')])

            self.logger.info(f"成功获取diff，长度: {len(filtered_diff)}")
            return filtered_diff

        except Exception as e:
            self.logger.error(f'Git操作失败: {str(e)}')
            self.increment_error_count()
            raise RuntimeError('Failed to get diff') from e

    def list_repo_files(self, project: str, repo: str, branch: Optional[str] = None, suffixes: Optional[List[str]] = None) -> List[str]:
        """
        获取本地仓库指定分支所有文件（可选后缀过滤），如本地无仓库则自动clone
        """
        try:
            repo_path = self.get_repo_path(project, repo)
            file_list = []
            ssh_url = self.get_ssh_url({'project': project, 'repo': repo})

            self.logger.info(f"列出仓库文件: {project}/{repo}, branch: {branch}, suffixes: {suffixes}")

            # 如果本地仓库不存在或损坏，自动clone
            if not os.path.exists(repo_path) or not os.path.exists(os.path.join(repo_path, '.git')):
                if os.path.exists(repo_path):
                    shutil.rmtree(repo_path)
                self.logger.info(f"克隆仓库: {ssh_url} -> {repo_path}")
                git.Repo.clone_from(ssh_url, repo_path)
            
            repo_obj = git.Repo(repo_path)

            # 如果指定分支，自动fetch并checkout
            if branch:
                repo_obj.git.fetch('origin', branch)
                # 检查本地分支是否存在
                if branch not in repo_obj.heads:
                    repo_obj.git.checkout('-b', branch, f'origin/{branch}')
                else:
                    repo_obj.git.checkout(branch)

            # 遍历文件
            for root, dirs, files in os.walk(repo_path):
                # 跳过.git目录
                if '.git' in root:
                    continue
                    
                for file in files:
                    if not suffixes or any(file.endswith(suf) for suf in suffixes):
                        rel_path = os.path.relpath(os.path.join(root, file), repo_path)
                        # 使用正斜杠作为路径分隔符，保持一致性
                        rel_path = rel_path.replace('\\', '/')
                        file_list.append(rel_path)

            self.logger.info(f"找到 {len(file_list)} 个文件")
            return file_list

        except Exception as e:
            self.logger.error(f'列出仓库文件失败: {str(e)}')
            self.increment_error_count()
            raise RuntimeError('Failed to list repo files') from e

    def read_file(self, project: str, repo: str, file_path: str) -> str:
        """
        读取本地仓库指定文件内容
        """
        try:
            repo_path = self.get_repo_path(project, repo)
            abs_path = os.path.join(repo_path, file_path)
            
            if not os.path.exists(abs_path):
                raise FileNotFoundError(f"文件不存在: {file_path}")

            with open(abs_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            self.logger.debug(f"成功读取文件: {file_path}, 长度: {len(content)}")
            return content

        except Exception as e:
            self.logger.error(f'读取文件失败: {file_path} - {str(e)}')
            self.increment_error_count()
            raise RuntimeError(f'Failed to read file: {file_path}') from e

    def cleanup_repo_cache(self, project: str = None, repo: str = None) -> bool:
        """
        清理仓库缓存
        
        Args:
            project: 项目名，如果为None则清理所有
            repo: 仓库名，只有在project不为None时有效
            
        Returns:
            是否成功
        """
        try:
            if not self.base_dir or not os.path.exists(self.base_dir):
                return True

            if project and repo:
                # 清理特定仓库
                repo_path = self.get_repo_path(project, repo)
                if os.path.exists(repo_path):
                    shutil.rmtree(repo_path)
                    self.logger.info(f"清理仓库缓存: {project}/{repo}")
            elif project:
                # 清理项目下所有仓库
                for item in os.listdir(self.base_dir):
                    if item.startswith(f"{project}_"):
                        item_path = os.path.join(self.base_dir, item)
                        if os.path.isdir(item_path):
                            shutil.rmtree(item_path)
                self.logger.info(f"清理项目缓存: {project}")
            else:
                # 清理所有缓存
                for item in os.listdir(self.base_dir):
                    item_path = os.path.join(self.base_dir, item)
                    if os.path.isdir(item_path):
                        shutil.rmtree(item_path)
                self.logger.info("清理所有仓库缓存")

            return True

        except Exception as e:
            self.logger.error(f"清理仓库缓存失败: {str(e)}")
            return False
