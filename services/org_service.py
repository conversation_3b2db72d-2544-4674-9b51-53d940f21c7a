"""
组织架构服务

按照第一阶段架构重构要求，将原有OrgService迁移到新架构
"""

import os
import hmac
import base64
import hashlib
from pathlib import Path
from typing import Any, Dict, Optional

import requests
import yaml

from services.base_service import BaseService

ROOT_DIR = Path(__file__).resolve().parent.parent.__str__()


class OrgService(BaseService):
    """
    组织架构服务，负责员工信息获取
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__("org_service", config)
        self.env = None
        self.org_config = {}

    async def initialize(self) -> bool:
        """初始化组织架构服务"""
        try:
            self.env = os.environ.get('INF_BOM_ENV', 'test')
            self.org_config = await self._load_org_config()
            
            if not self.org_config:
                self.logger.warning("组织架构配置为空，服务将以降级模式运行")
                return True
            
            self.logger.info(f"组织架构服务初始化成功，环境: {self.env}")
            return True
            
        except Exception as e:
            self.logger.error(f"组织架构服务初始化失败: {str(e)}")
            return False

    async def _load_org_config(self) -> Dict[str, Any]:
        """加载组织架构配置"""
        try:
            config_path = ROOT_DIR + '/conf/org_conf.yaml'
            if not os.path.exists(config_path):
                self.logger.warning(f"组织架构配置文件不存在: {config_path}")
                return {}
                
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
                env_config = config.get(self.env, {})
                
            self.logger.info(f"成功加载组织架构配置，环境: {self.env}")
            return env_config
            
        except Exception as e:
            self.logger.error(f"加载组织架构配置失败: {str(e)}")
            return {}

    async def cleanup(self) -> bool:
        """清理资源"""
        try:
            # 组织架构服务通常不需要特殊清理
            self.logger.info("组织架构服务资源清理完成")
            return True
            
        except Exception as e:
            self.logger.error(f"组织架构服务资源清理失败: {str(e)}")
            return False

    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            if not self.org_config:
                return {
                    "status": "degraded",
                    "message": "组织架构配置未加载"
                }

            # 检查必要的配置项
            required_keys = ['domain', 'accessKey']
            missing_keys = [key for key in required_keys if not self.org_config.get(key)]
            
            if missing_keys:
                return {
                    "status": "error",
                    "message": f"缺少必要配置: {', '.join(missing_keys)}"
                }

            # 尝试进行一个简单的API调用来验证服务可用性
            try:
                # 这里可以添加一个轻量级的健康检查API调用
                return {
                    "status": "healthy",
                    "message": "组织架构服务正常",
                    "config_loaded": True,
                    "environment": self.env
                }
            except Exception as e:
                return {
                    "status": "error",
                    "message": f"组织架构服务异常: {str(e)}"
                }
                
        except Exception as e:
            return {
                "status": "error",
                "message": f"健康检查异常: {str(e)}"
            }

    @staticmethod
    def create_signature(params: Dict[str, Any]) -> str:
        """
        创建ORG签名
        
        Args:
            params: 签名参数，包含accessKey, secretKey, httpMethod, path, signDate
            
        Returns:
            签名字符串
        """
        access_key = params.get('accessKey', '')
        secret_key = params.get('secretKey', '')
        http_method = params.get('httpMethod', 'GET')
        path = params.get('path', '')
        sign_date = params.get('signDate', '')

        # 构建待签名字符串
        string_to_sign = f"{http_method} {path}\n{sign_date}"

        # 使用HMAC-SHA1计算签名
        h = hmac.new(secret_key.encode('utf-8'), string_to_sign.encode('utf-8'), hashlib.sha1)
        signature = base64.b64encode(h.digest()).decode('utf-8')

        return f"MWS {access_key}:{signature}"

    def get_emp_info(self, request_headers: Dict[str, Any], query_params: Dict[str, str]) -> Dict[str, Any]:
        """
        获取员工信息
        
        Args:
            request_headers: 请求头
            query_params: 查询参数
            
        Returns:
            员工信息
        """
        try:
            # 从请求头或查询参数中获取misId
            mis_id_friday = request_headers.get('appfactory-context-user-id')
            mis_id = request_headers.get('mis') or query_params.get('mis')

            # 使用misId获取员工信息
            emp_info = self.get_emp_info_by_mis_id(mis_id_friday or mis_id)
            self.logger.debug(f"获取员工信息成功: {emp_info.get('mis', 'unknown')}")
            return emp_info
            
        except Exception as e:
            self.logger.error(f"获取员工信息失败: {str(e)}")
            self.increment_error_count()
            raise ValueError(f"获取员工信息失败: {str(e)}")

    def get_emp_info_by_mis_id(self, mis_id: Optional[str] = None) -> Dict[str, Any]:
        """
        根据misId获取员工信息
        
        Args:
            mis_id: 员工MIS ID
            
        Returns:
            员工信息
        """
        try:
            if not mis_id:
                raise ValueError("misId is empty")

            if not self.org_config:
                raise ValueError("组织架构配置未加载")

            self.logger.debug(f"查询员工信息，misId: {mis_id}")

            # 构建请求URL和头信息
            domain = self.org_config.get('domain')
            if not domain:
                raise ValueError("组织架构域名配置缺失")

            url = f"{domain}/api/org2/emps/_batch?mises={mis_id}"
            headers = {
                'Authorization': f"MWS {self.org_config.get('accessKey')}:{self.org_config.get('signature')}",
                'Date': self.org_config.get('signDate'),
                'data-scope': 'tenantId=1;source=MT'
            }

            # 发送请求
            self.logger.debug(f"发送组织架构请求: {url}")
            response = requests.get(url, headers=headers, timeout=10)
            response.raise_for_status()

            # 解析响应
            data = response.json().get('data', [])
            emp_info = data[0] if data else {}
            
            if emp_info:
                self.logger.info(f"成功获取员工信息: {emp_info.get('mis', 'unknown')}")
            else:
                self.logger.warning(f"未找到员工信息: {mis_id}")
                
            return emp_info

        except requests.RequestException as e:
            self.logger.error(f"组织架构API请求失败: {str(e)}")
            self.increment_error_count()
            raise ValueError(f"组织架构API请求失败: {str(e)}")
        except Exception as e:
            self.logger.error(f"获取员工信息失败: {str(e)}")
            self.increment_error_count()
            raise ValueError(f"获取员工信息失败: {str(e)}")

    def get_emp_info_batch(self, mis_ids: list) -> Dict[str, Any]:
        """
        批量获取员工信息
        
        Args:
            mis_ids: 员工MIS ID列表
            
        Returns:
            员工信息字典，key为mis_id，value为员工信息
        """
        try:
            if not mis_ids:
                return {}

            if not self.org_config:
                raise ValueError("组织架构配置未加载")

            # 将mis_ids列表转换为逗号分隔的字符串
            mis_ids_str = ','.join(mis_ids)
            
            self.logger.debug(f"批量查询员工信息，misIds: {mis_ids_str}")

            # 构建请求URL和头信息
            domain = self.org_config.get('domain')
            if not domain:
                raise ValueError("组织架构域名配置缺失")

            url = f"{domain}/api/org2/emps/_batch?mises={mis_ids_str}"
            headers = {
                'Authorization': f"MWS {self.org_config.get('accessKey')}:{self.org_config.get('signature')}",
                'Date': self.org_config.get('signDate'),
                'data-scope': 'tenantId=1;source=MT'
            }

            # 发送请求
            response = requests.get(url, headers=headers, timeout=15)
            response.raise_for_status()

            # 解析响应
            data = response.json().get('data', [])
            
            # 构建结果字典
            result = {}
            for emp_info in data:
                mis_id = emp_info.get('mis')
                if mis_id:
                    result[mis_id] = emp_info
            
            self.logger.info(f"批量获取员工信息成功，查询{len(mis_ids)}个，返回{len(result)}个")
            return result

        except requests.RequestException as e:
            self.logger.error(f"批量组织架构API请求失败: {str(e)}")
            self.increment_error_count()
            raise ValueError(f"批量组织架构API请求失败: {str(e)}")
        except Exception as e:
            self.logger.error(f"批量获取员工信息失败: {str(e)}")
            self.increment_error_count()
            raise ValueError(f"批量获取员工信息失败: {str(e)}")

    def search_employees(self, keyword: str, limit: int = 50) -> list:
        """
        搜索员工
        
        Args:
            keyword: 搜索关键词（姓名、MIS等）
            limit: 返回结果数量限制
            
        Returns:
            员工信息列表
        """
        try:
            if not keyword:
                return []

            if not self.org_config:
                raise ValueError("组织架构配置未加载")

            self.logger.debug(f"搜索员工，关键词: {keyword}")

            # 这里需要根据实际的组织架构API来实现搜索功能
            # 目前的API可能不支持搜索，这里提供一个框架
            
            self.logger.warning("员工搜索功能暂未实现")
            return []

        except Exception as e:
            self.logger.error(f"搜索员工失败: {str(e)}")
            self.increment_error_count()
            return []
