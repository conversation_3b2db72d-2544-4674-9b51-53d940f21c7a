"""
服务基类，提供统一的服务接口

按照第一阶段架构重构要求实现
"""

import logging
from abc import ABC, abstractmethod
from datetime import datetime
from enum import Enum
from typing import Any, Dict, Optional

from core.logging import setup_service_logger


class ServiceStatus(Enum):
    """服务状态枚举"""
    INITIALIZING = "initializing"
    RUNNING = "running"
    STOPPED = "stopped"
    ERROR = "error"


class BaseService(ABC):
    """服务基类，提供统一的服务接口"""
    
    def __init__(self, name: str, config: Optional[Dict[str, Any]] = None):
        self.name = name
        self.config = config or {}
        self.logger = setup_service_logger(name)
        self.status = ServiceStatus.INITIALIZING
        self.start_time = None
        self.error_count = 0
        
    @abstractmethod
    async def initialize(self) -> bool:
        """初始化服务"""
        pass
    
    @abstractmethod
    async def cleanup(self) -> bool:
        """清理资源"""
        pass
    
    @abstractmethod
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        pass
    
    async def start(self) -> bool:
        """启动服务"""
        try:
            self.logger.info(f"Starting service {self.name}")
            success = await self.initialize()
            if success:
                self.status = ServiceStatus.RUNNING
                self.start_time = datetime.now()
                self.logger.info(f"Service {self.name} started successfully")
            else:
                self.status = ServiceStatus.ERROR
                self.logger.error(f"Failed to start service {self.name}")
            return success
        except Exception as e:
            self.status = ServiceStatus.ERROR
            self.logger.error(f"Error starting service {self.name}: {str(e)}")
            return False
    
    async def stop(self) -> bool:
        """停止服务"""
        try:
            self.logger.info(f"Stopping service {self.name}")
            success = await self.cleanup()
            self.status = ServiceStatus.STOPPED
            self.logger.info(f"Service {self.name} stopped")
            return success
        except Exception as e:
            self.logger.error(f"Error stopping service {self.name}: {str(e)}")
            return False
    
    def get_service_info(self) -> Dict[str, Any]:
        """获取服务信息"""
        uptime = None
        if self.start_time:
            uptime = (datetime.now() - self.start_time).total_seconds()
            
        return {
            "name": self.name,
            "status": self.status.value,
            "uptime": uptime,
            "error_count": self.error_count,
            "config": self.config
        }
    
    def increment_error_count(self):
        """增加错误计数"""
        self.error_count += 1
