"""
大象通知服务

按照第一阶段架构重构要求，将原有DaXiangService迁移到新架构
"""

import json
import logging
import os
from pathlib import Path
from typing import Any, Dict, List, Optional

import dotenv
import octo_rpc
from octo_rpc import NonMeshClient, load

from services.base_service import BaseService
from services.kms_service import KmsService
from services.org_service import OrgService

dotenv.load_dotenv()
octo_rpc.log.setLevel(logging.ERROR)

ROOT_DIR = Path(__file__).resolve().parent.parent.__str__()


class DaXiangService(BaseService):
    """
    大象通知服务，负责与大象平台相关的API交互
    """

    def __init__(self, kms_service: KmsService, config: Optional[Dict[str, Any]] = None):
        super().__init__("daxiang_service", config)
        self.kms_service = kms_service
        self.org_service = None
        
        # 客户端配置
        self.dx_auth_client = None
        self.dx_invoke_client = None
        self.dx_messages_client = None
        
        # 配置参数
        self.CLIENT_APP_KEY = None
        self.DX_APP_ID = None
        self.DX_APP_KEY = None
        self.env = None

    async def initialize(self) -> bool:
        """初始化大象服务"""
        try:
            # 初始化组织服务
            self.org_service = OrgService()
            
            # 获取配置
            self.CLIENT_APP_KEY = os.environ.get('CLIENT_APP_KEY')
            if not self.CLIENT_APP_KEY:
                self.logger.error("CLIENT_APP_KEY 环境变量未设置")
                return False

            # 根据环境选择APP_ID
            self.env = os.environ.get('INF_BOM_ENV', 'prod')
            if self.env == 'test':
                self.DX_APP_ID = os.environ.get('DX_APP_ID_DEV')
            else:
                self.DX_APP_ID = os.environ.get('DX_APP_ID_PROD')
                
            if not self.DX_APP_ID:
                self.logger.error('DX_APP_ID 必须设置。请在 .env 文件中设置这些变量，或者在运行时设置环境变量。')
                return False

            self.DX_APP_KEY = os.environ.get("DX_APP_KEY")
            if not self.DX_APP_KEY:
                self.logger.error("DX_APP_KEY 环境变量未设置")
                return False

            # 初始化Thrift客户端
            await self._init_thrift_clients()
            
            self.logger.info("大象服务初始化成功")
            return True
            
        except Exception as e:
            self.logger.error(f"大象服务初始化失败: {str(e)}")
            return False

    async def _init_thrift_clients(self):
        """初始化Thrift客户端"""
        try:
            # 初始化认证服务客户端
            open_auth_service = load(ROOT_DIR + "/infra/thrift/daxiang/AuthService.thrift")
            self.dx_auth_client = NonMeshClient(
                appkey=self.CLIENT_APP_KEY,
                timeout=5000,
                env=self.env,
                service_name="com.sankuai.xm.openplatform.auth.service.XmAuthServiceI",
                remote_appkey=self.DX_APP_KEY,
                service=open_auth_service.XmAuthServiceI
            )

            # 初始化调用服务客户端
            open_invoke_service = load(ROOT_DIR + "/infra/thrift/daxiang/InvokeApi.thrift")
            self.dx_invoke_client = NonMeshClient(
                appkey=self.CLIENT_APP_KEY,
                remote_appkey=self.DX_APP_KEY,
                timeout=5000,
                service_name="com.sankuai.xm.openplatform.api.service.open.OpenServiceI",
                service=open_invoke_service.OpenServiceI
            )

            # 初始化消息服务客户端
            open_message_service = load(ROOT_DIR + "/infra/thrift/daxiang/OpenMessageService.thrift")
            self.dx_messages_client = NonMeshClient(
                appkey=self.CLIENT_APP_KEY,
                timeout=5000,
                service_name="com.sankuai.xm.openplatform.api.service.open.XmOpenMessageServiceI",
                remote_appkey=self.DX_APP_KEY,
                service=open_message_service.XmOpenMessageServiceI
            )
            
            self.logger.info("Thrift客户端初始化成功")
            
        except Exception as e:
            self.logger.error(f"Thrift客户端初始化失败: {str(e)}")
            raise

    async def cleanup(self) -> bool:
        """清理资源"""
        try:
            # 清理客户端连接
            self.dx_auth_client = None
            self.dx_invoke_client = None
            self.dx_messages_client = None
            
            self.logger.info("大象服务资源清理完成")
            return True
            
        except Exception as e:
            self.logger.error(f"大象服务资源清理失败: {str(e)}")
            return False

    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            # 检查客户端是否初始化
            if not all([self.dx_auth_client, self.dx_invoke_client, self.dx_messages_client]):
                return {
                    "status": "error",
                    "message": "Thrift客户端未初始化"
                }
            
            # 尝试获取访问令牌来验证服务可用性
            try:
                token = self.get_access_token({})
                if token:
                    return {
                        "status": "healthy",
                        "message": "大象服务正常",
                        "clients": {
                            "auth_client": "connected",
                            "invoke_client": "connected", 
                            "messages_client": "connected"
                        }
                    }
                else:
                    return {
                        "status": "degraded",
                        "message": "无法获取访问令牌"
                    }
            except Exception as e:
                return {
                    "status": "error",
                    "message": f"健康检查失败: {str(e)}"
                }
                
        except Exception as e:
            return {
                "status": "error",
                "message": f"健康检查异常: {str(e)}"
            }

    def get_access_token(self, ctx: Any) -> Optional[str]:
        """获取办公开放平台accessToken"""
        try:
            if not self.dx_auth_client:
                self.logger.error("认证客户端未初始化")
                return None

            # 获取办公开放平台APP_SECRET
            kms_service_res = self.kms_service.get_key(self.CLIENT_APP_KEY, "dxopen_sk")
            app_secret = kms_service_res.get('data')
            
            if not app_secret:
                self.logger.error("无法获取APP_SECRET")
                return None

            auth_entity = load(ROOT_DIR + "/infra/thrift/daxiang/AuthEntity.thrift")
            self.logger.debug(f"获取token - app_id: {self.DX_APP_ID}")
            
            data = self.dx_auth_client.accessToken(
                appAuthInfo=auth_entity.AppAuthInfo(appkey=self.DX_APP_ID, appSecret=app_secret)
            )
            
            code = data.status.code
            msg = data.status.msg
            
            if code != 0:
                self.logger.error(f"token 获取失败: {msg}")
                return None

            self.logger.info("获取访问令牌成功")
            return data.accessToken.token
            
        except Exception as e:
            self.logger.error(f"获取token失败: {str(e)}")
            self.increment_error_count()
            return None

    def convert_emp_id_to_user_id(self, emp_ids: List[str], ctx: Any) -> Optional[List[str]]:
        """将empId转换为userId"""
        try:
            if not self.dx_invoke_client:
                self.logger.error("调用客户端未初始化")
                return None

            token = self.get_access_token(ctx)
            if not token:
                return None

            json_data = json.dumps({'empIdList': emp_ids})

            invoke_entity = load(ROOT_DIR + "/infra/thrift/daxiang/AuthEntity.thrift")
            data = self.dx_invoke_client.invoke(
                token,
                invoke_entity.InvokeReq(
                    path='/open-apis/dx/queryUidByEmpIdList',
                    jsonData=json_data
                )
            )

            if data.status.code == 0:
                uid_map = json.loads(data.data).get('uidMap', {})
                uid_list = list(uid_map.values())
                self.logger.info(f"empId转换为userId成功，转换了{len(uid_list)}个ID")
                return uid_list

            self.logger.error(f"empId转换失败: {data.status.msg}")
            return None
            
        except Exception as e:
            self.logger.error(f"empId转换为userId失败: {str(e)}")
            self.increment_error_count()
            return None

    def convert_userid_to_emp_id(self, uids: List[str], ctx: Any) -> Optional[List[str]]:
        """将uid转换为empId"""
        try:
            if not self.dx_invoke_client:
                self.logger.error("调用客户端未初始化")
                return None

            token = self.get_access_token(ctx)
            if not token:
                return None

            json_data = json.dumps({'uidList': uids})

            invoke_entity = load(ROOT_DIR + "/infra/thrift/daxiang/AuthEntity.thrift")
            data = self.dx_invoke_client.invoke(
                token,
                invoke_entity.InvokeReq(
                    path='/open-apis/dx/queryUidByEmpIdList',
                    jsonData=json_data
                )
            )

            if data.status.code == 0:
                emp_id_map = json.loads(data.data).get('empIdMap', {})
                emp_list = list(emp_id_map.values())
                self.logger.info(f"uid转换为empId成功，转换了{len(emp_list)}个ID")
                return emp_list

            self.logger.error(f"uid转换失败: {data.status.msg}")
            return None
            
        except Exception as e:
            self.logger.error(f"uid转换为empId失败: {str(e)}")
            self.increment_error_count()
            return None

    async def send_chat_msg_by_robot(self, ctx: Any, params: Optional[Dict[str, Any]] = None) -> Any:
        """以机器人身份发送单聊消息"""
        try:
            if not self.dx_messages_client:
                self.logger.error("消息客户端未初始化")
                return None

            if params is None:
                params = {}

            # 获取办公开放平台accessToken
            token = self.get_access_token(ctx)
            if not token:
                return None

            # 获取接收者ID
            receiver_ids = await self._get_receiver_ids(ctx, params)
            if not receiver_ids:
                self.logger.error("无法获取接收者ID")
                return None

            self.logger.info(f"发送消息 - token: {token[:10]}..., receivers: {receiver_ids}, text: {params.get('text', '')[:50]}...")

            # 移除不需要的参数
            body_params = {k: v for k, v in params.items() if k not in ['uid', 'empId', 'type']}

            messages_entity = load(ROOT_DIR + "/infra/thrift/daxiang/MessageEntity.thrift")
            data = self.dx_messages_client.sendChatMsgByRobot(
                token,
                messages_entity.SendChatMsgByRobotReq(
                    receiverIds=receiver_ids,
                    sendMsgInfo=messages_entity.SendMsgInfo(
                        type=params.get('type', 'text'),
                        body=json.dumps(body_params),
                        isDynamicMsg=False,
                    )
                )
            )

            code = data.status.code
            msg = data.status.msg

            if code != 0:
                self.logger.error(f"发送消息失败: {msg}")
                return None

            self.logger.info("发送消息成功")
            return data
            
        except Exception as e:
            self.logger.error(f"发送消息失败: {str(e)}")
            self.increment_error_count()
            return None

    async def _get_receiver_ids(self, ctx: Any, params: Dict[str, Any]) -> Optional[List[str]]:
        """获取接收者ID列表"""
        try:
            # 如果直接提供了uid
            if params.get('uid'):
                return [params['uid']]

            # 通过empId获取
            emp_id = params.get('empId')
            if not emp_id and hasattr(ctx, 'sdk') and hasattr(ctx.sdk, 'sso'):
                emp_id = ctx.sdk.sso.userInfo.id

            if emp_id:
                return self.convert_emp_id_to_user_id([emp_id], ctx)

            # 通过组织服务获取员工信息
            if self.org_service:
                emp_info = self.org_service.get_emp_info(
                    getattr(ctx, 'headers', {}),
                    getattr(ctx, 'args', {})
                )
                if emp_info and emp_info.get('empId'):
                    return [str(emp_info.get('empId'))]

            return None
            
        except Exception as e:
            self.logger.error(f"获取接收者ID失败: {str(e)}")
            return None
