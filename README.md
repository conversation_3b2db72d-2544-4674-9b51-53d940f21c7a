# Python代码审查规则系统

## 项目简介

这是一个专业的Python代码审查(Code Review)规则配置系统，基于PEP规范和Python最佳实践，提供全面的代码质量检查规则。系统支持多语言、多业务场景的规则配置，帮助团队建立统一的代码审查标准。

## 核心特性

### 🎯 三级问题分类
- **P0级别**: 严重问题，必须修复（语法错误、安全漏洞、资源泄漏等）
- **P1级别**: 重要问题，建议修复（代码规范、异常处理、文档缺失等）  
- **P2级别**: 一般问题，建议优化（命名规范、代码结构、Python习惯等）

### 📋 全面的Python规则覆盖

#### P0级别规则（5条）
1. **语法错误和运行时错误** - 确保代码可正常运行
2. **安全漏洞** - 防范SQL注入、命令注入、明文密码等安全风险
3. **业务逻辑漏洞** - 避免数据丢失、状态不一致等业务风险
4. **资源泄漏** - 确保文件、连接等资源正确释放
5. **线程安全问题** - 多线程环境下的安全性检查

#### P1级别规则（6条）
1. **PEP8规范违反** - 遵循Python官方代码风格
2. **魔法数字和字符串** - 提高代码可维护性
3. **异常处理不当** - 规范异常处理机制
4. **文档和注释缺失** - 确保代码可读性
5. **类型提示缺失** - 提供更好的IDE支持和代码理解
6. **性能问题** - 识别明显的性能瓶颈

#### P2级别规则（6条）
1. **命名不清晰** - 提升代码语义表达
2. **代码结构问题** - 优化代码组织结构
3. **调试代码未清理** - 清理临时调试代码
4. **代码重复** - 遵循DRY原则
5. **不符合Python习惯** - 推广Pythonic编程风格
6. **测试相关问题** - 确保测试质量

## 使用方法

### 基本使用

```python
