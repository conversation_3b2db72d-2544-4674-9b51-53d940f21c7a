# Shangou AI CR 项目说明

## 项目简介

本项目为基于 Flask 的企业级后端服务脚手架，集成了 API 管理、用户认证、数据库操作、日志、配置管理、容器化部署等能力，适用于中大型业务系统的快速开发与扩展。支持本地开发、Docker 部署，内置 Swagger API 文档，便于接口调试与协作。

## 主要技术栈
- Python 3.10
- Flask 3.x
- peewee ORM
- MySQL 8.x
- Docker & docker-compose
- flasgger (Swagger UI)
- flask-login, flask-session, flask-cors

## 目录结构说明

```
├── api/                # API服务主目录
│   ├── app_server.py   # API服务启动脚本
│   ├── apps/           # 业务API蓝图注册与实现
│   └── __init__.py     # API包初始化
├── common/             # 全局配置与常量
├── conf/               # 配置文件（如数据库、服务等）
├── consts/             # 常量定义
├── docker/             # Docker相关文件与脚本
├── infra/              # 基础设施层（数据模型、服务、适配器等）
│   ├── repo/           # 数据仓库相关
│   │   ├── models/     # ORM数据模型定义（如db_models.py，定义所有数据库表结构、基础模型、用户模型等）
│   │   ├── services/   # 通用数据服务（如common_service.py，提供增删改查等通用数据库操作方法）
│   │   └── adpter/     # 数据库适配器（如zebra_peewee_adapter.py，对接公司Zebra数据库代理，支持标准与代理两种连接）
│   ├── serializers/    # 序列化相关，预留自定义数据序列化与反序列化逻辑
│   └── proxy/          # 代理相关，预留底层服务代理或中间件能力
├── tests/              # 单元测试
├── utils/              # 工具库
├── main.py             # 项目入口（简易启动）
├── requirements.txt    # Python依赖
├── Dockerfile          # Docker镜像构建文件
├── build.sh/install.sh/check.sh # 构建、安装、健康检查脚本
└── manifest.yaml       # 部署元信息
```

## 环境依赖
- Python 3.10+
- MySQL 8.x（可选，推荐容器化）
- pip / venv / virtualenv
- Docker & docker-compose（可选）

安装依赖：
```bash
pip install -r requirements.txt
```

## 快速开始

### 1. 本地运行

1. 安装依赖：
   ```bash
   pip install -r requirements.txt
   ```
2. 启动服务：
   ```bash
   python main.py
   ```
3. 访问接口文档：
   浏览器打开 http://localhost:5000/apidocs/

### 2. Docker 方式

1. 构建镜像并启动服务：
   ```bash
   cd docker
   docker-compose build
   docker-compose up -d
   ```
2. 访问接口文档：
   浏览器打开 http://localhost:5000/apidocs/

### 3. 其他常用命令
- 构建：`sh build.sh`
- 安装：`sh install.sh`
- 健康检查：`sh check.sh`

## API 说明
- 所有API均以 `/shangou_ai_cr/api/v1/` 为前缀（可在 `consts/service_consts.py` 配置版本号）。
- 支持Swagger UI，接口文档入口：`/apidocs/`
- 新增API：在 `api/apps/` 下新建 `xxx_app.py`，并在 `__init__.py` 自动注册。
- 示例API：`/shangou_ai_cr/api/v1/api/token_list`（需登录态）

## 配置说明
- 全局配置集中于 `common/settings.py` 和 `conf/` 目录。
- 数据库、认证、第三方服务等可通过环境变量或配置文件调整。
- 主要环境变量：
  - `DB_TYPE`/`DB_NAME`/`DB_USER`/`DB_PASSWORD`/`DB_HOST`/`DB_PORT`
  - `SECRET_KEY`（Flask会话密钥）
  - `DOC_ENGINE`（文档引擎类型，默认elasticsearch）

## 开发与扩展
- 新增API：
  1. 在 `api/apps/` 下新建 `xxx_app.py`，定义路由与逻辑。
  2. 蓝图会被自动注册，无需手动添加。
- 数据模型扩展：在 `infra/repo/models/db_models.py` 中定义ORM模型。
- 工具函数扩展：在 `utils/` 目录下添加。
- 日志、配置、认证等均可按需自定义。

## 测试说明
- 测试用例位于 `tests/` 目录。
- 运行示例：
  ```bash
  python -m unittest discover tests
  ```

## 常见问题
- **端口冲突**：请确保5000端口未被占用，或在配置中修改。
- **数据库连接失败**：检查MySQL服务是否启动，配置是否正确。
- **依赖安装失败**：建议使用Python 3.10+，并升级pip。
- **API未生效**：确认已在 `api/apps/` 下新建并命名为 `*_app.py`。

## 参考
- [Flask官方文档](https://flask.palletsprojects.com/)
- [peewee ORM](http://docs.peewee-orm.com/)
- [Swagger UI](https://swagger.io/tools/swagger-ui/)

---
如有问题请联系项目维护者或查阅源码注释。
