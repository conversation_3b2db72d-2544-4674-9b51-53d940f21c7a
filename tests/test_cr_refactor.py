#!/usr/bin/env python3
"""
CR服务重构测试

验证重构后的CR服务功能是否正常
"""

import unittest
import asyncio
import sys
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


class TestCRRefactor(unittest.TestCase):
    """CR服务重构测试类"""
    
    def setUp(self):
        """测试前置设置"""
        self.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.loop)
    
    def tearDown(self):
        """测试后置清理"""
        self.loop.close()
    
    def test_agent_imports(self):
        """测试Agent模块导入"""
        try:
            from core.agents.base_agent import BaseAgent, AgentResult
            from core.agents.llm_review_agent import LLMReviewAgent
            from core.agents.diff_analysis_agent import DiffAnalysisAgent
            from core.agents.result_aggregation_agent import ResultAggregationAgent
            from core.agents.agent_factory import AgentFactory, get_agent_factory
            
            self.assertTrue(True, "Agent模块导入成功")
            
        except ImportError as e:
            self.fail(f"Agent模块导入失败: {str(e)}")
    
    def test_orchestrator_imports(self):
        """测试编排器模块导入"""
        try:
            from core.orchestrator.base_orchestrator import BaseOrchestrator, WorkflowResult
            from core.orchestrator.cr_orchestrator import CROrchestrator
            from core.orchestrator.workflow_manager import WorkflowManager, get_workflow_manager
            from core.orchestrator.task_scheduler import TaskScheduler
            
            self.assertTrue(True, "编排器模块导入成功")
            
        except ImportError as e:
            self.fail(f"编排器模块导入失败: {str(e)}")
    
    def test_cr_service_import(self):
        """测试CR服务导入"""
        try:
            from services.cr_service import CRService
            from core.service_registry import get_cr_service
            
            self.assertTrue(True, "CR服务导入成功")
            
        except ImportError as e:
            self.fail(f"CR服务导入失败: {str(e)}")
    
    def test_agent_factory_creation(self):
        """测试Agent工厂创建"""
        from core.agents.agent_factory import get_agent_factory
        
        factory = get_agent_factory()
        self.assertIsNotNone(factory)
        
        # 测试Agent类型列表
        agent_types = factory.list_agent_types()
        expected_types = ['llm_review', 'diff_analysis', 'result_aggregation']
        
        for agent_type in expected_types:
            self.assertIn(agent_type, agent_types, f"缺少Agent类型: {agent_type}")
    
    def test_workflow_manager_creation(self):
        """测试工作流管理器创建"""
        from core.orchestrator.workflow_manager import get_workflow_manager
        
        manager = get_workflow_manager()
        self.assertIsNotNone(manager)
        
        # 测试编排器类型列表
        orchestrator_types = manager.list_orchestrator_types()
        self.assertIn('cr_orchestrator', orchestrator_types)
    
    def test_base_agent_functionality(self):
        """测试Agent基类功能"""
        from core.agents.base_agent import BaseAgent, AgentResult, AgentStatus
        
        # 创建测试Agent
        class TestAgent(BaseAgent):
            def validate_input(self, input_data):
                return 'test_data' in input_data
            
            async def execute(self, input_data):
                return AgentResult(success=True, data={'result': 'test'})
        
        agent = TestAgent("test_agent")
        
        # 测试状态
        self.assertEqual(agent.status, AgentStatus.IDLE)
        
        # 测试配置更新
        agent.update_config({'test_key': 'test_value'})
        self.assertEqual(agent.config['test_key'], 'test_value')
        
        # 测试状态获取
        status = agent.get_status()
        self.assertIn('name', status)
        self.assertIn('status', status)
    
    def test_base_orchestrator_functionality(self):
        """测试编排器基类功能"""
        from core.orchestrator.base_orchestrator import BaseOrchestrator, WorkflowResult, WorkflowStatus
        
        # 创建测试编排器
        class TestOrchestrator(BaseOrchestrator):
            def validate_input(self, input_data):
                return 'test_data' in input_data
            
            async def execute_workflow(self, input_data):
                return WorkflowResult(success=True, data={'result': 'test'})
        
        orchestrator = TestOrchestrator("test_orchestrator")
        
        # 测试状态
        self.assertEqual(orchestrator.status, WorkflowStatus.PENDING)
        
        # 测试配置更新
        orchestrator.update_config({'test_key': 'test_value'})
        self.assertEqual(orchestrator.config['test_key'], 'test_value')
    
    @patch('services.cr_service.get_service')
    def test_cr_service_initialization(self, mock_get_service):
        """测试CR服务初始化"""
        from services.cr_service import CRService
        
        # Mock依赖服务
        mock_services = {
            'llm_service': Mock(),
            'chunk_service': Mock(),
            'git_service': Mock(),
            'horn_service': Mock(),
            'kms_service': Mock()
        }
        
        def mock_get_service_side_effect(service_name):
            return mock_services.get(service_name)
        
        mock_get_service.side_effect = mock_get_service_side_effect
        
        # 创建CR服务
        cr_service = CRService()
        
        # 测试基本属性
        self.assertEqual(cr_service.name, "cr_service")
        self.assertIn('cr_mode', cr_service.config)
        self.assertEqual(cr_service.get_cr_mode(), 'standard')
    
    def test_agent_result_serialization(self):
        """测试Agent结果序列化"""
        from core.agents.base_agent import AgentResult
        
        result = AgentResult(
            success=True,
            data={'test': 'data'},
            metadata={'agent': 'test'}
        )
        
        result_dict = result.to_dict()
        
        self.assertIn('success', result_dict)
        self.assertIn('data', result_dict)
        self.assertIn('metadata', result_dict)
        self.assertTrue(result_dict['success'])
    
    def test_workflow_result_serialization(self):
        """测试工作流结果序列化"""
        from core.orchestrator.base_orchestrator import WorkflowResult
        
        result = WorkflowResult(
            success=True,
            data={'test': 'data'},
            execution_time=1.5
        )
        
        result_dict = result.to_dict()
        
        self.assertIn('success', result_dict)
        self.assertIn('data', result_dict)
        self.assertIn('execution_time', result_dict)
        self.assertTrue(result_dict['success'])
        self.assertEqual(result_dict['execution_time'], 1.5)
    
    async def async_test_agent_execution(self):
        """异步测试Agent执行"""
        from core.agents.base_agent import BaseAgent, AgentResult
        
        class MockAgent(BaseAgent):
            def validate_input(self, input_data):
                return True
            
            async def execute(self, input_data):
                await asyncio.sleep(0.1)  # 模拟异步操作
                return AgentResult(success=True, data={'processed': input_data})
        
        agent = MockAgent("mock_agent")
        result = await agent.run({'test': 'data'})
        
        self.assertTrue(result.success)
        self.assertIn('processed', result.data)
    
    def test_agent_execution_sync(self):
        """同步测试Agent执行"""
        self.loop.run_until_complete(self.async_test_agent_execution())
    
    async def async_test_orchestrator_execution(self):
        """异步测试编排器执行"""
        from core.orchestrator.base_orchestrator import BaseOrchestrator, WorkflowResult
        
        class MockOrchestrator(BaseOrchestrator):
            def validate_input(self, input_data):
                return True
            
            async def execute_workflow(self, input_data):
                await asyncio.sleep(0.1)  # 模拟异步操作
                return WorkflowResult(success=True, data={'processed': input_data})
        
        orchestrator = MockOrchestrator("mock_orchestrator")
        result = await orchestrator.run({'test': 'data'})
        
        self.assertTrue(result.success)
        self.assertIn('processed', result.data)
    
    def test_orchestrator_execution_sync(self):
        """同步测试编排器执行"""
        self.loop.run_until_complete(self.async_test_orchestrator_execution())


class TestCRServiceIntegration(unittest.TestCase):
    """CR服务集成测试"""
    
    def test_service_registry_integration(self):
        """测试服务注册表集成"""
        try:
            from core.service_registry import get_cr_service, register_service
            from services.cr_service import CRService
            
            # 测试服务注册
            cr_service = CRService()
            register_service("test_cr_service", cr_service)
            
            # 测试服务获取
            retrieved_service = get_cr_service()
            # 注意：这里可能返回新创建的服务实例，所以只测试类型
            self.assertIsNotNone(retrieved_service)
            
        except Exception as e:
            self.fail(f"服务注册表集成测试失败: {str(e)}")
    
    def test_config_management(self):
        """测试配置管理"""
        try:
            from config.base_config import BaseConfig
            
            config = BaseConfig()
            
            # 测试基本配置
            self.assertIsNotNone(config.app_name)
            self.assertIsNotNone(config.version)
            
            # 测试服务配置获取
            llm_config = config.get_service_config("llm")
            self.assertIsInstance(llm_config, dict)
            
        except Exception as e:
            self.fail(f"配置管理测试失败: {str(e)}")


def run_tests():
    """运行所有测试"""
    print("🧪 开始运行CR服务重构测试...")
    
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试类
    test_classes = [TestCRRefactor, TestCRServiceIntegration]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出结果
    if result.wasSuccessful():
        print("\n✅ 所有测试通过！CR服务重构验证成功。")
        return True
    else:
        print(f"\n❌ 测试失败！失败数: {len(result.failures)}, 错误数: {len(result.errors)}")
        return False


if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
