#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import unittest
import time
import json
import uuid
import logging
from concurrent.futures import ThreadPoolExecutor

# 添加项目根目录到导入路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
sys.path.insert(0, project_root)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

# 导入测试目标
from rag.utils.squirrel_conn import MT_SQUIRREL_CONN

class TestSquirrelQueue(unittest.TestCase):
    """测试Squirrel队列实现"""
    
    def setUp(self):
        """测试前准备"""
        self.queue_name = f"test_queue_{uuid.uuid4().hex[:8]}"
        self.group_name = "test_group"
        self.consumer_name = "test_consumer"
        self.redis_conn = MT_SQUIRREL_CONN
        logging.info(f"创建测试队列: {self.queue_name}")
        
    def tearDown(self):
        """测试后清理"""
        try:
            # 获取所有相关键名
            base_key = self.redis_conn.build_category_key(f"{{queue}}:{self.queue_name}")
            queue_key = base_key
            messages_key = f"{base_key}:messages"
            pending_key = f"{base_key}:pending"
            group_key = f"{base_key}:groups"
            group_pending_key = f"{base_key}:pending:{self.group_name}"
            consumer_key = f"{base_key}:consumer:{self.group_name}:{self.consumer_name}"
            
            # 清理所有键
            keys_to_delete = [
                queue_key, messages_key, pending_key, group_key, 
                group_pending_key, consumer_key, f"{base_key}:errors"
            ]
            
            for key in keys_to_delete:
                try:
                    self.redis_conn.REDIS.delete(key)
                except Exception as e:
                    logging.warning(f"清理键 {key} 失败: {str(e)}")
                    
            logging.info(f"已清理测试队列: {self.queue_name}")
        except Exception as e:
            logging.exception(f"清理资源异常: {str(e)}")
    
    def test_basic_queue_operations(self):
        """测试基本的队列生产消费操作"""
        # 创建测试消息
        test_message = {
            "id": str(uuid.uuid4()),
            "content": "测试消息内容",
            "timestamp": time.time()
        }
        
        # 生产消息
        result = self.redis_conn.queue_product(self.queue_name, test_message)
        self.assertTrue(result, "生产消息应该成功")
        
        # 消费消息
        redis_msg = self.redis_conn.queue_consumer(self.queue_name, self.group_name, self.consumer_name)
        self.assertIsNotNone(redis_msg, "应该能够消费到消息")
        
        # 验证消息内容
        received_message = redis_msg.get_message()
        self.assertEqual(received_message["id"], test_message["id"], "消息ID应该一致")
        self.assertEqual(received_message["content"], test_message["content"], "消息内容应该一致")
        
        # 确认消息
        ack_result = redis_msg.ack()
        self.assertTrue(ack_result, "确认消息应该成功")
        
        # 再次尝试消费，应该没有消息
        redis_msg = self.redis_conn.queue_consumer(self.queue_name, self.group_name, self.consumer_name)
        self.assertIsNone(redis_msg, "确认后应该没有消息可消费")
    
    def test_message_reprocessing(self):
        """测试消息重处理功能"""
        # 创建测试消息
        test_message = {
            "id": str(uuid.uuid4()),
            "content": "需要重处理的消息",
            "timestamp": time.time()
        }
        
        # 生产消息
        self.redis_conn.queue_product(self.queue_name, test_message)
        
        # 消费消息但不确认
        redis_msg = self.redis_conn.queue_consumer(self.queue_name, self.group_name, self.consumer_name)
        self.assertIsNotNone(redis_msg, "应该能够消费到消息")
        msg_id = redis_msg.get_msg_id()
        
        # 再次消费相同消息ID
        redis_msg2 = self.redis_conn.queue_consumer(self.queue_name, self.group_name, "consumer2", msg_id)
        self.assertIsNotNone(redis_msg2, "应该能够使用ID重新消费消息")
        
        # 验证两次消费的是同一条消息
        self.assertEqual(redis_msg.get_message()["id"], redis_msg2.get_message()["id"], "两次消费的消息ID应该一致")
        self.assertEqual(msg_id, redis_msg2.get_msg_id(), "消息ID应保持一致")
        
        # 确认消息
        ack_result = redis_msg2.ack()
        self.assertTrue(ack_result, "确认消息应该成功")
    
    def test_multiple_consumers(self):
        """测试多个消费者并行处理"""
        # 创建多条测试消息
        messages = []
        for i in range(10):
            message = {
                "id": str(uuid.uuid4()),
                "content": f"测试消息-{i}",
                "timestamp": time.time()
            }
            messages.append(message)
            result = self.redis_conn.queue_product(self.queue_name, message)
            self.assertTrue(result, f"生产消息{i}应该成功")
        
        # 创建多个消费者并发消费
        received_messages = set()
        
        def consumer_task(consumer_id):
            consumer_name = f"consumer_{consumer_id}"
            msg = self.redis_conn.queue_consumer(self.queue_name, self.group_name, consumer_name)
            if msg:
                received_message = msg.get_message()
                received_messages.add(received_message["id"])
                msg.ack()
                return True
            return False
        
        # 使用线程池模拟多个消费者
        with ThreadPoolExecutor(max_workers=5) as executor:
            results = list(executor.map(consumer_task, range(10)))
        
        # 验证所有消息都被处理
        self.assertEqual(len(received_messages), 10, "应处理全部10条消息")
        
        # 验证没有消息可消费
        redis_msg = self.redis_conn.queue_consumer(self.queue_name, self.group_name, "final_check")
        self.assertIsNone(redis_msg, "处理后应该没有消息")
    
    def test_memory_cleanup(self):
        """测试内存清理功能"""
        # 设置较小的队列长度，测试超出时的清理逻辑
        # 生产5条消息，最大队列长度为3
        for i in range(5):
            message = {
                "id": str(uuid.uuid4()),
                "content": f"测试消息-{i}",
                "timestamp": time.time()
            }
            
            # 获取原始脚本并修改最大长度参数
            base_key = self.redis_conn.build_category_key(f"{{queue}}:{self.queue_name}")
            queue_key = base_key
            pending_key = f"{base_key}:pending" 
            group_key = f"{base_key}:groups"
            
            # 直接使用eval执行脚本，手动设置最大长度为3
            msg_str = json.dumps({"message": json.dumps(message)})
            msg_id = str(uuid.uuid4())
            
            self.redis_conn.REDIS.eval(
                self.redis_conn.producer_script,
                3,
                queue_key,
                pending_key, 
                group_key,
                msg_str,
                msg_id,
                3  # 最大队列长度为3
            )
        
        # 检查队列长度，应该只有最新的3条
        queue_length = self.redis_conn.REDIS.llen(self.redis_conn.build_category_key(f"{{queue}}:{self.queue_name}"))
        self.assertEqual(queue_length, 3, "队列长度应该被限制为3")
        
        # 检查消息存储哈希表的大小，应该也是3
        # 注意：在Lua脚本中，消息哈希表键名是通过 queue_key .. ':messages' 构建的
        base_key = self.redis_conn.build_category_key(f"{{queue}}:{self.queue_name}")
        messages_key = f"{base_key}:messages"  # 直接添加后缀，与Lua脚本一致
        messages_count = self.redis_conn.REDIS.hlen(messages_key)
        self.assertEqual(messages_count, 3, "消息存储哈希表大小应该为3")
        
        # 消费所有消息确认内容可访问
        consumed = 0
        while True:
            msg = self.redis_conn.queue_consumer(self.queue_name, self.group_name, self.consumer_name)
            if not msg:
                break
            msg.ack()
            consumed += 1
        
        self.assertEqual(consumed, 3, "应该消费到3条消息")

if __name__ == "__main__":
    unittest.main() 