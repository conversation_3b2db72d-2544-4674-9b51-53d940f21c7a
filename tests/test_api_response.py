"""
测试实际API返回结果
验证最终API返回的数据是否正确反映了我们的修复
"""

import asyncio
import sys
import os
from unittest.mock import Mock, patch

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.cr_service import CRService


def create_mock_services():
    """创建模拟服务"""
    horn_service = Mock()
    horn_service.fetch_config.return_value = {
        "projectConfig": [],
        "pythonBaseCrRules": "Python代码审查规则"
    }
    
    kms_service = Mock()
    dx_service = Mock()
    git_service = Mock()
    devmind_service = Mock()
    
    return horn_service, kms_service, dx_service, git_service, devmind_service


def create_sample_code_diff():
    """创建示例代码差异"""
    return """
diff --git a/test.py b/test.py
index 1234567..abcdefg 100644
--- a/test.py
+++ b/test.py
@@ -1,3 +1,8 @@
+def risky_function(user):
+    # 这里有空指针异常风险
+    return user.getName()  # P0问题：没有空值检查
+
+user_name = "test"  # P1问题：变量命名不规范
 def main():
     print("Hello World")
+    # P2问题：缺少注释
"""


async def test_cr_service_api_response():
    """测试CR服务的API响应"""
    print("🧪 测试CR服务的API响应...")
    
    # 创建模拟服务
    horn_service, kms_service, dx_service, git_service, devmind_service = create_mock_services()
    
    # 创建CR服务
    cr_service = CRService(
        horn_service=horn_service,
        kms_service=kms_service,
        dx_service=dx_service,
        git_service=git_service,
        devmind_service=devmind_service,
        config={
            'cr_mode': 'fast',  # 使用fast模式避免外部依赖
            'enable_parallel_processing': False,
            'max_concurrent_tasks': 1,
            'enable_performance_monitoring': False
        }
    )
    
    # 初始化服务
    await cr_service.initialize()
    
    # 创建请求参数
    mock_request = Mock()
    code_diff = create_sample_code_diff()
    params = {
        'project': 'test-project',
        'repo': 'test-repo',
        'fromBranch': 'feature/api-test',
        'toBranch': 'main',
        'prLink': 'https://dev.sankuai.com/code/repo-detail/test-project/test-repo/pr/123',
        'taskGitInfo': {
            'project': 'test-project',
            'repo': 'test-repo',
            'fromBranch': 'feature/api-test',
            'toBranch': 'main'
        }
    }
    
    try:
        # 模拟编排器返回包含问题的结果
        mock_orchestrator_result = {
            'success': True,
            'data': {
                'summary': {
                    'checkBranch': 'test-project/test-repo:feature/api-test',
                    'reviewTime': '2024-01-01 12:00:00',
                    'reviewer': 'AI代码审查系统',
                    'overallResult': '不通过',  # 有严重问题
                    'resultDescription': 'P0:1个,P1:1个,P2:1个',
                    'totalProblems': 3,
                    'crMode': 'fast'
                },
                'scoring': {
                    'overallScore': 50,  # 100 - (1*25 + 1*15 + 1*10) = 50
                    'qualityGrade': 'C',
                    'passThreshold': 80,
                    'isPassed': False
                },
                'statistics': {
                    'totalProblems': 3,
                    'criticalCount': 1,
                    'warningCount': 1,
                    'moderateCount': 1,
                    'minorCount': 0
                },
                'problems': [
                    {
                        'id': 'p1',
                        'severity': 'CRITICAL',
                        'problem': '空指针异常风险',
                        'suggestion': '添加空值检查',
                        'targetCode': 'user.getName()',
                        'codePosition': [2, 1, 2, 15]
                    },
                    {
                        'id': 'p2',
                        'severity': 'WARNING',
                        'problem': '变量命名不规范',
                        'suggestion': '使用驼峰命名',
                        'targetCode': 'user_name',
                        'codePosition': [5, 1, 5, 9]
                    },
                    {
                        'id': 'p3',
                        'severity': 'MODERATE',
                        'problem': '缺少注释',
                        'suggestion': '添加注释',
                        'targetCode': '# P2问题：缺少注释',
                        'codePosition': [8, 1, 8, 20]
                    }
                ]
            }
        }
        
        # 模拟编排器
        with patch.object(cr_service.workflow_manager, 'execute_workflow') as mock_execute:
            mock_execute.return_value = mock_orchestrator_result
            
            # 执行CR请求
            api_result = await cr_service.process_cr_request(mock_request, code_diff, params)
            
            print(f"  📊 API返回结果分析:")
            print(f"    checkBranch: {api_result.get('checkBranch', 'N/A')}")
            print(f"    sumCheckResult: {api_result.get('sumCheckResult', 'N/A')}")
            print(f"    totalProblem: {api_result.get('totalProblem', 'N/A')}")
            print(f"    resultDesc: {api_result.get('resultDesc', 'N/A')}")
            
            if 'summary' in api_result:
                summary = api_result['summary']
                print(f"    summary.overallResult: {summary.get('overallResult', 'N/A')}")
                print(f"    summary.totalProblems: {summary.get('totalProblems', 'N/A')}")
                print(f"    summary.resultDescription: {summary.get('resultDescription', 'N/A')}")
            
            if 'scoring' in api_result:
                scoring = api_result['scoring']
                print(f"    scoring.overallScore: {scoring.get('overallScore', 'N/A')}")
                print(f"    scoring.isPassed: {scoring.get('isPassed', 'N/A')}")
            
            # 验证关键字段
            print(f"\n  🔍 关键字段验证:")
            
            # 1. 验证checkBranch
            expected_branch = "test-project/test-repo:feature/api-test"
            actual_branch = api_result.get('checkBranch', '')
            if actual_branch == expected_branch:
                print(f"    ✅ checkBranch正确: {actual_branch}")
            else:
                print(f"    ❌ checkBranch错误: 期望{expected_branch}, 实际{actual_branch}")
                return False
            
            # 2. 验证总体结果
            expected_result = "不通过"
            actual_result = api_result.get('sumCheckResult', '')
            if actual_result == expected_result:
                print(f"    ✅ sumCheckResult正确: {actual_result}")
            else:
                print(f"    ❌ sumCheckResult错误: 期望{expected_result}, 实际{actual_result}")
                return False
            
            # 3. 验证问题总数
            expected_total = "3"
            actual_total = api_result.get('totalProblem', '')
            if actual_total == expected_total:
                print(f"    ✅ totalProblem正确: {actual_total}")
            else:
                print(f"    ❌ totalProblem错误: 期望{expected_total}, 实际{actual_total}")
                return False
            
            # 4. 验证结果描述
            expected_desc = "P0:1个,P1:1个,P2:1个"
            actual_desc = api_result.get('resultDesc', '')
            if expected_desc in actual_desc or actual_desc == expected_desc:
                print(f"    ✅ resultDesc正确: {actual_desc}")
            else:
                print(f"    ❌ resultDesc错误: 期望包含{expected_desc}, 实际{actual_desc}")
                return False
            
            # 5. 验证summary中的数据一致性
            if 'summary' in api_result:
                summary = api_result['summary']
                summary_result = summary.get('overallResult', '')
                summary_total = summary.get('totalProblems', 0)
                
                if summary_result == expected_result:
                    print(f"    ✅ summary.overallResult正确: {summary_result}")
                else:
                    print(f"    ❌ summary.overallResult错误: 期望{expected_result}, 实际{summary_result}")
                    return False
                
                if summary_total == 3:
                    print(f"    ✅ summary.totalProblems正确: {summary_total}")
                else:
                    print(f"    ❌ summary.totalProblems错误: 期望3, 实际{summary_total}")
                    return False
            
            # 6. 验证scoring中的数据
            if 'scoring' in api_result:
                scoring = api_result['scoring']
                score = scoring.get('overallScore', 0)
                is_passed = scoring.get('isPassed', True)
                
                if score == 50:
                    print(f"    ✅ scoring.overallScore正确: {score}")
                else:
                    print(f"    ❌ scoring.overallScore错误: 期望50, 实际{score}")
                    return False
                
                if not is_passed:
                    print(f"    ✅ scoring.isPassed正确: {is_passed}")
                else:
                    print(f"    ❌ scoring.isPassed错误: 期望False, 实际{is_passed}")
                    return False
            
            print(f"  ✅ API响应测试通过")
            return True
            
    except Exception as e:
        print(f"    ❌ API响应测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """运行测试"""
    print("🚀 开始测试实际API返回结果")
    print("=" * 60)
    
    try:
        success = await test_cr_service_api_response()
        
        print("=" * 60)
        if success:
            print("🎉 API响应测试通过！")
            print()
            print("🎯 验证结果:")
            print("  • checkBranch正确显示分支信息")
            print("  • sumCheckResult正确反映问题状态")
            print("  • totalProblem与实际问题数量一致")
            print("  • resultDesc准确描述问题分布")
            print("  • summary和scoring数据一致")
            print("  • 有严重问题时正确显示'不通过'")
        else:
            print("❌ API响应测试失败！")
            print("需要进一步检查API返回逻辑")
        
        return success
        
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == '__main__':
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
