import json
import unittest
from unittest.mock import patch, MagicMock
from api.service.devmind_service import DevmindService
import os
import dotenv
dotenv.load_dotenv()

service = DevmindService(base_url=os.environ["DM_BASE_URL"], api_key=os.environ["DM_API_KEY"])
result = service.retrieve_chunks(
    question="pytorch开发",
    dataset_ids=["dfd670ee36c611f0a20e0a580aab36c4"],
    document_ids=[],
    page=1,
    page_size=10,
    similarity_threshold=0.3,
    vector_similarity_weight=0.7,
    top_k=100,
    keyword=True,
    highlight=True
)
print(result)
contents = [chunk.get("content") for chunk in result.get("data").get("chunks")]
# 格式化展示result
print(contents)

if __name__ == '__main__':
    unittest.main()
