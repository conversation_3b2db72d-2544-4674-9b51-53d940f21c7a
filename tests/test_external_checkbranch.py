"""
测试外部参数驱动的checkBranch设置
验证checkBranch完全从外部fromBranch参数获取，不依赖模型处理
"""

import asyncio
import sys
import os
from unittest.mock import Mock

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.agents.result_aggregation_agent import ResultAggregationAgent


def create_sample_results_with_various_checkbranch():
    """创建包含各种checkBranch格式的示例结果"""
    return [
        {
            'checkBranch': '.env:1-10',  # 错误格式1
            'sumCheckResult': '通过',
            'resultDesc': 'P1:1个',
            'totalProblem': '1',
            'problemList': [
                {
                    'scene': 'commonCheck',
                    'num': '1',
                    'checkResult': '通过',
                    'detail': [
                        {
                            'level': 'P1',
                            'problem': '变量命名建议',
                            'suggestion': '使用更描述性的变量名',
                            'targetCode': 'let x = 1;',
                            'codePosition': [1, 1, 1, 10]
                        }
                    ]
                }
            ]
        },
        {
            'checkBranch': 'config/settings.py:15-25',  # 错误格式2
            'sumCheckResult': '通过',
            'resultDesc': 'P2:1个',
            'totalProblem': '1',
            'problemList': [
                {
                    'scene': 'customCheck',
                    'num': '1',
                    'checkResult': '通过',
                    'detail': [
                        {
                            'level': 'P2',
                            'problem': '代码注释不足',
                            'suggestion': '添加注释',
                            'targetCode': 'function test() {}',
                            'codePosition': [15, 1, 15, 20]
                        }
                    ]
                }
            ]
        },
        {
            'checkBranch': 'old-branch-name',  # 正确格式但不是目标分支
            'sumCheckResult': '通过',
            'resultDesc': 'P3:1个',
            'totalProblem': '1',
            'problemList': [
                {
                    'scene': 'styleCheck',
                    'num': '1',
                    'checkResult': '通过',
                    'detail': [
                        {
                            'level': 'P3',
                            'problem': '代码风格',
                            'suggestion': '统一代码格式',
                            'targetCode': 'if(condition){...}',
                            'codePosition': [20, 1, 20, 15]
                        }
                    ]
                }
            ]
        }
    ]


def test_determine_check_branch_from_external():
    """测试从外部参数确定checkBranch"""
    print("🧪 测试从外部参数确定checkBranch...")
    
    # 创建模拟LLM服务
    mock_llm_service = Mock()
    agent = ResultAggregationAgent(mock_llm_service)
    
    # 测试用例1：完整的外部参数
    options1 = {
        'project': 'shangou',
        'repo': 'ai_cr',
        'fromBranch': 'feature/external-checkbranch',
        'toBranch': 'main'
    }
    metadata1 = {}
    
    check_branch1 = agent._determine_check_branch_from_external(options1, metadata1)
    expected1 = "shangou/ai_cr:feature/external-checkbranch"
    
    if check_branch1 == expected1:
        print(f"  ✅ 测试1通过: {check_branch1}")
    else:
        print(f"  ❌ 测试1失败: 期望 {expected1}, 实际 {check_branch1}")
        return False
    
    # 测试用例2：只有分支名
    options2 = {
        'fromBranch': 'feature/simple-branch'
    }
    metadata2 = {}
    
    check_branch2 = agent._determine_check_branch_from_external(options2, metadata2)
    expected2 = "feature/simple-branch"
    
    if check_branch2 == expected2:
        print(f"  ✅ 测试2通过: {check_branch2}")
    else:
        print(f"  ❌ 测试2失败: 期望 {expected2}, 实际 {check_branch2}")
        return False
    
    # 测试用例3：从metadata获取
    options3 = {}
    metadata3 = {
        'project': 'meta-project',
        'repo': 'meta-repo',
        'fromBranch': 'dev/metadata-branch'
    }
    
    check_branch3 = agent._determine_check_branch_from_external(options3, metadata3)
    expected3 = "meta-project/meta-repo:dev/metadata-branch"
    
    if check_branch3 == expected3:
        print(f"  ✅ 测试3通过: {check_branch3}")
    else:
        print(f"  ❌ 测试3失败: 期望 {expected3}, 实际 {check_branch3}")
        return False
    
    # 测试用例4：没有分支信息
    options4 = {}
    metadata4 = {}
    
    check_branch4 = agent._determine_check_branch_from_external(options4, metadata4)
    expected4 = "未知分支"
    
    if check_branch4 == expected4:
        print(f"  ✅ 测试4通过: {check_branch4}")
    else:
        print(f"  ❌ 测试4失败: 期望 {expected4}, 实际 {check_branch4}")
        return False
    
    print("  ✅ 从外部参数确定checkBranch测试完成")
    return True


def test_preprocess_results_with_correct_branch():
    """测试结果预处理"""
    print("🧪 测试结果预处理...")
    
    # 创建模拟LLM服务
    mock_llm_service = Mock()
    agent = ResultAggregationAgent(mock_llm_service)
    
    # 创建测试数据
    original_results = create_sample_results_with_various_checkbranch()
    correct_check_branch = "project/repo:feature/target-branch"
    
    # 执行预处理
    preprocessed_results = agent._preprocess_results_with_correct_branch(original_results, correct_check_branch)
    
    print(f"  📊 预处理结果分析:")
    print(f"    原始结果数量: {len(original_results)}")
    print(f"    预处理后数量: {len(preprocessed_results)}")
    print(f"    目标checkBranch: {correct_check_branch}")
    
    # 验证所有结果的checkBranch都被正确设置
    for i, result in enumerate(preprocessed_results):
        original_branch = original_results[i].get('checkBranch')
        new_branch = result.get('checkBranch')
        
        print(f"    结果{i+1}: {original_branch} → {new_branch}")
        
        if new_branch != correct_check_branch:
            print(f"    ❌ 错误：结果{i+1}的checkBranch未正确设置")
            return False
    
    print(f"  ✅ 所有结果的checkBranch都正确设置为: {correct_check_branch}")
    print("  ✅ 结果预处理测试完成")
    return True


async def test_end_to_end_external_checkbranch():
    """测试端到端的外部checkBranch设置"""
    print("🧪 测试端到端的外部checkBranch设置...")
    
    # 创建模拟LLM服务
    mock_llm_service = Mock()
    agent = ResultAggregationAgent(mock_llm_service)
    
    # 创建测试输入数据
    input_data = {
        'results': create_sample_results_with_various_checkbranch(),
        'options': {
            'project': 'test-project',
            'repo': 'test-repo',
            'fromBranch': 'feature/external-driven',
            'toBranch': 'main'
        },
        'segments': [
            {'id': 'seg1', 'content': 'test code 1'},
            {'id': 'seg2', 'content': 'test code 2'}
        ],
        'metadata': {
            'cr_mode': 'standard',
            'parallel_processing': True
        }
    }
    
    # 执行聚合
    result = await agent.execute(input_data)
    
    print(f"  📊 端到端聚合结果分析:")
    print(f"    聚合成功: {result.success}")
    
    if result.success:
        data = result.data
        summary = data.get('summary', {})
        final_check_branch = summary.get('checkBranch', '')
        
        print(f"    最终checkBranch: {final_check_branch}")
        print(f"    总问题数: {summary.get('totalProblems', 0)}")
        print(f"    总体结果: {summary.get('overallResult', 'N/A')}")
        
        # 验证最终的checkBranch是从外部参数设置的
        expected_branch = "test-project/test-repo:feature/external-driven"
        if final_check_branch == expected_branch:
            print(f"    ✅ 正确：最终checkBranch完全来自外部参数")
        else:
            print(f"    ❌ 错误：最终checkBranch不是来自外部参数")
            print(f"        期望: {expected_branch}")
            print(f"        实际: {final_check_branch}")
            return False
        
        # 验证不再有任何文件路径格式
        if ':' in final_check_branch and any(char.isdigit() for char in final_check_branch.split(':')[-1]):
            print(f"    ❌ 错误：最终结果仍有文件路径格式")
            return False
        
        # 验证原始结果中的错误格式被完全忽略
        original_branches = [r.get('checkBranch') for r in input_data['results']]
        print(f"    原始checkBranch: {original_branches}")
        print(f"    ✅ 正确：原始错误格式被完全忽略，使用外部参数")
        
        print(f"  ✅ 端到端外部checkBranch设置测试通过")
        return True
    else:
        print(f"    ❌ 错误：聚合失败: {result.error}")
        return False


async def test_no_external_branch_fallback():
    """测试没有外部分支信息时的降级处理"""
    print("🧪 测试没有外部分支信息时的降级处理...")
    
    # 创建模拟LLM服务
    mock_llm_service = Mock()
    agent = ResultAggregationAgent(mock_llm_service)
    
    # 创建没有分支信息的测试输入数据
    input_data = {
        'results': create_sample_results_with_various_checkbranch(),
        'options': {},  # 没有分支信息
        'segments': [],
        'metadata': {}  # 也没有分支信息
    }
    
    # 执行聚合
    result = await agent.execute(input_data)
    
    print(f"  📊 降级处理结果分析:")
    print(f"    聚合成功: {result.success}")
    
    if result.success:
        data = result.data
        summary = data.get('summary', {})
        final_check_branch = summary.get('checkBranch', '')
        
        print(f"    最终checkBranch: {final_check_branch}")
        
        # 验证降级为"未知分支"
        if final_check_branch == "未知分支":
            print(f"    ✅ 正确：没有外部分支信息时正确降级为'未知分支'")
        else:
            print(f"    ❌ 错误：降级处理不正确，期望'未知分支'，实际'{final_check_branch}'")
            return False
        
        print(f"  ✅ 降级处理测试通过")
        return True
    else:
        print(f"    ❌ 错误：聚合失败: {result.error}")
        return False


async def main():
    """运行所有测试"""
    print("🚀 开始测试外部参数驱动的checkBranch设置")
    print("=" * 60)
    
    success_count = 0
    total_tests = 4
    
    try:
        # 测试1: 从外部参数确定checkBranch
        if test_determine_check_branch_from_external():
            success_count += 1
        print()
        
        # 测试2: 结果预处理
        if test_preprocess_results_with_correct_branch():
            success_count += 1
        print()
        
        # 测试3: 端到端外部checkBranch设置
        if await test_end_to_end_external_checkbranch():
            success_count += 1
        print()
        
        # 测试4: 降级处理
        if await test_no_external_branch_fallback():
            success_count += 1
        print()
        
        print("=" * 60)
        print(f"🎉 测试完成: {success_count}/{total_tests} 个测试通过")
        
        if success_count == total_tests:
            print("✅ 所有测试通过！外部参数驱动的checkBranch设置功能正常工作")
            print()
            print("🎯 实现效果:")
            print("  • checkBranch完全从外部fromBranch参数获取")
            print("  • 不再依赖模型处理或原始结果中的值")
            print("  • 预处理阶段统一设置所有结果的checkBranch")
            print("  • 支持project/repo:branch和简单branch两种格式")
            print("  • 没有外部参数时正确降级为'未知分支'")
            print("  • 完全忽略原始结果中的错误格式")
            return True
        else:
            print("⚠️  部分测试失败，需要进一步检查")
            return False
        
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == '__main__':
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
