"""
测试精确的summary生成
验证基于统计计算的summary字段，确保精确度
"""

import sys
import os
from unittest.mock import Mock
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.agents.result_aggregation_agent import ResultAggregationAgent


async def test_precise_summary_generation():
    """测试精确的summary生成"""
    print("🧪 测试精确的summary生成...")
    
    # 创建模拟LLM服务
    mock_llm_service = Mock()
    agent = ResultAggregationAgent(mock_llm_service)
    
    # 创建测试数据 - 模拟您提到的场景
    results = [
        {
            'checkBranch': '~wangqichen02/shangou_ai_cr:dev/20250516-cr-rag',
            'sumCheckResult': '不通过',
            'resultDesc': 'P2:28个',
            'totalProblem': '28',
            'problemList': [
                {
                    'scene': 'file1.py:1-100',
                    'num': '15',
                    'checkResult': '不通过',
                    'detail': [{'level': 'P2', 'problem': f'中等问题{i}'} for i in range(15)]
                },
                {
                    'scene': 'file2.js:1-50',
                    'num': '13',
                    'checkResult': '不通过',
                    'detail': [{'level': 'P2', 'problem': f'中等问题{i+15}'} for i in range(13)]
                }
            ]
        }
    ]
    
    segments = [
        {'id': 'seg1', 'file': 'file1.py', 'lines_count': 100, 'content': 'python code'},
        {'id': 'seg2', 'file': 'file2.js', 'lines_count': 50, 'content': 'javascript code'},
        {'id': 'seg3', 'file': 'file3.css', 'lines_count': 30, 'content': 'css code'},
        {'id': 'seg4', 'file': 'file4.html', 'lines_count': 20, 'content': 'html code'}
    ]
    
    metadata = {
        'cr_mode': 'standard',
        'parallel_processing': True
    }
    
    options = {
        'project': 'wangqichen02',
        'repo': 'shangou_ai_cr',
        'fromBranch': 'dev/20250516-cr-rag'
    }
    
    try:
        # 执行聚合
        merged_result = results[0]
        result = await agent._builtin_enhance_result(merged_result, results, segments, metadata, options)
        
        print(f"    📊 聚合结果生成成功")
        
        # 验证summary结构
        if 'summary' in result:
            summary = result['summary']
            print(f"    ✅ 包含summary字段")
            
            # 验证基础信息
            print(f"    📋 基础信息验证:")
            print(f"      checkBranch: {summary.get('checkBranch')}")
            print(f"      reviewTime: {summary.get('reviewTime')}")
            print(f"      reviewer: {summary.get('reviewer')}")
            
            # 验证审查结果
            print(f"    📋 审查结果验证:")
            print(f"      overallResult: {summary.get('overallResult')}")
            print(f"      resultDescription: {summary.get('resultDescription')}")
            print(f"      totalProblems: {summary.get('totalProblems')}")
            
            # 验证任务执行统计
            if 'taskExecutionSummary' in summary:
                print(f"    📋 任务执行统计: {summary['taskExecutionSummary']}")
                expected_pattern = "执行1个任务，1个成功，1个发现问题"
                if expected_pattern == summary['taskExecutionSummary']:
                    print(f"      ✅ 任务执行统计正确")
                else:
                    print(f"      ❌ 任务执行统计错误")
                    return False
            else:
                print(f"      ❌ 缺少taskExecutionSummary字段")
                return False
            
            # 验证质量门禁统计
            if 'qualityGatesSummary' in summary:
                print(f"    📋 质量门禁统计: {summary['qualityGatesSummary']}")
                # 28个P2问题，应该不通过某些门禁
                if "质量门禁" in summary['qualityGatesSummary']:
                    print(f"      ✅ 质量门禁统计格式正确")
                else:
                    print(f"      ❌ 质量门禁统计格式错误")
                    return False
            else:
                print(f"      ❌ 缺少qualityGatesSummary字段")
                return False
            
            # 验证代码覆盖统计
            if 'coverageSummary' in summary:
                print(f"    📋 代码覆盖统计: {summary['coverageSummary']}")
                expected_files = 4  # file1.py, file2.js, file3.css, file4.html
                expected_segments = 4
                expected_lines = 200  # 100+50+30+20
                
                if (f"分析{expected_files}个文件" in summary['coverageSummary'] and
                    f"{expected_segments}个代码片段" in summary['coverageSummary'] and
                    f"共{expected_lines}行代码" in summary['coverageSummary']):
                    print(f"      ✅ 代码覆盖统计正确")
                else:
                    print(f"      ❌ 代码覆盖统计错误")
                    return False
            else:
                print(f"      ❌ 缺少coverageSummary字段")
                return False
            
            # 验证问题分布统计
            if 'problemDistribution' in summary:
                distribution = summary['problemDistribution']
                print(f"    📋 问题分布统计:")
                print(f"      critical: {distribution.get('critical', 0)}")
                print(f"      warning: {distribution.get('warning', 0)}")
                print(f"      moderate: {distribution.get('moderate', 0)}")
                print(f"      minor: {distribution.get('minor', 0)}")
                
                # 验证数据准确性
                if (distribution.get('critical', 0) == 0 and
                    distribution.get('warning', 0) == 0 and
                    distribution.get('moderate', 0) == 28 and
                    distribution.get('minor', 0) == 0):
                    print(f"      ✅ 问题分布统计正确")
                else:
                    print(f"      ❌ 问题分布统计错误")
                    return False
            else:
                print(f"      ❌ 缺少problemDistribution字段")
                return False
            
            # 验证审查效率统计
            if 'efficiencySummary' in summary:
                print(f"    📋 审查效率统计: {summary['efficiencySummary']}")
                expected_density = round(28 / 4, 2)  # 28个问题 / 4个片段 = 7.0
                if (f"并行处理" in summary['efficiencySummary'] and
                    f"问题密度{expected_density}个/片段" in summary['efficiencySummary']):
                    print(f"      ✅ 审查效率统计正确")
                else:
                    print(f"      ❌ 审查效率统计错误")
                    return False
            else:
                print(f"      ❌ 缺少efficiencySummary字段")
                return False
            
        else:
            print(f"    ❌ 缺少summary字段")
            return False
        
        print(f"  ✅ 精确summary生成测试通过")
        return True
        
    except Exception as e:
        print(f"    ❌ 精确summary生成测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


async def test_statistical_accuracy():
    """测试统计计算的准确性"""
    print("🧪 测试统计计算的准确性...")
    
    # 创建模拟LLM服务
    mock_llm_service = Mock()
    agent = ResultAggregationAgent(mock_llm_service)
    
    # 测试各种统计方法
    print(f"    📊 测试任务执行统计:")
    
    # 测试任务执行统计
    test_results = [
        {'sumCheckResult': '通过', 'totalProblem': '0'},
        {'sumCheckResult': '不通过', 'totalProblem': '5'},
        {'sumCheckResult': '通过', 'totalProblem': '0'},
        {'totalProblem': '3'}  # 缺少sumCheckResult
    ]
    
    task_summary = agent._calculate_task_execution_summary(test_results)
    expected_task = "执行4个任务，4个成功，2个发现问题"
    
    if task_summary == expected_task:
        print(f"      ✅ 任务执行统计正确: {task_summary}")
    else:
        print(f"      ❌ 任务执行统计错误: 期望'{expected_task}', 实际'{task_summary}'")
        return False
    
    # 测试质量门禁统计
    print(f"    📊 测试质量门禁统计:")
    
    # 测试用例1：有严重问题
    quality_summary1 = agent._calculate_quality_gates_summary(10, 2, 3, 5, 0)
    if "质量门禁未通过" in quality_summary1:
        print(f"      ✅ 有严重问题时质量门禁正确: {quality_summary1}")
    else:
        print(f"      ❌ 有严重问题时质量门禁错误: {quality_summary1}")
        return False
    
    # 测试用例2：无严重问题但警告过多
    quality_summary2 = agent._calculate_quality_gates_summary(8, 0, 8, 0, 0)
    if "质量门禁未通过" in quality_summary2:
        print(f"      ✅ 警告过多时质量门禁正确: {quality_summary2}")
    else:
        print(f"      ❌ 警告过多时质量门禁错误: {quality_summary2}")
        return False
    
    # 测试用例3：完全通过
    quality_summary3 = agent._calculate_quality_gates_summary(3, 0, 2, 1, 0)
    if "质量门禁通过" in quality_summary3:
        print(f"      ✅ 完全通过时质量门禁正确: {quality_summary3}")
    else:
        print(f"      ❌ 完全通过时质量门禁错误: {quality_summary3}")
        return False
    
    # 测试代码覆盖统计
    print(f"    📊 测试代码覆盖统计:")
    
    test_segments = [
        {'file': 'test1.py', 'lines_count': 100, 'content': 'code1'},
        {'file': 'test2.js', 'lines_count': 50, 'content': 'code2'},
        {'file': 'test1.py', 'lines_count': 30, 'content': 'code3'},  # 重复文件
        {'file': 'unknown', 'lines_count': 20, 'content': 'code4'}   # unknown文件
    ]
    
    coverage_summary = agent._calculate_coverage_summary(test_segments)
    expected_files = 2  # test1.py, test2.js (去重，不包括unknown)
    expected_segments = 4
    expected_lines = 200  # 100+50+30+20
    
    if (f"分析{expected_files}个文件" in coverage_summary and
        f"{expected_segments}个代码片段" in coverage_summary and
        f"共{expected_lines}行代码" in coverage_summary):
        print(f"      ✅ 代码覆盖统计正确: {coverage_summary}")
    else:
        print(f"      ❌ 代码覆盖统计错误: {coverage_summary}")
        return False
    
    # 测试审查效率统计
    print(f"    📊 测试审查效率统计:")
    
    efficiency_summary1 = agent._calculate_efficiency_summary(
        test_results, test_segments, {'parallel_processing': True}, 8
    )
    expected_density = round(8 / 4, 2)  # 8个问题 / 4个片段 = 2.0
    
    if (f"并行处理" in efficiency_summary1 and
        f"问题密度{expected_density}个/片段" in efficiency_summary1):
        print(f"      ✅ 并行处理效率统计正确: {efficiency_summary1}")
    else:
        print(f"      ❌ 并行处理效率统计错误: {efficiency_summary1}")
        return False
    
    efficiency_summary2 = agent._calculate_efficiency_summary(
        test_results, [], {'parallel_processing': False}, 0
    )
    
    if "串行处理，无代码片段分析" == efficiency_summary2:
        print(f"      ✅ 无片段时效率统计正确: {efficiency_summary2}")
    else:
        print(f"      ❌ 无片段时效率统计错误: {efficiency_summary2}")
        return False
    
    print(f"  ✅ 统计计算准确性测试通过")
    return True


async def test_llm_recommendations():
    """测试LLM全局建议生成"""
    print("🧪 测试LLM全局建议生成...")
    
    # 创建模拟LLM服务
    mock_llm_service = Mock()
    mock_llm = Mock()
    mock_response = Mock()
    mock_response.content = """
- 优先修复所有严重问题，确保系统稳定性
- 建立代码审查流程，防止类似问题再次出现
- 加强单元测试覆盖率，提高代码质量
- 定期进行代码重构，减少技术债务
"""
    mock_llm.ainvoke.return_value = mock_response
    mock_llm_service.get_llm.return_value = mock_llm
    
    agent = ResultAggregationAgent(mock_llm_service)
    
    # 测试LLM建议生成
    test_problems = [
        {'level': 'P0', 'problem': '空指针异常', 'suggestion': '添加空值检查'},
        {'level': 'P1', 'problem': '变量命名不规范', 'suggestion': '使用驼峰命名'},
        {'level': 'P2', 'problem': '代码注释不足', 'suggestion': '添加注释'}
    ]
    
    try:
        recommendations = await agent._generate_llm_recommendations(
            test_problems, 1, 1, 1, 0, 3
        )
        
        print(f"    📋 LLM生成的建议:")
        for i, rec in enumerate(recommendations, 1):
            print(f"      {i}. {rec}")
        
        # 验证建议数量和内容
        if len(recommendations) >= 3:
            print(f"      ✅ LLM建议数量合适: {len(recommendations)}条")
        else:
            print(f"      ❌ LLM建议数量不足: {len(recommendations)}条")
            return False
        
        # 验证建议内容包含关键词
        recommendations_text = ' '.join(recommendations)
        if any(keyword in recommendations_text for keyword in ['修复', '问题', '质量', '代码']):
            print(f"      ✅ LLM建议内容相关")
        else:
            print(f"      ❌ LLM建议内容不相关")
            return False
        
    except Exception as e:
        print(f"    ❌ LLM建议生成失败: {str(e)}")
        return False
    
    # 测试基础建议生成（LLM不可用时的降级）
    print(f"    📋 测试基础建议生成:")
    
    basic_recommendations = agent._generate_basic_recommendations(2, 3, 5, 10, 20)
    
    print(f"    📋 基础建议:")
    for i, rec in enumerate(basic_recommendations, 1):
        print(f"      {i}. {rec}")
    
    if len(basic_recommendations) >= 3:
        print(f"      ✅ 基础建议数量合适: {len(basic_recommendations)}条")
    else:
        print(f"      ❌ 基础建议数量不足: {len(basic_recommendations)}条")
        return False
    
    print(f"  ✅ LLM全局建议生成测试通过")
    return True


async def main():
    """运行所有测试"""
    print("🚀 开始测试精确的summary生成")
    print("=" * 60)
    
    success_count = 0
    total_tests = 3
    
    try:
        # 测试1: 精确summary生成
        if await test_precise_summary_generation():
            success_count += 1
        print()
        
        # 测试2: 统计计算准确性
        if await test_statistical_accuracy():
            success_count += 1
        print()
        
        # 测试3: LLM全局建议生成
        if await test_llm_recommendations():
            success_count += 1
        print()
        
        print("=" * 60)
        print(f"🎉 测试完成: {success_count}/{total_tests} 个测试通过")
        
        if success_count == total_tests:
            print("✅ 所有测试通过！精确summary生成完成")
            print()
            print("🎯 优化效果:")
            print("  • summary字段基于统计计算，保证精确度")
            print("  • 任务执行、质量门禁、代码覆盖等统计准确")
            print("  • 问题分布和审查效率统计精确")
            print("  • 只有全局建议使用LLM汇总分点输出")
            print("  • 支持LLM不可用时的基础建议降级")
            return True
        else:
            print("⚠️  部分测试失败，需要进一步检查")
            return False
        
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == '__main__':
    import asyncio
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
