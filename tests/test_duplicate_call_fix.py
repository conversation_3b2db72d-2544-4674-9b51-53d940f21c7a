"""
测试重复调用修复
验证enhance_cr_result不会被重复调用，以及CR结果增强器的错误修复
"""

import sys
import os
from unittest.mock import Mock, patch

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.cr_result_enhancer import CRResultEnhancer
from core.orchestrator.cr_orchestrator import CROrchestrator


def test_cr_result_enhancer_string_resultdesc():
    """测试CR结果增强器处理字符串类型的resultDesc"""
    print("🧪 测试CR结果增强器处理字符串类型的resultDesc...")
    
    enhancer = CRResultEnhancer()
    
    # 创建包含字符串类型resultDesc的测试数据
    original_result = {
        'checkBranch': 'test-project/test-repo:feature/string-test',
        'sumCheckResult': '不通过',
        'resultDesc': 'P0:1个,P1:2个,P2:1个',  # 字符串类型
        'totalProblem': '4',
        'problemList': [
            {
                'scene': '.env:1-10',
                'num': '3',
                'checkResult': '不通过',
                'detail': []  # 空的detail，会触发从描述中提取问题的逻辑
            },
            {
                'scene': 'api/app_server.py:1-2',
                'num': '0',
                'checkResult': '通过',
                'detail': []
            }
        ]
    }
    
    try:
        # 测试增强结果
        enhanced_result = enhancer.enhance_cr_result(original_result)
        
        print(f"    ✅ 增强成功，无异常抛出")
        print(f"    检查分支: {enhanced_result.check_branch}")
        print(f"    总体结果: {enhanced_result.overall_result}")
        print(f"    问题数量: {enhanced_result.statistics.total_count}")
        print(f"    评分: {enhanced_result.overall_score}")
        
        # 验证基本字段
        if enhanced_result.check_branch == 'test-project/test-repo:feature/string-test':
            print(f"    ✅ checkBranch正确")
        else:
            print(f"    ❌ checkBranch错误")
            return False
        
        if enhanced_result.overall_result in ['通过', '不通过']:
            print(f"    ✅ overallResult格式正确: {enhanced_result.overall_result}")
        else:
            print(f"    ❌ overallResult格式错误: {enhanced_result.overall_result}")
            return False
        
        print(f"  ✅ CR结果增强器字符串resultDesc处理测试通过")
        return True
        
    except Exception as e:
        print(f"    ❌ 增强失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_orchestrator_complete_format_check():
    """测试编排器的完整格式检查"""
    print("🧪 测试编排器的完整格式检查...")

    # 创建模拟的编排器
    mock_devmind_service = Mock()
    mock_chunk_service = Mock()
    mock_git_service = Mock()
    orchestrator = CROrchestrator("test_orchestrator", mock_devmind_service, mock_chunk_service, mock_git_service)
    
    # 测试用例1：完整格式
    complete_result = {
        'summary': {
            'checkBranch': 'test-branch',
            'overallResult': '通过',
            'totalProblems': 0
        },
        'scoring': {
            'overallScore': 100,
            'isPassed': True
        },
        'statistics': {
            'totalProblems': 0,
            'criticalCount': 0
        },
        'problems': []
    }
    
    if orchestrator._is_complete_format(complete_result):
        print(f"    ✅ 完整格式检查正确")
    else:
        print(f"    ❌ 完整格式检查错误")
        return False
    
    # 测试用例2：不完整格式
    incomplete_result = {
        'checkBranch': 'test-branch',
        'sumCheckResult': '通过',
        'totalProblem': '0'
    }
    
    if not orchestrator._is_complete_format(incomplete_result):
        print(f"    ✅ 不完整格式检查正确")
    else:
        print(f"    ❌ 不完整格式检查错误")
        return False
    
    print(f"  ✅ 编排器完整格式检查测试通过")
    return True


def test_no_duplicate_enhance_calls():
    """测试没有重复的enhance_cr_result调用"""
    print("🧪 测试没有重复的enhance_cr_result调用...")

    # 创建模拟的编排器
    mock_devmind_service = Mock()
    mock_chunk_service = Mock()
    mock_git_service = Mock()
    orchestrator = CROrchestrator("test_orchestrator", mock_devmind_service, mock_chunk_service, mock_git_service)
    
    # 模拟已经完整的结果（由聚合代理处理过）
    complete_result = {
        'summary': {
            'checkBranch': 'test-project/test-repo:feature/no-duplicate',
            'overallResult': '不通过',
            'resultDescription': 'P0:1个,P1:1个',
            'totalProblems': 2
        },
        'scoring': {
            'overallScore': 60,
            'isPassed': False
        },
        'statistics': {
            'totalProblems': 2,
            'criticalCount': 1,
            'warningCount': 1,
            'moderateCount': 0,
            'minorCount': 0
        },
        'problems': [
            {
                'level': 'P0',
                'problem': '严重问题',
                'suggestion': '立即修复'
            },
            {
                'level': 'P1',
                'problem': '警告问题',
                'suggestion': '建议修复'
            }
        ]
    }
    
    review_results = []
    segments = []
    
    try:
        # 调用_supplement_result_format，应该不会重复调用enhance_cr_result
        result = orchestrator._supplement_result_format(complete_result, review_results, segments)
        
        print(f"    ✅ 格式补充成功，无异常抛出")
        
        # 验证结果保持一致
        if result['summary']['checkBranch'] == 'test-project/test-repo:feature/no-duplicate':
            print(f"    ✅ checkBranch保持一致")
        else:
            print(f"    ❌ checkBranch不一致")
            return False
        
        if result['summary']['overallResult'] == '不通过':
            print(f"    ✅ overallResult保持一致")
        else:
            print(f"    ❌ overallResult不一致")
            return False
        
        if result['statistics']['totalProblems'] == 2:
            print(f"    ✅ totalProblems保持一致")
        else:
            print(f"    ❌ totalProblems不一致")
            return False
        
        # 验证添加了元数据
        if 'originalReviewResults' in result and 'segmentsInfo' in result:
            print(f"    ✅ 元数据添加成功")
        else:
            print(f"    ❌ 元数据添加失败")
            return False
        
        print(f"  ✅ 无重复调用测试通过")
        return True
        
    except Exception as e:
        print(f"    ❌ 格式补充失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_fallback_for_incomplete_format():
    """测试不完整格式的降级处理"""
    print("🧪 测试不完整格式的降级处理...")

    # 创建模拟的编排器
    mock_devmind_service = Mock()
    mock_chunk_service = Mock()
    mock_git_service = Mock()
    orchestrator = CROrchestrator("test_orchestrator", mock_devmind_service, mock_chunk_service, mock_git_service)
    
    # 模拟不完整的结果
    incomplete_result = {
        'checkBranch': 'test-project/test-repo:feature/incomplete',
        'sumCheckResult': '不通过',
        'resultDesc': 'P0:1个',
        'totalProblem': '1'
    }
    
    review_results = [
        {
            'problemList': [
                {
                    'scene': 'test',
                    'num': '1',
                    'checkResult': '不通过',
                    'detail': [
                        {
                            'level': 'P0',
                            'problem': '严重问题',
                            'suggestion': '立即修复',
                            'targetCode': 'test code',
                            'codePosition': [1, 0, 1, 10]
                        }
                    ]
                }
            ]
        }
    ]
    
    segments = [{'id': 'seg1', 'content': 'test segment'}]
    
    try:
        # 调用_supplement_result_format，应该使用降级方案
        result = orchestrator._supplement_result_format(incomplete_result, review_results, segments)
        
        print(f"    ✅ 降级处理成功，无异常抛出")
        
        # 验证降级结果包含必要字段
        required_fields = ['summary', 'scoring', 'statistics', 'problems']
        missing_fields = [field for field in required_fields if field not in result]
        
        if not missing_fields:
            print(f"    ✅ 降级结果包含所有必要字段")
        else:
            print(f"    ❌ 降级结果缺少字段: {missing_fields}")
            return False
        
        # 验证问题统计
        if result['statistics']['totalProblems'] == 1:
            print(f"    ✅ 问题统计正确")
        else:
            print(f"    ❌ 问题统计错误: {result['statistics']['totalProblems']}")
            return False
        
        # 验证评分逻辑
        if result['scoring']['overallScore'] == 75:  # 100 - 1*25 = 75
            print(f"    ✅ 评分计算正确: {result['scoring']['overallScore']}")
        else:
            print(f"    ❌ 评分计算错误: {result['scoring']['overallScore']}")
            return False
        
        print(f"  ✅ 降级处理测试通过")
        return True
        
    except Exception as e:
        print(f"    ❌ 降级处理失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """运行所有测试"""
    print("🚀 开始测试重复调用修复")
    print("=" * 60)
    
    success_count = 0
    total_tests = 4
    
    try:
        # 测试1: CR结果增强器字符串resultDesc处理
        if test_cr_result_enhancer_string_resultdesc():
            success_count += 1
        print()
        
        # 测试2: 编排器完整格式检查
        if test_orchestrator_complete_format_check():
            success_count += 1
        print()
        
        # 测试3: 无重复调用
        if test_no_duplicate_enhance_calls():
            success_count += 1
        print()
        
        # 测试4: 降级处理
        if test_fallback_for_incomplete_format():
            success_count += 1
        print()
        
        print("=" * 60)
        print(f"🎉 测试完成: {success_count}/{total_tests} 个测试通过")
        
        if success_count == total_tests:
            print("✅ 所有测试通过！重复调用问题已修复")
            print()
            print("🎯 修复效果:")
            print("  • enhance_cr_result不会被重复调用")
            print("  • CR结果增强器正确处理字符串类型的resultDesc")
            print("  • 编排器正确识别完整格式，避免不必要的增强")
            print("  • 不完整格式时正确使用降级方案")
            print("  • 消除了'str' object has no attribute 'items'错误")
            return True
        else:
            print("⚠️  部分测试失败，需要进一步检查")
            return False
        
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
