"""
测试增强后的兼容性字段处理
验证修复后的CR服务兼容性字段处理逻辑
"""

import sys
import os
from unittest.mock import Mock

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


def create_mock_logger():
    """创建模拟日志器"""
    logger = Mock()
    logger.warning = Mock()
    logger.info = Mock()
    return logger


def test_enhanced_compatibility_fields():
    """测试增强后的兼容性字段处理"""
    print("🧪 测试增强后的兼容性字段处理...")
    
    # 模拟增强后的兼容性字段处理逻辑
    def enhanced_process_compatibility_fields(final_result, logger):
        """增强后的兼容性字段处理逻辑"""
        # 确保包含基础的兼容字段，并进行数据一致性验证
        if 'summary' in final_result:
            summary = final_result['summary']
            
            # 获取实际问题数量进行验证
            actual_problems_count = len(final_result.get('problems', []))
            summary_problems_count = summary.get('totalProblems', 0)
            
            # 数据一致性验证和修正
            if actual_problems_count != summary_problems_count:
                logger.warning(f"⚠️  问题数量不一致: summary={summary_problems_count}, actual={actual_problems_count}")
                # 使用实际问题数量
                summary['totalProblems'] = actual_problems_count
            
            # 设置兼容字段，优先使用已存在的正确值
            overall_result = summary.get('overallResult')
            if overall_result:
                final_result['sumCheckResult'] = overall_result
            else:
                # 当无法确定结果时，基于实际问题数量判断
                if actual_problems_count > 0:
                    # 检查是否有严重问题
                    has_critical = any(
                        p.get('severity') == 'CRITICAL' or p.get('level') == 'P0' 
                        for p in final_result.get('problems', [])
                    )
                    final_result['sumCheckResult'] = '不通过' if has_critical else '通过'
                    logger.warning(f"⚠️  overallResult缺失，基于问题分析设置为: {final_result['sumCheckResult']}")
                else:
                    final_result['sumCheckResult'] = '通过'
            
            final_result['totalProblem'] = str(summary.get('totalProblems', actual_problems_count))
            final_result['resultDesc'] = summary.get('resultDescription', '')
            
            if 'checkBranch' not in final_result:
                final_result['checkBranch'] = summary.get('checkBranch', 'unknown')
            
            # 验证最终结果的一致性
            final_check_result = final_result.get('sumCheckResult', '通过')
            final_problems_count = int(final_result.get('totalProblem', '0'))
            
            if final_problems_count > 0 and final_check_result == '通过':
                # 有问题但显示通过，需要进一步检查
                has_critical = any(
                    p.get('severity') == 'CRITICAL' or p.get('level') == 'P0' 
                    for p in final_result.get('problems', [])
                )
                if has_critical:
                    final_result['sumCheckResult'] = '不通过'
                    logger.warning(f"⚠️  修正最终结果: 有严重问题但显示通过，已修正为不通过")
            
            logger.info(f"✅ 兼容性字段设置完成: {final_check_result}, 问题{final_problems_count}个")
        
        return final_result
    
    # 测试用例1：数据一致性问题修正
    print(f"  📊 测试用例1: 数据一致性问题修正")
    logger1 = create_mock_logger()
    
    final_result1 = {
        'summary': {
            'checkBranch': 'test-project/test-repo:feature/test',
            'overallResult': '不通过',
            'resultDescription': 'P0:1个,P1:1个',
            'totalProblems': 1  # 错误：实际有2个问题
        },
        'problems': [
            {'severity': 'CRITICAL', 'problem': '空指针异常风险'},
            {'severity': 'WARNING', 'problem': '变量命名不规范'}
        ]
    }
    
    result1 = enhanced_process_compatibility_fields(final_result1, logger1)
    
    # 验证数据一致性修正
    if result1['summary']['totalProblems'] == 2:
        print(f"    ✅ 数据一致性修正成功: summary.totalProblems已修正为2")
    else:
        print(f"    ❌ 数据一致性修正失败")
        return False
    
    if result1['totalProblem'] == '2':
        print(f"    ✅ 兼容字段正确: totalProblem=2")
    else:
        print(f"    ❌ 兼容字段错误: totalProblem={result1['totalProblem']}")
        return False
    
    # 测试用例2：缺失overallResult的智能判断
    print(f"\n  📊 测试用例2: 缺失overallResult的智能判断")
    logger2 = create_mock_logger()
    
    final_result2 = {
        'summary': {
            'checkBranch': 'test-project/test-repo:feature/test',
            # 缺少overallResult字段
            'resultDescription': 'P0:1个',
            'totalProblems': 1
        },
        'problems': [
            {'severity': 'CRITICAL', 'problem': '空指针异常风险'}
        ]
    }
    
    result2 = enhanced_process_compatibility_fields(final_result2, logger2)
    
    # 验证智能判断
    if result2['sumCheckResult'] == '不通过':
        print(f"    ✅ 智能判断成功: 有严重问题时正确设置为'不通过'")
    else:
        print(f"    ❌ 智能判断失败: 期望'不通过', 实际'{result2['sumCheckResult']}'")
        return False
    
    # 验证警告日志
    logger2.warning.assert_called()
    warning_calls = [call.args[0] for call in logger2.warning.call_args_list]
    has_missing_warning = any("overallResult缺失" in msg for msg in warning_calls)
    if has_missing_warning:
        print(f"    ✅ 正确记录了overallResult缺失的警告")
    else:
        print(f"    ❌ 未记录overallResult缺失的警告")
        return False
    
    # 测试用例3：最终一致性验证和修正
    print(f"\n  📊 测试用例3: 最终一致性验证和修正")
    logger3 = create_mock_logger()
    
    final_result3 = {
        'summary': {
            'checkBranch': 'test-project/test-repo:feature/test',
            'overallResult': '通过',  # 错误：有严重问题但显示通过
            'resultDescription': 'P0:1个',
            'totalProblems': 1
        },
        'problems': [
            {'severity': 'CRITICAL', 'problem': '空指针异常风险'}
        ]
    }
    
    result3 = enhanced_process_compatibility_fields(final_result3, logger3)
    
    # 验证最终一致性修正
    if result3['sumCheckResult'] == '不通过':
        print(f"    ✅ 最终一致性修正成功: 有严重问题时修正为'不通过'")
    else:
        print(f"    ❌ 最终一致性修正失败: 期望'不通过', 实际'{result3['sumCheckResult']}'")
        return False
    
    # 验证修正日志
    warning_calls3 = [call.args[0] for call in logger3.warning.call_args_list]
    has_correction_warning = any("修正最终结果" in msg for msg in warning_calls3)
    if has_correction_warning:
        print(f"    ✅ 正确记录了最终结果修正的警告")
    else:
        print(f"    ❌ 未记录最终结果修正的警告")
        return False
    
    # 测试用例4：正常情况（无问题）
    print(f"\n  📊 测试用例4: 正常情况（无问题）")
    logger4 = create_mock_logger()
    
    final_result4 = {
        'summary': {
            'checkBranch': 'test-project/test-repo:feature/clean',
            'overallResult': '通过',
            'resultDescription': '代码质量良好，未发现问题',
            'totalProblems': 0
        },
        'problems': []
    }
    
    result4 = enhanced_process_compatibility_fields(final_result4, logger4)
    
    # 验证正常情况
    if result4['sumCheckResult'] == '通过' and result4['totalProblem'] == '0':
        print(f"    ✅ 正常情况处理正确: 无问题时显示'通过'")
    else:
        print(f"    ❌ 正常情况处理错误")
        return False
    
    # 测试用例5：非严重问题的处理
    print(f"\n  📊 测试用例5: 非严重问题的处理")
    logger5 = create_mock_logger()
    
    final_result5 = {
        'summary': {
            # 缺少overallResult，需要智能判断
            'totalProblems': 2
        },
        'problems': [
            {'severity': 'WARNING', 'problem': '变量命名不规范'},
            {'severity': 'MODERATE', 'problem': '代码注释不足'}
        ]
    }
    
    result5 = enhanced_process_compatibility_fields(final_result5, logger5)
    
    # 验证非严重问题的处理
    if result5['sumCheckResult'] == '通过':
        print(f"    ✅ 非严重问题处理正确: 无严重问题时可以通过")
    else:
        print(f"    ❌ 非严重问题处理错误: 期望'通过', 实际'{result5['sumCheckResult']}'")
        return False
    
    print(f"  ✅ 增强后的兼容性字段处理测试通过")
    return True


def main():
    """运行测试"""
    print("🚀 开始测试增强后的兼容性字段处理")
    print("=" * 60)
    
    try:
        success = test_enhanced_compatibility_fields()
        
        print("=" * 60)
        if success:
            print("🎉 增强后的兼容性字段处理测试通过！")
            print()
            print("🎯 修复效果:")
            print("  • 数据一致性验证和自动修正")
            print("  • 缺失overallResult时的智能判断")
            print("  • 最终结果一致性验证和修正")
            print("  • 基于实际问题严重程度的智能决策")
            print("  • 详细的警告日志记录")
            print("  • 优先使用已存在的正确值")
        else:
            print("❌ 增强后的兼容性字段处理测试失败！")
        
        return success
        
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
