from api.service.chunk_service import ChunkService
from api.service.git_service import GitService

project = "~wangqichen02"  # 项目名
repo = "shangou_ai_cr"  # 仓库名
from_branch = "dev/20250516-cr-rag"  # 分支名
to_branch = "dev/test_cr"
# 假设你已初始化好git_service、project、repo、branch（可用mock或真实对象）
# 读取diff内容
git_service = GitService()
chunker = ChunkService(git_service=git_service, project=project, repo=repo, branch=from_branch)

# 对diff内容进行分块
chunks = chunker.chunk_diff_file(None, from_branch, to_branch)

chunker.save_chunks_to_json(chunks, "python_diff_chunk.json")
