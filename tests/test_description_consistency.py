"""
测试结果描述格式的一致性
验证所有组件生成的结果描述格式是否统一
"""

import sys
import os
from unittest.mock import Mock

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.cr_result_enhancer import CRResultEnhancer, ProblemStatistics
from utils.cr_result_optimizer import CRResultOptimizer
from core.agents.result_aggregation_agent import ResultAggregationAgent


def test_cr_result_enhancer_description():
    """测试CR结果增强器的描述生成"""
    print("🧪 测试CR结果增强器的描述生成...")
    
    enhancer = CRResultEnhancer()
    
    # 测试用例1：有多种问题
    statistics1 = ProblemStatistics(
        critical_count=1,
        warning_count=2,
        moderate_count=1,
        minor_count=0
    )
    
    description1 = enhancer._generate_description(statistics1)
    expected1 = "P0:1个,P1:2个,P2:1个"
    
    if description1 == expected1:
        print(f"    ✅ 多种问题描述正确: {description1}")
    else:
        print(f"    ❌ 多种问题描述错误: 期望'{expected1}', 实际'{description1}'")
        return False
    
    # 测试用例2：无问题
    statistics2 = ProblemStatistics(
        critical_count=0,
        warning_count=0,
        moderate_count=0,
        minor_count=0
    )
    
    description2 = enhancer._generate_description(statistics2)
    expected2 = "代码质量良好，未发现问题"
    
    if description2 == expected2:
        print(f"    ✅ 无问题描述正确: {description2}")
    else:
        print(f"    ❌ 无问题描述错误: 期望'{expected2}', 实际'{description2}'")
        return False
    
    # 测试用例3：只有严重问题
    statistics3 = ProblemStatistics(
        critical_count=2,
        warning_count=0,
        moderate_count=0,
        minor_count=0
    )
    
    description3 = enhancer._generate_description(statistics3)
    expected3 = "P0:2个"
    
    if description3 == expected3:
        print(f"    ✅ 只有严重问题描述正确: {description3}")
    else:
        print(f"    ❌ 只有严重问题描述错误: 期望'{expected3}', 实际'{description3}'")
        return False
    
    print(f"  ✅ CR结果增强器描述生成测试通过")
    return True


def test_cr_result_optimizer_description():
    """测试CR结果优化器的描述生成"""
    print("🧪 测试CR结果优化器的描述生成...")
    
    optimizer = CRResultOptimizer()
    
    # 测试用例1：有多种问题
    problems1 = [
        {'level': 'P0', 'problem': '严重问题1'},
        {'level': 'P1', 'problem': '警告问题1'},
        {'level': 'P1', 'problem': '警告问题2'},
        {'level': 'P2', 'problem': '中等问题1'}
    ]
    
    summary1 = {'totalProblems': 0}  # 初始值
    optimized_summary1 = optimizer._recalculate_summary(summary1, problems1)
    
    expected_desc1 = "P0:1个,P1:2个,P2:1个"
    actual_desc1 = optimized_summary1.get('resultDescription', '')
    
    if actual_desc1 == expected_desc1:
        print(f"    ✅ 多种问题描述正确: {actual_desc1}")
    else:
        print(f"    ❌ 多种问题描述错误: 期望'{expected_desc1}', 实际'{actual_desc1}'")
        return False
    
    # 测试用例2：无问题
    problems2 = []
    summary2 = {'totalProblems': 0}
    optimized_summary2 = optimizer._recalculate_summary(summary2, problems2)
    
    expected_desc2 = "代码质量良好，未发现问题"
    actual_desc2 = optimized_summary2.get('resultDescription', '')
    
    if actual_desc2 == expected_desc2:
        print(f"    ✅ 无问题描述正确: {actual_desc2}")
    else:
        print(f"    ❌ 无问题描述错误: 期望'{expected_desc2}', 实际'{actual_desc2}'")
        return False
    
    print(f"  ✅ CR结果优化器描述生成测试通过")
    return True


def test_aggregation_agent_description():
    """测试聚合代理的描述生成"""
    print("🧪 测试聚合代理的描述生成...")
    
    # 创建模拟LLM服务
    mock_llm_service = Mock()
    agent = ResultAggregationAgent(mock_llm_service)
    
    # 模拟内置增强逻辑中的描述生成
    def test_builtin_description_generation():
        """测试内置描述生成逻辑"""
        # 模拟问题统计
        critical_count = 1
        warning_count = 2
        moderate_count = 1
        minor_count = 0
        total_problems = 4
        
        # 生成结果描述 - 确保与实际问题一致
        if total_problems == 0:
            result_description = "代码质量良好，未发现问题"
        else:
            desc_parts = []
            if critical_count > 0:
                desc_parts.append(f"P0:{critical_count}个")
            if warning_count > 0:
                desc_parts.append(f"P1:{warning_count}个")
            if moderate_count > 0:
                desc_parts.append(f"P2:{moderate_count}个")
            if minor_count > 0:
                desc_parts.append(f"P3+:{minor_count}个")
            result_description = ",".join(desc_parts)
        
        return result_description
    
    # 测试用例1：有多种问题
    description1 = test_builtin_description_generation()
    expected1 = "P0:1个,P1:2个,P2:1个"
    
    if description1 == expected1:
        print(f"    ✅ 聚合代理多种问题描述正确: {description1}")
    else:
        print(f"    ❌ 聚合代理多种问题描述错误: 期望'{expected1}', 实际'{description1}'")
        return False
    
    print(f"  ✅ 聚合代理描述生成测试通过")
    return True


def test_description_format_consistency():
    """测试所有组件的描述格式一致性"""
    print("🧪 测试所有组件的描述格式一致性...")
    
    # 定义标准测试用例
    test_cases = [
        {
            'name': '多种问题',
            'critical': 1,
            'warning': 2,
            'moderate': 1,
            'minor': 0,
            'expected': 'P0:1个,P1:2个,P2:1个'
        },
        {
            'name': '只有严重问题',
            'critical': 2,
            'warning': 0,
            'moderate': 0,
            'minor': 0,
            'expected': 'P0:2个'
        },
        {
            'name': '只有警告问题',
            'critical': 0,
            'warning': 3,
            'moderate': 0,
            'minor': 0,
            'expected': 'P1:3个'
        },
        {
            'name': '无问题',
            'critical': 0,
            'warning': 0,
            'moderate': 0,
            'minor': 0,
            'expected': '代码质量良好，未发现问题'
        }
    ]
    
    for case in test_cases:
        print(f"    测试用例: {case['name']}")
        
        # 1. 测试CR结果增强器
        enhancer = CRResultEnhancer()
        statistics = ProblemStatistics(
            critical_count=case['critical'],
            warning_count=case['warning'],
            moderate_count=case['moderate'],
            minor_count=case['minor']
        )
        enhancer_desc = enhancer._generate_description(statistics)
        
        # 2. 测试CR结果优化器
        optimizer = CRResultOptimizer()
        problems = []
        for _ in range(case['critical']):
            problems.append({'level': 'P0', 'problem': '严重问题'})
        for _ in range(case['warning']):
            problems.append({'level': 'P1', 'problem': '警告问题'})
        for _ in range(case['moderate']):
            problems.append({'level': 'P2', 'problem': '中等问题'})
        for _ in range(case['minor']):
            problems.append({'level': 'P3', 'problem': '轻微问题'})
        
        summary = {'totalProblems': 0}
        optimized_summary = optimizer._recalculate_summary(summary, problems)
        optimizer_desc = optimized_summary.get('resultDescription', '')
        
        # 3. 测试聚合代理逻辑
        total_problems = case['critical'] + case['warning'] + case['moderate'] + case['minor']
        if total_problems == 0:
            agent_desc = "代码质量良好，未发现问题"
        else:
            desc_parts = []
            if case['critical'] > 0:
                desc_parts.append(f"P0:{case['critical']}个")
            if case['warning'] > 0:
                desc_parts.append(f"P1:{case['warning']}个")
            if case['moderate'] > 0:
                desc_parts.append(f"P2:{case['moderate']}个")
            if case['minor'] > 0:
                desc_parts.append(f"P3+:{case['minor']}个")
            agent_desc = ",".join(desc_parts)
        
        # 验证一致性
        if enhancer_desc == optimizer_desc == agent_desc == case['expected']:
            print(f"      ✅ 所有组件描述一致: {enhancer_desc}")
        else:
            print(f"      ❌ 描述不一致:")
            print(f"        期望: {case['expected']}")
            print(f"        增强器: {enhancer_desc}")
            print(f"        优化器: {optimizer_desc}")
            print(f"        聚合代理: {agent_desc}")
            return False
    
    print(f"  ✅ 所有组件描述格式一致性测试通过")
    return True


def main():
    """运行所有测试"""
    print("🚀 开始测试结果描述格式的一致性")
    print("=" * 60)
    
    success_count = 0
    total_tests = 4
    
    try:
        # 测试1: CR结果增强器描述生成
        if test_cr_result_enhancer_description():
            success_count += 1
        print()
        
        # 测试2: CR结果优化器描述生成
        if test_cr_result_optimizer_description():
            success_count += 1
        print()
        
        # 测试3: 聚合代理描述生成
        if test_aggregation_agent_description():
            success_count += 1
        print()
        
        # 测试4: 描述格式一致性
        if test_description_format_consistency():
            success_count += 1
        print()
        
        print("=" * 60)
        print(f"🎉 测试完成: {success_count}/{total_tests} 个测试通过")
        
        if success_count == total_tests:
            print("✅ 所有测试通过！结果描述格式已统一")
            print()
            print("🎯 统一格式:")
            print("  • 有问题时：P0:X个,P1:X个,P2:X个")
            print("  • 无问题时：代码质量良好，未发现问题")
            print("  • 所有组件使用相同的格式")
            print("  • 总结和审查结果完全一致")
            return True
        else:
            print("⚠️  部分测试失败，需要进一步检查")
            return False
        
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
