#!/usr/bin/env python3
"""
基础重构测试

测试重构后的基础架构是否正常工作
"""

import unittest
import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


class TestBasicRefactor(unittest.TestCase):
    """基础重构测试类"""
    
    def setUp(self):
        """测试前置设置"""
        self.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.loop)
    
    def tearDown(self):
        """测试后置清理"""
        self.loop.close()
    
    def test_base_agent_import(self):
        """测试Agent基类导入"""
        try:
            from core.agents.base_agent import BaseAgent, AgentResult, AgentStatus
            self.assertTrue(True, "Agent基类导入成功")
        except ImportError as e:
            self.fail(f"Agent基类导入失败: {str(e)}")
    
    def test_base_orchestrator_import(self):
        """测试编排器基类导入"""
        try:
            from core.orchestrator.base_orchestrator import BaseOrchestrator, WorkflowResult, WorkflowStatus
            self.assertTrue(True, "编排器基类导入成功")
        except ImportError as e:
            self.fail(f"编排器基类导入失败: {str(e)}")
    
    def test_services_import(self):
        """测试服务模块导入"""
        try:
            from services import BaseService, ServiceManager
            self.assertTrue(True, "服务基础模块导入成功")
        except ImportError as e:
            self.fail(f"服务基础模块导入失败: {str(e)}")
    
    def test_config_import(self):
        """测试配置模块导入"""
        try:
            from config.base_config import BaseConfig
            self.assertTrue(True, "配置模块导入成功")
        except ImportError as e:
            self.fail(f"配置模块导入失败: {str(e)}")
    
    def test_agent_functionality(self):
        """测试Agent基础功能"""
        from core.agents.base_agent import BaseAgent, AgentResult, AgentStatus
        
        # 创建测试Agent
        class TestAgent(BaseAgent):
            def validate_input(self, input_data):
                return 'test_data' in input_data
            
            async def execute(self, input_data):
                return AgentResult(success=True, data={'result': 'test'})
        
        agent = TestAgent("test_agent")
        
        # 测试基本属性
        self.assertEqual(agent.name, "test_agent")
        self.assertEqual(agent.status, AgentStatus.IDLE)
        
        # 测试配置
        agent.update_config({'test_key': 'test_value'})
        self.assertEqual(agent.config['test_key'], 'test_value')
        
        # 测试状态获取
        status = agent.get_status()
        self.assertIn('name', status)
        self.assertIn('status', status)
        self.assertEqual(status['name'], 'test_agent')
    
    def test_orchestrator_functionality(self):
        """测试编排器基础功能"""
        from core.orchestrator.base_orchestrator import BaseOrchestrator, WorkflowResult, WorkflowStatus
        
        # 创建测试编排器
        class TestOrchestrator(BaseOrchestrator):
            def validate_input(self, input_data):
                return 'test_data' in input_data
            
            async def execute_workflow(self, input_data):
                return WorkflowResult(success=True, data={'result': 'test'})
        
        orchestrator = TestOrchestrator("test_orchestrator")
        
        # 测试基本属性
        self.assertEqual(orchestrator.name, "test_orchestrator")
        self.assertEqual(orchestrator.status, WorkflowStatus.PENDING)
        
        # 测试配置
        orchestrator.update_config({'test_key': 'test_value'})
        self.assertEqual(orchestrator.config['test_key'], 'test_value')
    
    def test_agent_result_serialization(self):
        """测试Agent结果序列化"""
        from core.agents.base_agent import AgentResult
        
        result = AgentResult(
            success=True,
            data={'test': 'data'},
            metadata={'agent': 'test'}
        )
        
        result_dict = result.to_dict()
        
        self.assertIn('success', result_dict)
        self.assertIn('data', result_dict)
        self.assertIn('metadata', result_dict)
        self.assertTrue(result_dict['success'])
        self.assertEqual(result_dict['data']['test'], 'data')
    
    def test_workflow_result_serialization(self):
        """测试工作流结果序列化"""
        from core.orchestrator.base_orchestrator import WorkflowResult
        
        result = WorkflowResult(
            success=True,
            data={'test': 'data'},
            execution_time=1.5
        )
        
        result_dict = result.to_dict()
        
        self.assertIn('success', result_dict)
        self.assertIn('data', result_dict)
        self.assertIn('execution_time', result_dict)
        self.assertTrue(result_dict['success'])
        self.assertEqual(result_dict['execution_time'], 1.5)
    
    async def async_test_agent_execution(self):
        """异步测试Agent执行"""
        from core.agents.base_agent import BaseAgent, AgentResult
        
        class MockAgent(BaseAgent):
            def validate_input(self, input_data):
                return True
            
            async def execute(self, input_data):
                await asyncio.sleep(0.01)  # 模拟异步操作
                return AgentResult(success=True, data={'processed': input_data})
        
        agent = MockAgent("mock_agent")
        result = await agent.run({'test': 'data'})
        
        self.assertTrue(result.success)
        self.assertIn('processed', result.data)
        self.assertEqual(result.data['processed']['test'], 'data')
    
    def test_agent_execution_sync(self):
        """同步测试Agent执行"""
        self.loop.run_until_complete(self.async_test_agent_execution())
    
    async def async_test_orchestrator_execution(self):
        """异步测试编排器执行"""
        from core.orchestrator.base_orchestrator import BaseOrchestrator, WorkflowResult
        
        class MockOrchestrator(BaseOrchestrator):
            def validate_input(self, input_data):
                return True
            
            async def execute_workflow(self, input_data):
                await asyncio.sleep(0.01)  # 模拟异步操作
                return WorkflowResult(success=True, data={'processed': input_data})
        
        orchestrator = MockOrchestrator("mock_orchestrator")
        result = await orchestrator.run({'test': 'data'})
        
        self.assertTrue(result.success)
        self.assertIn('processed', result.data)
        self.assertEqual(result.data['processed']['test'], 'data')
    
    def test_orchestrator_execution_sync(self):
        """同步测试编排器执行"""
        self.loop.run_until_complete(self.async_test_orchestrator_execution())
    
    def test_config_functionality(self):
        """测试配置功能"""
        from config.base_config import BaseConfig
        
        config = BaseConfig()
        
        # 测试基本配置
        self.assertIsNotNone(config.app_name)
        self.assertIsNotNone(config.version)
        
        # 测试服务配置获取
        llm_config = config.get_service_config("llm")
        self.assertIsInstance(llm_config, dict)
    
    def test_service_manager_functionality(self):
        """测试服务管理器功能"""
        from services import ServiceManager, BaseService
        from config.base_config import BaseConfig
        
        config = BaseConfig()
        manager = ServiceManager(config)
        
        # 创建测试服务
        class TestService(BaseService):
            async def initialize(self):
                return True
            
            async def cleanup(self):
                return True
            
            async def health_check(self):
                return {'status': 'healthy'}
        
        test_service = TestService("test_service")
        
        # 测试服务注册
        manager.register_service(test_service)
        
        # 测试服务获取
        retrieved_service = manager.get_service("test_service")
        self.assertIsNotNone(retrieved_service)
        self.assertEqual(retrieved_service.name, "test_service")
        
        # 测试服务列表
        services = manager.list_services()
        self.assertIn("test_service", services)


def run_basic_tests():
    """运行基础测试"""
    print("🧪 开始运行基础重构测试...")
    
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试类
    tests = unittest.TestLoader().loadTestsFromTestCase(TestBasicRefactor)
    test_suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出结果
    if result.wasSuccessful():
        print("\n✅ 基础测试通过！重构架构验证成功。")
        return True
    else:
        print(f"\n❌ 基础测试失败！失败数: {len(result.failures)}, 错误数: {len(result.errors)}")
        return False


if __name__ == "__main__":
    success = run_basic_tests()
    sys.exit(0 if success else 1)
