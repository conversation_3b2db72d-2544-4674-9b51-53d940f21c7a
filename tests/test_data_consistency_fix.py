"""
测试CR结果数据一致性修复
验证评分逻辑、总体结果判断和统计数据的一致性
"""

import asyncio
import sys
import os
from unittest.mock import Mock

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.cr_result_enhancer import CRResultEnhancer
from core.agents.result_aggregation_agent import ResultAggregationAgent


def create_sample_results_with_problems():
    """创建包含多个问题的示例结果"""
    return [
        {
            'checkBranch': 'feature/test-consistency',
            'sumCheckResult': '通过',  # 原始结果错误地显示通过
            'resultDesc': 'P0:1个,P1:2个,P2:1个',
            'totalProblem': '4',
            'problemList': [
                {
                    'scene': 'commonCheck',
                    'num': '4',
                    'checkResult': '不通过',
                    'detail': [
                        {
                            'level': 'P0',
                            'problem': '空指针异常风险',
                            'suggestion': '添加空值检查',
                            'targetCode': 'user.getName()',
                            'codePosition': [10, 1, 10, 15]
                        },
                        {
                            'level': 'P1',
                            'problem': '变量命名不规范',
                            'suggestion': '使用驼峰命名',
                            'targetCode': 'user_name',
                            'codePosition': [5, 1, 5, 9]
                        },
                        {
                            'level': 'P1',
                            'problem': '缺少异常处理',
                            'suggestion': '添加try-catch块',
                            'targetCode': 'File file = new File(path);',
                            'codePosition': [15, 1, 15, 25]
                        },
                        {
                            'level': 'P2',
                            'problem': '代码注释不足',
                            'suggestion': '添加注释',
                            'targetCode': 'function test() {}',
                            'codePosition': [20, 1, 20, 20]
                        }
                    ]
                }
            ]
        }
    ]


def test_cr_result_enhancer_consistency():
    """测试CR结果增强器的数据一致性"""
    print("🧪 测试CR结果增强器的数据一致性...")
    
    # 创建CR结果增强器
    enhancer = CRResultEnhancer()
    
    # 创建包含问题的测试数据
    original_result = create_sample_results_with_problems()[0]
    
    # 增强结果
    enhanced_result = enhancer.enhance_cr_result(original_result)
    
    print(f"  📊 增强结果分析:")
    print(f"    问题统计: P0={enhanced_result.statistics.critical_count}, P1={enhanced_result.statistics.warning_count}, P2={enhanced_result.statistics.moderate_count}, P3+={enhanced_result.statistics.minor_count}")
    print(f"    总问题数: {enhanced_result.statistics.total_count}")
    print(f"    实际问题数: {len(enhanced_result.problems)}")
    print(f"    总体评分: {enhanced_result.overall_score}")
    print(f"    总体结果: {enhanced_result.overall_result}")
    print(f"    结果描述: {enhanced_result.result_description}")
    
    # 验证数据一致性
    print(f"\n  🔍 一致性验证:")
    
    # 1. 验证问题统计与实际问题数量一致
    actual_problems = len(enhanced_result.problems)
    if enhanced_result.statistics.total_count == actual_problems:
        print(f"    ✅ 问题统计一致: {enhanced_result.statistics.total_count}")
    else:
        print(f"    ❌ 问题统计不一致: 统计{enhanced_result.statistics.total_count}, 实际{actual_problems}")
        return False
    
    # 2. 验证有严重问题时必须不通过
    if enhanced_result.statistics.critical_count > 0:
        if enhanced_result.overall_result == "不通过":
            print(f"    ✅ 有{enhanced_result.statistics.critical_count}个严重问题，正确显示不通过")
        else:
            print(f"    ❌ 有{enhanced_result.statistics.critical_count}个严重问题但显示{enhanced_result.overall_result}")
            return False
    
    # 3. 验证评分计算正确性
    expected_score = max(0, 100 - (
        enhanced_result.statistics.critical_count * 25 +
        enhanced_result.statistics.warning_count * 15 +
        enhanced_result.statistics.moderate_count * 10 +
        enhanced_result.statistics.minor_count * 5
    ))
    
    if enhanced_result.overall_score == expected_score:
        print(f"    ✅ 评分计算正确: {enhanced_result.overall_score}分")
    else:
        print(f"    ❌ 评分计算错误: 期望{expected_score}分, 实际{enhanced_result.overall_score}分")
        return False
    
    # 4. 验证结果描述与问题数量一致
    if enhanced_result.statistics.total_count > 0:
        if "未发现问题" in enhanced_result.result_description:
            print(f"    ❌ 有{enhanced_result.statistics.total_count}个问题但描述为'未发现问题'")
            return False
        else:
            print(f"    ✅ 结果描述与问题数量一致")
    
    print(f"  ✅ CR结果增强器数据一致性测试通过")
    return True


async def test_aggregation_agent_consistency():
    """测试聚合代理的数据一致性"""
    print("🧪 测试聚合代理的数据一致性...")
    
    # 创建模拟LLM服务
    mock_llm_service = Mock()
    agent = ResultAggregationAgent(mock_llm_service)
    
    # 创建测试输入数据
    input_data = {
        'results': create_sample_results_with_problems(),
        'options': {
            'project': 'test-project',
            'repo': 'test-repo',
            'fromBranch': 'feature/consistency-test',
            'toBranch': 'main'
        },
        'segments': [],
        'metadata': {'cr_mode': 'standard'}
    }
    
    # 执行聚合
    result = await agent.execute(input_data)
    
    print(f"  📊 聚合结果分析:")
    print(f"    聚合成功: {result.success}")
    
    if result.success:
        data = result.data
        summary = data.get('summary', {})
        scoring = data.get('scoring', {})
        statistics = data.get('statistics', {})
        problems = data.get('problems', [])
        
        print(f"    总问题数(summary): {summary.get('totalProblems', 0)}")
        print(f"    总问题数(statistics): {statistics.get('totalProblems', 0)}")
        print(f"    实际问题数: {len(problems)}")
        print(f"    总体评分: {scoring.get('overallScore', 0)}")
        print(f"    总体结果: {summary.get('overallResult', 'N/A')}")
        print(f"    结果描述: {summary.get('resultDescription', 'N/A')}")
        
        # 验证数据一致性
        print(f"\n  🔍 一致性验证:")
        
        # 1. 验证各处的问题总数一致
        summary_total = summary.get('totalProblems', 0)
        stats_total = statistics.get('totalProblems', 0)
        actual_total = len(problems)
        
        if summary_total == stats_total == actual_total:
            print(f"    ✅ 问题总数一致: {summary_total}")
        else:
            print(f"    ❌ 问题总数不一致: summary={summary_total}, stats={stats_total}, actual={actual_total}")
            return False
        
        # 2. 验证有严重问题时的结果
        critical_count = statistics.get('criticalCount', 0)
        overall_result = summary.get('overallResult', '')
        
        if critical_count > 0:
            if overall_result == "不通过":
                print(f"    ✅ 有{critical_count}个严重问题，正确显示不通过")
            else:
                print(f"    ❌ 有{critical_count}个严重问题但显示{overall_result}")
                return False
        
        # 3. 验证评分与问题数量的关系
        overall_score = scoring.get('overallScore', 0)
        if actual_total > 0 and overall_score == 100:
            print(f"    ❌ 有{actual_total}个问题但评分为100分")
            return False
        elif actual_total == 0 and overall_score != 100:
            print(f"    ❌ 无问题但评分不是100分: {overall_score}")
            return False
        else:
            print(f"    ✅ 评分与问题数量关系正确")
        
        # 4. 验证结果描述的一致性
        result_desc = summary.get('resultDescription', '')
        if actual_total > 0:
            if "未发现问题" in result_desc:
                print(f"    ❌ 有{actual_total}个问题但描述为'未发现问题'")
                return False
            else:
                print(f"    ✅ 结果描述与实际问题一致")
        else:
            if "未发现问题" not in result_desc:
                print(f"    ⚠️  无问题但描述不是'未发现问题': {result_desc}")
            else:
                print(f"    ✅ 无问题时结果描述正确")
        
        print(f"  ✅ 聚合代理数据一致性测试通过")
        return True
    else:
        print(f"    ❌ 聚合失败: {result.error}")
        return False


def test_scoring_algorithm():
    """测试评分算法的正确性"""
    print("🧪 测试评分算法的正确性...")
    
    # 测试用例
    test_cases = [
        {"P0": 0, "P1": 0, "P2": 0, "P3": 0, "expected": 100, "result": "通过"},
        {"P0": 1, "P1": 0, "P2": 0, "P3": 0, "expected": 75, "result": "不通过"},
        {"P0": 0, "P1": 1, "P2": 0, "P3": 0, "expected": 85, "result": "通过"},
        {"P0": 0, "P1": 0, "P2": 1, "P3": 0, "expected": 90, "result": "通过"},
        {"P0": 0, "P1": 0, "P2": 0, "P3": 1, "expected": 95, "result": "通过"},
        {"P0": 1, "P1": 2, "P2": 1, "P3": 1, "expected": 30, "result": "不通过"},  # 100 - (1*25 + 2*15 + 1*10 + 1*5) = 100 - 70 = 30
        {"P0": 0, "P1": 6, "P2": 0, "P3": 0, "expected": 10, "result": "不通过"},
    ]
    
    for i, case in enumerate(test_cases):
        print(f"  测试用例{i+1}: P0={case['P0']}, P1={case['P1']}, P2={case['P2']}, P3={case['P3']}")
        
        # 计算评分
        actual_score = max(0, 100 - (case['P0'] * 25 + case['P1'] * 15 + case['P2'] * 10 + case['P3'] * 5))
        
        # 判断结果
        if case['P0'] > 0:
            actual_result = "不通过"  # 有严重问题必须不通过
        elif actual_score < 80:
            actual_result = "不通过"  # 评分低于80分不通过
        else:
            actual_result = "通过"
        
        # 验证评分
        if actual_score == case['expected']:
            print(f"    ✅ 评分正确: {actual_score}分")
        else:
            print(f"    ❌ 评分错误: 期望{case['expected']}分, 实际{actual_score}分")
            return False
        
        # 验证结果
        if actual_result == case['result']:
            print(f"    ✅ 结果正确: {actual_result}")
        else:
            print(f"    ❌ 结果错误: 期望{case['result']}, 实际{actual_result}")
            return False
    
    print(f"  ✅ 评分算法测试通过")
    return True


async def main():
    """运行所有测试"""
    print("🚀 开始测试CR结果数据一致性修复")
    print("=" * 60)
    
    success_count = 0
    total_tests = 3
    
    try:
        # 测试1: CR结果增强器一致性
        if test_cr_result_enhancer_consistency():
            success_count += 1
        print()
        
        # 测试2: 聚合代理一致性
        if await test_aggregation_agent_consistency():
            success_count += 1
        print()
        
        # 测试3: 评分算法正确性
        if test_scoring_algorithm():
            success_count += 1
        print()
        
        print("=" * 60)
        print(f"🎉 测试完成: {success_count}/{total_tests} 个测试通过")
        
        if success_count == total_tests:
            print("✅ 所有测试通过！CR结果数据一致性修复成功")
            print()
            print("🎯 修复效果:")
            print("  • 有严重问题(P0)时，overallResult正确显示'不通过'")
            print("  • 评分算法统一：100 - (P0×25 + P1×15 + P2×10 + P3+×5)")
            print("  • summary.totalProblems与实际problems数组长度一致")
            print("  • resultDescription准确反映实际问题情况")
            print("  • 评分低于80分时正确显示'不通过'")
            print("  • 数据一致性验证机制自动检测和修正不一致")
            return True
        else:
            print("⚠️  部分测试失败，需要进一步检查")
            return False
        
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == '__main__':
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
