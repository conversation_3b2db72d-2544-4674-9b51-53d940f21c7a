"""
测试CR服务的兼容性字段处理
验证services/cr_service.py中第375-382行的兼容性字段处理逻辑
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


def test_compatibility_fields_processing():
    """测试兼容性字段处理逻辑"""
    print("🧪 测试兼容性字段处理逻辑...")
    
    # 模拟聚合代理返回的正确结果（有问题的情况）
    final_result_with_problems = {
        'summary': {
            'checkBranch': 'test-project/test-repo:feature/test',
            'reviewTime': '2024-01-01 12:00:00',
            'reviewer': 'AI代码审查系统',
            'overallResult': '不通过',  # 正确的结果
            'resultDescription': 'P0:1个,P1:2个,P2:1个',  # 正确的描述
            'totalProblems': 4,  # 正确的问题数量
            'crMode': 'standard'
        },
        'scoring': {
            'overallScore': 35,  # 正确的评分
            'qualityGrade': 'D',
            'passThreshold': 80,
            'isPassed': False  # 正确的通过状态
        },
        'statistics': {
            'totalProblems': 4,
            'criticalCount': 1,
            'warningCount': 2,
            'moderateCount': 1,
            'minorCount': 0
        },
        'problems': [
            {'severity': 'CRITICAL', 'problem': '空指针异常风险'},
            {'severity': 'WARNING', 'problem': '变量命名不规范'},
            {'severity': 'WARNING', 'problem': '缺少异常处理'},
            {'severity': 'MODERATE', 'problem': '代码注释不足'}
        ]
    }
    
    # 模拟CR服务中的兼容性字段处理逻辑（第375-382行）
    def process_compatibility_fields(final_result):
        """模拟CR服务中的兼容性字段处理"""
        # 确保包含基础的兼容字段
        if 'summary' in final_result:
            summary = final_result['summary']
            final_result['sumCheckResult'] = summary.get('overallResult', '通过')  # 这里的默认值可能有问题
            final_result['totalProblem'] = str(summary.get('totalProblems', 0))
            final_result['resultDesc'] = summary.get('resultDescription', '')
            if 'checkBranch' not in final_result:
                final_result['checkBranch'] = summary.get('checkBranch', 'unknown')
        
        return final_result
    
    # 测试有问题的情况
    print(f"  📊 测试有问题的情况:")
    result_copy = final_result_with_problems.copy()
    processed_result = process_compatibility_fields(result_copy)
    
    print(f"    原始summary.overallResult: {final_result_with_problems['summary']['overallResult']}")
    print(f"    处理后sumCheckResult: {processed_result.get('sumCheckResult')}")
    print(f"    原始summary.totalProblems: {final_result_with_problems['summary']['totalProblems']}")
    print(f"    处理后totalProblem: {processed_result.get('totalProblem')}")
    print(f"    原始summary.resultDescription: {final_result_with_problems['summary']['resultDescription']}")
    print(f"    处理后resultDesc: {processed_result.get('resultDesc')}")
    
    # 验证处理结果
    success = True
    
    if processed_result.get('sumCheckResult') != '不通过':
        print(f"    ❌ sumCheckResult错误: 期望'不通过', 实际'{processed_result.get('sumCheckResult')}'")
        success = False
    else:
        print(f"    ✅ sumCheckResult正确: {processed_result.get('sumCheckResult')}")
    
    if processed_result.get('totalProblem') != '4':
        print(f"    ❌ totalProblem错误: 期望'4', 实际'{processed_result.get('totalProblem')}'")
        success = False
    else:
        print(f"    ✅ totalProblem正确: {processed_result.get('totalProblem')}")
    
    if processed_result.get('resultDesc') != 'P0:1个,P1:2个,P2:1个':
        print(f"    ❌ resultDesc错误: 期望'P0:1个,P1:2个,P2:1个', 实际'{processed_result.get('resultDesc')}'")
        success = False
    else:
        print(f"    ✅ resultDesc正确: {processed_result.get('resultDesc')}")
    
    # 测试无问题的情况
    print(f"\n  📊 测试无问题的情况:")
    final_result_no_problems = {
        'summary': {
            'checkBranch': 'test-project/test-repo:feature/clean',
            'reviewTime': '2024-01-01 12:00:00',
            'reviewer': 'AI代码审查系统',
            'overallResult': '通过',
            'resultDescription': '代码质量良好，未发现问题',
            'totalProblems': 0,
            'crMode': 'standard'
        },
        'scoring': {
            'overallScore': 100,
            'qualityGrade': 'A',
            'passThreshold': 80,
            'isPassed': True
        },
        'statistics': {
            'totalProblems': 0,
            'criticalCount': 0,
            'warningCount': 0,
            'moderateCount': 0,
            'minorCount': 0
        },
        'problems': []
    }
    
    result_copy2 = final_result_no_problems.copy()
    processed_result2 = process_compatibility_fields(result_copy2)
    
    print(f"    原始summary.overallResult: {final_result_no_problems['summary']['overallResult']}")
    print(f"    处理后sumCheckResult: {processed_result2.get('sumCheckResult')}")
    print(f"    原始summary.totalProblems: {final_result_no_problems['summary']['totalProblems']}")
    print(f"    处理后totalProblem: {processed_result2.get('totalProblem')}")
    
    if processed_result2.get('sumCheckResult') != '通过':
        print(f"    ❌ sumCheckResult错误: 期望'通过', 实际'{processed_result2.get('sumCheckResult')}'")
        success = False
    else:
        print(f"    ✅ sumCheckResult正确: {processed_result2.get('sumCheckResult')}")
    
    if processed_result2.get('totalProblem') != '0':
        print(f"    ❌ totalProblem错误: 期望'0', 实际'{processed_result2.get('totalProblem')}'")
        success = False
    else:
        print(f"    ✅ totalProblem正确: {processed_result2.get('totalProblem')}")
    
    # 测试边界情况：summary为空或缺失字段
    print(f"\n  📊 测试边界情况:")
    final_result_missing_fields = {
        'summary': {
            'checkBranch': 'test-project/test-repo:feature/edge-case',
            # 缺少overallResult字段
            # 缺少totalProblems字段
            # 缺少resultDescription字段
        }
    }
    
    result_copy3 = final_result_missing_fields.copy()
    processed_result3 = process_compatibility_fields(result_copy3)
    
    print(f"    缺失字段时的默认值:")
    print(f"    sumCheckResult: {processed_result3.get('sumCheckResult')}")
    print(f"    totalProblem: {processed_result3.get('totalProblem')}")
    print(f"    resultDesc: {processed_result3.get('resultDesc')}")
    
    # 这里的默认值应该是合理的
    if processed_result3.get('sumCheckResult') == '通过':
        print(f"    ⚠️  注意：缺失overallResult时默认为'通过'，这可能不合适")
    
    if processed_result3.get('totalProblem') == '0':
        print(f"    ✅ 缺失totalProblems时默认为'0'，这是合理的")
    
    if processed_result3.get('resultDesc') == '':
        print(f"    ✅ 缺失resultDescription时默认为空字符串，这是合理的")
    
    return success


def test_potential_issues():
    """测试潜在的问题"""
    print("\n🔍 分析潜在问题:")
    
    print("  1. 默认值问题:")
    print("     summary.get('overallResult', '通过') - 默认值为'通过'可能不合适")
    print("     当summary中没有overallResult字段时，会默认显示'通过'")
    print("     这可能导致有问题的代码被错误地标记为通过")
    
    print("\n  2. 字段覆盖问题:")
    print("     兼容性字段处理会覆盖final_result中的字段")
    print("     如果聚合代理已经设置了正确的值，这里可能会被覆盖")
    
    print("\n  3. 数据一致性问题:")
    print("     兼容性字段处理只考虑了summary中的数据")
    print("     没有验证与scoring、statistics、problems的一致性")
    
    print("\n💡 建议的修复方案:")
    print("  1. 修改默认值逻辑，当无法确定结果时应该更保守")
    print("  2. 添加数据一致性验证")
    print("  3. 优先使用已存在的正确字段值")


def main():
    """运行测试"""
    print("🚀 开始测试兼容性字段处理逻辑")
    print("=" * 60)
    
    try:
        success = test_compatibility_fields_processing()
        test_potential_issues()
        
        print("=" * 60)
        if success:
            print("✅ 兼容性字段处理逻辑测试通过")
            print("\n🎯 测试结果:")
            print("  • 有问题时正确设置兼容性字段")
            print("  • 无问题时正确设置兼容性字段")
            print("  • 字段映射逻辑正确")
        else:
            print("❌ 兼容性字段处理逻辑测试失败")
        
        print("\n⚠️  需要注意的问题:")
        print("  • 默认值'通过'可能导致错误的结果")
        print("  • 需要添加数据一致性验证")
        print("  • 建议优化兼容性字段处理逻辑")
        
        return success
        
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
