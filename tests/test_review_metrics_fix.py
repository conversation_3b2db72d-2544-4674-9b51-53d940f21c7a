"""
测试reviewMetrics字段名修复
验证编排器验证逻辑与聚合代理返回字段的一致性
"""

import sys
import os
from unittest.mock import Mock

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.orchestrator.cr_orchestrator import CROrchestrator


def test_validate_final_result_with_review_metrics():
    """测试最终结果验证包含reviewMetrics字段"""
    print("🧪 测试最终结果验证包含reviewMetrics字段...")
    
    # 创建模拟服务
    mock_devmind_service = Mock()
    mock_chunk_service = Mock()
    mock_git_service = Mock()
    orchestrator = CROrchestrator("test_orchestrator", mock_devmind_service, mock_chunk_service, mock_git_service)
    
    # 测试用例1：包含所有必需字段（使用reviewMetrics）
    complete_result = {
        'summary': {
            'checkBranch': 'test-project/test-repo:feature/metrics-test',
            'overallResult': '不通过',
            'totalProblems': 2
        },
        'scoring': {
            'overallScore': 70,
            'isPassed': False
        },
        'problems': [
            {
                'level': 'P0',
                'problem': '严重问题',
                'suggestion': '立即修复'
            },
            {
                'level': 'P1',
                'problem': '警告问题',
                'suggestion': '建议修复'
            }
        ],
        'statistics': {
            'totalProblems': 2,
            'criticalCount': 1,
            'warningCount': 1,
            'moderateCount': 0,
            'minorCount': 0
        },
        'reviewMetrics': {  # 使用驼峰命名
            'qualityScore': 70,
            'riskLevel': '高',
            'totalProblems': 2,
            'problemDensity': '2/文件'
        },
        'recommendations': [
            '优先修复所有严重问题',
            '关注警告级别问题的修复'
        ]
    }
    
    # 验证完整结果
    if orchestrator._validate_final_result(complete_result):
        print(f"    ✅ 包含reviewMetrics的完整结果验证通过")
    else:
        print(f"    ❌ 包含reviewMetrics的完整结果验证失败")
        return False
    
    # 测试用例2：缺少reviewMetrics字段
    incomplete_result = {
        'summary': {
            'checkBranch': 'test-project/test-repo:feature/incomplete',
            'overallResult': '通过',
            'totalProblems': 0
        },
        'scoring': {
            'overallScore': 100,
            'isPassed': True
        },
        'problems': [],
        'statistics': {
            'totalProblems': 0,
            'criticalCount': 0,
            'warningCount': 0,
            'moderateCount': 0,
            'minorCount': 0
        },
        'recommendations': ['代码质量良好']
        # 缺少reviewMetrics字段
    }
    
    # 验证不完整结果
    if not orchestrator._validate_final_result(incomplete_result):
        print(f"    ✅ 缺少reviewMetrics的不完整结果验证正确失败")
    else:
        print(f"    ❌ 缺少reviewMetrics的不完整结果验证错误通过")
        return False
    
    # 测试用例3：使用错误的字段名（review_metrics）
    wrong_field_result = {
        'summary': {
            'checkBranch': 'test-project/test-repo:feature/wrong-field',
            'overallResult': '通过',
            'totalProblems': 0
        },
        'scoring': {
            'overallScore': 100,
            'isPassed': True
        },
        'problems': [],
        'statistics': {
            'totalProblems': 0,
            'criticalCount': 0,
            'warningCount': 0,
            'moderateCount': 0,
            'minorCount': 0
        },
        'review_metrics': {  # 错误的下划线命名
            'qualityScore': 100,
            'riskLevel': '低',
            'totalProblems': 0
        },
        'recommendations': ['代码质量良好']
    }
    
    # 验证错误字段名结果
    if not orchestrator._validate_final_result(wrong_field_result):
        print(f"    ✅ 使用错误字段名review_metrics的结果验证正确失败")
    else:
        print(f"    ❌ 使用错误字段名review_metrics的结果验证错误通过")
        return False
    
    print(f"  ✅ reviewMetrics字段验证测试通过")
    return True


def test_create_basic_result_format_includes_review_metrics():
    """测试基础结果格式创建包含reviewMetrics字段"""
    print("🧪 测试基础结果格式创建包含reviewMetrics字段...")
    
    # 创建模拟服务
    mock_devmind_service = Mock()
    mock_chunk_service = Mock()
    mock_git_service = Mock()
    orchestrator = CROrchestrator("test_orchestrator", mock_devmind_service, mock_chunk_service, mock_git_service)
    
    # 创建测试数据
    original_result = {
        'checkBranch': 'test-project/test-repo:feature/basic-format',
        'sumCheckResult': '不通过',
        'resultDesc': 'P0:1个,P1:1个',
        'totalProblem': '2'
    }
    
    review_results = [
        {
            'problemList': [
                {
                    'scene': 'test-file.py',
                    'num': '2',
                    'checkResult': '不通过',
                    'detail': [
                        {
                            'level': 'P0',
                            'problem': '空指针异常风险',
                            'suggestion': '添加空值检查',
                            'targetCode': 'user.getName()',
                            'codePosition': [10, 1, 10, 15]
                        },
                        {
                            'level': 'P1',
                            'problem': '变量命名不规范',
                            'suggestion': '使用驼峰命名',
                            'targetCode': 'user_name',
                            'codePosition': [5, 1, 5, 9]
                        }
                    ]
                }
            ]
        }
    ]
    
    segments = [
        {'id': 'seg1', 'content': 'test segment 1'},
        {'id': 'seg2', 'content': 'test segment 2'}
    ]
    
    try:
        # 创建基础格式结果
        basic_result = orchestrator._create_basic_result_format(original_result, review_results, segments)
        
        print(f"    📊 基础结果格式创建成功")
        print(f"    包含字段: {list(basic_result.keys())}")
        
        # 验证包含reviewMetrics字段
        if 'reviewMetrics' in basic_result:
            print(f"    ✅ 包含reviewMetrics字段")
            
            review_metrics = basic_result['reviewMetrics']
            print(f"    reviewMetrics内容: {review_metrics}")
            
            # 验证reviewMetrics的内容
            required_metrics = ['qualityScore', 'riskLevel', 'totalProblems']
            missing_metrics = [metric for metric in required_metrics if metric not in review_metrics]
            
            if not missing_metrics:
                print(f"    ✅ reviewMetrics包含所有必需字段")
            else:
                print(f"    ❌ reviewMetrics缺少字段: {missing_metrics}")
                return False
            
            # 验证数据一致性
            if review_metrics['totalProblems'] == 2:
                print(f"    ✅ reviewMetrics.totalProblems正确: {review_metrics['totalProblems']}")
            else:
                print(f"    ❌ reviewMetrics.totalProblems错误: {review_metrics['totalProblems']}")
                return False
            
            if review_metrics['riskLevel'] == '高':  # 有P0问题应该是高风险
                print(f"    ✅ reviewMetrics.riskLevel正确: {review_metrics['riskLevel']}")
            else:
                print(f"    ❌ reviewMetrics.riskLevel错误: {review_metrics['riskLevel']}")
                return False
            
        else:
            print(f"    ❌ 缺少reviewMetrics字段")
            return False
        
        # 验证创建的结果能通过验证
        if orchestrator._validate_final_result(basic_result):
            print(f"    ✅ 创建的基础结果通过验证")
        else:
            print(f"    ❌ 创建的基础结果未通过验证")
            return False
        
        print(f"  ✅ 基础结果格式创建测试通过")
        return True
        
    except Exception as e:
        print(f"    ❌ 基础结果格式创建失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_field_name_consistency():
    """测试字段名一致性"""
    print("🧪 测试字段名一致性...")
    
    # 创建模拟服务
    mock_devmind_service = Mock()
    mock_chunk_service = Mock()
    mock_git_service = Mock()
    orchestrator = CROrchestrator("test_orchestrator", mock_devmind_service, mock_chunk_service, mock_git_service)
    
    # 模拟聚合代理返回的结果格式
    aggregation_result = {
        'summary': {
            'checkBranch': 'test-project/test-repo:feature/consistency',
            'overallResult': '通过',
            'totalProblems': 0
        },
        'scoring': {
            'overallScore': 100,
            'isPassed': True
        },
        'problems': [],
        'statistics': {
            'totalProblems': 0,
            'criticalCount': 0,
            'warningCount': 0,
            'moderateCount': 0,
            'minorCount': 0
        },
        'reviewMetrics': {  # 聚合代理使用的字段名
            'qualityScore': 100,
            'riskLevel': '低',
            'totalProblems': 0,
            'problemDensity': '0/文件'
        },
        'recommendations': ['代码质量良好']
    }
    
    # 验证聚合代理格式能通过编排器验证
    if orchestrator._validate_final_result(aggregation_result):
        print(f"    ✅ 聚合代理返回格式通过编排器验证")
    else:
        print(f"    ❌ 聚合代理返回格式未通过编排器验证")
        return False
    
    # 验证编排器创建的格式也使用相同字段名
    basic_result = orchestrator._create_basic_result_format({}, [], [])
    
    if 'reviewMetrics' in basic_result:
        print(f"    ✅ 编排器创建的基础格式使用reviewMetrics字段")
    else:
        print(f"    ❌ 编排器创建的基础格式未使用reviewMetrics字段")
        return False
    
    print(f"  ✅ 字段名一致性测试通过")
    return True


def main():
    """运行所有测试"""
    print("🚀 开始测试reviewMetrics字段名修复")
    print("=" * 60)
    
    success_count = 0
    total_tests = 3
    
    try:
        # 测试1: reviewMetrics字段验证
        if test_validate_final_result_with_review_metrics():
            success_count += 1
        print()
        
        # 测试2: 基础结果格式创建
        if test_create_basic_result_format_includes_review_metrics():
            success_count += 1
        print()
        
        # 测试3: 字段名一致性
        if test_field_name_consistency():
            success_count += 1
        print()
        
        print("=" * 60)
        print(f"🎉 测试完成: {success_count}/{total_tests} 个测试通过")
        
        if success_count == total_tests:
            print("✅ 所有测试通过！reviewMetrics字段名问题已修复")
            print()
            print("🎯 修复效果:")
            print("  • 编排器验证逻辑使用reviewMetrics（驼峰命名）")
            print("  • 与聚合代理返回字段名保持一致")
            print("  • 基础结果格式创建包含正确的reviewMetrics字段")
            print("  • 消除了'最终结果缺少必需字段: review_metrics'警告")
            print("  • 确保了字段名的一致性")
            return True
        else:
            print("⚠️  部分测试失败，需要进一步检查")
            return False
        
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
