"""
测试分支信息修复
验证checkBranch字段是否正确设置为fromBranch而不是文件路径
"""

import asyncio
import sys
import os
from unittest.mock import Mock

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.agents.result_aggregation_agent import ResultAggregationAgent
from utils.cr_result_enhancer import CRResultEnhancer


def test_branch_info_extraction():
    """测试分支信息提取功能"""
    print("🧪 测试分支信息提取功能...")
    
    # 创建模拟LLM服务
    mock_llm_service = Mock()
    agent = ResultAggregationAgent(mock_llm_service)
    
    # 测试用例1：从options中提取分支信息
    options1 = {
        'project': 'test-project',
        'repo': 'test-repo',
        'fromBranch': 'feature/user-management',
        'toBranch': 'main'
    }
    metadata1 = {}
    
    branch_info = agent._extract_correct_branch_info(options1, metadata1)
    expected = "test-project/test-repo:feature/user-management"
    
    if branch_info == expected:
        print(f"  ✅ 测试1通过: {branch_info}")
    else:
        print(f"  ❌ 测试1失败: 期望 {expected}, 实际 {branch_info}")
    
    # 测试用例2：只有分支名，没有项目和仓库信息
    options2 = {
        'fromBranch': 'feature/api-optimization'
    }
    metadata2 = {}
    
    branch_info2 = agent._extract_correct_branch_info(options2, metadata2)
    expected2 = "feature/api-optimization"
    
    if branch_info2 == expected2:
        print(f"  ✅ 测试2通过: {branch_info2}")
    else:
        print(f"  ❌ 测试2失败: 期望 {expected2}, 实际 {branch_info2}")
    
    # 测试用例3：从metadata中提取分支信息
    options3 = {}
    metadata3 = {
        'project': 'meta-project',
        'repo': 'meta-repo',
        'fromBranch': 'dev/feature-branch'
    }
    
    branch_info3 = agent._extract_correct_branch_info(options3, metadata3)
    expected3 = "meta-project/meta-repo:dev/feature-branch"
    
    if branch_info3 == expected3:
        print(f"  ✅ 测试3通过: {branch_info3}")
    else:
        print(f"  ❌ 测试3失败: 期望 {expected3}, 实际 {branch_info3}")
    
    print("  ✅ 分支信息提取功能测试完成")


def test_branch_info_building():
    """测试分支信息构建功能"""
    print("🧪 测试分支信息构建功能...")
    
    # 创建模拟LLM服务
    mock_llm_service = Mock()
    agent = ResultAggregationAgent(mock_llm_service)
    
    # 测试用例1：完整的分支信息
    options1 = {
        'project': 'shangou',
        'repo': 'ai_cr',
        'fromBranch': 'feature/branch-fix',
        'toBranch': 'main'
    }
    
    branch_info1 = agent._build_branch_info(options1)
    expected1 = {
        'project': 'shangou',
        'repo': 'ai_cr',
        'fromBranch': 'feature/branch-fix',
        'toBranch': 'main'
    }
    
    if branch_info1 == expected1:
        print(f"  ✅ 测试1通过: {branch_info1}")
    else:
        print(f"  ❌ 测试1失败: 期望 {expected1}, 实际 {branch_info1}")
    
    # 测试用例2：部分信息
    options2 = {
        'fromBranch': 'dev/test-branch'
    }
    
    branch_info2 = agent._build_branch_info(options2)
    expected2 = {
        'fromBranch': 'dev/test-branch'
    }
    
    if branch_info2 == expected2:
        print(f"  ✅ 测试2通过: {branch_info2}")
    else:
        print(f"  ❌ 测试2失败: 期望 {expected2}, 实际 {branch_info2}")
    
    print("  ✅ 分支信息构建功能测试完成")


def test_cr_result_enhancer_branch_fix():
    """测试CR结果增强器的分支信息修复"""
    print("🧪 测试CR结果增强器的分支信息修复...")
    
    # 创建CR结果增强器
    enhancer = CRResultEnhancer()
    
    # 测试用例1：错误的checkBranch（文件路径格式）
    original_result1 = {
        'checkBranch': '.env:1-10',  # 错误的格式
        'sumCheckResult': '通过',
        'resultDesc': '无问题发现',
        'totalProblem': '0',
        'problemList': []
    }
    
    branch_info1 = {
        'project': 'test-project',
        'repo': 'test-repo',
        'fromBranch': 'feature/correct-branch',
        'toBranch': 'main'
    }
    
    enhanced_result1 = enhancer.enhance_cr_result(
        original_result1,
        reviewer="AI代码审查",
        branch_info=branch_info1
    )
    
    expected_branch1 = "test-project/test-repo:feature/correct-branch"
    if enhanced_result1.check_branch == expected_branch1:
        print(f"  ✅ 测试1通过: 错误格式已修正为 {enhanced_result1.check_branch}")
    else:
        print(f"  ❌ 测试1失败: 期望 {expected_branch1}, 实际 {enhanced_result1.check_branch}")
    
    # 测试用例2：没有分支信息，但checkBranch是错误格式
    original_result2 = {
        'checkBranch': 'src/main/java/Test.java:15-25',  # 错误的格式
        'sumCheckResult': '通过',
        'resultDesc': '无问题发现',
        'totalProblem': '0',
        'problemList': []
    }
    
    enhanced_result2 = enhancer.enhance_cr_result(
        original_result2,
        reviewer="AI代码审查",
        branch_info=None
    )
    
    if enhanced_result2.check_branch == "未知分支":
        print(f"  ✅ 测试2通过: 错误格式已重置为 {enhanced_result2.check_branch}")
    else:
        print(f"  ❌ 测试2失败: 期望 '未知分支', 实际 {enhanced_result2.check_branch}")
    
    # 测试用例3：正确的checkBranch格式
    original_result3 = {
        'checkBranch': 'feature/good-branch',  # 正确的格式
        'sumCheckResult': '通过',
        'resultDesc': '无问题发现',
        'totalProblem': '0',
        'problemList': []
    }
    
    enhanced_result3 = enhancer.enhance_cr_result(
        original_result3,
        reviewer="AI代码审查",
        branch_info=None
    )
    
    if enhanced_result3.check_branch == "feature/good-branch":
        print(f"  ✅ 测试3通过: 正确格式保持不变 {enhanced_result3.check_branch}")
    else:
        print(f"  ❌ 测试3失败: 期望 'feature/good-branch', 实际 {enhanced_result3.check_branch}")
    
    print("  ✅ CR结果增强器分支信息修复测试完成")


async def test_aggregation_agent_branch_fix():
    """测试聚合代理的分支信息修复"""
    print("🧪 测试聚合代理的分支信息修复...")
    
    # 创建模拟LLM服务
    mock_llm_service = Mock()
    agent = ResultAggregationAgent(mock_llm_service)
    
    # 创建包含错误checkBranch的合并结果
    merged_result = {
        'checkBranch': 'config/settings.py:1-50',  # 错误的格式
        'sumCheckResult': '通过',
        'resultDesc': '无问题发现',
        'totalProblem': '0',
        'problemList': []
    }
    
    original_results = [merged_result]
    segments = []
    metadata = {}
    options = {
        'project': 'test-project',
        'repo': 'test-repo',
        'fromBranch': 'feature/settings-update',
        'toBranch': 'main'
    }
    
    # 测试内置增强逻辑
    enhanced_result = await agent._builtin_enhance_result(
        merged_result, original_results, segments, metadata, options
    )
    
    expected_branch = "test-project/test-repo:feature/settings-update"
    actual_branch = enhanced_result['summary']['checkBranch']
    
    if actual_branch == expected_branch:
        print(f"  ✅ 聚合代理测试通过: checkBranch已修正为 {actual_branch}")
    else:
        print(f"  ❌ 聚合代理测试失败: 期望 {expected_branch}, 实际 {actual_branch}")
    
    print("  ✅ 聚合代理分支信息修复测试完成")


async def main():
    """运行所有测试"""
    print("🚀 开始测试分支信息修复功能")
    print("=" * 60)
    
    try:
        # 测试1: 分支信息提取
        test_branch_info_extraction()
        print()
        
        # 测试2: 分支信息构建
        test_branch_info_building()
        print()
        
        # 测试3: CR结果增强器分支修复
        test_cr_result_enhancer_branch_fix()
        print()
        
        # 测试4: 聚合代理分支修复
        await test_aggregation_agent_branch_fix()
        print()
        
        print("=" * 60)
        print("🎉 所有测试完成！分支信息修复功能正常工作")
        print()
        print("🎯 修复效果:")
        print("  • checkBranch不再显示文件路径格式（如 '.env:1-10'）")
        print("  • 正确使用fromBranch作为分支信息")
        print("  • 支持project/repo:branch格式")
        print("  • 错误格式自动重置为'未知分支'")
        print("  • 优先使用提供的分支信息而不是原始结果")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == '__main__':
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
