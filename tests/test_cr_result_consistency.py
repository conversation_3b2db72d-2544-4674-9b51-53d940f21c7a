"""
测试CR结果一致性
验证修复后的逻辑是否能正确处理有问题但显示通过的情况
"""

import sys
import os
from unittest.mock import Mock

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.cr_result_enhancer import CRResultEnhancer


def test_result_consistency_with_problems():
    """测试有问题时结果的一致性"""
    print("🧪 测试有问题时结果的一致性...")
    
    # 创建包含问题的原始结果
    original_result_with_problems = {
        'checkBranch': 'feature/test-consistency',
        'sumCheckResult': '通过',  # 原始结果错误地显示通过
        'resultDesc': 'P0:1个,P1:2个',
        'totalProblem': '3',
        'problemList': [
            {
                'scene': 'commonCheck',
                'num': '3',
                'checkResult': '不通过',
                'detail': [
                    {
                        'level': 'P0',
                        'problem': '空指针异常风险',
                        'suggestion': '添加空值检查',
                        'targetCode': 'user.getName()',
                        'codePosition': [10, 1, 10, 15]
                    },
                    {
                        'level': 'P1',
                        'problem': '变量命名不规范',
                        'suggestion': '使用驼峰命名',
                        'targetCode': 'user_name',
                        'codePosition': [5, 1, 5, 9]
                    },
                    {
                        'level': 'P1',
                        'problem': '缺少异常处理',
                        'suggestion': '添加try-catch块',
                        'targetCode': 'File file = new File(path);',
                        'codePosition': [15, 1, 15, 25]
                    }
                ]
            }
        ]
    }
    
    # 创建CR结果增强器
    enhancer = CRResultEnhancer()
    
    # 增强结果
    enhanced_result = enhancer.enhance_cr_result(original_result_with_problems)
    
    # 转换为完整格式
    complete_result = enhancer.to_complete_format(enhanced_result)
    
    print(f"  📊 增强结果分析:")
    print(f"    原始sumCheckResult: {original_result_with_problems.get('sumCheckResult')}")
    print(f"    增强后overallResult: {enhanced_result.overall_result}")
    print(f"    问题总数: {enhanced_result.statistics.total_count}")
    print(f"    严重问题数: {enhanced_result.statistics.critical_count}")
    print(f"    警告问题数: {enhanced_result.statistics.warning_count}")
    print(f"    总体评分: {enhanced_result.overall_score}")
    print(f"    结果描述: {enhanced_result.result_description}")
    
    # 验证一致性
    print(f"\n  🔍 一致性验证:")
    
    # 1. 验证有问题时不应该显示"通过"
    if enhanced_result.statistics.total_count > 0:
        if enhanced_result.overall_result == "通过":
            print(f"    ❌ 错误：有{enhanced_result.statistics.total_count}个问题但显示通过")
            return False
        else:
            print(f"    ✅ 正确：有{enhanced_result.statistics.total_count}个问题，显示{enhanced_result.overall_result}")
    
    # 2. 验证严重问题必须导致不通过
    if enhanced_result.statistics.critical_count > 0:
        if enhanced_result.overall_result == "通过":
            print(f"    ❌ 错误：有{enhanced_result.statistics.critical_count}个严重问题但显示通过")
            return False
        else:
            print(f"    ✅ 正确：有{enhanced_result.statistics.critical_count}个严重问题，显示{enhanced_result.overall_result}")
    
    # 3. 验证结果描述与问题数量一致
    if enhanced_result.statistics.total_count > 0:
        if "未发现问题" in enhanced_result.result_description:
            print(f"    ❌ 错误：有{enhanced_result.statistics.total_count}个问题但描述为'未发现问题'")
            return False
        else:
            print(f"    ✅ 正确：结果描述与问题数量一致")
    
    # 4. 验证完整格式的一致性
    summary = complete_result.get('summary', {})
    if summary.get('totalProblems', 0) != enhanced_result.statistics.total_count:
        print(f"    ❌ 错误：summary中的问题数量不一致")
        return False
    else:
        print(f"    ✅ 正确：summary中的问题数量一致")
    
    if summary.get('overallResult') != enhanced_result.overall_result:
        print(f"    ❌ 错误：summary中的总体结果不一致")
        return False
    else:
        print(f"    ✅ 正确：summary中的总体结果一致")
    
    print(f"  ✅ 一致性验证通过")
    return True


def test_result_consistency_without_problems():
    """测试无问题时结果的一致性"""
    print("🧪 测试无问题时结果的一致性...")
    
    # 创建无问题的原始结果
    original_result_no_problems = {
        'checkBranch': 'feature/test-no-problems',
        'sumCheckResult': '通过',
        'resultDesc': '无问题发现',
        'totalProblem': '0',
        'problemList': []
    }
    
    # 创建CR结果增强器
    enhancer = CRResultEnhancer()
    
    # 增强结果
    enhanced_result = enhancer.enhance_cr_result(original_result_no_problems)
    
    # 转换为完整格式
    complete_result = enhancer.to_complete_format(enhanced_result)
    
    print(f"  📊 增强结果分析:")
    print(f"    增强后overallResult: {enhanced_result.overall_result}")
    print(f"    问题总数: {enhanced_result.statistics.total_count}")
    print(f"    总体评分: {enhanced_result.overall_score}")
    print(f"    结果描述: {enhanced_result.result_description}")
    
    # 验证一致性
    print(f"\n  🔍 一致性验证:")
    
    # 1. 验证无问题时应该显示"通过"
    if enhanced_result.statistics.total_count == 0:
        if enhanced_result.overall_result != "通过":
            print(f"    ❌ 错误：无问题但显示{enhanced_result.overall_result}")
            return False
        else:
            print(f"    ✅ 正确：无问题，显示通过")
    
    # 2. 验证无问题时评分应该是100
    if enhanced_result.statistics.total_count == 0:
        if enhanced_result.overall_score != 100:
            print(f"    ❌ 错误：无问题但评分为{enhanced_result.overall_score}")
            return False
        else:
            print(f"    ✅ 正确：无问题，评分为100")
    
    # 3. 验证结果描述应该包含"未发现问题"
    if enhanced_result.statistics.total_count == 0:
        if "未发现问题" not in enhanced_result.result_description:
            print(f"    ❌ 错误：无问题但描述不正确：{enhanced_result.result_description}")
            return False
        else:
            print(f"    ✅ 正确：结果描述正确")
    
    print(f"  ✅ 一致性验证通过")
    return True


def test_edge_cases():
    """测试边界情况"""
    print("🧪 测试边界情况...")
    
    # 测试只有轻微问题的情况
    original_result_minor_only = {
        'checkBranch': 'feature/test-minor',
        'sumCheckResult': '通过',  # 原始可能显示通过
        'resultDesc': 'P3:2个',
        'totalProblem': '2',
        'problemList': [
            {
                'scene': 'commonCheck',
                'num': '2',
                'checkResult': '通过',
                'detail': [
                    {
                        'level': 'P3',
                        'problem': '代码注释不足',
                        'suggestion': '添加注释',
                        'targetCode': 'function test() {}',
                        'codePosition': [1, 1, 1, 20]
                    },
                    {
                        'level': 'P3',
                        'problem': '变量名可以更清晰',
                        'suggestion': '使用更描述性的变量名',
                        'targetCode': 'let x = 1;',
                        'codePosition': [2, 1, 2, 10]
                    }
                ]
            }
        ]
    }
    
    enhancer = CRResultEnhancer()
    enhanced_result = enhancer.enhance_cr_result(original_result_minor_only)
    
    print(f"  📊 轻微问题测试:")
    print(f"    问题总数: {enhanced_result.statistics.total_count}")
    print(f"    轻微问题数: {enhanced_result.statistics.minor_count}")
    print(f"    总体结果: {enhanced_result.overall_result}")
    print(f"    总体评分: {enhanced_result.overall_score}")
    
    # 验证轻微问题的处理
    if enhanced_result.statistics.total_count > 0:
        # 根据当前逻辑，有问题就应该不通过
        if enhanced_result.overall_result == "通过":
            print(f"    ❌ 错误：有轻微问题但显示通过")
            return False
        else:
            print(f"    ✅ 正确：有轻微问题，显示不通过")
    
    print(f"  ✅ 边界情况测试通过")
    return True


def main():
    """运行所有测试"""
    print("🚀 开始测试CR结果一致性")
    print("=" * 60)
    
    success_count = 0
    total_tests = 3
    
    try:
        # 测试1: 有问题时的一致性
        if test_result_consistency_with_problems():
            success_count += 1
        print()
        
        # 测试2: 无问题时的一致性
        if test_result_consistency_without_problems():
            success_count += 1
        print()
        
        # 测试3: 边界情况
        if test_edge_cases():
            success_count += 1
        print()
        
        print("=" * 60)
        print(f"🎉 测试完成: {success_count}/{total_tests} 个测试通过")
        
        if success_count == total_tests:
            print("✅ 所有测试通过！CR结果一致性修复成功")
            print()
            print("🎯 修复效果:")
            print("  • 有问题时正确显示'不通过'")
            print("  • 严重问题必定导致不通过")
            print("  • 结果描述与实际问题数量一致")
            print("  • summary数据与统计数据一致")
            print("  • 无问题时正确显示'通过'和满分")
            return True
        else:
            print("⚠️  部分测试失败，需要进一步检查")
            return False
        
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
