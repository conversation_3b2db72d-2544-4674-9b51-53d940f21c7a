[{"type": "file", "name": "api/app_server.py", "file": "api/app_server.py", "content": "initRootLogger(\"app_server\")", "start_line": 1, "end_line": 1, "from_diff": true}, {"type": "file", "name": "api/apps/devmind_app.py", "file": "api/apps/devmind_app.py", "content": "import os\n\nimport requests as requests\nfrom flask import request\n\nfrom flask import Response, stream_with_context, jsonify\n\nfrom api.service.devmind_service import DevmindService\n\n\***************('/devmind/chat/<chat_id>', methods=['POST'])\ndef chat_with_devmind(chat_id):\n    data = request.get_json()\n    question = data.get(\"question\")\n    stream = data.get(\"stream\", True)\n    chat_id = chat_id or os.environ[\"DM_DEFAULT_CHAT_ID\"]\n    if not question:\n        return jsonify({\"code\": 102, \"message\": \"Please input your question.\"}), 400\n\n    try:\n        if stream:\n            # 流式返回\n            devmind_response = DevmindService().converse_with_assistant(\n                chat_id=chat_id,\n                question=question,\n                stream=True\n            )\n\n            def generate():\n                for line in devmind_response.iter_lines(decode_unicode=True):\n                    if line:\n                        yield line + '\\n'\n\n            return Response(stream_with_context(generate()), content_type='text/event-stream')\n        else:\n            # 普通返回\n            result = DevmindService().converse_with_assistant(\n                chat_id=chat_id,\n                question=question,\n                stream=False\n            )\n            return jsonify(result)\n    except requests.RequestException as e:\n        return jsonify({\"code\": 500, \"message\": str(e)}), 500", "start_line": 1, "end_line": 44, "from_diff": true}, {"type": "function", "name": "chat_with_devmind", "file": "api/apps/devmind_app.py", "content": "def chat_with_devmind(chat_id):\n    data = request.get_json()\n    question = data.get(\"question\")\n    stream = data.get(\"stream\", True)\n    chat_id = chat_id or os.environ[\"DM_DEFAULT_CHAT_ID\"]\n    if not question:\n        return jsonify({\"code\": 102, \"message\": \"Please input your question.\"}), 400\n\n    try:\n        if stream:\n            # 流式返回\n            devmind_response = DevmindService().converse_with_assistant(\n                chat_id=chat_id,\n                question=question,\n                stream=True\n            )\n\n            def generate():\n                for line in devmind_response.iter_lines(decode_unicode=True):\n                    if line:\n                        yield line + '\\n'\n\n            return Response(stream_with_context(generate()), content_type='text/event-stream')\n        else:\n            # 普通返回\n            result = DevmindService().converse_with_assistant(\n                chat_id=chat_id,\n                question=question,\n                stream=False\n            )\n            return jsonify(result)\n    except requests.RequestException as e:\n        return jsonify({\"code\": 500, \"message\": str(e)}), 500", "start_line": 12, "end_line": 44, "from_diff": true}, {"type": "function", "name": "generate", "file": "api/apps/devmind_app.py", "content": "\n            def generate():\n                for line in devmind_response.iter_lines(decode_unicode=True):\n                    if line:\n                        yield line + '\\n'\n", "start_line": 29, "end_line": 34, "from_diff": true}, {"type": "file", "name": "api/apps/devtools_app.py", "file": "api/apps/devtools_app.py", "content": "from api.service.devmind_service import DevmindService\ndevmind_service = DevmindService()\ncr_lc_service = CrLCService(horn_service, kms_service, dx_service, git_service, devmind_service)", "start_line": 1, "end_line": 3, "from_diff": true}, {"type": "file", "name": "api/apps/main_app.py", "file": "api/apps/main_app.py", "content": "from api.service.devmind_service import DevmindService\ndevmind_service = DevmindService()\ncr_lc_service = CrLCService(horn_service, kms_service, dx_service, git_service, devmind_service)\n            content=json.dumps(tpl_result),\n            operator_emp_id=cr_params.get('empId') or request.headers.get('appfactory-context-user-id'),", "start_line": 1, "end_line": 5, "from_diff": true}, {"type": "file", "name": "api/apps/test_app.py", "file": "api/apps/test_app.py", "content": "from api.service.devmind_service import DevmindService\n# manager = Blueprint('test', __name__)\ndevmind_service = DevmindService()\ncr_lc_service = CrLCService(horn_service, kms_service, dx_service, git_service, devmind_service)", "start_line": 1, "end_line": 4, "from_diff": true}, {"type": "file", "name": "api/service/cr_lc_service.py", "file": "api/service/cr_lc_service.py", "content": "from utils import singleton\nfrom . import devmind_service\nfrom .devmind_service import DevmindService\n@singleton\n    def __init__(self, horn_service, kms_service, daxiang_service, git_service, dm_service):\n        self.devmind_service = dm_service\n        from langchain_core.runnables import RunnableLambda\n\n        chain = (prompt | llm\n                 # | RunnableLambda(self.devmind_service.converse_with_assistant())\n                 )\n```diff\n```", "start_line": 1, "end_line": 13, "from_diff": true}, {"type": "function", "name": "__init__", "file": "api/service/cr_lc_service.py", "content": "    def __init__(self, horn_service, kms_service, daxiang_service, git_service, dm_service):\n        self.devmind_service = dm_service\n        from langchain_core.runnables import RunnableLambda\n\n        chain = (prompt | llm\n                 # | RunnableLambda(self.devmind_service.converse_with_assistant())\n                 )\n", "start_line": 5, "end_line": 12, "from_diff": true}, {"type": "file", "name": "api/service/daxiang_service.py", "file": "api/service/daxiang_service.py", "content": "from api.service.org_service import OrgService\nfrom utils import singleton\n\n@singleton\n        self.org_service = OrgService()\n        self.CLIENT_APP_KEY = os.environ['CLIENT_APP_KEY']\n        if os.environ['INF_BOM_ENV'] == 'test':\n            self.DX_APP_ID = os.environ['DX_APP_ID_DEV']\n        else:\n            self.DX_APP_ID = os.environ['DX_APP_ID_PROD']\n        if not self.DX_APP_ID:\n        self.DX_APP_KEY = os.environ[\"DX_APP_KEY\"]\n        print(ROOT_DIR + \"/infra/thrift/daxiang/AuthEntity.thrift\")\n        open_auth_service = load(ROOT_DIR + \"/infra/thrift/daxiang/AuthService.thrift\")\n            appkey=self.CLIENT_APP_KEY,\n        open_invoke_service = load(ROOT_DIR + \"/infra/thrift/daxiang/InvokeApi.thrift\")\n            appkey=self.CLIENT_APP_KEY,\n        open_message_service = load(ROOT_DIR + \"/infra/thrift/daxiang/OpenMessageService.thrift\")\n            appkey=self.CLIENT_APP_KEY,\n\n        kms_service_res = self.kms_service.get_key(self.CLIENT_APP_KEY, \"dxopen_sk\")\n            auth_entity = load(ROOT_DIR + \"/infra/thrift/daxiang/AuthEntity.thrift\")\n            print(\"app_id:\", self.DX_APP_ID, \"secret:\", app_secret)\n                appAuthInfo=auth_entity.AppAuthInfo(appkey=self.DX_APP_ID, appSecret=app_secret))\n            invoke_entity = load(ROOT_DIR + \"/infra/thrift/daxiang/AuthEntity.thrift\")\n            invoke_entity = load(ROOT_DIR + \"/infra/thrift/daxiang/AuthEntity.thrift\")\n            else:\n                emp_info = self.org_service.get_emp_info(\n                    getattr(ctx, 'headers', {}),\n                    getattr(ctx, 'args', {})\n                )\n                receiver_ids = [int(emp_info.get('empId'))] if emp_info.get('empId') else None\n            messages_entity = load(ROOT_DIR + \"/infra/thrift/daxiang/MessageEntity.thrift\")", "start_line": 1, "end_line": 33, "from_diff": true}, {"type": "file", "name": "api/service/devmind_service.py", "file": "api/service/devmind_service.py", "content": "import os\n\nimport dotenv\ndotenv.load_dotenv()\n\n    def __init__(self, base_url: Optional[str] = None, api_key: Optional[str] = None):\n        self.base_url = base_url.rstrip('/') if base_url else os.environ[\"DM_BASE_URL\"]\n        self.api_key = api_key or os.environ[\"DM_API_KEY\"]\n            stream: bool = True", "start_line": 1, "end_line": 9, "from_diff": true}, {"type": "function", "name": "__init__", "file": "api/service/devmind_service.py", "content": "\n    def __init__(self, base_url: Optional[str] = None, api_key: Optional[str] = None):\n        self.base_url = base_url.rstrip('/') if base_url else os.environ[\"DM_BASE_URL\"]\n        self.api_key = api_key or os.environ[\"DM_API_KEY\"]\n            stream: bool = True", "start_line": 6, "end_line": 10, "from_diff": true}, {"type": "file", "name": "api/service/horn_service.py", "file": "api/service/horn_service.py", "content": "## Python 代码审查规则（commonCheck）\n### P0（严重问题，必须修复）\n### P1（重要问题，建议修复）\n### P2（一般问题，建议优化）\n## 代码审查要求\n## 输出格式要求", "start_line": 1, "end_line": 6, "from_diff": true}, {"type": "file", "name": "api/service/km_service.py", "file": "api/service/km_service.py", "content": "                params.operatorEmpId = int(emp_info['empId']) if emp_info['empId'] else None\n            print(request_context.json.get(\"spaceId\"))\n            params.spaceId = request_context.json.get(\"spaceId\")\n                raise ValueError(f\"[yunzhuan:km:createDoc] {status}\\n\")", "start_line": 1, "end_line": 4, "from_diff": true}, {"type": "file", "name": "api/service/org_service.py", "file": "api/service/org_service.py", "content": "from pathlib import Path\n\nfrom utils import singleton\n\nROOT_DIR = Path(__file__).resolve().parent.parent.parent.__str__()\n\n@singleton\n\n\n            with open(ROOT_DIR + '/conf/org_conf.yaml', 'r') as f:\n\n\n\n\n\n            mis_id = request_headers.get('mis') or query_params.get('mis')\n\n            print(\"emp_info:\", emp_info)\n\n\n\n\n\n", "start_line": 1, "end_line": 23, "from_diff": true}, {"type": "file", "name": "common/km.py", "file": "common/km.py", "content": "            operator_emp_id: Optional[str] = None,\n        self.operatorEmpId = int(operator_emp_id) if operator_emp_id else None", "start_line": 1, "end_line": 2, "from_diff": true}, {"type": "file", "name": "tests/km_obj_test.py", "file": "tests/km_obj_test.py", "content": "from services.km_service import CreateDocParams\n\ndata = CreateDocParams(\n    title=\"test\",\n    content=\"test\",\n    space_id=\"19748\",\n    parent_id=\"19748\"\n).to_dict()\n\nprint(**data)", "start_line": 1, "end_line": 10, "from_diff": true}, {"type": "file", "name": "utils/__init__.py", "file": "utils/__init__.py", "content": "def singleton(cls):\n    instance = None\n    def _singleton(*args, **kwargs):\n        nonlocal instance\n        if instance is None:\n            instance = cls(*args, **kwargs)\n        return instance\n           response.headers.get('Content-Type', 'image/jpg') + \";\" + \\\n           \"base64,\" + base64.b64encode(response.content).decode(\"utf-8\")", "start_line": 1, "end_line": 9, "from_diff": true}, {"type": "function", "name": "singleton", "file": "utils/__init__.py", "content": "def singleton(cls):\n    instance = None\n    def _singleton(*args, **kwargs):\n        nonlocal instance\n        if instance is None:\n            instance = cls(*args, **kwargs)\n        return instance\n           response.headers.get('Content-Type', 'image/jpg') + \";\" + \\\n           \"base64,\" + base64.b64encode(response.content).decode(\"utf-8\")", "start_line": 1, "end_line": 9, "from_diff": true}, {"type": "function", "name": "_singleton", "file": "utils/__init__.py", "content": "    def _singleton(*args, **kwargs):\n        nonlocal instance\n        if instance is None:\n            instance = cls(*args, **kwargs)\n        return instance\n           response.headers.get('Content-Type', 'image/jpg') + \";\" + \\\n           \"base64,\" + base64.b64encode(response.content).decode(\"utf-8\")", "start_line": 3, "end_line": 9, "from_diff": true}]