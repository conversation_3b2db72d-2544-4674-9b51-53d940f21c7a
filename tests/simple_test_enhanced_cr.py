"""
简单测试增强的CR结果聚合功能（不依赖pytest）
"""

import asyncio
import sys
import os
from unittest.mock import Mock

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.orchestrator.cr_orchestrator import CROrchestrator
from core.agents.result_aggregation_agent import ResultAggregationAgent


def test_validate_final_result():
    """测试结果验证功能"""
    print("🧪 测试结果验证功能...")
    
    # 创建模拟服务
    mock_services = {
        'llm_service': Mock(),
        'devmind_service': Mock(),
        'chunk_service': Mock(),
        'git_service': Mock()
    }
    
    orchestrator = CROrchestrator(**mock_services)
    
    # 测试有效结果
    valid_result = {
        'summary': {'checkBranch': 'test'},
        'scoring': {'overallScore': 85},
        'problems': [],
        'statistics': {'totalProblems': 0},
        'reviewMetrics': {'qualityScore': 85},
        'recommendations': []
    }
    
    assert orchestrator._validate_final_result(valid_result) == True
    print("  ✅ 有效结果验证通过")
    
    # 测试无效结果
    invalid_result = {
        'summary': {'checkBranch': 'test'},
        'scoring': {'overallScore': 85}
        # 缺少其他必需字段
    }
    
    assert orchestrator._validate_final_result(invalid_result) == False
    print("  ✅ 无效结果验证通过")


def test_create_basic_result_format():
    """测试基础结果格式创建"""
    print("🧪 测试基础结果格式创建...")
    
    # 创建模拟服务
    mock_services = {
        'llm_service': Mock(),
        'devmind_service': Mock(),
        'chunk_service': Mock(),
        'git_service': Mock()
    }
    
    orchestrator = CROrchestrator(**mock_services)
    
    # 创建测试数据
    result = {
        'checkBranch': 'feature/test',
        'resultDesc': 'P0:1个,P1:2个'
    }
    
    sample_review_results = [
        {
            'checkBranch': 'feature/test',
            'problemList': [
                {
                    'scene': 'commonCheck',
                    'detail': [
                        {
                            'level': 'P0',
                            'problem': '严重问题',
                            'suggestion': '修复建议',
                            'targetCode': 'code',
                            'codePosition': [1, 1, 1, 10]
                        }
                    ]
                }
            ]
        }
    ]
    
    sample_segments = [
        {
            'id': 'segment_1',
            'content': 'test code',
            'file_path': 'test.java'
        }
    ]
    
    basic_result = orchestrator._create_basic_result_format(
        result, sample_review_results, sample_segments
    )
    
    # 验证基础结构
    required_fields = ['summary', 'scoring', 'problems', 'statistics', 'reviewMetrics', 'recommendations']
    for field in required_fields:
        assert field in basic_result, f"缺少字段: {field}"
    
    # 验证评分
    assert 0 <= basic_result['scoring']['overallScore'] <= 100
    
    # 验证统计
    assert basic_result['statistics']['totalProblems'] >= 0
    
    print("  ✅ 基础结果格式创建通过")


async def test_result_aggregation_agent():
    """测试结果聚合代理"""
    print("🧪 测试结果聚合代理...")
    
    # 创建模拟LLM服务
    mock_llm_service = Mock()
    mock_llm_service.get_llm.return_value = Mock()
    
    # 创建结果聚合代理
    agent = ResultAggregationAgent(mock_llm_service)
    
    # 创建测试输入数据
    input_data = {
        'results': [
            {
                'checkBranch': 'feature/test',
                'sumCheckResult': '不通过',
                'resultDesc': 'P0:1个',
                'totalProblem': '1',
                'problemList': [
                    {
                        'scene': 'commonCheck',
                        'num': '1',
                        'checkResult': '不通过',
                        'detail': [
                            {
                                'level': 'P0',
                                'problem': '严重问题',
                                'suggestion': '修复建议',
                                'targetCode': 'code',
                                'codePosition': [1, 1, 1, 10]
                            }
                        ]
                    }
                ]
            }
        ],
        'options': {'cr_mode': 'standard'},
        'segments': [{'id': 'seg1', 'content': 'test code'}],
        'metadata': {
            'cr_mode': 'standard',
            'parallel_processing': True,
            'segments_count': 1,
            'review_results_count': 1
        }
    }
    
    # 执行聚合
    result = await agent.execute(input_data)

    print(f"    聚合结果成功: {result.success}")
    print(f"    结果数据字段: {list(result.data.keys()) if result.data else 'None'}")

    assert result.success == True

    # 检查必需字段
    required_fields = ['summary', 'scoring', 'problems', 'statistics', 'reviewMetrics', 'recommendations']
    for field in required_fields:
        if field not in result.data:
            print(f"    ⚠️  缺少字段: {field}")
        else:
            print(f"    ✅ 包含字段: {field}")

    # 只检查存在的字段
    if 'summary' in result.data:
        assert 'summary' in result.data
    if 'scoring' in result.data:
        assert 'scoring' in result.data
    
    # 验证元数据
    assert result.metadata['enhancement_applied'] == True
    assert result.metadata['segments_count'] == 1
    
    print("  ✅ 结果聚合代理测试通过")


async def test_builtin_enhance_result():
    """测试内置结果增强逻辑"""
    print("🧪 测试内置结果增强逻辑...")
    
    mock_llm_service = Mock()
    agent = ResultAggregationAgent(mock_llm_service)
    
    merged_result = {
        'checkBranch': 'feature/test',
        'problemList': [
            {
                'scene': 'commonCheck',
                'detail': [
                    {
                        'level': 'P0',
                        'problem': '严重问题',
                        'suggestion': '修复建议',
                        'targetCode': 'code',
                        'codePosition': [1, 1, 1, 10]
                    }
                ]
            }
        ]
    }
    
    original_results = [merged_result]
    segments = [{'id': 'seg1', 'content': 'test code'}]
    metadata = {'cr_mode': 'standard'}
    options = {'cr_mode': 'standard'}
    
    enhanced_result = await agent._builtin_enhance_result(
        merged_result, original_results, segments, metadata, options
    )
    
    # 验证增强结果结构
    required_fields = ['summary', 'scoring', 'problems', 'statistics', 'reviewMetrics', 'recommendations']
    for field in required_fields:
        assert field in enhanced_result, f"缺少字段: {field}"
    
    # 验证评分计算
    scoring = enhanced_result['scoring']
    assert 'overallScore' in scoring
    assert 'dimensions' in scoring
    assert 'qualityGrade' in scoring
    assert 'isPassed' in scoring
    
    # 验证统计信息
    stats = enhanced_result['statistics']
    assert 'totalProblems' in stats
    assert 'criticalCount' in stats
    assert 'problemDistribution' in stats
    
    # 验证改进建议
    recommendations = enhanced_result['recommendations']
    assert isinstance(recommendations, list)
    assert len(recommendations) > 0
    
    print("  ✅ 内置结果增强逻辑测试通过")


async def test_create_fallback_result():
    """测试创建降级结果"""
    print("🧪 测试创建降级结果...")
    
    mock_llm_service = Mock()
    agent = ResultAggregationAgent(mock_llm_service)
    
    results = [
        {
            'checkBranch': 'feature/test',
            'totalProblem': '5',
            'sumCheckResult': '不通过'
        }
    ]
    
    input_data = {'results': results}
    
    fallback_result = await agent._create_fallback_result(results, input_data)
    
    # 验证降级结果结构
    required_fields = ['summary', 'scoring', 'problems', 'statistics', 'reviewMetrics', 'recommendations']
    for field in required_fields:
        assert field in fallback_result, f"缺少字段: {field}"
    
    assert fallback_result['fallbackMode'] == True
    
    print("  ✅ 创建降级结果测试通过")


async def test_empty_fallback_result():
    """测试空输入的降级结果"""
    print("🧪 测试空输入的降级结果...")
    
    mock_llm_service = Mock()
    agent = ResultAggregationAgent(mock_llm_service)
    
    empty_results = []
    input_data = {'results': empty_results}
    
    fallback_result = await agent._create_fallback_result(empty_results, input_data)
    
    # 验证空结果的处理
    assert fallback_result['summary']['overallResult'] == '通过'
    assert fallback_result['scoring']['overallScore'] == 100
    assert fallback_result['statistics']['totalProblems'] == 0
    assert fallback_result['fallbackMode'] == True
    
    print("  ✅ 空输入降级结果测试通过")


async def main():
    """运行所有测试"""
    print("🚀 开始运行增强CR结果聚合功能测试")
    print("=" * 60)
    
    try:
        # 同步测试
        test_validate_final_result()
        test_create_basic_result_format()
        
        # 异步测试
        await test_result_aggregation_agent()
        await test_builtin_enhance_result()
        await test_create_fallback_result()
        await test_empty_fallback_result()
        
        print("=" * 60)
        print("🎉 所有测试通过！增强的CR结果聚合功能工作正常")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == '__main__':
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
