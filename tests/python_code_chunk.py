# 仓库信息
from api.service.chunk_service import ChunkService
from api.service.git_service import GitService

project = "~wangqichen02"  # 项目名
repo = "shangou_ai_cr"  # 仓库名
branch = "dev/20250516-cr-rag"  # 分支名

# 初始化服务
git_service = GitService()
chunker = ChunkService(git_service, project, repo, branch)

# 指定只处理 .py 文件
suffixes = ['.py']

# 获取所有核心代码文件（会自动clone并切换分支）
py_files = chunker.get_core_code_files(suffixes)
print("Python文件列表：", py_files)

# 对第一个 Python 文件进行切块
chunks = chunker.chunk_all_files(suffixes)
chunker.save_chunks_to_json(chunks, 'python_chunks.json')