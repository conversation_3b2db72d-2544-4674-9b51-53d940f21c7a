"""
测试智能合并修复
验证LLM智能合并不再生成错误的checkBranch格式
"""

import asyncio
import sys
import os
from unittest.mock import Mock, patch

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.agents.result_aggregation_agent import ResultAggregationAgent


def create_sample_results_with_bad_checkbranch():
    """创建包含错误checkBranch的示例结果"""
    return [
        {
            'checkBranch': '.env:1-10',  # 错误格式
            'sumCheckResult': '通过',
            'resultDesc': 'P1:1个',
            'totalProblem': '1',
            'problemList': [
                {
                    'scene': 'commonCheck',
                    'num': '1',
                    'checkResult': '通过',
                    'detail': [
                        {
                            'level': 'P1',
                            'problem': '变量命名建议',
                            'suggestion': '使用更描述性的变量名',
                            'targetCode': 'let x = 1;',
                            'codePosition': [1, 1, 1, 10]
                        }
                    ]
                }
            ]
        },
        {
            'checkBranch': 'config/settings.py:15-25',  # 错误格式
            'sumCheckResult': '通过',
            'resultDesc': 'P2:1个',
            'totalProblem': '1',
            'problemList': [
                {
                    'scene': 'customCheck',
                    'num': '1',
                    'checkResult': '通过',
                    'detail': [
                        {
                            'level': 'P2',
                            'problem': '代码注释不足',
                            'suggestion': '添加注释',
                            'targetCode': 'function test() {}',
                            'codePosition': [15, 1, 15, 20]
                        }
                    ]
                }
            ]
        }
    ]


async def test_intelligent_merge_checkbranch_fix():
    """测试智能合并的checkBranch修复"""
    print("🧪 测试智能合并的checkBranch修复...")
    
    # 创建模拟LLM服务
    mock_llm_service = Mock()
    
    # 模拟LLM响应（正确的合并结果）
    mock_llm_response = Mock()
    mock_llm_response.content = '''
    {
        "checkBranch": "未知分支",
        "sumCheckResult": "通过",
        "resultDesc": "P1:1个,P2:1个",
        "totalProblem": "2",
        "problemList": [
            {
                "scene": "commonCheck",
                "num": "1",
                "checkResult": "通过"
            },
            {
                "scene": "customCheck", 
                "num": "1",
                "checkResult": "通过"
            }
        ]
    }
    '''
    
    # 模拟LLM链
    mock_chain = Mock()
    mock_chain.invoke.return_value = {
        'checkBranch': '未知分支',
        'sumCheckResult': '通过',
        'resultDesc': 'P1:1个,P2:1个',
        'totalProblem': '2',
        'problemList': [
            {'scene': 'commonCheck', 'num': '1', 'checkResult': '通过'},
            {'scene': 'customCheck', 'num': '1', 'checkResult': '通过'}
        ]
    }
    
    # 模拟LLM实例
    mock_llm = Mock()
    mock_llm_service.get_llm.return_value = mock_llm
    
    # 创建聚合代理
    agent = ResultAggregationAgent(mock_llm_service)
    
    # 创建包含错误checkBranch的测试数据
    test_results = create_sample_results_with_bad_checkbranch()
    
    # 模拟LangChain组件
    with patch('core.agents.result_aggregation_agent.ChatPromptTemplate') as mock_prompt, \
         patch('core.agents.result_aggregation_agent.JsonOutputParser') as mock_parser, \
         patch('core.agents.result_aggregation_agent.SystemMessage') as mock_sys_msg, \
         patch('core.agents.result_aggregation_agent.HumanMessage') as mock_human_msg:
        
        # 配置模拟对象
        mock_prompt.from_messages.return_value = mock_chain
        mock_parser.return_value = Mock()
        mock_sys_msg.return_value = Mock()
        mock_human_msg.return_value = Mock()
        
        # 执行智能合并
        merged_result = await agent._intelligent_merge(test_results)
        
        print(f"  📊 合并结果分析:")
        print(f"    原始checkBranch: {[r.get('checkBranch') for r in test_results]}")
        print(f"    合并后checkBranch: {merged_result.get('checkBranch')}")
        print(f"    合并后totalProblem: {merged_result.get('totalProblem')}")
        print(f"    合并后sumCheckResult: {merged_result.get('sumCheckResult')}")
        
        # 验证checkBranch不再是文件路径格式
        check_branch = merged_result.get('checkBranch', '')
        if ':' in check_branch and any(char.isdigit() for char in check_branch.split(':')[-1]):
            print(f"    ❌ 错误：合并后仍有文件路径格式的checkBranch: {check_branch}")
            return False
        else:
            print(f"    ✅ 正确：checkBranch格式正确: {check_branch}")
        
        # 验证问题合并正确
        if merged_result.get('totalProblem') == '2':
            print(f"    ✅ 正确：问题数量合并正确")
        else:
            print(f"    ❌ 错误：问题数量合并不正确")
            return False
        
        # 验证problemList包含detail信息
        problem_list = merged_result.get('problemList', [])
        has_details = any('detail' in problem for problem in problem_list)
        if has_details:
            print(f"    ✅ 正确：problemList包含detail信息")
        else:
            print(f"    ❌ 错误：problemList缺少detail信息")
            return False
        
        print(f"  ✅ 智能合并checkBranch修复测试通过")
        return True


async def test_simple_merge_checkbranch_fix():
    """测试简单合并的checkBranch修复"""
    print("🧪 测试简单合并的checkBranch修复...")
    
    # 创建模拟LLM服务
    mock_llm_service = Mock()
    agent = ResultAggregationAgent(mock_llm_service)
    
    # 创建包含错误checkBranch的测试数据
    test_results = create_sample_results_with_bad_checkbranch()
    
    # 执行简单合并
    merged_result = await agent._simple_merge(test_results)
    
    print(f"  📊 简单合并结果分析:")
    print(f"    原始checkBranch: {test_results[0].get('checkBranch')}")
    print(f"    合并后checkBranch: {merged_result.get('checkBranch')}")
    print(f"    合并后totalProblem: {merged_result.get('totalProblem')}")
    
    # 验证checkBranch被修正
    check_branch = merged_result.get('checkBranch', '')
    if check_branch == '未知分支':
        print(f"    ✅ 正确：错误的checkBranch已被修正为'未知分支'")
    else:
        print(f"    ❌ 错误：checkBranch未被正确修正: {check_branch}")
        return False
    
    # 验证问题合并
    if merged_result.get('totalProblem') == '2':
        print(f"    ✅ 正确：问题数量合并正确")
    else:
        print(f"    ❌ 错误：问题数量合并不正确")
        return False
    
    print(f"  ✅ 简单合并checkBranch修复测试通过")
    return True


async def test_end_to_end_aggregation():
    """测试端到端的聚合流程"""
    print("🧪 测试端到端的聚合流程...")
    
    # 创建模拟LLM服务
    mock_llm_service = Mock()
    agent = ResultAggregationAgent(mock_llm_service)
    
    # 创建测试输入数据
    input_data = {
        'results': create_sample_results_with_bad_checkbranch(),
        'options': {
            'project': 'test-project',
            'repo': 'test-repo',
            'fromBranch': 'feature/fix-checkbranch',
            'toBranch': 'main'
        },
        'segments': [
            {'id': 'seg1', 'content': 'test code 1'},
            {'id': 'seg2', 'content': 'test code 2'}
        ],
        'metadata': {
            'cr_mode': 'standard',
            'parallel_processing': True
        }
    }
    
    # 执行聚合
    result = await agent.execute(input_data)
    
    print(f"  📊 端到端聚合结果分析:")
    print(f"    聚合成功: {result.success}")
    
    if result.success:
        data = result.data
        summary = data.get('summary', {})
        check_branch = summary.get('checkBranch', '')
        
        print(f"    最终checkBranch: {check_branch}")
        print(f"    总问题数: {summary.get('totalProblems', 0)}")
        print(f"    总体结果: {summary.get('overallResult', 'N/A')}")
        
        # 验证最终的checkBranch是正确的
        expected_branch = "test-project/test-repo:feature/fix-checkbranch"
        if check_branch == expected_branch:
            print(f"    ✅ 正确：最终checkBranch格式正确")
        elif check_branch == 'feature/fix-checkbranch':
            print(f"    ✅ 正确：最终checkBranch使用了fromBranch")
        elif check_branch == '未知分支':
            print(f"    ⚠️  警告：最终checkBranch为未知分支（可能是降级处理）")
        else:
            print(f"    ❌ 错误：最终checkBranch格式不正确: {check_branch}")
            return False
        
        # 验证不再有文件路径格式
        if ':' in check_branch and any(char.isdigit() for char in check_branch.split(':')[-1]):
            print(f"    ❌ 错误：最终结果仍有文件路径格式的checkBranch")
            return False
        
        print(f"  ✅ 端到端聚合流程测试通过")
        return True
    else:
        print(f"    ❌ 错误：聚合失败: {result.error}")
        return False


async def main():
    """运行所有测试"""
    print("🚀 开始测试智能合并checkBranch修复功能")
    print("=" * 60)
    
    success_count = 0
    total_tests = 3
    
    try:
        # 测试1: 智能合并checkBranch修复
        if await test_intelligent_merge_checkbranch_fix():
            success_count += 1
        print()
        
        # 测试2: 简单合并checkBranch修复
        if await test_simple_merge_checkbranch_fix():
            success_count += 1
        print()
        
        # 测试3: 端到端聚合流程
        if await test_end_to_end_aggregation():
            success_count += 1
        print()
        
        print("=" * 60)
        print(f"🎉 测试完成: {success_count}/{total_tests} 个测试通过")
        
        if success_count == total_tests:
            print("✅ 所有测试通过！智能合并checkBranch修复功能正常工作")
            print()
            print("🎯 修复效果:")
            print("  • LLM智能合并不再生成文件路径格式的checkBranch")
            print("  • 简单合并自动检测和修正错误格式")
            print("  • 合并前预处理移除错误的checkBranch字段")
            print("  • 合并后验证和修正checkBranch格式")
            print("  • 端到端流程确保最终结果正确")
            return True
        else:
            print("⚠️  部分测试失败，需要进一步检查")
            return False
        
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == '__main__':
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
