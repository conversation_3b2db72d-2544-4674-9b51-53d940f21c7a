"""
测试增强的CR编排器结果聚合功能
"""

import asyncio
import json
import pytest
from unittest.mock import Mock, AsyncMock, patch
from typing import Dict, Any, List

# 导入要测试的模块
from core.orchestrator.cr_orchestrator import CROrchestrator
from core.agents.result_aggregation_agent import ResultAggregationAgent
from core.agents.base_agent import AgentResult


class TestEnhancedCROrchestrator:
    """测试增强的CR编排器"""
    
    @pytest.fixture
    def mock_services(self):
        """创建模拟服务"""
        llm_service = Mock()
        devmind_service = Mock()
        chunk_service = Mock()
        git_service = Mock()
        
        return {
            'llm_service': llm_service,
            'devmind_service': devmind_service,
            'chunk_service': chunk_service,
            'git_service': git_service
        }
    
    @pytest.fixture
    def sample_review_results(self):
        """创建示例审查结果"""
        return [
            {
                'checkBranch': 'feature/test',
                'sumCheckResult': '不通过',
                'resultDesc': 'P0:1个,P1:2个,P2:1个',
                'totalProblem': '4',
                'problemList': [
                    {
                        'scene': 'commonCheck',
                        'num': '4',
                        'checkResult': '不通过',
                        'detail': [
                            {
                                'level': 'P0',
                                'problem': '空指针异常风险',
                                'suggestion': '添加空值检查',
                                'targetCode': 'user.getName()',
                                'codePosition': [10, 1, 10, 15]
                            },
                            {
                                'level': 'P1',
                                'problem': '变量命名不规范',
                                'suggestion': '使用驼峰命名',
                                'targetCode': 'user_name',
                                'codePosition': [5, 1, 5, 9]
                            }
                        ]
                    }
                ]
            }
        ]
    
    @pytest.fixture
    def sample_segments(self):
        """创建示例代码片段"""
        return [
            {
                'id': 'segment_1',
                'content': 'public void test() { user.getName(); }',
                'file_path': 'src/main/java/Test.java',
                'start_line': 1,
                'end_line': 5
            }
        ]
    
    def test_validate_final_result_success(self, mock_services):
        """测试结果验证成功的情况"""
        orchestrator = CROrchestrator(**mock_services)
        
        valid_result = {
            'summary': {'checkBranch': 'test'},
            'scoring': {'overallScore': 85},
            'problems': [],
            'statistics': {'totalProblems': 0},
            'reviewMetrics': {'qualityScore': 85},
            'recommendations': []
        }
        
        assert orchestrator._validate_final_result(valid_result) == True
    
    def test_validate_final_result_failure(self, mock_services):
        """测试结果验证失败的情况"""
        orchestrator = CROrchestrator(**mock_services)
        
        # 缺少必需字段
        invalid_result = {
            'summary': {'checkBranch': 'test'},
            'scoring': {'overallScore': 85}
            # 缺少其他必需字段
        }
        
        assert orchestrator._validate_final_result(invalid_result) == False
    
    def test_create_basic_result_format(self, mock_services, sample_review_results, sample_segments):
        """测试基础结果格式创建"""
        orchestrator = CROrchestrator(**mock_services)
        
        result = {
            'checkBranch': 'feature/test',
            'resultDesc': 'P0:1个,P1:2个'
        }
        
        basic_result = orchestrator._create_basic_result_format(
            result, sample_review_results, sample_segments
        )
        
        # 验证基础结构
        assert 'summary' in basic_result
        assert 'scoring' in basic_result
        assert 'problems' in basic_result
        assert 'statistics' in basic_result
        assert 'reviewMetrics' in basic_result
        assert 'recommendations' in basic_result
        
        # 验证评分计算
        assert basic_result['scoring']['overallScore'] <= 100
        assert basic_result['scoring']['overallScore'] >= 0
        
        # 验证问题统计
        assert basic_result['statistics']['totalProblems'] >= 0
        assert len(basic_result['problems']) >= 0


class TestEnhancedResultAggregationAgent:
    """测试增强的结果聚合代理"""
    
    @pytest.fixture
    def mock_llm_service(self):
        """创建模拟LLM服务"""
        llm_service = Mock()
        llm_service.get_llm.return_value = Mock()
        return llm_service
    
    @pytest.fixture
    def aggregation_agent(self, mock_llm_service):
        """创建结果聚合代理实例"""
        return ResultAggregationAgent(mock_llm_service)
    
    @pytest.fixture
    def sample_input_data(self):
        """创建示例输入数据"""
        return {
            'results': [
                {
                    'checkBranch': 'feature/test',
                    'sumCheckResult': '不通过',
                    'resultDesc': 'P0:1个',
                    'totalProblem': '1',
                    'problemList': [
                        {
                            'scene': 'commonCheck',
                            'num': '1',
                            'checkResult': '不通过',
                            'detail': [
                                {
                                    'level': 'P0',
                                    'problem': '严重问题',
                                    'suggestion': '修复建议',
                                    'targetCode': 'code',
                                    'codePosition': [1, 1, 1, 10]
                                }
                            ]
                        }
                    ]
                }
            ],
            'options': {'cr_mode': 'standard'},
            'segments': [{'id': 'seg1', 'content': 'test code'}],
            'metadata': {
                'cr_mode': 'standard',
                'parallel_processing': True,
                'segments_count': 1,
                'review_results_count': 1
            }
        }
    
    @pytest.mark.asyncio
    async def test_execute_with_enhancement(self, aggregation_agent, sample_input_data):
        """测试执行结果聚合和增强"""
        # 模拟CR结果增强器不可用，使用内置增强逻辑
        with patch('core.agents.result_aggregation_agent.CRResultEnhancer', side_effect=ImportError):
            result = await aggregation_agent.execute(sample_input_data)
            
            assert result.success == True
            assert 'summary' in result.data
            assert 'scoring' in result.data
            assert 'problems' in result.data
            assert 'statistics' in result.data
            assert 'reviewMetrics' in result.data
            assert 'recommendations' in result.data
            
            # 验证元数据
            assert result.metadata['enhancement_applied'] == True
            assert result.metadata['segments_count'] == 1
    
    @pytest.mark.asyncio
    async def test_builtin_enhance_result(self, aggregation_agent, sample_input_data):
        """测试内置结果增强逻辑"""
        merged_result = sample_input_data['results'][0]
        original_results = sample_input_data['results']
        segments = sample_input_data['segments']
        metadata = sample_input_data['metadata']
        options = sample_input_data['options']
        
        enhanced_result = await aggregation_agent._builtin_enhance_result(
            merged_result, original_results, segments, metadata, options
        )
        
        # 验证增强结果结构
        assert 'summary' in enhanced_result
        assert 'scoring' in enhanced_result
        assert 'problems' in enhanced_result
        assert 'statistics' in enhanced_result
        assert 'reviewMetrics' in enhanced_result
        assert 'recommendations' in enhanced_result
        
        # 验证评分计算
        scoring = enhanced_result['scoring']
        assert 'overallScore' in scoring
        assert 'dimensions' in scoring
        assert 'qualityGrade' in scoring
        assert 'isPassed' in scoring
        
        # 验证统计信息
        stats = enhanced_result['statistics']
        assert 'totalProblems' in stats
        assert 'criticalCount' in stats
        assert 'problemDistribution' in stats
        
        # 验证改进建议
        recommendations = enhanced_result['recommendations']
        assert isinstance(recommendations, list)
        assert len(recommendations) > 0
    
    @pytest.mark.asyncio
    async def test_create_fallback_result(self, aggregation_agent, sample_input_data):
        """测试创建降级结果"""
        results = sample_input_data['results']
        
        fallback_result = await aggregation_agent._create_fallback_result(results, sample_input_data)
        
        # 验证降级结果结构
        assert 'summary' in fallback_result
        assert 'scoring' in fallback_result
        assert 'problems' in fallback_result
        assert 'statistics' in fallback_result
        assert 'reviewMetrics' in fallback_result
        assert 'recommendations' in fallback_result
        assert fallback_result['fallbackMode'] == True
    
    @pytest.mark.asyncio
    async def test_create_fallback_result_empty_input(self, aggregation_agent):
        """测试空输入的降级结果创建"""
        empty_results = []
        input_data = {'results': empty_results}
        
        fallback_result = await aggregation_agent._create_fallback_result(empty_results, input_data)
        
        # 验证空结果的处理
        assert fallback_result['summary']['overallResult'] == '通过'
        assert fallback_result['scoring']['overallScore'] == 100
        assert fallback_result['statistics']['totalProblems'] == 0
        assert fallback_result['fallbackMode'] == True


if __name__ == '__main__':
    # 运行测试
    pytest.main([__file__, '-v'])
