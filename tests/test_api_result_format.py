"""
测试API返回结果格式
验证优化后的聚合结果是否正确返回给API调用者
"""

import asyncio
import sys
import os
import json
from unittest.mock import Mock, patch

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.cr_service import CRService
from core.orchestrator.cr_orchestrator import CROrchestrator


def create_mock_services():
    """创建模拟服务"""
    return {
        'horn_service': Mock(),
        'kms_service': Mock(),
        'dx_service': Mock(),
        'git_service': <PERSON>ck(),
        'devmind_service': <PERSON>ck()
    }


def create_sample_code_diff():
    """创建示例代码差异"""
    return """
diff --git a/src/main/java/UserService.java b/src/main/java/UserService.java
index 1234567..abcdefg 100644
--- a/src/main/java/UserService.java
+++ b/src/main/java/UserService.java
@@ -10,6 +10,10 @@ public class UserService {
     public User getUserById(String userId) {
+        if (userId == null) {
+            throw new IllegalArgumentException("User ID cannot be null");
+        }
         return userRepository.findById(userId);
     }
 }
    """.strip()


async def test_cr_service_api_result():
    """测试CR服务API结果格式"""
    print("🧪 测试CR服务API结果格式...")

    # 创建模拟服务
    mock_services = create_mock_services()

    # 模拟服务注册表
    with patch('core.service_registry.get_service') as mock_get_service:
        # 配置模拟服务返回
        def mock_service_getter(service_name):
            if service_name == 'llm_service':
                return Mock()
            elif service_name == 'chunk_service':
                return Mock()
            return None

        mock_get_service.side_effect = mock_service_getter

        # 模拟工作流管理器
        with patch('core.orchestrator.workflow_manager.get_workflow_manager') as mock_get_wm:
            mock_workflow_manager = Mock()
            mock_get_wm.return_value = mock_workflow_manager

            # 创建CR服务实例
            cr_service = CRService(**mock_services)
    
    # 模拟编排器返回的聚合结果
    mock_aggregated_result = {
        'summary': {
            'checkBranch': 'feature/test-api',
            'reviewTime': '2024-01-15 14:30:00',
            'reviewer': 'AI代码审查系统',
            'overallResult': '通过',
            'resultDescription': '代码质量良好',
            'totalProblems': 0,
            'crMode': 'standard'
        },
        'scoring': {
            'overallScore': 95,
            'maxScore': 100,
            'qualityGrade': 'A',
            'isPassed': True,
            'dimensions': {
                'criticalIssues': {'score': 40, 'maxScore': 40},
                'warningIssues': {'score': 30, 'maxScore': 30},
                'moderateIssues': {'score': 20, 'maxScore': 20},
                'minorIssues': {'score': 5, 'maxScore': 10}
            }
        },
        'problems': [],
        'statistics': {
            'totalProblems': 0,
            'criticalCount': 0,
            'warningCount': 0,
            'moderateCount': 0,
            'minorCount': 0
        },
        'reviewMetrics': {
            'qualityScore': 95,
            'riskLevel': '低',
            'problemDensity': '0/文件'
        },
        'recommendations': ['代码质量良好，保持当前标准'],
        'originalReviewResults': [],
        'segmentsInfo': {'count': 1, 'segments': []}
    }

    # 模拟编排器
    mock_orchestrator = Mock()
    mock_orchestrator.execute.return_value = Mock(
        success=True,
        data=mock_aggregated_result,
        metadata={'execution_time': 1.5}
    )

    # 配置工作流管理器
    mock_workflow_manager.get_orchestrator.return_value = mock_orchestrator
    mock_workflow_manager.create_orchestrator.return_value = mock_orchestrator
    mock_workflow_manager.execute_workflow.return_value = Mock(
        success=True,
        data=mock_aggregated_result,
        metadata={'execution_time': 1.5}
    )

    # 初始化CR服务
    await cr_service.initialize()

    # 准备测试参数
    request = Mock()
    code_diff = create_sample_code_diff()
    params = {
        'project': 'test-project',
        'repo': 'test-repo',
        'fromBranch': 'feature/test-api',
        'toBranch': 'main',
        'prLink': 'https://example.com/pr/123'
    }

    # 执行CR请求
    result = await cr_service.process_cr_request(request, code_diff, params)

    print(f"  📊 API返回结果结构:")
    print(f"    结果字段: {list(result.keys())}")

    # 验证API返回结果包含完整的UI展示数据
    expected_ui_fields = ['summary', 'scoring', 'problems', 'statistics', 'reviewMetrics', 'recommendations']
    for field in expected_ui_fields:
        if field in result:
            print(f"    ✅ 包含UI字段: {field}")
        else:
            print(f"    ⚠️  缺少UI字段: {field}")

    # 验证兼容性字段
    compatibility_fields = ['checkBranch', 'sumCheckResult', 'totalProblem', 'resultDesc']
    for field in compatibility_fields:
        if field in result:
            print(f"    ✅ 包含兼容字段: {field}")
        else:
            print(f"    ⚠️  缺少兼容字段: {field}")

    # 验证具体数据
    if 'summary' in result:
        summary = result['summary']
        print(f"    📋 总结信息:")
        print(f"      分支: {summary.get('checkBranch', 'N/A')}")
        print(f"      结果: {summary.get('overallResult', 'N/A')}")
        print(f"      问题数: {summary.get('totalProblems', 'N/A')}")

    if 'scoring' in result:
        scoring = result['scoring']
        print(f"    🎯 评分信息:")
        print(f"      总分: {scoring.get('overallScore', 'N/A')}")
        print(f"      等级: {scoring.get('qualityGrade', 'N/A')}")
        print(f"      通过: {scoring.get('isPassed', 'N/A')}")

    # 验证兼容性字段的正确性
    if 'sumCheckResult' in result and 'summary' in result:
        expected_result = result['summary'].get('overallResult', '通过')
        actual_result = result['sumCheckResult']
        if expected_result == actual_result:
            print(f"    ✅ 兼容字段sumCheckResult正确: {actual_result}")
        else:
            print(f"    ❌ 兼容字段sumCheckResult不匹配: 期望{expected_result}, 实际{actual_result}")

    if 'totalProblem' in result and 'summary' in result:
        expected_total = str(result['summary'].get('totalProblems', 0))
        actual_total = result['totalProblem']
        if expected_total == actual_total:
            print(f"    ✅ 兼容字段totalProblem正确: {actual_total}")
        else:
            print(f"    ❌ 兼容字段totalProblem不匹配: 期望{expected_total}, 实际{actual_total}")

    print(f"  ✅ CR服务API结果格式测试完成")
    return result


def test_api_response_json_serialization():
    """测试API响应的JSON序列化"""
    print("🧪 测试API响应JSON序列化...")
    
    # 创建包含完整UI数据的示例结果
    sample_result = {
        'summary': {
            'checkBranch': 'feature/test',
            'reviewTime': '2024-01-15 14:30:00',
            'reviewer': 'AI代码审查系统',
            'overallResult': '不通过',
            'totalProblems': 3
        },
        'scoring': {
            'overallScore': 75,
            'qualityGrade': 'C',
            'isPassed': False
        },
        'problems': [
            {
                'level': 'P0',
                'problem': '空指针异常风险',
                'suggestion': '添加空值检查',
                'targetCode': 'user.getName()',
                'codePosition': [10, 1, 10, 15]
            }
        ],
        'statistics': {
            'totalProblems': 3,
            'criticalCount': 1,
            'warningCount': 2
        },
        'reviewMetrics': {
            'qualityScore': 75,
            'riskLevel': '中'
        },
        'recommendations': ['优先修复严重问题'],
        # 兼容性字段
        'checkBranch': 'feature/test',
        'sumCheckResult': '不通过',
        'totalProblem': '3',
        'resultDesc': 'P0:1个,P1:2个'
    }
    
    try:
        # 测试JSON序列化
        json_str = json.dumps(sample_result, ensure_ascii=False, indent=2)
        print(f"  ✅ JSON序列化成功，长度: {len(json_str)} 字符")
        
        # 测试JSON反序列化
        parsed_result = json.loads(json_str)
        print(f"  ✅ JSON反序列化成功，字段数: {len(parsed_result)}")
        
        # 验证关键字段
        key_fields = ['summary', 'scoring', 'problems', 'checkBranch', 'sumCheckResult']
        for field in key_fields:
            if field in parsed_result:
                print(f"    ✅ 关键字段存在: {field}")
            else:
                print(f"    ❌ 关键字段缺失: {field}")
        
        print(f"  ✅ API响应JSON序列化测试通过")
        return True
        
    except Exception as e:
        print(f"  ❌ JSON序列化测试失败: {str(e)}")
        return False


def simulate_api_call():
    """模拟API调用流程"""
    print("🧪 模拟API调用流程...")
    
    # 模拟main_app.py中的处理逻辑
    def mock_api_handler():
        # 模拟从CR服务获取的结果（优化后的聚合结果）
        cr_result = {
            'summary': {
                'checkBranch': 'feature/api-test',
                'reviewTime': '2024-01-15 15:00:00',
                'reviewer': 'AI代码审查系统',
                'overallResult': '通过',
                'totalProblems': 0
            },
            'scoring': {
                'overallScore': 100,
                'qualityGrade': 'A',
                'isPassed': True
            },
            'problems': [],
            'statistics': {'totalProblems': 0},
            'reviewMetrics': {'qualityScore': 100},
            'recommendations': ['代码质量优秀'],
            # 兼容性字段
            'checkBranch': 'feature/api-test',
            'sumCheckResult': '通过',
            'totalProblem': '0',
            'resultDesc': '无问题发现'
        }
        
        # 模拟Flask响应处理
        try:
            response_data = json.dumps(cr_result, ensure_ascii=False, indent=2)
            print(f"  📤 API响应数据生成成功")
            print(f"    响应大小: {len(response_data)} 字符")
            print(f"    包含字段: {list(cr_result.keys())}")
            
            # 验证响应包含UI所需的完整数据
            ui_required_fields = ['summary', 'scoring', 'problems', 'statistics', 'reviewMetrics', 'recommendations']
            ui_fields_present = [field for field in ui_required_fields if field in cr_result]
            print(f"    UI字段覆盖: {len(ui_fields_present)}/{len(ui_required_fields)}")
            
            # 验证兼容性字段
            compat_fields = ['checkBranch', 'sumCheckResult', 'totalProblem']
            compat_fields_present = [field for field in compat_fields if field in cr_result]
            print(f"    兼容字段覆盖: {len(compat_fields_present)}/{len(compat_fields)}")
            
            return {
                'status': 'success',
                'data': cr_result,
                'response_size': len(response_data),
                'ui_fields_coverage': len(ui_fields_present) / len(ui_required_fields),
                'compat_fields_coverage': len(compat_fields_present) / len(compat_fields)
            }
            
        except Exception as e:
            print(f"  ❌ API响应处理失败: {str(e)}")
            return {'status': 'error', 'error': str(e)}
    
    # 执行模拟API调用
    result = mock_api_handler()
    
    if result['status'] == 'success':
        print(f"  ✅ API调用模拟成功")
        print(f"    UI字段覆盖率: {result['ui_fields_coverage']*100:.1f}%")
        print(f"    兼容字段覆盖率: {result['compat_fields_coverage']*100:.1f}%")
        print(f"    响应大小: {result['response_size']} 字符")
        return True
    else:
        print(f"  ❌ API调用模拟失败: {result.get('error', '未知错误')}")
        return False


async def main():
    """运行所有测试"""
    print("🚀 开始测试API返回结果格式")
    print("=" * 60)
    
    success_count = 0
    total_tests = 3
    
    try:
        # 测试1: CR服务API结果格式
        await test_cr_service_api_result()
        success_count += 1
        print()
        
        # 测试2: JSON序列化
        if test_api_response_json_serialization():
            success_count += 1
        print()
        
        # 测试3: API调用流程模拟
        if simulate_api_call():
            success_count += 1
        print()
        
        print("=" * 60)
        print(f"🎉 测试完成: {success_count}/{total_tests} 个测试通过")
        
        if success_count == total_tests:
            print("✅ 所有测试通过！API返回结果格式优化成功")
            print("📊 API现在返回包含完整UI展示数据的聚合结果")
            return True
        else:
            print("⚠️  部分测试失败，需要进一步检查")
            return False
        
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == '__main__':
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
