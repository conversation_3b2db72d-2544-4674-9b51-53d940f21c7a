import unittest
import os
from api.service.chunk_service import ChunkService
from api.service.git_service import GitService
import json
from api.service.git_service import GitService
from api.service.chunk_service import ChunkService

class TestChunkServiceIntegration(unittest.TestCase):
    @classmethod
    def setUpClass(cls):
        # 选择一个公开仓库（如python/cpython的一个小仓库分支）
        cls.project = 'reco'
        cls.repo = 'state_subsidies_audit'
        cls.branch = 'feature/scaffold-20250416'
        cls.git_service = GitService()
        # 直接用list_repo_files自动clone+checkout
        cls.chunker = ChunkService(cls.git_service, cls.project, cls.repo, cls.branch)
        cls.suffixes = ['.java']
        cls.output_path = 'output.json'

    def test_chunk_and_save(self):
        # 先确保分支文件拉取
        files = self.chunker.git_service.list_repo_files(self.chunker.project, self.chunker.repo,
                                                         branch=self.chunker.branch, suffixes=self.suffixes)
        self.assertTrue(len(files) > 0)
        # 分块
        chunks = self.chunker.chunk_all_files(self.suffixes)
        self.chunker.save_chunks_to_json(chunks, self.output_path)
        self.assertTrue(os.path.exists(self.output_path))
        with open(self.output_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        self.assertIsInstance(data, list)
        self.assertTrue(any('type' in c for c in data))

    @classmethod
    def tearDownClass(cls):
        if os.path.exists(cls.output_path):
            os.remove(cls.output_path)


if __name__ == '__main__':
    unittest.main()



